import express from 'express';
import OfferCategoryController from '../controllers/offerCategoryController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initofferCategoryRoutes = () => {
  const offerCategoryRoutes = express.Router();

  offerCategoryRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  OfferCategoryController.create);
  offerCategoryRoutes.post('/getOfferCategory',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON><PERSON><PERSON> ,  OfferCategoryController.getOfferCategory);

  return offerCategoryRoutes;
};

export default initofferCategoryRoutes;
