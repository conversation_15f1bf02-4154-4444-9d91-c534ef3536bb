angular.module('userContacts.controllers', [])

    .controller('userContactsCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';


       APIService.getData({
            req_url: PrefixUrl + '/userContacts/show'
        }).then(function(resp) {
          	console.log("====resp======",resp);
        	$scope.contactsList=resp.data;
           },function(resp) {
              // This block execute in case of error.
               
        });


        
        $scope.viewContacts= function(user){
           $state.go("app.viewUserContacts",{data:JSON.stringify(user)});
        }

        $scope.findUserBusiness = function(user) {
     
         console.log(user)
         $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
       }
})

