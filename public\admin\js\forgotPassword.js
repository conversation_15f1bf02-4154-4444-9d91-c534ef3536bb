angular.module('forgotPassword.controllers', [])

.controller('forgotPasswordCtrl', function($scope, $state,APIService,$stateParams) {   
    $scope.page = 'main';
    $scope.phone_number;
    $scope.userdata= {};
    $scope.jsonData= {};
    $scope.disableButton= false;
    
    $scope.verifyPhoneNumber = function() {
        $scope.disableButton= true;

        console.log('ppp'+$scope.phone_number)
                if( $scope.phone_number){
                // APIService.setData({ req_url: PrefixUrl + "/user/forgotPassword/" ,data:{phone_number:$scope.phone_number,role:'subAdmin'}})
                // .then(function (res1) {
                    // console.log(res1)
                    APIService.getData({ req_url: PrefixUrl + "/user/getUserForPhoneNumber/" + $scope.phone_number})
                    .then(function (user) {
                        console.log(user)
                        if (user.length > 0 && !user[0].active_status) {
                            alert('User is not active');
                            $scope.disableButton= false;

                        }else{
                            if (user.length > 0 && user[0].suspend == true) {
                                alert('User is Suspended');
                                $scope.disableButton= false;

                            }else{
                                APIService.setData({ req_url: PrefixUrl + "/user/forgotPassword/" ,data:{phone_number:$scope.phone_number,role:'admin'}})
                                .then(function (data) {
                                    console.log('forgotdata ',data)
                                        if(data.data["success"]){
                                            APIService.setData({ req_url: PrefixUrl + "/user/otp",data:{phone_number: $scope.phone_number}})
                                            .then(function (sendOtp) {
                                                $scope.userdata.phone_number= $scope.phone_number;
                                                // $scope.userdata.otp= sendOtp.data.message;
                                                console.log('sendOtp=== ',sendOtp)
                                                if (sendOtp.data.success) {
                                                    $scope.jsonData.mobile= $scope.userdata;
                                                    $scope.jsonData.come_from = 'forgotPassword'
                                                    $state.go("otp",{data:JSON.stringify($scope.jsonData)});
                                                    
                                                }
                                            
                                            },function(er){
                                                $scope.disableButton= false;
                             
                                            })
                                            // alert('send otp page');
                                            // this.presentToast("Sms Sent");
                                            // this.navCtrl.push(OtpPage,{mobile:userLogin.phone_number,from:'forgot'});
                                        }else{
                                            alert(data.data['message'])
                                            $scope.disableButton= false;

                                        }
                                });
                            }
                        }

                    })
                    // if (res1.data.message) {
                    //     console.log('mobile verify')
                    //     var jsonData={};
                    //     jsonData.phone_number= $scope.phone_number;
                    //     jsonData.come_from= 'forgotPassword';
                    //     jsonData.otp= res1.data.message;
                    //  $state.go("app.otp",{data:JSON.stringify(jsonData)});

         
                    // }
                   
                // },function(er){
                 // 
                // })
            }else{
                alert("Please fill phone number.")
                $scope.disableButton= false;

            }
    }


})