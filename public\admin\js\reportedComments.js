angular.module('reportedComments.controllers', [])

    .controller('reportedCommentsCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.newRecord=true;
        $scope.processedRecord=false;




    	 
    	// $scope.deleteComment = function(rate_review_id,reportCommentId){
    	// 	APIService.removeData({ req_url: PrefixUrl + "/rateReview/removeReview/"+rate_review_id }).then(function (res) {

     //           APIService.removeData({ req_url: PrefixUrl + "/reportComment/removeReview/"+reportCommentId }).then(function (res) {
     //              alert('removed successfully')
     //              location.reload(); 

     //            },function(er){

     //            })
     //           // location.reload(); 

	    //     },function(er){

	    //     })
    	// }


      $scope.hideReview = function(rate_review_id,reportCommentId){
              if (confirm("Are you sure")) { 
               APIService.updateData({ req_url: PrefixUrl + "/reportComment/hideReview/"+reportCommentId }).then(function (res) {
                  alert('Hide successfully')
                  location.reload(); 

                },function(er){

                })
             }
               // location.reload(); 


      }


      

      $scope.deleteReview = function(rate_review_id,reportCommentId){
              if (confirm("Are you sure")) { 
               APIService.removeData({ req_url: PrefixUrl + "/rateReview/removeReview/"+rate_review_id }).then(function (res) {
                  // alert('Deleted successfully')
                  // location.reload();
                   APIService.removeData({ req_url: PrefixUrl + "/reportComment/removeReview/"+reportCommentId }).then(function (res) {
                      alert('Deleted successfully')
                      location.reload(); 

                    },function(er){

                    }) 

                },function(er){

                })
             }
               // location.reload(); 


      }


        $scope.newRecords=function(){

            $scope.newRecord=true;
            $scope.processedRecord=false;
            
             APIService.getData({ req_url: PrefixUrl + "/reportComment/viewComments/"}).then(function (res) {
             console.log('result')
             console.log(res)
             $scope.viewComments= res.data;
             $scope.viewCommentsLength= res.data.length;

              
              },function(er){

              })
            
        }


        $scope.processedRecords=function(){

            $scope.newRecord=false;
            $scope.processedRecord=true;
            
             APIService.getData({ req_url: PrefixUrl + "/reportComment/viewCommentsProcessed/"}).then(function (res) {
             console.log('result')
             console.log(res)
             $scope.viewComments= res.data;
             $scope.viewCommentsLength= res.data.length;

              
              },function(er){

              })
            
        }


        $scope.reviewCount=function(){

            $scope.newRecord=false;
            $scope.processedRecord=true;
            
             APIService.setData({ req_url: PrefixUrl + "/reportComment/reviewCount/"}).then(function (res) {
              

              
              },function(er){

              })
            
        }

    


        $scope.verify= function(obj_id,processedComment){
          console.log('sssssssssssss',processedComment)
          
          if (confirm("Are you sure?")) {
            APIService.updateData({
                req_url: PrefixUrl + '/reportComment/update/'+ obj_id,data:{processedComment:processedComment}
            }).then(function(resp) {
              // console.log("====resp======",resp);
              alert('verified successfully')
              location.reload();

              console.log('reeeeeeeeee'+resp.suspend)
               },function(resp) {
                  // This block execute in case of error.
            });
          }
        }

      $scope.reviewCount();  

      $scope.newRecords();


    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }

    });