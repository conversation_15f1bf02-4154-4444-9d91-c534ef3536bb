import Responder from '../../lib/expressResponder';
import RateReview from '../models/rateReview';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class RateAndReviewController {

  static page(req, res) {


    RateReview.aggregate([ 
          {
                $match: {
                    'user_id':ObjectId(req.params.id)
                  }
                
          },
          
            { $lookup:
             {
               from: 'users',
               localField: 'rated_by',
               foreignField: '_id',
               as: 'userDetails'
             }
           }


                ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   

  }



  static getByPostMethod(req, res) {

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)


    RateReview.aggregate([ 
          
              {
                    $match: {
                      // $and:[
                        // 'user_id': req.params.id,
                        // 'status': 'ACTIVE',
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'reported'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'rated_by',
                   foreignField: '_id',
                   as: 'reportedBy'
                 }
               },
               { 
                "$sort": {  
                    "created_at": -1,
                } 
              }

                ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   

  }


  static reviewCount(req, res) {

    RateReview.aggregate([ 
            {
                  $match: {
                    // $and:[
                      // 'user_id': req.params.id,
                      // 'status': 'ACTIVE',
                      // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                      // ]
                    }
                  
            },
            { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'reported'
               }
             },
              { $lookup:
               {
                 from: 'users',
                 localField: 'rated_by',
                 foreignField: '_id',
                 as: 'reportedBy'
               }
             },
             {
               $group: {
                    _id: {
                      
                    },
                    myCount: { $sum: 1 } ,
                  }
            },

              ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))

  }

  static show(req, res) {

    RateReview.aggregate([ 
              {
                    $match: {
                      // $and:[
                        // 'user_id': req.params.id,
                        // 'status': 'ACTIVE',
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'reported'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'rated_by',
                   foreignField: '_id',
                   as: 'reportedBy'
                 }
               },
               { 
                "$sort": {  
                    "created_at": -1,
                } 
              }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   

   

    //  var rateRe =[];
    //  RateReview.find({})
    // .then((usr)=> {rateRe=usr; return User.find({_id:{$in:_.map(usr,'rated_by')}})})
    //   .then((user)=>{console.log("VEH====",user) ; var response=[];_.each(rateRe,us =>{
    //     var userDe={_id:us._id,
    //       user_id:us.user_id,
    //       rated_by:us.rated_by,
    //       review:us.review,
    //       reg_no:us.reg_no,
    //       rate:us.rate,
    //     };
      
    
    //   _.each(user,usr=>{
    //   if(usr._id == userDe.rated_by){
    //     userDe.reportedBy=usr;
    //   }
    //   if(usr._id == userDe.user_id){
    //     userDe.reported=usr;
    //   }
         
    //   }) 
    //   response.push(userDe);
    // });Responder.success(res,response) })
    // .catch((err)=>Responder.operationFailed(res,err))
    //   // .then((usr)=> {rateRe=usr; return User.find({_id:{$in:_.map(usr,'rated_by')}})})
    //   // .then((user)=>{console.log("VEH====",user) ; var response=[];_.each(rateRe,us =>{
    //   //   var userDe={_id:us._id,
    //   //     user_id:us.user_id,
    //   //     rated_by:us.rated_by,
    //   //     review:us.review,
    //   //     reg_no:us.reg_no,
    //   //     rate:us.rate,
    //   //   };
      
    //   //   _.each(user,usde=>{
    //   //   if(usde._id == userDe.rated_by){
    //   //     userDe.userDetails=usde;
    //   //   }
           
           
    //   //   }) 
    //   //   response.push(userDe);
    //   // });Responder.success(res,response) })
    //   // .catch((err)=>Responder.operationFailed(res,err))
   

   
  }
 
  static create(req, res) {
    
    RateReview.create(req.body)
       .then((rateR)=>Responder.success(res,rateR))
       .catch((err)=>Responder.operationFailed(res,err))
    
      
  }

  static update(req, res) {

    if(!req.params.vehicle_id)
    return Responder.operationFailed(res,{message:'Vehicle Id Is Required.'})
    Vehicle.findOneAndUpdate({_id:req.params.vehicle_id},{$set:req.body})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
  }

  static remove(req, res) {
    Vehicle.remove({_id:req.params.id})
    .then((product)=>Responder.success(res,product))
    .catch((err)=>Responder.operationFailed(res,err))
  }

  static reviewBy(req, res) {
    console.log('uuuuuuuu'+req.body.user_id)
    console.log(req.body)
    RateReview.count({ $and: [{ user_id:ObjectId(req.body.feedbackToId),rated_by:ObjectId(req.body.user_id)}]},function(err,count){
      console.log(count);
      Responder.success(res,{data:{count}});
    });
   
  }

  static removeReview(req, res) {
    console.log(req.params);
    RateReview.deleteOne({ _id: req.params.id })
    .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }


  
  static filterReview(req, res) {
      User.aggregate([ 
                   {
                    $match: {
                              $or: [ 
                               {name: {$regex : "^" + req.body.name,$options: 'i'}},
                               {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                               {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                               {district: {$regex : "^" + req.body.district,$options: 'i'}},
                               
                              ]
                              
                            },
                        
                  }

                        ])
        
        .then((makers)=>{
            var object= [];
            makers.forEach(function (maker, k) {                
              console.log('ffffffffffffffff',maker.name)
              object.push(ObjectId(maker._id))
            });


            console.log('ooooooo',object)


                RateReview.aggregate([ 
                   {
                    $match: {
                              // $and: [ {'role': {$ne: 'admin'}} ,
                               // user_id: object,
                               // "user_id":{$all:object}
                               user_id: { $in: object }
                              // ]
                              
                            }
                    },   
                      { $lookup:
                       {
                         from: 'users',
                         localField: 'user_id',
                         foreignField: '_id',
                         as: 'reported'
                       }
                     },
                      { $lookup:
                       {
                         from: 'users',
                         localField: 'rated_by',
                         foreignField: '_id',
                         as: 'reportedBy'
                       }
                     },
                     { 
                        "$sort": {  
                            "created_at": -1,
                        } 
                      }
                  ])
              .then((product)=> {
                console.log('lengthhhhhh'+product.length)
                // if (product.length > 0) {
                  Responder.success(res,product)
                // }
              }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
  }



}
