import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const ExtendDayLogSchema = new Schema({
    renewal_date: Number,
    state: String,
    created_at:{ type: Date, default: Date.now },
    user_id: ObjectId
});
ExtendDayLogSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('ExtendDayLog', ExtendDayLogSchema);
