import Responder from '../../lib/expressResponder';
import User from '../models/user';
import Transaction from '../models/transaction';
import GatewayTransaction from '../models/gatewayTransaction';
import user from '../models/user';
import Vehicle from '../models/vehicle';
import _ from "lodash";
import http from 'http';
import async from 'async';
import otplib from 'otplib';
import config from '../../config/default';
import nodemailer from 'nodemailer';
const { sendNotification } = require('./helpers/push_notification');
import WithdrawWallet from '../models/withdrawWallet';
import CommonSettings from '../models/commonSettings';
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
// import Responder from '../../public/pdfDocs';
import Trip from '../models/trip';
import Settings from '../models/settings';

var dateFormat = require('dateformat');
var moment = require('moment-timezone');
const crypto = require("crypto");
const https = require('https');

const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
import TripArchieve from '../models/tripArchieve';
import getFileSignedUrl from './helpers/spacesHelper';
import sendTXTGuruSMS from './helpers/sms_helper';
import wpSendOtp from './helpers/whatsapp_helper';

import { PutObjectCommand, GetObjectCommand, GetObjectAclCommand,S3Client } from '@aws-sdk/client-s3';
const baseSpaces = "https://swaritest.sgp1.digitaloceanspaces.com";
// Step 2: The s3Client function validates your request and directs it to your Space's specified endpoint using the AWS SDK.
const s3Client = new S3Client({
    endpoint: "https://sgp1.digitaloceanspaces.com", // Find your endpoint in the control panel, under Settings. Prepend "https://".
    forcePathStyle: false,
    region: "sgp1", // Must be "us-east-1" when creating new Spaces. Otherwise, use the region in your endpoint (e.g. nyc3).
    credentials: {
      accessKeyId: "DO00DDZBQ27VM24JGC8W", // Access key pair. You can create access key pairs using the control panel or API.
      secretAccessKey: "zF2lMVNnNkZYL9M6pfdcyqZPe3Hw5wSgzMc3yCFx96E" // Secret access key defined through an environment variable.
    }
});

var pdf = require('dynamic-html-pdf');
var html;
var myCache  = require( "../service/appCache" );


// for (var i = 0; i <= 50; i++) {

//    User.create({
//     // "name" : "Cjvksd", "phone_number" : "8000000"+i, "email" : "<EMAIL>", "password" : "$2b$10$4Bf8uzpaMvDdut1u/sb/sergUYjBbaB2frwueRjGN2xxXqLAtMFYK", "referal_code" : "5TQ"+i, "referred_by_code": "**********", "renewal_date" : 'ISODate("2019-11-25T00:00:00Z")', "bankDetails" : { "remarks" : "" }, "role" : "normalUser", "rating_review" : [ ], "report" : [ ], "wallet_balance" : 99999, "is_subscribed" : false, "created_at" : 'ISODate("2019-11-15T11:35:26.098Z")', "suspend" : false, "__v" : 0, "device_id" : "7e5e8e0e78727a78", "device_info" : { "serial" : "11d9a81f7d73", "manufacturer" : "Xiaomi", "isVirtual" : false, "uuid" : "7e5e8e0e78727a78", "version" : "6.0.1", "platform" : "Android", "model" : "Redmi 3S", "cordova" : "8.1.0" }, "device_platform" : "Android", "fcm_registration_token" : "dUklu97X6oo:APA91bEefkYlrT3qEHDyi-TOXBOgPXYP2cQu8mjdRmY-wb-pkM3-cMbvRr1hXYDyx4iz0pKgrfOSGQvhPoNs_sIQlaarY_fxNh0zPD0cH_IAxxrd7A8Pzc6yjRFk352uPppbK0-SicRd", "address" : "Vcjcj", "district" : "CJK", "state" : "Bihar", "active_status" : true 
//     "name" : "aaaaaaaaaa", 
//     "phone_number" : "99997"+i,
//     "referal_code" : "6UKP"+i, 
//     "referred_by_code": "NZZM0L",
//      // "renewal_date" : 'ISODate("2019-11-25T00:00:00Z")'
//    }).then((user) => function(){
//       console.log('************ --  ',user)
//     })

    
//     console.log(i+'   user')
// }


  console.log('crondate-- ',moment.tz('Asia/Kolkata').format('YYYY-MM-DDT00:00:00.000'))
    console.log('crondate renewal_date-- ',moment(new Date()).toDate())
    console.log('crondate renewal_date update-- ',new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)))
    console.log('crondate renewal_date update--1 ',moment.utc().format('YYYY-MM-DDTHH:mm:ss.SSS'))
    console.log('crondate renewal_date update--2 ',moment.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'))
    console.log('crondate renewal_date update--3 ',moment().format('YYYY-MM-DDTHH:mm:ss.SSS'))
    console.log('crondate renewal_date update--4 ',moment().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'))
    console.log('crondate renewal_date update--5 ',moment().toDate())
    console.log('crondate renewal_date update--6 ',moment.utc().toDate())
    console.log('crondate renewal_date update--7 ',moment.utc().add(5.5,'hours').startOf('day').toDate());
    console.log('crondate renewal_date update--8 ',moment(new Date()).utc())
    console.log('crondate renewal_date update--9 ',moment(new Date(),'HH:mm:ss.SSS').utc())
    console.log('crondate renewal_date update--9 ',moment().format('YYYY-MM-DDTHH:mm:ss.SSS'))

var freetrialperiod=moment(new Date());
freetrialperiod=moment(freetrialperiod).format('YYYY-MM-DDTHH:mm:ss.SSS');

//freetrialperiod=moment(freetrialperiod).startOf('day').format();
// freetrialperiod=moment()
console.log('mm---',freetrialperiod)
function createPdfBoostTrip(transaction_id, amount, balance, date, gst, origin, destination, newSubject, user) {

    const PDFDocument = require('pdfkit');

    // Create a document
    const doc = new PDFDocument;

    // Pipe its output somewhere, like to a file or HTTP response
    // See below for browser usage
    var fs = require('fs');

    doc.pipe(fs.createWriteStream('pdfDocs/tr_id_'+transaction_id+'.pdf'));

    // Embed a font, set the font size, and render some text

    // doc.image('public/img/icon.png', 0, 15, {width: 300})
   // .text('Proportional to width', 0, 0);
    // doc.image('public/img/icon.png', 320, 145, {width: 200, height: 100}, align: 'center', valign: 'center')
   // .text('Stretch', 320, 130);

   // doc.image('public/img/background_design.png', 0, 0, { align: 'center', valign: 'center'})
   // Stretch the image
    
    doc.image('public/img/header.jpg', -100, 0, {width: 800, height: 100});

   doc.image('public/img/icon.png', 230, 10, {fit: [100, 100], align: 'center', valign: 'center'})

    doc.fontSize(30);
    doc.text('                      ', {
      // width: 410,
      align: 'left'
    }
    );

    doc.fontSize(12);
    doc.text('Date: '+date, {
      width: 410,
      align: 'left'
    }
    );


    doc.fontSize(12);
    doc.text('                      ', {
      // width: 410,
      align: 'center'
    }
    );

    doc.fontSize(10);
    doc.text('SWARI', {
      width: 410,
      align: 'center'
    }
    );

    doc.fontSize(12);
    doc.text('                      ', {
      // width: 410,
      align: 'left'
    }
    );

    doc.fontSize(25);
    doc.text('Trip Boost Invoice', {
      width: 410,
      align: 'center'
    }
    );


    doc.fontSize(14);
    doc.text('                      ', {
      width: 410,
      align: 'left'
    }
    );

    if (destination) {
      destination= destination;
    }else{
      destination= ''
    }

    doc.fontSize(12);
    doc.text('Trip From '+origin+ 'To '+ destination, {
      width: 410,
      align: 'center'
    }
    );


    doc.fontSize(18);
    doc.text('                      ', {
      width: 410,
      align: 'left'
    }
    );

    doc.fontSize(12);
    doc.text('Transaction Id                               '+transaction_id, {
      width: 410,
      align: 'left'
    }
    );

    doc.fontSize(18);
    doc.text('                      ', {
      width: 410,
      align: 'left'
    }
    );

    doc.fontSize(12);
    doc.text('Total Trip Boost Amount               Rs '+amount, {
      width: 410,
      align: 'left'
    }
    );


    doc.fontSize(12);
    doc.text('                      ', {
      // width: 410,
      align: 'left'
    }
    );

    doc.fontSize(12);
    doc.text('Trip Boost GST                            Rs '+gst, {
      // width: 410,
      align: 'left'
    }
    );

    doc.fontSize(18);
    doc.text('                      ', {
      // width: 410,
      align: 'left'
    }
    );


    doc.fontSize(12);
    doc.text('Amount                                         Rs '+(amount-gst), {
      // width: 410,
      align: 'left'
    }
    );

    // doc.fontSize(18);
    // doc.text('                      ', {
    //   // width: 410,
    //   align: 'left'
    // }
    // );

    // doc.fontSize(12);
    // doc.text('Balance                                          Rs '+'346.59', {
    //   // width: 410,
    //   align: 'left'
    // }
    // );


    doc.image('public/img/header.jpg', -100, 350, {width: 800, height: 100});

   // .text('Stretch', 320, 130);

    // Finalize PDF file
    doc.end();


      var mailOptions = {
          from: 'Swari <<EMAIL>>',
          to: user.email,
          subject:newSubject ,
          // html: 'pdfDocs/'+'tr_id_'+req.body.transaction_id+'.pdf',
          //  attachments:[{
          //     filename: 'tr_id_'+transaction_id+'.pdf',
          //     content: new Buffer('pdfDocs/'+'tr_id_'+transaction_id+'.pdf', 'base64'),
          //     contentType: 'application/pdf'
          // }]
          attachments : [
            { // use URL as an attachment
              filename: 'tr_id_'+transaction_id+'.pdf',
              path: 'pdfDocs/'+'tr_id_'+transaction_id+'.pdf'
              // streamSource: fs.createReadStream('pdfDocs/'+'tr_id_'+transaction_id+'.pdf')

            }
          ]
        };


        transporter.sendMail(mailOptions, function(error, info){
                  if (error) {
                    console.log(error);
                  } else {
                    console.log('Email sent: ' + info.response);
                    fs.unlink('pdfDocs/tr_id_'+transaction_id+'.pdf');
                    // send message start
                     // var extServerOptionsPost = {
                     //      hostname: "***************",
                     //      path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                     //      method: 'POST',
                     //      port: '80',
                     //      headers: {
                     //        'Content-Type': 'application/json'
                     //      }
                     //    }

                     //    const token = otplib.authenticator.generate(secret);
                     //    console.log(token);

                     //    var reqPost = http.request(extServerOptionsPost, function (response) {

                     //      response.on('data', function (data) {
                     //        console.log("line no: 87 ", data);
                     //      });
                     //    });
                     //    var body =
                     //      {
                            
                     //        // "smsContent": 'Boosting Trip fee Rs '+ req.body.amount +' deducted from your wallet on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' with TR ID '+ req.body.transaction_id +'. A/c Bal:RS '+ req.body.balance +'.00. Thanks triva.in',
                     //        "smsContent": 'pdfDocs/tr_id_'+'req.body.transaction_id.pdf',
                     //        "routeId": "1",
                     //        "mobileNumbers": user.phone_number,
                     //        // "mobileNumbers": '9855621130,7009232617',
                     //        "senderId": "SWARII",
                     //        "signature": "signature",
                     //        "smsContentType": "english"
                     //      }
                     //    reqPost.write(JSON.stringify(body));
                     //    reqPost.end();
                     //    reqPost.on('error', function (e) {
                     //      console.error("line: 102 " + e);
                     //    });
                    // send message end

                  }
                });


}


function phonePayCheckStatus(merchantTransactionId) {
    return new Promise((resolve, reject) => {
        const merchantId = 'SWARIONLINE';
        const saltKey = "4d83d84f-e3f1-46cd-933c-60d2f92a1661";
        const saltIndex = "1";

        const dataToHash = '/pg/v1/status/' + merchantId + '/' + merchantTransactionId + saltKey;
        const verfHash = crypto.createHash('sha256').update(dataToHash).digest('hex') + '###' + saltIndex;

        const headers = {
            'Content-Type': 'application/json',
            'X-VERIFY': verfHash,
            'X-MERCHANT-ID': merchantId,
        };

        const requestOptions = {
            hostname: 'api.phonepe.com',
            path: '/apis/hermes/pg/v1/status/' + merchantId + '/' + merchantTransactionId,
            method: 'GET',
            headers: headers,
        };

        const req = https.request(requestOptions, (res) => {
            let data = '';

            // A chunk of data has been received.
            res.on('data', (chunk) => {
                data += chunk;
            });

            // The whole response has been received.
            res.on('end', () => {
                const responseData = JSON.parse(data);
                resolve(responseData); // Resolve the promise with the API response data
            });
        });

        // Handle errors with the request
        req.on('error', (error) => {
            reject(error); // Reject the promise with the error
        });

        // End the request
        req.end();
    });
}


var common = require("./helpers/common");
var makeReferCode = require("./helpers/makeReferCode");

var jwt = require('jsonwebtoken');

const cron = require('node-cron');

console.log('date----',new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)))

// var task = cron.schedule('*/5 * * * * *', () => { //run after every five seconds
var task = cron.schedule('0 1 * * *', () => { // run at 1 am every night 
  // console.log('Printing this line every minute in the terminal');
  // showAllVehicleByUsers();


                    
});

function createPdf(car_reg_no,pdfFor,transaction_id, amount, total_amount, transaction_date, gstAmount, origin, destination , user){

    var str = origin;
    var checkPunjabOrigin;

  if (pdfFor == 'boostTrip') {
      html = fs.readFileSync('pdfTemplates/template.html', 'utf8');
      checkPunjabOrigin = str.includes("Punjab");
    console.log('?????????Punjab   ',checkPunjabOrigin)

    }
    else if (pdfFor == 'Razorpay') {
      html = fs.readFileSync('pdfTemplates/template1.html', 'utf8');
    }
    else if (pdfFor == 'subscribeFees') {
      html = fs.readFileSync('pdfTemplates/template2.html', 'utf8');
    }

    
    pdf.registerHelper('ifCond', function (v1, v2, options) {
    if (v1 === v2) {
        return options.fn(this);
    }
    return options.inverse(this);
    })

  console.log('createPdf  --- '+'transaction_id'+transaction_id +'amount'+amount +'total_amount'+total_amount+'transaction_date'+ transaction_date+'gstAmount'+gstAmount+'origin'+ origin+'destination'+ destination )
    // start pdf code
    var options = {
        format: "A3",
        orientation: "portrait",
        border: "10mm"
    };

    var igstAmount= (gstAmount*7)/100;
    var sgstAmount= (gstAmount*9)/100;
    var destination;
    console.log('destination:::::  >>> ',destination)
    if (!destination) {
      destination= null;
    }
    else{
    }
    var userData1 = [
        {
            pdfFor: pdfFor,
            transaction_id: transaction_id,
            amount: amount,
            total_amount: total_amount,
            transaction_date: transaction_date,
            gstAmount: gstAmount,
            igstAmount: igstAmount,
            sgstAmount: sgstAmount,
            origin: origin,
            checkPunjabOrigin:checkPunjabOrigin,
            destination: destination,
            user_name: user.name,
            car_reg_no: car_reg_no,
            tr_date: transaction_date,
        }
    ];

    var document = {
        type: 'file',     // 'file' or 'buffer'
        template: html,
        context: {
            userData: userData1
        },
        path: 'referImage/tr_id_'+transaction_id+'.pdf'    // it is not required if type is buffer
    };

    pdf.create(document, options)
        .then(res => {
            console.log('resssssssssssssss',res)
            var type;
            if (pdfFor == 'boostTrip') {
              type= 'transactionBoostTrip';

            }
            else if (pdfFor == 'Razorpay') {
              type= 'transactionRazorpay';

            }
            else if (pdfFor == 'subscribeFees') {
              type= 'transactionSubscriptionFees';

            }
            
            // star mail
             CommonSettings.find({type:type})
             .then((res)=>
             {
              console.log('rrrrresponse')
              console.log(res)
              console.log('rrrrresponse')

                  var oldsubject1=res[0].subject;
                  var userName= user.name;
                  var newSubject1 = oldsubject1.replace("{name}",userName);
                  var newSubject = newSubject1.replace("{amount}",amount);

                  var oldBodySubject1=res[0].emailBody;
                  var userName=user.name;
                  var newBodySubject1= oldBodySubject1.replace("{name}",userName);
                  newBodySubject1= newBodySubject1.replace("{name}",userName);
                  var newBodySubject= newBodySubject1.replace("{amount}",amount);


                      Settings.findOne({ type: 'app_settings' })
                      .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                        var mailOptions = {
                          from: 'Swari <'+app_settings.billingEmail+'>',
                          to: user.email,
                          subject:newSubject ,
                          html: newBodySubject,
                          // html: 'pdfDocs/'+'tr_id_'+req.body.transaction_id+'.pdf',
                          "attachments" : [
                            { // use URL as an attachment
                              filename: 'tr_id_'+transaction_id+'.pdf',
                              // filename: 'template'+'.pdf',
                              path: 'referImage/tr_id_'+transaction_id+'.pdf'
                            }
                          ]
                        };

                        transporter.sendMail(mailOptions, function(error, info){
                          if (error) {
                            console.log(error);
                          } else {
                            console.log('Email sent: ' + info.response);

                            // send message start
                             var extServerOptionsPost = {
                                  hostname: "***************",
                                  path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                  method: 'POST',
                                  port: '80',
                                  headers: {
                                    'Content-Type': 'application/json'
                                  }
                                }

                                const token = otplib.authenticator.generate(secret);
                                console.log(token);

                                var reqPost = http.request(extServerOptionsPost, function (response) {

                                  response.on('data', function (data) {
                                    console.log("line no: 87 ", data);
                                  });
                                });
                           
                                  var smsContent;
                                  var templateId = null;
                                  if (pdfFor == 'boostTrip') {
                                    smsContent= 'Boosting Trip fee Rs. '+ amount +' deducted on '+ dateFormat(new Date(transaction_date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ transaction_id +'. A/c Bal: Rs. '+ total_amount +'. Thanks. www.swari.in';
                                    templateId = "1507166609077394912";
                                    templateId = null;// "1507166609077394912";
                                  }
                                  else if (pdfFor == 'Razorpay') {
                                    smsContent= 'Payment is successfull via Razorpay';
                                  }
                                  else if (pdfFor == 'subscribeFees') {
                                    smsContent= 'Rs. '+ amount +' Subscription Fee deducted on '+ dateFormat(new Date(transaction_date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ transaction_id +'. Subscription valid for 30 days. A/c Bal: Rs. '+ balance +'. Thanks. www.swari.in';     
                                    templateId = "1507166609163112979";                             
                                  }


                                var body =
                                  {
                                    
                                    "smsContent": smsContent,
                                    // "smsContent": 'pdfDocs/tr_id_'+'req.body.transaction_id.pdf',
                                    "routeId": "1",
                                    "mobileNumbers": user.phone_number,
                                    // "mobileNumbers": '9855621130,7009232617',
                                    "senderId": "SWARII",
                                    "signature": "signature",
                                    "smsContentType": "english"
                                  }
                                
                                  if(templateId) {
                                    sendTXTGuruSMS(templateId,body);
                                    return Responder.success(res, { success: true, message: "" }) 
                                  }

                                /*  reqPost.write(JSON.stringify(body));
                                reqPost.end();
                                reqPost.on('error', function (e) {
                                  console.error("line: 102 " + e);
                                });
                                */
                            // send message end

                            //Unlink

                            fs.unlink('referImage/tr_id_'+transaction_id+'.pdf');

                          }
                        }); 

                  })
                  .catch((err) => Responder.operationFailed('res', err))




             })
             .catch((err)=>Responder.operationFailed('res',err)) 
            // end mail
        })
        .catch(error => {
            console.error('eeeeeeeeeeeeeeee',error)
        });
    // emd pdf code
}



function createToken(user) {
    return jwt.sign({ id: user.id, email: user.email }, config.jwtSecret, {
        expiresIn: 60*60*24*100 // 86400 expires in 24 hours
        // expiresIn: 200 // 86400 expires in 24 hours
      });
}





const secret = otplib.authenticator.generateSecret();
otplib.authenticator.options = {
  step: 1000, 
  // window: 1
  // window: [0]
}

var transporter = nodemailer.createTransport({
  host: 'server.gxhosts.com',
  service: 'SMTP',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'GSNfn2hqVZ'
  },
  tls: {
    rejectUnauthorized: false
  }
});


var count;


// function referalCode() {

//    return new Promise(function(resolve, reject) {

//    // });

//     let rederCode = "";
//     var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
//             console.log('000000000000  ');  

//     for (var i = 0; i < 6; i++){
//       rederCode += possible.charAt(Math.floor(Math.random() * possible.length));
//     }
//        User.count({'referal_code':rederCode}, function (err, count) {
//               console.log('1111111111111111111000  ',count);  
//               count= count
            
//             if(count>0){
//               referalCode();
//             }else{
//                 console.log('1111111111111111222  ',rederCode);  

//               // return(result);
//               resolve(rederCode);

//              }
//        });


//   });

// }

// start pdf code

// Custom handlebar helper
// pdf.registerHelper('ifCond', function (v1, v2, options) {
//     if (v1 === v2) {
//         return options.fn(this);
//     }
//     return options.inverse(this);
// })

// var options = {
//     format: "A3",
//     orientation: "portrait",
//     border: "10mm"
// };

// var users = [
//     {
//         name: 'aaa',
//         age: 24,
//         dob: '1/1/1991'
//     }
// ];

// var document = {
//     type: 'file',     // 'file' or 'buffer'
//     template: html,
//     context: {
//         users: users
//     },
//     path: "referImage/template.pdf"    // it is not required if type is buffer
// };

// pdf.create(document, options)
//     .then(res => {
//         console.log('resssssssssssssss',res)
//     })
//     .catch(error => {
//         console.error('eeeeeeeeeeeeeeee',error)
//     });
// emd pdf code




function referalCode() {


      let rederCode = "";
    var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            console.log('000000000000  ');  

    for (var i = 0; i < 6; i++){
      rederCode += possible.charAt(Math.floor(Math.random() * possible.length));
    }


      // return User
      //   .count({'referal_code':rederCode}).exec()
      //   .then(function(count) {

      //     if(count>0){
      //         referalCode();
      //       }else{
      //           console.log('1111111111111111222  ',rederCode);  

      //         // return(result);
      //       return rederCode;


      //         // resolve(rederCode);

      //        }
      //   })
      //   .catch(function(err) {
      //       console.log(err);
      //   });


       // var myPromise = () => {
         return new Promise((resolve, reject) => {
        
            
             User
        .count({'referal_code':rederCode}).exec()
        .then(function(count) {

          if(count>0){
              referalCode();
            }else{
                console.log('1111111111111111222  ',rederCode);  

              // return(result);
            resolve(rederCode);


              // resolve(rederCode);

             }
        });
        });
             // .toArray(function(err, data) {
             //    console.log('1111111111111111222  ',data);  

             //     // err 
             //     //    ? reject(err) 
             //     //    : resolve(data);
             //   });
         // });
       // };

       //  var callMyPromise = async () => {
          
       //    var result = await (myPromise());
       //    //anything here is executed after result is resolved
       //    return result;
       // };


}



// for userDetails
function getUserDataFunc(id,callback) {
let us;
 User.findOne({_id:id})
  .then((user)=>{return callback(user)})
 

}
export default class UserController {
  static page(req, res) {
    //getFromDB
    //.then(plans => Responder.success(res, plans))
    //.catch(errorOnDBOp => Responder.operationFailed(res, errorOnDBOp));
  }

  static count(req, res) {
  }


  static cronTripArchieveUpdate(req, res) {
    
    console.log('date---',moment(new Date()).subtract(1, 'days').format('YYYY-MM-DDT23:59:59.999[Z]'))
    var createdate = moment(new Date()).subtract(1, 'days').endOf('day').toDate();
    createdate = moment(createdate).add(5.5, 'hours').toDate()
    console.log('final date',createdate);
    // console.log('date---',moment(new Date()).subtract(1, 'days').endOf('day').toDate())
       Trip.update({ $or:[{status:"ACCEPTED"},{status:"CANCELLED"}],time:{$lt:moment.tz('Asia/Kolkata').format('YYYY-MM-DD')}},{$set:{status:"DONE"}},{multi: true}).
         then((result) =>{
          console.log('resultiiiiiiiiii',result)
          Trip.update({ status:"ACTIVE",time:{$lt:moment.tz('Asia/Kolkata').format('YYYY-MM-DD')}},{$set:{status:"NOT_BOOKED"}},{multi: true}).
         then((updateNotBooked) =>{
          console.log('updateNotBooked  ',updateNotBooked)
         })
      Trip.aggregate([
              {

                $match: {
                          time:{$lt:createdate}

                        }

              }]

         
        ).
         then((trips) =>{
            // console.log('past records',trips)
            if (trips.length > 0) {              
              async.forEachLimit(trips, 1, function(trip, userCallback){
                async.waterfall([
                  function(callback){
                    console.log('ttttttttrip--- ',trip)
                        trip.time = moment(trip.time).subtract(5.5,'hours').toDate();
                        trip.created_at = moment(trip.created_at).subtract(5.5,'hours').toDate();
                        trip.canceled_at = moment(trip.canceled_at?trip.canceled_at:trip.created_at).subtract(5.5,'hours').toDate();
                        trip.cancel_request_sent = moment(trip.cancel_request_sent?trip.cancel_request_sent:trip.created_at).subtract(5.5,'hours').toDate();
                        trip.booking_request_sent = moment(trip.booking_request_sent).subtract(5.5,'hours').toDate();
                        trip.booking_request_accept = moment(trip.booking_request_accept).subtract(5.5,'hours').toDate();
                        trip.cancel_request_accept = moment(trip.cancel_request_accept).subtract(5.5,'hours').toDate();



                        console.log('archieve trip ',trip);
                     TripArchieve.create(trip)
                      .then((tripvalue)=>{ 
                          console.log('create archieve req444',tripvalue)
                          callback(null,tripvalue);
                         })
                      .catch((err)=>Responder.operationFailed(res,err))
                  },
                  function(tripvalue,callback){
                    Trip.remove({_id: tripvalue._id})
                    .then((tripupdate)=>{ 
                      console.log('remove trip req222',tripupdate)
                      // Responder.success(res,tripupdate);
                     // callback(null,tripupdate);
                      callback(null);

                       })
                    .catch((err)=>Responder.operationFailed(res,err))
                  },        
                  
                ], function (err, result) {
                  // result now equals 'done'
                  console.log('done')
                  // Responder.success(res, 'success')

                  userCallback();
                });
              });
            }
            
            Responder.success(res,'success')
         
         });


        });

    
  }


  static cronJobCheckSubscription(req, res) {
   
  var renewal_date=moment(new Date()).add(30, 'days');
  renewal_date=moment(renewal_date).startOf('day').format('YYYY-MM-DDT00:00:01.000[Z]');
  console.log('cronJob------ ',moment.utc().add(5.5,'hours').toDate())
     
    
    Settings.findOne({  })
    .then((settings) =>{ 
      console.log('settings--- ',settings)
      var amount = parseFloat(settings.subscriptionFeesByAdmin + settings.gstAmount).toFixed(2);
      // var timeNow = new Date().getTime();
      // var  = new Date(timeNow - 1000  60  60 * 24).toISOString();
        User.find({                 
              renewal_date: {$lt:moment.utc().add(5.5,'hours').toDate()},
              role :"normalUser",
              is_subscribed:true,
              active_status:true,
              wallet_balance: {$lt:amount},    
              
            })
        .then((usr) => 
              {

                console.log('user found ????  ',usr)

                async.forEachLimit(usr, 1, function(user, userCallback){
                  console.log(user);
                  async.waterfall([
                      
                     function(callback){
                         console.log('user found ',user)

                           User.findOneAndUpdate({
                                  _id: user._id
                              }, {
                                  $set: {
                                      'is_subscribed': false,

                                  }
                              }).
                              exec((result) => {
                              console.log('user result ',result)
                              callback(null);

                                console.log("updated 1");
                              });
                       
                      },
                 
                  ], function (err, result) {
                      // result now equals 'done'
                      console.log('done')
                      userCallback();
                  });


              }, function(err){
                  console.log("User For Loop Completed");
              });
            
            Responder.success(res,'success')

              })
        .catch((err) => {
         console.log("error update user ",err);
       })
       
       })
  }



  static cronJobUG(req, res) {
   
  var renewal_date=moment(new Date()).add(30, 'days');
  renewal_date=moment(renewal_date).startOf('day').format('YYYY-MM-DDT00:00:01.000[Z]');
  console.log('cronJob------ ',moment.utc().add(5.5,'hours').toDate())
     
    
    Settings.findOne({  })
    .then((settings) =>{ 
      console.log('settings--- ',settings)
      var amount = parseFloat(settings.subscriptionFeesByAdmin + settings.gstAmount).toFixed(2);
      // var timeNow = new Date().getTime();
      // var  = new Date(timeNow - 1000  60  60 * 24).toISOString();
        User.find({                           
              renewal_date: {$lt:moment.utc().add(5.5,'hours').toDate()},
              role :"normalUser",
              // is_subscribed:true,
              active_status:true,
              wallet_balance: {$gte:amount},                                        
            }
            )
            .then((usr) => 
              {
                console.log(usr.length+"~~~~~~~~~");
                async.forEachLimit(usr, 1, function(user, userCallback){
                console.log(user);
                let tx_id = common.generateTransactionId()
                async.waterfall([
                  
                   function(callback){
                        console.log("updated 2 called");
                      //deduct Subscription Fee by server
                        var tr = {};
                        tr.amount = parseFloat(amount).toFixed(2);
                        tr.transaction_reason = "Subscription Fee";
                        tr.reason = "Subscription Fee Deducted";
                        tr.gstAmount = parseFloat(settings.gstAmount).toFixed(2);
                        tr.transaction_date = new Date();
                        tr.transaction_type = "DR";
                        tr.transaction_id = tx_id;
                        tr.user_id = user._id;
                        tr.total_amount = parseFloat(user.wallet_balance - amount).toFixed(2);
                        Transaction.create(tr)
                            .then((trnc) => {
                              console.log("updated 2");                              

                              callback(null,user,tr.total_amount);
                            }).catch((err) => {
                                console.log("cancellled");
                            });



                    },
                    function(user,wallet_balance, callback){
                      console.log('wallet_balance--- ',parseFloat(user.wallet_balance - amount).toFixed(2))
                        User.findOneAndUpdate({
                              _id: user._id
                          }, {
                              $set: {
                                  'wallet_balance': wallet_balance,
                                  'renewal_date':moment(renewal_date).toDate(),
                                  'is_subscribed': true,
                                  'lastSubscribed': moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

                              }
                          }).
                          exec((result) => {
                            console.log("updated 1");      
                            
                            UserController.sendEmailSubscriptionFeesBackground({
                              user_id: user._id,
                              date: moment(new Date()).toDate(),
                              amount: parseFloat(amount).toFixed(2),
                              transaction_id: tx_id 
                            });
                            callback(null,user);
                          });
                        
                    },


                    function(user,callback){
                      User.find({
                          referal_code: user.referred_by_code
                      }).
                      then((referred_by) => {
                        callback(null,user,referred_by);
                      });
                    },
                    function(user,referred_by,callback){
                      Transaction.count({
                          due_to_user: ObjectId(user._id),
                          transaction_reason: 'Referral Commission'
                      }).then((referred_by_count) => {
                          callback(null,user,referred_by,referred_by_count);
                      });
                    },
                    function(user,referred_by,referred_by_count,callback){
                      console.log('referred_by_count  --- ',referred_by_count); 
                      // console.log(referred_by[0].wallet_balance + "internal total Balance check");

                      if(referred_by.length!=0){
                        var tr = {};
                        if (referred_by_count == 0) {
                            var subscribeFees = (settings.subscriptionFeesByAdmin / 100) * 30;
                        } else {
                            var subscribeFees = (settings.subscriptionFeesByAdmin / 100) * 8;
                        }
                        // console.log('Referral Commission11',referred_by);
                        // console.log('Referral Commission12',wb);
                        // console.log('Referral Commission13',subscribeFees);

                        tr.amount = parseFloat(subscribeFees).toFixed(2);
                        tr.transaction_reason = "Referral Commission";
                        tr.reason = "Referral Commission for " + referred_by[0].name;
                        
                        tr.due_to_user_name = user.name;
                        tr.referred_by_name = referred_by[0].name;
                        tr.due_to_user = user._id;
                        tr.transaction_date = new Date();
                        tr.transaction_type = "CR";
                        var totalwb = parseFloat(referred_by[0].wallet_balance + subscribeFees).toFixed(2);
                        console.log('walet balance  -563-- ', totalwb);
                        tr.total_amount = parseFloat(totalwb).toFixed(2);
                        tr.transaction_id = common.generateTransactionId();
                        if (referred_by_count == 0) {
                            tr.subscribeFeePercentage = 30;
                        } else {
                            tr.subscribeFeePercentage = 8;
                        }
                        tr.user_id = referred_by[0]._id;
                        tr.referred_by_code = referred_by[0].referal_code;
                        // console.log('Referral Commission1---------2',wb);




                        Transaction.create(tr)
                            .then((trnc) => {
                                // console.log("success",trnc)

                                callback(null,referred_by,parseFloat(subscribeFees).toFixed(2),totalwb)
                            }).catch((err) => {
                                console.log("err_referral_commission  ", err);
                            })
                      }else{
                        callback(null,referred_by,null,null);
                      }

                    },
                    function(referred_by,subscribeFees,totalwb,callback){
                      console.log("846------   "+totalwb);
                      if(referred_by.length!=0){
                        User.update({
                            _id: referred_by[0]._id
                        }, {
                            $set:{
                              wallet_balance: totalwb
                            }
                            // $inc: {
                            //     wallet_balance: subscribeFees
                            // }
                        }).
                        then((trnc) => {
                            callback(null);
                            console.log("success852",trnc)
                        }).catch((err) => {
                            console.log("cancellled");
                        })
                      }else{
                        callback(null);
                      }
                    }

                    
                    
                ], function (err, result) {
                    // result now equals 'done'
                    console.log('done')
                    userCallback();
                });


            }, function(err){
                console.log("User For Loop Completed");
            });

               
            }); //aggregate then
        Responder.success(res,'success')
      });
  }
  

//   static cronJob(req, res) {
    
//     User.findOne({ role: "admin" })
//     .then((admin) =>{ 
//       // var timeNow = new Date().getTime();
//       // var  = new Date(timeNow - 1000 * 60 * 60 * 24).toISOString();
//            User.aggregate([ 
//               {
//                 $match: {
//                           renewal_date: {$lt:new Date(Date.now())},
//                           // is_subscribed:true,
//                           wallet_balance: {$gte:(admin.subscriptionFeesByAdmin+admin.gstAmount)},
//                         }
                    
//               }]
//               )
//               .then((usr) => 
//                 {
//                 usr.forEach(function (user, k) {                
//                 // if (!user.is_subscribed) {  //find users are not subscribed
//                   // console.log('user'+user)



//            var amount= (admin.subscriptionFeesByAdmin+admin.gstAmount);
//               if(user.wallet_balance >= amount){
                 
//                  User.findOneAndUpdate({ _id:user._id },{$set:{'wallet_balance':user.wallet_balance - amount,'renewal_date':new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),'is_subscribed':true}}).
//                  then((result) =>{
//                     // User.findOneAndUpdate({ _id:user._id },{$set:{'is_subscribed':true}}).
//                     // then((result1) =>{
                          
//                           //deduct Subscription Fee by server
//                           var tr = {};
                      
//                           tr.amount= amount;
//                           tr.transaction_reason= "Subscription Fee by server Deducted";
//                           tr.transaction_date = new Date();
//                           tr.transaction_type= "DR";

//                           // tr.total_amount= req.body.wallet_balance + req.body.wallet_balance;
//                           tr.transaction_id = common.generateTransactionId();
//                           tr.user_id= user._id;
//                           tr.total_amount= user.wallet_balance - amount;
//                           // tr.referred_by_code= req.body.referred_by_code;
//                           // console.log(tr);
//                           Transaction.create(tr)
//                            .then((trnc)=>{
                            
//                            }).catch((err) => {
//                                console.log("cancellled");
//                              })


//                            User.update({_id:user._id},{$set:{wallet_balance:(user.wallet_balance - amount),lastSubscribed:new Date()}}).
//                            then((trnc)=>{
                            
//                            }).catch((err) => {
//                                console.log("cancellled");
//                              })

//                               setTimeout(function() {

//                           //deduct referral commission by server
//                             // console.log('referred_by.length  --- ',referred_by.length);
//                             // console.log('user.referred_by_code',user.referred_by_code);
//                             User.find({ referal_code: user.referred_by_code}).
//                             then((referred_by) =>{


                              

//                               // console.log('referred_by.length  --- ',referred_by[0]._id);
//                               console.log('referred_by  --- ',referred_by);
//                               var wb= referred_by[0].wallet_balance;
//                               console.log('walet total balance  -1111111-- ',wb);
//                               // due_to_user:ObjectId(req.params.user_id),transaction_reason:'Referral Commission'
//                               var a = 0;
//                                 Transaction.count({ due_to_user:ObjectId(user._id),transaction_reason:'Referral Commission' }).
//                                 then((referred_by_count) =>{
//                                   // console.log('referred_by_count  --- ',referred_by_count);
//                                   console.log(a+"AAA");
//                                   a++;
//                                   console.log(a+"AAA1");
                                
//                                  console.log(referred_by[0].wallet_balance+"internal total Balance check");
                                      
//                                           var tr = {};
//                                           if(referred_by_count == 0){ 
//                                             var subscribeFees = (admin.subscriptionFeesByAdmin / 100)*20;
//                                           }else{
//                                             var subscribeFees = (admin.subscriptionFeesByAdmin / 100)*5;
//                                           }
//                                           // console.log('Referral Commission11',referred_by);
//                                           // console.log('Referral Commission12',wb);
//                                           // console.log('Referral Commission13',subscribeFees);
                                    
//                                           tr.amount= subscribeFees;
//                                           tr.transaction_reason= "Referral Commission";
//                                           tr.due_to_user_name= user.name;
//                                           tr.referred_by_name= referred_by[0].name;
//                                           tr.due_to_user= user._id;
//                                           tr.transaction_date = new Date();
//                                           tr.transaction_type= "CR";
//                                           var totalwb = referred_by[0].wallet_balance + subscribeFees;
//                                           console.log('walet balance  -563-- ',totalwb);
//                                           tr.total_amount= totalwb;
//                                           tr.transaction_id = common.generateTransactionId();
//                                           if(referred_by_count == 0){ 
//                                             tr.subscribeFeePercentage = 20;
//                                           }else{
//                                             tr.subscribeFeePercentage = 5;
//                                           }
//                                           tr.user_id=  referred_by[0]._id;
//                                           tr.referred_by_code=  referred_by[0].referal_code;
//                                           // console.log('Referral Commission1---------2',wb);



                                      
//                                         Transaction.create(tr)
//                                          .then((trnc)=>{
//                                           // console.log("success",trnc)
//                                          }).catch((err) => {
//                                              console.log("err_referral_commission  ",err);
//                                            })



//                                          User.update({_id:referred_by[0]._id},{$inc:{wallet_balance:subscribeFees}}).
//                                          then((trnc)=>{
//                                           // console.log("success",trnc)
//                                          }).catch((err) => {
//                                              console.log("cancellled");
//                                            })

//                             });
//                                 wb=0;
//                        });
//                     // });
//                     }, 2000);

 
//                  });
                 
//                  // var wb;
//                  //  User.findOne({_id:req.params.id })
//                  //  .then((usr) =>{ wb = usr.wallet_balance - req.body.subscribedFees; console.log(wb); return  User.findOneAndUpdate({ _id:usr._id },{$set:{'wallet_balance':wb,'renewal_date':req.body.renewal_date}})})
//                  //  .then((data) =>{ return User.findOne({_id:data._id })})
//                  //  .then((usrs) =>  Responder.success(res,usrs))
//               }else{
//                  User.findOneAndUpdate({ _id:user._id },{$set:{'is_subscribed':false}}).
//                  then((result) =>{
//                  });
//               }



//       //   var oneDay = 24*60*60*1000; // hours*minutes*seconds*milliseconds
//       //   var firstDate = new Date(user.renewal_date);
//       //   var secondDate = new Date();

//       //   var diffDays = Math.round((firstDate.getTime() - secondDate.getTime())/(oneDay));

//       //    var dateShow = diffDays;
//       //   console.log(dateShow+"@@ssssssssss@");
//       //   if(isNaN(dateShow)){
//       //     dateShow = 0;
//       //   }
//       //   console.log(dateShow+"@@@0");
//       //   // console.log("asdakd");
//       //   if(diffDays<0){
//       //     //red      
//       //     // this.disabledAccout();
//       //     //unsubscribe account
//       //      User.findOneAndUpdate({ _id:user._id },{$set:{'is_subscribed':false}}).

//       //      then((result) =>{
//       //      });

//       //   }else if (!user.is_subscribed){
//       //                 // console.log('bbbbbbbbbb',user)

//       // }
        

//   // }
   
// });
// }
// )
//       Responder.success(res,'success')

//       });

    
//   }


  static generateQRCodeOld(req, res) {
 

      async.waterfall([
        function(callback){
          console.log('done ---------111')

              const fs = require('fs');
              const qrcode = require('qrcode');

              run().catch(error => console.error(error.stack));

              async function run() {
                  const qrCodeShare = await qrcode.toDataURL('https://app.swari.in/register?refferal_code='+req.body.referal_code);
                  var base64Data = qrCodeShare.replace(/^data:image\/png;base64,/, "");
                  callback(null,base64Data,qrCodeShare);
                }
        },
        function(base64Data,qrCodeShare,callback) {
          console.log('done ---------22')

          require("fs").writeFileSync('referImage/'+req.body.phone_number+'qrCode.png', base64Data, 'base64');
          // , function(err) {
          //   console.log('err 11 refer_image',err);
          // });

            console.log(" qr created ");
            callback(null,true,qrCodeShare);      
        },
        function(success,qrCodeShare,callback){
          console.log('done ---------333')

               /* AWS.config.update({
                    accessKeyId: "********************",
                  secretAccessKey: "4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t",
                  });

                var s3 = new AWS.S3();

                console.log('s3  -- ',s3)
                // console.log('refer_image-- 1222',refer_image);

                */

                //configuring parameters
                var uploadParams = {
                  Bucket: 'swaritest',
                  ACL: "public-read",
                  Body : fs.createReadStream('referImage/'+req.body.phone_number+'qrCode.png'),
                  Key : "qrs/"+req.body.phone_number+'qrCode.png'
                };

                var params = {
                  Bucket: 'swaritest',
                  // Body : fs.createReadStream(file.path),
                  Key : "qrs/"+req.body.phone_number+'qrCode.png'
                };
               

                s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {				                 
                // console.log('Bucket locaction -- ',s3.getPublicUrl('triva-in', "folder/file-1574407048917.jpg", ["https://triva-in.s3.amazonaws.com/"]));
                // s3.upload(uploadParams, function (err, data) {
                  //handle error
                  // if (err) {
                  //   console.log("Error", err);
                  // }

                  //success
                  if (data) {
                    console.log("Uploaded in:", data);
                    // require("fs").unlinkSync('referImage/'+req.body.phone_number+'qrCode.png');                    
                    User.update({phone_number:req.body.phone_number},{$set:{qrCode: baseSpaces+"/"+params.Key}})
                           .then((val)=>{
                            console.log('valueeeeeee ',val)
                            callback(null,true,qrCodeShare);

                          });
                    }
                  }).catch((err)=>{
                    console.log(" err ", err);
                    callback(null,true,qrCodeShare);
                  });
        },
        function(success,qrCodeShare,callback){
          console.log('doneg ---------444',qrCodeShare)

              const nodeHtmlToImage = require('node-html-to-image')
              nodeHtmlToImage({
                output: 'referImage/'+req.body.phone_number+'shareImage.png',
                html : `<!DOCTYPE html>
                        <html>
                          <head>
                            <title>SWARI</title>
                            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        </head>
                        <style type='text/css'>
                        body{
                          width:55%;
                          border:1px solid #16BBBB;
                          padding: 10px;
                          margin: 5% 10%;
                          text-align:center;
                        }
                        @media only screen and (min-width: 320px) {
                          body {
                          width:75%;
                          border:1px solid #16BBBB;
                          padding: 10px;
                          margin: 5% 11%;
                          text-align:center;
                          }
                        }
                          .text_left{
                            text-align: left !important;
                          }
                         .text_center{
                            text-align: center !important;
                            padding:10px 20px;
                           
                          }
                        .pic{
                              border-radius: 50% !important;
                            padding: 3px;
                            background: white;
                        }

                          .gray_color{
                            color: #403e3e;
                            font-size: 16px;
                            border:1px solid #16BBBB;
                            padding: 10px 8px;
                            font-family:  sans-serif;

                            font-weight:bold;
                          }
                          .headers{
                            background:linear-gradient(to bottom, rgba(12, 251, 251, 0.62) 0%, #1ad4d4 33%, #108a8a 100%);
                            color:white;
                            padding: 5px;
                            position: relative;
                            display: block;
                            border: 1px solid #e5e5e5;

                          }
                          .f1{
                            font-size: 1.5em;
                            font-family: sans-serif;
                            font-weight: bold;

                        }
                        .buttons{
                          border-radius: 15px;
                          border: 3px solid #129F9F;
                          padding: 10px;
                          color:#129F9F;
                          font-size: 1.2em;
                          font-weight: bold;
                          background-color: white;
                          text-align:center;
                          font-family:  sans-serif;
                          letter-spacing: 2px;
                        }
                        .p{
                          font-family:  sans-serif;
                          
                        }
                        </style>
                        <center>
                        <div class='animate '>
                            <div class='row'>
                                <div class='col-sm-12'>
                                   <!--  <div class='card'>
                                         -->
                                        <div class='headers card-header card-info card-inverse' >
                                          
                                          <div class=' col-sm-6 '><img height='60' width='60' src='http://panel.swari.in/img/icon.png' class='pic user-avatar' </div>
                                           <div class=' col-sm-6 f1'>SWARI</div>
                                          <!--   <button class='btn btn-danger pull-left' ng-click='back()'>
                                            BACK
                                            <button> -->
                                     <!--       
                                        </div> -->
                                    </div>
                                </div>

                        <div class='row'>
                          <p style='font-weight: bold;font-size:21px;font-family:  sans-serif;text-transform: uppercase;'>"{{name}}", </p>
                          <p style='font-size: 1em !important;font-family:  sans-serif;'> is requesting you to install India's first Taxi & Travel Agency connecting APP by scanning the below QR code or using the Referral code during registration</p>
                                <div class='text_center col-sm-12'>
                                    <img height='150' width='150' src='{{qr_code}}' class='user-avatar'  style='border:1px solid grey;'>
                                  </div>
                                   <div class=' text_center col-sm-12'>
                                            <button class='buttons'>{{referalCode}}
                                            </button>

                                           </div>
                                     
                                    </div>
                                </div>

                        <div class='row'>
                                <div class=' col-sm-12'>
                                  <p class=gray_color>
                                  Please download SWARI Application by using the link  <a href='#'>https://app.swari.in/register?referral_code={{referalCode}}</a>    or use Referral code <a style='font-weight:bold;'>{{referalCode}}</a> during registration.
                                  </p>
                                </div>
                              </div>

                        <div class='row'>
                                <div class=' col-sm-12'>
                                  <div class='text_center col-sm-12'>
                                    <img width='180' src='http://panel.swari.in/img/android.png' class='user-avatar' / > <img src='http://panel.swari.in/img/ios.png' width='180' class='user-avatar' / >
                                  </div>
                            </div>
                        </center>
                        `,
                        content: { name: req.body.name,referalCode: req.body.referal_code, qr_code: qrCodeShare },
                        puppeteerArgs:{args: ['--no-sandbox']}

              })
                .then((success) =>{
                  callback(null,true);
                })
              // Responder.success(res, user)


        },
        function(success,callback){
          console.log('done ---------5555')
              //configuring the AWS environment
              //TBD - remove the deperacted
            // AWS.config.update({
            //     accessKeyId: "********************",
            //   secretAccessKey: "4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t",
            //   });

            // var s3 = new AWS.S3();

            // console.log('s3  -- ',s3)

            //configuring parameters
            var uploadParams = {
              Bucket: 'swaritest',
              Body : fs.createReadStream('referImage/'+req.body.phone_number+'shareImage.png'),
              ACL: 'public-read',
              Key : "qrs/"+req.body.phone_number+'shareImage.png'
            };

            var params = {
              Bucket: 'swaritest',
              // Body : fs.createReadStream(file.path),
              Key : "qrs/"+req.body.phone_number+'shareImage.png'
            };

            // var uploadParams = {
            //   Bucket: 'swaritest',
            //   ACL: "public-read",
            //   Body : fs.createReadStream('referImage/'+req.body.phone_number+'qrCode.png'),
            //   Key : "qrs/"+req.body.phone_number+'qrCode.png'
            // };

            // var params = {
            //   Bucket: 'swaritest',
            //   // Body : fs.createReadStream(file.path),
            //   Key : "qrs/"+req.body.phone_number+'qrCode.png'
            // };
           

            // s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		

            
            // console.log('Bucket locaction -- ',s3.getPublicUrl('triva-in', "folder/file-1574407048917.jpg", ["https://triva-in.s3.amazonaws.com/"]));
            // s3.upload(uploadParams, function (err, data) {
              s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		
              //handle error
              // if (err) {
              //   console.log("Error", err);
              // }

              //success
              if (data) {
                console.log("Uploaded in:", data);
                // console.log("Uploaded in location:", data.Location);
                        User.update({phone_number:req.body.phone_number},{$set:{shareImage: baseSpaces+"/"+params.Key}})
                       .then((val)=>{
                        console.log('valueeeeeee ',val)
                        callback(null);
                        
                       })
                       .catch((err)=>{
                        console.log('errrrrrrrrrr ',err)

                       })

              }
            });


        },
        
        
    ], function (err, result) {
        // result now equals 'done'
        console.log('done')
        Responder.success(res, 'success')

        // userCallback();
    });
  }
  
  static generateQRCode(req, res) {
 

    async.waterfall([
      function(callback){
        console.log('done ---------111')

            const fs = require('fs');
            const qrcode = require('qrcode');

            run().catch(error => console.error(error.stack));

            async function run() {
                const qrCodeShare = await qrcode.toDataURL('https://app.swari.in/register?refferal_code='+req.body.referal_code);
                var base64Data = qrCodeShare.replace(/^data:image\/png;base64,/, "");
                callback(null,base64Data,qrCodeShare);
              }
      },
      function(base64Data,qrCodeShare,callback) {
        console.log('done ---------22')

        require("fs").writeFileSync('referImage/'+req.body.phone_number+'qrCode.png', base64Data, 'base64');
        // , function(err) {
        //   console.log('err 11 refer_image',err);
        // });

          console.log(" qr created ");
          callback(null,true,qrCodeShare);      
      },
      function(success,qrCodeShare,callback){
        console.log('done ---------333')

             /* AWS.config.update({
                  accessKeyId: "********************",
                secretAccessKey: "4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t",
                });

              var s3 = new AWS.S3();

              console.log('s3  -- ',s3)
              // console.log('refer_image-- 1222',refer_image);

              */

              //configuring parameters
              var uploadParams = {
                Bucket: 'swaritest',
                ACL: "public-read",
                Body : fs.createReadStream('referImage/'+req.body.phone_number+'qrCode.png'),
                Key : "qrs/"+req.body.phone_number+'qrCode.png'
              };

              var params = {
                Bucket: 'swaritest',
                // Body : fs.createReadStream(file.path),
                Key : "qrs/"+req.body.phone_number+'qrCode.png'
              };
             

              s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {				                 
              // console.log('Bucket locaction -- ',s3.getPublicUrl('triva-in', "folder/file-1574407048917.jpg", ["https://triva-in.s3.amazonaws.com/"]));
              // s3.upload(uploadParams, function (err, data) {
                //handle error
                // if (err) {
                //   console.log("Error", err);
                // }

                //success
                if (data) {
                  console.log("Uploaded in:", data);
                  // require("fs").unlinkSync('referImage/'+req.body.phone_number+'qrCode.png');                    
                  User.update({phone_number:req.body.phone_number},{$set:{qrCode: baseSpaces+"/"+params.Key}})
                         .then((val)=>{
                          console.log('valueeeeeee ',val)
                          callback(null,true,qrCodeShare);

                        });
                  }
                }).catch((err)=>{
                  console.log(" err ", err);
                  callback(null,true,qrCodeShare);
                });
      },
      function(success,qrCodeShare,callback){
        console.log('done ---------444',qrCodeShare)

            const nodeHtmlToImage = require('node-html-to-image')
            nodeHtmlToImage({
              output: 'referImage/'+req.body.phone_number+'shareImageHindi.png',
              html : `<!DOCTYPE html>
<html lang="hi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>स्वारी ऐप निमंत्रण</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&display=swap" rel="stylesheet">
</head>
<body style="font-family: 'Noto Sans Devanagari', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f9f9f9; color: #333;">
  <table border="5" cellpadding="2" cellspacing="2" align="center" style="border-color:#1c9aab; border-collapse: collapse;">
	<tr>
  	<td>
 	<div style="max-width: 1080px; margin: 0 auto; background-color: #ffffff; border: 1px solid #0073aa; padding: 20px; border-radius: 5px; box-shadow: 0;">
 	<div style="text-align: center;">
   	<img src="https://panel.swari.in/img/icon.png" alt="स्वारी लोगो" style="max-width: 400px;">
 	</div>
 	<div style="text-align: center; margin-top: 20px;">
   	<h2 style="color: #0073aa;">{{name}} आपसे भारत का पहला टैक्सी और ट्रैवल एजेंसी कनेक्टिंग ऐप इंस्टॉल करने का अनुरोध कर रहा है</h2>
   	<p>कृपया नीचे दिए गए <strong>QR कोड</strong> को स्कैन करके SWARI एप्लिकेशन डाउनलोड करें या पंजीकरण के दौरान रेफरल कोड <strong>{{referalCode}}</strong> का उपयोग करें।</p>
 	</div>
 	<div style="text-align: center; margin: 20px 0;">
   	<img src="{{qr_code}}" alt="क्यूआर कोड" style="width: 150px; height: 150px;">
 	</div>
 	<div style="margin-top: 20px;">
   	<h2 style="color: #0073aa; border-bottom: 2px solid #0073aa; padding-bottom: 5px; margin-bottom: 10px;">स्वारी ऐप की विशेषताएँ</h2>
   	<ul style="list-style-type: none; padding-left: 0;">
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">कई टैक्सियों को जोड़ें:</span>
       	</strong> एक ही अकाउंट से आसानी से कई टैक्सियाँ मैनेज करें।
     	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">यात्राओं पर कोई कमीशन नहीं:</span>
       	</strong> टैक्सी बिज़नेस किराए पर कोई कमीशन स्वारी ऐप को नहीं देते हैं।
     	</li>
      	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">लोकेशन सेटिंग:</span>
       	</strong> टैक्सी की उपलब्धता के हिसाब से लोकेशन अपडेट करें।
     	</li>
      	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;"> नज़दीकी उपलब्धता:</span>
       	</strong> 25-30 किलोमीटर के अंदर पैसेंजर और टैक्सी आसानी से दिखें।
     	</li>
      	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">वॉइस कमांड्स:</span>
       	</strong> वॉइस कमांड्स से ट्रिप और पैसेंजर को जोड़ें या खोेजें।
        	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">रूट चयन:</span>
       	</strong> BLABLA CAR जैसे रूट चुनें और रास्ते में यात्रियों को लेने के लिए कई स्टॉप जोड़ें।
     	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">प्रत्यक्ष व्यापार विनिमय:</span>
       	</strong> अन्य टैक्सी ऑपरेटरों के साथ आसानी से बिज़नेस का आदान-प्रदान करें।
     	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">व्हाट्सएप ग्रुप की आवश्यकता नहीं:</span>
       	</strong> बिज़नेस के सुचारू आदान-प्रदान के लिए कई व्हाट्सएप ग्रुपों की परेशानी से बचें।
     	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">अपनी टैक्सी पहले से बुक करें:</span>
       	</strong> बाहर जाने वाले शहरों के लिए अपनी यात्राओं की पहले से योजना बनाएं और उस शहर में अपनी टैक्सी को बुक करने के लिए उपलब्ध कराएं।
     	</li>
     	<li>
       	<strong>
         	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg></span>
         	<span style="color: #0073aa;">भारत भर में उपलब्धता:</span>
       	</strong> आप भारत में कहीं भी सेवा प्रदान कर सकते हैं, जिससे आप सभी ग्राहकों की सेवा कर सकते हैं और अधिक पैसे कमा सकते हैं।
     	</li>
   	</ul>
 	</div>
 	<div style="margin-top: 30px; text-align: center;">
   	<h4 style="color: #E70021; border-bottom: 2px solid #E70021; padding-bottom: 5px; margin-bottom: 10px; font-size: 24px;">
     	<strong>टैक्सी बिज़नेस को रेफर करके पैसे कमाएं!</strong>
   	</h4>
   	<p style="text-align: center;">पहले महीने के सब्सक्रिप्शन शुल्क का <strong>30%</strong> और बाद के महीनों के सब्सक्रिप्शन शुल्क का <strong>8%</strong> अर्जित करें।</p>
   	<p style="text-align: center;">
     	<strong>अधिक जानकारी के लिए, हमसे <span style="color: #0073aa;">+91 97803 87103</span> पर संपर्क करें।</strong>
   	</p>
   	<p style="color: #0073aa; text-align: center;">
     	<strong>https://app.swari.in</strong>
   	</p>
 	</div>
 	</div>
  	</td>
	</tr>
  </table>
</body>
</html>

`,
              content: { name: req.body.name,referalCode: req.body.referal_code, qr_code: qrCodeShare },
              puppeteerArgs:{args: ['--no-sandbox']}

            }).then((success) =>{
                callback(null,true,qrCodeShare);
            })         
      },
       function(success,qrCodeShare,callback){
        console.log('done ---------444',qrCodeShare)

        const nodeHtmlToImage = require('node-html-to-image')
        nodeHtmlToImage({
          output: 'referImage/'+req.body.phone_number+'shareImage.png',
          html : `<!DOCTYPE html>
<html lang="en">
  <head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Swari App Invitation
  </title>
  </head>
  <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f9f9f9; color: #333;">
  <table border="5" cellpadding="2" cellspacing="2" align="center" style="border-color:#1c9aab; border-collapse: collapse;">
	<tr>
  	<td>
    	<div style="max-width: 1080px; margin: 0 auto; background-color: #ffffff; border: 3px solid #0073aa; padding: 20px; border-radius: 5px;">
      	<div style="text-align: center;">
        	<img src="https://panel.swari.in/img/icon.png" alt="Swari Logo" style="max-width: 400px;">
      	</div>
      	<div style="text-align: center; margin-top: 20px;">
        	<h2 style="color: #0073aa;">{{name}} is requesting you to install India’s first Taxi & Travel Agency connecting APP
        	</h2>
        	<p>Please download the SWARI Application by scanning the QR code below or by using the Referral code
          	<strong>{{referalCode}}
          	</strong> during registration.
        	</p>
      	</div>
      	<div style="text-align: center; margin: 20px 0;">
        	<img src="{{qr_code}}" alt="QR Code" style="width: 150px; height: 150px;">
      	</div>
      	<div style="margin-top: 20px;">
        	<h2 style="color: #0073aa; border-bottom: 2px solid #0073aa; padding-bottom: 5px; margin-bottom: 10px;">App Features
        	</h2>
        	<ul style="list-style-type: none; padding-left: 0;">
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Add Multiple Taxis:
              	</span>
            	</strong> Manage multiple taxis under one account seamlessly.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">No Commission on Trips:
              	</span>
            	</strong> Taxi businesses don’t pay any commission on fares.
          	</li>
          	<li>
              	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Location Setting:
              	</span>
            	</strong> Update your location based on taxi availability.
          	</li>
          	<li>
              	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Nearby Availability:
              	</span>
            	</strong> See passengers and taxis within 30 km for easier connections.
          	</li>
          	<li>
              	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Voice Commands:
              	</span>
            	</strong>Use voice commands to add or search Trips and Passengers hands-free.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Route Selection:
              	</span>
            	</strong> Choose routes like BLABLA CAR and add multiple stops to pick up passengers along the way.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Direct Business Exchange:
              	</span>
            	</strong> Easily exchange business with other taxi operators.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">No WhatsApp Groups Required:
              	</span>
            	</strong> Avoid the hassle of multiple WhatsApp groups for smoother business exchange.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Pre-Book Your Taxi:
              	</span>
            	</strong> Plan your trips in advance for outstation bookings and make your taxi available in that city.
          	</li>
          	<li>
            	<strong>
              	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
    	<path d="M0 0h24v24H0V0z" fill="none"/>
    	<path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
  	</svg>
              	</span>
              	<span style="color: #0073aa;">Pan India Availability:
              	</span>
            	</strong> Provide service anywhere in India, serving all customers and earning more money.
          	</li>
        	</ul>
      	</div>
      	<div style="margin-top: 30px; text-align: center;">
        	<h4 style="color: #E70021; border-bottom: 2px solid #E70021; padding-bottom: 5px; margin-bottom: 10px; font-size: 24px;">
          	<strong>Refer a Taxi Business & Earn:
          	</strong>
        	</h4>
        	<p style="text-align: center;">Earn
          	<strong>30%
          	</strong> of the first month's subscription fee and
          	<strong>8%
          	</strong> of the subscription fee for subsequent months.
        	</p>
        	<p style="text-align: center;">
          	<strong>For more information, contact us at
            	<span style="color: #0073aa;">+91 98703 87103
            	</span>
          	</strong>
        	</p>
        	<p style="text-align: center;color: #0073aa">
          	<strong>https://app.swari.in
          	</strong>
        	</p>
      	</div>
    	</div>
  	</td>
	</tr>
  </table>
  </body>
</html>
`,
          content: { name: req.body.name,referalCode: req.body.referal_code, qr_code: qrCodeShare },
          puppeteerArgs:{args: ['--no-sandbox']}

        }).then((success) =>{
            callback(null,true);
          })           
      },
      function(success,callback){                        
          //configuring parameters
          var uploadParams = {
            Bucket: 'swaritest',
            Body : fs.createReadStream('referImage/'+req.body.phone_number+'shareImageHindi.png'),
            ACL: 'public-read',
            Key : "qrs/"+req.body.phone_number+'shareImageHindi.png'
          };

          var hindiKey ="qrs/"+req.body.phone_number+'shareImageHindi.png';
         
          s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		              
            //success
            if (data) {
              console.log("Uploaded in:", data);                
              User.update({phone_number:req.body.phone_number},{$set:{ shareImageHindi: baseSpaces+"/"+hindiKey}})
              .then((val)=>{
              console.log('valueeeeeee ',val)
                // callback(null);              
              })
              .catch((err)=>{
                console.log('errrrrrrrrrr ',err)
              })

            }
          });


          //configuring parameters
          var uploadParams = {
            Bucket: 'swaritest',
            Body : fs.createReadStream('referImage/'+req.body.phone_number+'shareImage.png'),
            ACL: 'public-read',
            Key : "qrs/"+req.body.phone_number+'shareImage.png'
          };

          var shareImage = "qrs/"+req.body.phone_number+'shareImage.png';
         
          s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		              
            //success
            if (data) {
              console.log("Uploaded in:", data);                
              User.update({ phone_number:req.body.phone_number },{ $set:{ shareImage: baseSpaces+"/"+shareImage }})
              .then((val)=>{
              console.log('valueeeeeee ',val)
              callback(null);
              
              })
              .catch((err)=>{
              console.log('errrrrrrrrrr ',err)

              })

            }
          });
      },                  
  ], function (err, result) {
      // result now equals 'done'
      console.log('done')
      if(req) {
        Responder.success(res, 'success')
      } else {
        return true;
      }
    });
  }

  static generateQRCodeAuto() {

    console.log("generateQRCodeAuto")
    User.findOne({
      $or: [
        { shareImageHindi: { $exists: false } },
        { shareImageHindi: { $eq: "" } }
    ]      
    }).then((usr) => 
      {

        if(!user) {
          return
        }

        console.log({
          phone_number: usr.phone_number,
          referal_code: usr.referal_code,
          name: usr.name,
        })
        this.generateQRCode({
          body: {
            phone_number: usr.phone_number,
            referal_code: usr.referal_code,
            name: usr.name,
          }
        },null)

      });

  }


  static show(req, res) {

    // console.log(" req user ===============  ",req.user);

    // let cacheProfile = myCache.get(req.params.id+"-profile");
    // if(cacheProfile) {
    //   return Responder.success(res,cacheProfile)
    // }


    User.aggregate([{
          $match: {
            _id: ObjectId(req.params.id)
          }                    
        },
          { $lookup:
            {
              from: 'vehicles',
              localField: '_id',
              foreignField: 'user_id',
              as: 'vehicleList'
            }
          },
          { 
          "$sort": { 
              "created_at": -1,
          } 
      },
    ])
    .then((trc)=>{
      // myCache.set(req.params.id+"-profile", trc,60*60*12);

      if(trc.length) {

        let docs = [];
        if(trc[0].documents && trc[0].documents.length) {                 
            getFileSignedUrl(trc[0].documents).then((docs)=>{
              console.log("  signed URL ")
              let usObj = trc[0];
              usObj.document_public = docs;
              trc = [usObj];
              console.log("  =====  usObj ",trc)
              return    Responder.success(res,trc)
            }).catch( (err)=>{ console.log(err)})        
        } else {
          return    Responder.success(res,trc)
        }
       
      }
      // return    Responder.success(res,trc)
  })
    .catch((err)=>Responder.operationFailed(res,err))


    // User.findOne({ _id: req.params.id })
    //   .then((user) => Responder.success(res, user))
    //   .catch((err) => Responder.operationFailed(res, err))

  }


  static getUserForPhoneNumber(req, res) {

    User.find({"phone_number":req.params.phone_number})
      .then((user) => {
          Responder.success(res, user)
       
      })
      .catch((err) => Responder.operationFailed(res, err))

  }
  


  static showAllUsers(req, res) {

    User.find({})
      .then((usr) => Responder.success(res, usr))
      .catch((err) => Responder.operationFailed(res, err))

  }

static showAllVehicleByUsersPagination(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)
    var users = [];

    User.aggregate([ 
              {
                $match: {
                    // role:"admin",
                    role:"normalUser",
                    active_status:true
                  }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicles',
                   localField: '_id',
                   foreignField: 'user_id',
                   as: 'vehicleList'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 

                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  static showAllVehicleByReferralUsersPagination(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)
    var users = [];

    User.aggregate([ 
              {
                $match: {
                          role:"referalUser",
                          'active_status':true
                          
                        }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },{ 
                "$sort": { 
                    "created_at": -1,
                } 
            },

                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }

  


  static showAllSubAdminUsersPagination(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)
    var users = [];

    User.aggregate([ 
              {
                $match: {
                          role:"subAdmin"

                        }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },{ 
                "$sort": { 
                    "created_at": -1,
                } 
            },

                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  static showAllVehicleByUsers(req, res) {
    var users = [];
    User.find({})
      .then((usr) => { users = usr; return Vehicle.find({}) })
      .then((veh) => {
        // console.log("VEH====", veh);
         var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed:us.referal_code,
            is_subscribed:us.is_subscribed,
            referred_by_code:us.referred_by_code,
            fcm_registration_token:us.fcm_registration_token,
            suspend:us.suspend,
            gst:us.gst,
          };
          trp.vehicleDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.vehicleDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))

  }

  static showAllReportedUsers(req, res) {
    var users = [];
    User.find({})
      .then((usr) => { users = usr; console.log("usr====", usr); return User.find({_id:{$in:_.map(usr.report,'reportedBy')}})})
      .then((veh) => {
        console.log("VEH====", veh); var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at
          };
          trp.reporteByUsers = [];
          _.each(veh, vehi => {
            if (report.reportedBy == vehi.user_id) {
              trp.reporteByUsers.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))


  }

  static userLogin(req, res) {

    console.log(req.body)


    if (req.body.role == "user") {
      User.findOne({ phone_number: req.body.phone_number, password: req.body.password, role: req.body.role })
        .then((user) => {
          if (user) {
            Responder.success(res, user)
          } else {
            return Responder.success(res, { message: "Please enter valid Email and Passowrd." })
          }
        })
        .catch((err) => Responder.operationFailed(res, err))

    }

    if (req.body.role == "admin") {

      User.findOne({ phone_number: req.body.phone_number, password: req.body.password, role: req.body.role })
        .then((user) => {
          if (user) {
            Responder.success(res, user)
          } else {
            return Responder.success(res, { message: "Please enter valid Email and Passowrd." })
          }
        })
        .catch((err) => Responder.operationFailed(res, err))

    }
  }

  static resetPassword(req, res) {
    console.log('rrrrrrrrrrrrrrr');
    console.log(req.body)
    if (!req.body.userDetails._id)
      return Responder.operationFailed(res, { message: 'User Id Is Required.' })
      User.findOne({ phone_number: req.body.phone_number })
        .then((user) => {
          // if (user) {
          //   return Responder.operationFailed(res, { message: "Mobile number is already exists. " })
          // } else {
            // User.create(req.body)
            //   .then((user) => {
            //     Responder.success(res, user);
            //   }).catch((err) => Responder.operationFailed(res, err))
            //   // }
            //   }).catch((err) => Responder.operationFailed(res, err))
              
          // user.bupdate(req.body.password, (err, password) => {
          //   console.log('papppp'+password);
          //   console.log(password);
          // });
    var bcrypt = require('bcrypt');
    bcrypt.hash(req.body.password, 10, function(err, hash){
      console.log('ppppppppppppppppppppppp---'+hash);
      console.log(hash);

// bcrypt.genSalt(10, function(err, salt) {
//          if (err) return next(err);
 
//          bcrypt.hash(user.password, salt, function(err, hash) {
//              if (err) return next(err);
 
//              user.password = hash;
//              next();
//          });
//      });
  User.findOneAndUpdate({ _id: req.body.userDetails._id }, { $set: { password:hash} })
      .then((data) =>{ console.log("succcess1100"); return User.findOne({_id:data._id })})  
      .then((val) => { console.log("succcess110a");  Responder.success(res, val)} )
      .catch((err) => { console.log("succcess110f"); Responder.operationFailed(res, err)})
      // User.findOneAndUpdate({ _id: req.body.userDetails._id }, { $set: { password:req.body.password} })
      // .then((data) =>{ console.log("succcess1100"); console.log(data); return User.findOne({_id:data._id })})  
      // .then((val) => { console.log("succcess110a"); console.log(val); Responder.success(res, val)} )
      // .catch((err) => { console.log("succcess110f"); Responder.operationFailed(res, err)})
    });
  
  })
  }


  static forgotPassword(req, res) {

    console.log(req.body);
    console.log(req.body+"~~~~~~");

      const token = otplib.authenticator.generate(secret);
      console.log(token);

      User.find({'phone_number':req.body.phone_number})
      .then((userAlreadyCreated) => {
         User.findOneAndUpdate({ phone_number: req.body.phone_number }, { $set: { otp:token} })
          .then((data) =>{
            
          }) 
      });





    User.findOne({ phone_number: req.body.phone_number })
      .then((user) => {
        if (user) {
          // send otp here
          /*console.log("here~~~~~");
           var extServerOptionsPost = {
            hostname: "***************",
            path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
            method: 'POST',
            port: '80',
            headers: {
              'Content-Type': 'application/json'
            }
          }

        

          var reqPost = http.request(extServerOptionsPost, function (response) {

            response.on('data', function (data) {
              console.log("line no: 87 ", data);
              return Responder.success(res, { success: true, message: token })
            });
          });
          */
          var body =
            {
              "smsContent":  token+" is the OTP for your password change request on SWARI. Thanks. www.swari.in " +req.body.appSignature +" by Triva.",
              "routeId": "1",
              "mobileNumbers": req.body.phone_number,
              "senderId": "SWARII",
              "signature": "signature",
              "smsContentType": "english"
            }

            // body.templateId="1707172433152755325";

            sendTXTGuruSMS("1707172433152755325",body);
            return Responder.success(res, { success: true, message: "" }) 
            
         /* reqPost.write(JSON.stringify(body));
          reqPost.end();
          reqPost.on('error', function (e) {
            console.error("line: 102 " + e);
            return Responder.success(res, { success: false, message: "Some Error Occured." })
          });
          */
          // Responder.success(res, user)
        } else {
          return Responder.success(res, { message: "Please enter valid Mobile Number." })
        }
      })
      .catch((err) => Responder.operationFailed(res, err))
  }



  static create(req, res) {
    let userdetails = req.body;

    //userdetails.name = userdetails.name.toLowerCase();
    //userdetails.email = userdetails.email.toLowerCase();
    if (!(userdetails.name && userdetails.password))
      return Responder.operationFailed(res, { message: "Please insert Mandatory fields." })
    User.findOne({ phone_number: userdetails.phone_number })
      .then((user) => {
        if (user) {
          return Responder.operationFailed(res, { message: "Mobile Number is already exists. " })
        } else {
          User.create(req.body)
            .then((user) => function(){
              Responder.success(res, user);
              console.log("Adfjgks");
            })
            .catch((err) => Responder.operationFailed(res, err))
        }
      })

  }

  static findUserById(req, res) {
    if (!req.params.userId)
      return Responder.operationFailed(res, { message: 'user Id is required' });
          User.findOne({ _id: req.params.userId })
          .then((user) => Responder.success(res, user))
          .catch(err => Responder.operationFailed(res, err))

  }


  static createdPayment(req, res) {

    // let merchantTransactionId = req.body.merchantTransactionId;

    //check exists
 console.log(" ==========================  ", req.body);
    GatewayTransaction.findOne({_id: ObjectId(req.body.tx_id)}).then((gtx)=>{
      if (!gtx) {
        return res.status(400).json({message: "No txn found"})
      }
      console.log(" ==============================  GTX  " ,gtx);
      
      GatewayTransaction.findOneAndUpdate({_id: ObjectId(req.body.tx_id) },{ $set: {status: 1,  state:'INIT'} }).then((e)=>{ 
        console.log(" ========= ",e);
      });

    })

    return  Responder.success(res, "Payment inititated")

  }

  static initiateTxn(req, res) {

    // let merchantTransactionId = req.body.merchantTransactionId;

    //check exists

    let merchantTransactionId = ObjectId();

    let gtPayload = {
      _id: merchantTransactionId,
      amount: req.body.amount,
      transaction_id: merchantTransactionId,
      user_id: req.body.user_id,
      merchantTransactionId: merchantTransactionId,
      state:'DRAFT',      
      status: 0, // Init,
      transaction_date: new Date().toISOString()
    }

    console.log({gtPayload});

    GatewayTransaction.create(gtPayload);

    return  Responder.created(res, {
      tx_id: merchantTransactionId
    })

  }
  
  static checkTxnStatus(req, res) {
    

      /*
        const options = {
        method: 'get',
        url: 'https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/status/{merchantId}/{merchantTransactionId}',
        headers: {
              accept: 'text/plain',
              'Content-Type': 'application/json',
              },

      };
      */
     console.log("  req  checkTxnStatus checkTxnStatus checkTxnStatus ",req.body)
     let  {
      live, tx_id
     } = req.body;

     try {
    
    phonePayCheckStatus(tx_id).then(resJson => {
        console.log("API Response:", resJson);
        
        if(resJson.data && resJson.success) {
          console.log(" ===========  ",resJson.data.state);
          return res.status(200).json({message: "success", data: {
            success: resJson.data.success , state: resJson.data.state
          }});
        } else {
          console.log(" ===========  Failed ",resJson.data.state);
          return res.status(200).json({message: "failed", data: {
            success: false , state: 'FAILED'
          }});
        }   
    })
    .catch(error => {
        console.error("Error: =============================================================", error);
        return res.status(500).json({message: "Something went wrong"});
    });;            
    
    }
    catch(err) {
      console.log(err);
    }
  }

  static checkPhonePayTransaction(req, res) {   
    try { 
    // console.log('RP payout ??????   ',req.body)
    // check razorpay transaction start
    // var request = require('request');

    // txn-webhook
    /* Webhook response
      {"success":true,"code":"PAYMENT_SUCCESS","message":"Your payment is successful.","data":{"merchantId":"PGTESTPAYUAT","merchantTransactionId":"MT7850590068188108","transactionId":"T2401142023526075477511","amount":2200,"state":"COMPLETED","responseCode":"SUCCESS","paymentInstrument":{"type":"NETBANKING","pgTransactionId":"**********","pgServiceTransactionId":"PG2212291607083344934300","bankTransactionId":null,"bankId":""}}}
    */

    let { response } = req.body;

    if(!response) {
      return res.status(400).json({message: "Invalid response "})
    }
    
    if(!req.params.phone) {
      return res.status(400).json({message: "Invalid request "})
    }


    let decodedResponse = Buffer.from(response,"base64").toString('utf-8');
    let gatewayResponse = JSON.parse(decodedResponse)

    console.log('direct response-----------'+response);
    console.log('\n next line');
    
  
    console.log('res decoded-----------',gatewayResponse, gatewayResponse.data);
    let {
      merchantTransactionId,
      amount,
      transactionId,
      state
    } = gatewayResponse.data;

    let userMobileNumber = req.params.phone;// merchantTransactionId.split("-")[0];
    
  

  
    // request('https://rzp_test_1DP5mmOlF5G5ag:<EMAIL>/v1/payments/'+req.body.payment_info, function (error, response, body) {
    //   console.log('Response:', body);
    // });

    // request('https://rzp_test_7aqNiYD3XzMJOd:<EMAIL>/v1/payments/'+req.body.payment_info, function (error, response, body) {
      /*
      {
        "success": true,
        "code": "PAYMENT_SUCCESS",
        "message": "Your request has been successfully completed.",
        "data": {
          "merchantId": "PGTESTPAYUAT",
          "merchantTransactionId": "MT7850590068188104",
          "transactionId": "T2111221437456190170379",
          "amount": 100,
          "state": "COMPLETED",
          "responseCode": "SUCCESS",
          "paymentInstrument": {
            "type": "UPI",
            "utr": "206378866112"
          }
        }
      }
    */

    // request(`https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/status/${merchantId}/${merchantTransactionId}`+req.body.payment_info, function (error, response, body) {
      
    //Save gateway response here
   
      if (state==="COMPLETED") {
        
          // console.log('usercontact  ?? ',resJson.contact)
          //  resJson.contact= (resJson.contact).substring(3);
          // console.log('usercontact  11?? ',resJson.contact)
        
        User.aggregate([ 
          {
            $match: {
                      phone_number:  userMobileNumber
                    }
                
          }])
          .then((userFound)=>
          {
          // if else start for user not found
          if (userFound.length == 0) {
            return res.status(400).json({message: "No user found"})
          }else{      
            
          //Gateway response
          GatewayTransaction.findOneAndUpdate({ _id: ObjectId(merchantTransactionId) },{
              $set: {
                      gateway_response: gatewayResponse.data,
                      amount: parseInt(amount),
                      state,
                      status:2,
                      transaction_id: transactionId,
                      user_id: userFound[0]._id, 
                      mobile:userMobileNumber       
                  }
      	   },{new:true}).then((txnG)=> {
              console.log("Updated gateway ",txnG)
           });
    
            
          console.log('Updating the user ',userFound)
          var resJsonObj= {};
          var req= {body:{}};
          req.body.transaction_id= transactionId;
          req.body.amount = parseInt(amount) / 100;
          req.body.user_id= userFound[0]._id;
          resJsonObj= req;
          // console.log('resJsonObj  ?? ',req)
          
          
            
          // UserController.backendRazorpay(resJsonObj)
          // .then((backendRazorpay) => {
          //   console.log('resJsonObj1111  ?? ',backendRazorpay)

          // })

          // start backendRazorpay

                // req.body.amount=  parseInt(amount);
                console.log('body backendRazorpay  ---',req.body)
                async.waterfall([
                function(callback){
                  console.log('user_id---1111')
                  User.aggregate([ 
                        {
                          $match: {
                                    _id: ObjectId(req.body.user_id),
                                    // is_subscribed : false
                                  }
                              
                        },
                        ])
                  .then((user)=>{
                    console.log('test user ',user)
                    callback(null,user);
                  })
                  .catch((err)=>Responder.operationFailed(res,err))
                
                },
                function(user,callback){
                  console.log('user_id---222',user)
                  Settings.aggregate([ 
                        {
                          $match: {
                                    'type': 'app_settings'
                                  }
                              
                        },
                        ])
                  .then((app_settings)=>{
                    callback(null,app_settings,user);
                  })
                  .catch((err)=>Responder.operationFailed(res,err))
                  
                },
                function(app_settings,user,callback){
                  console.log('Razorpay transaction')
                  console.log('app_settings  ',app_settings)
                    console.log("updated 0 called ",user);
                  //deduct Subscription Fee by server
                    var tr = {};
                    tr.amount = parseFloat(req.body.amount).toFixed(2);
                    tr.transaction_reason = "Funds Added";
                    tr.reason = "Subscription Fee by server Deducted";
                    // tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
                    tr.transaction_date = moment.tz('Asia/Kolkata').format('YYYY-MM-DDTHH:mm:ss.SSS');
                    tr.transaction_type = "CR";
                    tr.transaction_id = req.body.transaction_id;
                    tr.user_id = req.body.user_id;
                    tr.total_amount = parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2);                        

                    Transaction.create(tr)
                        .then((trnc) => {
                          console.log("updated tr 2",trnc);
                          // createPdf(null,'Razorpay',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);
                          callback(null,app_settings,user);
                        }).catch((err) => {
                            console.log("cancellled-- ",err);
                            Responder.operationFailed(res,err);

                        });
                
                },
                
                function(app_settings,user,callback){
                  console.log('wallet_balance---',user)
                  // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                    // console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
                    User.findOneAndUpdate(
                    {
                        _id: user[0]._id
                    }, 
                    {
                        $set: {
                            'wallet_balance': parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2),
                            // 'renewal_date':new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
                            // 'is_subscribed': true
                        }
                    })
                    .then((wallet_updated)=>{

                      UserController.sendEmailRazorpay(req.body.user_id, req.body.amount, req.body.transaction_id)
                      // Responder.success(res, wallet_updated)
                      callback(null,wallet_updated,app_settings);
                    })


                },
              
                
            ], function (err, result) {
                // result now equals 'done'
                console.log('done')
                // Responder.success(res, 'success')
                // start dashboardBackend
                // var reqParams= {params:{}};
                // reqParams.params.id= req.body.user_id;
                // var req= {body:{}};


                    var renewal_date=moment(new Date()).add(30, 'days');
                    renewal_date=moment(renewal_date).startOf('day').format('YYYY-MM-DDT00:00:01.000[Z]');
                    console.log('user_idrenewal_date ',renewal_date)

                      // find user
                          async.waterfall([
                          function(callback){
                            console.log('user_id dashboardBackend---1111')
                            User.aggregate([ 
                                  {
                                    $match: {
                                              _id: ObjectId(req.body.user_id),
                                              is_subscribed : false
                                            }
                                  },
                                  ])
                            .then((user)=>{
                              console.log('length--- -',user.length);
                              if(user.length > 0){
                              console.log('dashboardBackend user--- ',user)
                              callback(null,user);

                            }else{
                              Responder.success(res, 'success')

                            }
                            })
                            .catch((err)=>Responder.operationFailed(res,err))
                          
                          },
                          function(user,callback){
                            console.log('user_id---222',user)
                            Settings.aggregate([ 
                                  {
                                    $match: {
                                              'type': 'app_settings'
                                            }
                                        
                                  },
                                  ])
                            .then((app_settings)=>{
                              // callback(null,app_settings,user);
                              var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                              callback(null,user,subscribeFees,app_settings);

                            })
                            .catch((err)=>Responder.operationFailed(res,err))
                            
                          },
                          function(user,subscribeFees,app_settings,callback){

                            if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
                            console.log('wwwwwwwwwww  a',user)
                            console.log('wwwwwwwwwww  b',subscribeFees)
                            console.log('wwwwwwwwwww  c',app_settings)

                            var tr = {};
                            tr.amount = parseFloat(subscribeFees).toFixed(2);
                            tr.transaction_reason = "Subscription Fee";
                            tr.reason = "Subscription Fee by server Deducted";
                            tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
                            tr.transaction_date = new Date();
                            tr.transaction_type = "DR";
                            tr.transaction_id = common.generateTransactionId();
                            tr.user_id = user[0]._id;
                            tr.total_amount = parseFloat(user[0].wallet_balance - tr.amount).toFixed(2);
                            console.log('amounttttttttttttt  ###',tr)
                            console.log('user.wallet_balance - tr.amount  ###',user[0].wallet_balance + 'dfddddd' + tr.amount)
                            Transaction.create(tr)
                                .then((trnc) => {
                                  console.log("updated 2",subscribeFees);

                                  createPdf(null,'subscribeFees',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);

                            //     // send mail start
                            //     CommonSettings.find({type:'transactionSubscriptionFees'})
                            //  .then((res)=>
                            //  {
                            //   console.log('rrrrresponse')
                            //   console.log(res)
                            //   console.log('rrrrresponse')

                            //       var oldsubject=res[0].subject;
                            //       var userName= user[0].name;
                            //       var newSubject= oldsubject.replace("{name}",userName);

                            //       var oldBodySubject=res[0].emailBody;
                            //       var userName=user[0].name;
                            //       var newBodySubject= oldBodySubject.replace("{name}",userName);

                            //        Settings.findOne({ type: 'app_settings' })
                            //       .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                            //           var mailOptions = {
                            //             from: app_settings.billingEmail,
                            //             to: user[0].email,
                            //             subject:newSubject ,
                            //             html: newBodySubject,
                            //           };

                            //           transporter.sendMail(mailOptions, function(error, info){
                            //             if (error) {
                            //               console.log(error);
                            //             } else {
                            //               console.log('Email sent: ' + info.response);
                            //                 // send message start
                            //                var extServerOptionsPost = {
                            //                     hostname: "***************",
                            //                     path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                            //                     method: 'POST',
                            //                     port: '80',
                            //                     headers: {
                            //                       'Content-Type': 'application/json'
                            //                     }
                            //                   }

                            //                   const token = otplib.authenticator.generate(secret);
                            //                   console.log(token);

                            //                   var reqPost = http.request(extServerOptionsPost, function (response) {

                            //                     response.on('data', function (data) {
                            //                       console.log("line no: 87 ", data);
                            //                     });
                            //                   });
                            //                   var body =
                            //                     {
                            //                       "smsContent": 'RS '+ subscribeFees +' Subscription Fee deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' with TR Id '+ req.body.transaction_id +'. Valid for 30 days. A/c Bal:RS '+ req.body.balance +'.00. Thanks www.swari.in',
                            //                       "routeId": "1",
                            //                       "mobileNumbers": user[0].phone_number,
                            //                       // "mobileNumbers": '9855621130,7009232617',
                            //                       "senderId": "SWARII",
                            //                       "signature": "signature",
                            //                       "smsContentType": "english"
                            //                     }
                            //                   reqPost.write(JSON.stringify(body));
                            //                   reqPost.end();
                            //                   reqPost.on('error', function (e) {
                            //                     console.error("line: 102 " + e);
                            //                   });
                            //               // send message end
                            //             }
                            //           }); 


                            //       })
                            //       .catch((err) => {})
                    

                            //  })
                            // .then((trc)=>{})
                            //  .catch((err)=>{}) 
                            //     // send mail end

                                    callback(null,user,app_settings,tr.total_amount,trnc);
                                    // callback(null,user,trnc,subscribeFees,app_settings);
                                })
                                .catch((err) => {
                                    console.log("cancellled999999",err);
                                    Responder.operationFailed(res,err);

                                });
                            }else{

                                Responder.success(res, 'not_enough_balance')
                            }
                          },
                          function(user,app_settings,subscribeFees,trnc,callback){
                            console.log('wallet_balance---',user)
                            console.log('renewal_date---',renewal_date)

                            // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                            // if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
                              console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
                              User.findOneAndUpdate(
                              {
                                  _id: user[0]._id
                              }, 
                              {
                                  $set: {
                                      'wallet_balance': subscribeFees,
                                      'renewal_date':renewal_date,
                                      'is_subscribed': true,
                                      'lastSubscribed': moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

                                  }
                              })
                              .then((wallet_updated)=>{
                                // Responder.success(res, wallet_updated)
                                // callback(null,wallet_updated,subscribeFees,app_settings);
                                    callback(null,user[0],app_settings);

                              })
                            // }else{
                                // Responder.success(res, 'not_enough_balance')

                            // }

                          },

                            // function(user,trnc,amount,app_settings,callback){
                            //  console.log('ttttttttt11 ',moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                            //  console.log('ttttttttt22 ',moment.utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                            //  console.log('ttttttttt33 ',moment().utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                            //  // exit();

                            //              console.log("updated 3 called",parseFloat(user[0].wallet_balance - amount).toFixed(2));
                            //              User.update({
                            //                  _id: user._id
                            //              }, {
                            //                  $set: {
                            //                      wallet_balance: parseFloat(user.wallet_balance - amount).toFixed(2),
                            //                      lastSubscribed: moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")
                            //                  }
                            //              }).
                            //              then((trnc) => {
                            //                console.log("updated 3");
                            //                callback(null,user,app_settings);
                            //              }).catch((err) => {
                            //                console.log(err);
                            //                  console.log("cancellled");
                            //              });
                            //            },
                                      function(user,app_settings,callback){
                                        console.log('referal_code11',user);
                                        console.log('referal_code33',app_settings);
                                        User.find({
                                            referal_code: user.referred_by_code
                                        }).
                                        then((referred_by) => {
                                          console.log('referred_by-------- ',referred_by);
                                          callback(null,user,referred_by,app_settings);
                                        });
                                      },
                                      function(user,referred_by,app_settings,callback){
                                        Transaction.count({
                                            due_to_user: ObjectId(user._id),
                                            transaction_reason: 'Referral Commission'
                                        }).then((referred_by_count) => {
                                            console.log('referal_code44',referred_by_count);
                                            callback(null,user,referred_by,referred_by_count,app_settings);
                                        });
                                      },
                                      function(user,referred_by,referred_by_count,app_settings,callback){
                                        console.log('referred_by_count  --- ',referred_by_count); 
                                        // console.log(referred_by[0].wallet_balance + "internal total Balance check");
                                        console.log('referred_by_user',referred_by)
                                        console.log('app_settings----',app_settings)
                                        if(referred_by.length!=0){
                                          var tr = {};
                                          if (referred_by_count == 0) {
                                              var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 30;
                                          } else {
                                              var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 8;
                                          }
                                          // console.log('Referral Commission11',referred_by);
                                          // console.log('Referral Commission12',wb);
                                            console.log('Referral Commission13',subscribeFees);

                                          tr.amount = parseFloat(subscribeFees).toFixed(2);
                                          tr.transaction_reason = "Referral Commission";
                                          tr.reason = "Refe56rral Commission for " + referred_by[0].name;
                                          
                                          tr.due_to_user_name = user.name;
                                          tr.referred_by_name = referred_by[0].name;
                                          tr.due_to_user = user._id;
                                          tr.transaction_date = new Date();
                                          tr.transaction_type = "CR";
                                          var totalwb = parseFloat(referred_by[0].wallet_balance + subscribeFees).toFixed(2);
                                          console.log('walet balance  -563-- ', totalwb);
                                          tr.total_amount = parseFloat(totalwb).toFixed(2);
                                          tr.transaction_id = common.generateTransactionId();
                                          if (referred_by_count == 0) {
                                              tr.subscribeFeePercentage = 30;
                                          } else {
                                              tr.subscribeFeePercentage = 8;
                                          }
                                          tr.user_id = referred_by[0]._id;
                                          tr.referred_by_code = referred_by[0].referal_code;
                                          // console.log('Referral Commission1---------2',wb);




                                          Transaction.create(tr)
                                              .then((trnc) => {
                                                  // console.log("success",trnc)

                                                  callback(null,referred_by,parseFloat(subscribeFees).toFixed(2),totalwb,user)
                                              }).catch((err) => {
                                                  console.log("err_referral_commission  ", err);
                                              })
                                        }else{
                                          callback(null,referred_by,null,null,user);
                                        }

                                      },
                                      function(referred_by,subscribeFees,totalwb,user,callback){
                                        console.log("846------   "+totalwb);
                                        if(referred_by.length!=0){
                                          User.update({
                                              _id: referred_by[0]._id
                                          }, {
                                              $set:{
                                                wallet_balance: totalwb
                                              }
                                              // $inc: {
                                              //     wallet_balance: subscribeFees
                                              // }
                                          }).
                                          then((trnc) => {
                                              callback(null);
                                              console.log("success852",trnc)
                                              User.aggregate([ 
                                                    {
                                                      $match: {
                                                                _id: ObjectId(user._id)
                                                              }
                                                          
                                                    },
                                                    ])
                                              .then((user)=>{
                                                Responder.success(res, user)
                                              })

                                          }).catch((err) => {
                                              console.log("cancellled");
                                          })
                                        }else{
                                          callback(null);
                                        }
                                      }

                          
                          
                      ], function (err, result) {
                          // result now equals 'done'
                          console.log('done')
                          Responder.success(res, 'success')

                          // userCallback();
                      });

                // end dashboardBackend


                // userCallback();
            });
          // end backendRazorpay
          // if else close for user not found
          }

        });

      }
      else if(state=="FAILED" ){

        User.aggregate([ 
          {
            $match: {
                      phone_number:  userMobileNumber
                    }
                
          }])
          .then((userFound)=>
          {
          // if else start for user not found
          if (userFound.length == 0) {
            return res.status(400).json({message: "No user found"})
          }
            //Gateway response
        //   GatewayTransaction.findOneAndUpdate({_id: ObjectId(merchantTransactionId)},{
	      // $set: {	
        //     gateway_response: gatewayResponse.data,
        //     amount: parseInt(amount) / 100,
        //     state,
        //     status:3, //Failed
        //     transaction_id: merchantTransactionId,
        //     user_id: userFound[0]._id,        
        //   }} );

          GatewayTransaction.findOneAndUpdate({ _id: ObjectId(merchantTransactionId) },{
            $set: {
                    gateway_response: gatewayResponse.data,
                    amount: parseInt(amount),
                    state,
                    status:3,
                    transaction_id: transactionId,
                    user_id: userFound[0]._id, 
                    mobile:userMobileNumber       
                }
         },{new:true}).then((txnG)=> {
            console.log("Updated gateway ",txnG)
         });


          res.status(400).json({message: "Txn failed"})

        });
              
      } else{
         //Gateway response
         GatewayTransaction.findOneAndUpdate({_id: ObjectId(merchantTransactionId)},{
          $set: { gateway_response: gatewayResponse.data,
          amount: parseInt(amount) / 100,
          state,
          status:4, //Pending
          transaction_id: merchantTransactionId,
          user_id: userFound[0]._id,  
          mobile:userMobileNumber      
        }});
      }

      }catch(err) {
        console.log(" txn web hook err",err);
        res.status(400).json({message: "Txn failed"})
      }
  }

  static checkRPTransaction(req, res) {
      console.log('RP payout ??????   ')
      console.log('RP payout ??????   ',req.body)
      // check razorpay transaction start
      var request = require('request');
      var transaction_id= req.body.payment_info;

      // request('https://rzp_test_1DP5mmOlF5G5ag:<EMAIL>/v1/payments/'+req.body.payment_info, function (error, response, body) {
      //   console.log('Response:', body);
      // });

      request('https://rzp_test_7aqNiYD3XzMJOd:<EMAIL>/v1/payments/'+req.body.payment_info, function (error, response, body) {
        console.log('Response:111', response);
        console.log('Response:222', JSON.parse(body));
        var  resJson= JSON.parse(body);
        console.log('Response:333', resJson.id);
        
        if (resJson.id) {
              console.log('usercontact  ?? ',resJson.contact)
               resJson.contact= (resJson.contact).substring(3);
              console.log('usercontact  11?? ',resJson.contact)
            
            User.aggregate([ 
              {
                $match: {
                          phone_number: resJson.contact 
                        }
                    
              }])
              .then((userFound)=>
             {
              // if else start for user not found
              if (userFound.length == 0) {
                Responder.operationFailed(res,userFound)
              }else{                
              console.log('userFound  ?? ',userFound)
              var resJsonObj= {};
              var req= {body:{}};
              req.body.transaction_id= transaction_id;
              req.body.amount= resJson.amount / 100;
              req.body.user_id= userFound[0]._id;
              resJsonObj= req;
              console.log('resJsonObj  ?? ',req)
              
              
                
              // UserController.backendRazorpay(resJsonObj)
              // .then((backendRazorpay) => {
              //   console.log('resJsonObj1111  ?? ',backendRazorpay)

              // })

              // start backendRazorpay

                    req.body.amount=  parseInt(req.body.amount);
                    console.log('body backendRazorpay  ---',req.body)
                    async.waterfall([
                    function(callback){
                      console.log('user_id---1111')
                      User.aggregate([ 
                            {
                              $match: {
                                        _id: ObjectId(req.body.user_id),
                                        // is_subscribed : false
                                      }
                                  
                            },
                            ])
                      .then((user)=>{
                        console.log('test user ',user)
                        callback(null,user);
                      })
                      .catch((err)=>Responder.operationFailed(res,err))
                    
                    },
                    function(user,callback){
                      console.log('user_id---222',user)
                      Settings.aggregate([ 
                            {
                              $match: {
                                        'type': 'app_settings'
                                      }
                                  
                            },
                            ])
                      .then((app_settings)=>{
                        callback(null,app_settings,user);
                      })
                      .catch((err)=>Responder.operationFailed(res,err))
                      
                    },
                    function(app_settings,user,callback){
                      console.log('Razorpay transaction')
                      console.log('app_settings  ',app_settings)
                       console.log("updated 0 called ",user);
                      //deduct Subscription Fee by server
                        var tr = {};
                        tr.amount = parseFloat(req.body.amount).toFixed(2);
                        tr.transaction_reason = "Funds Added";
                        tr.reason = "Subscription Fee by server Deducted";
                        // tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
                        tr.transaction_date = moment.tz('Asia/Kolkata').format('YYYY-MM-DDTHH:mm:ss.SSS');
                        tr.transaction_type = "CR";
                        tr.transaction_id = req.body.transaction_id;
                        tr.user_id = req.body.user_id;
                        tr.total_amount = parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2);                        

                        Transaction.create(tr)
                            .then((trnc) => {
                              console.log("updated tr 2",trnc);
                              // createPdf(null,'Razorpay',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);
                              callback(null,app_settings,user);
                            }).catch((err) => {
                                console.log("cancellled-- ",err);
                                Responder.operationFailed(res,err);

                            });
                    
                    },
                    
                    function(app_settings,user,callback){
                      console.log('wallet_balance---',user)
                      // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                        // console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
                        User.findOneAndUpdate(
                        {
                            _id: user[0]._id
                        }, 
                        {
                            $set: {
                                'wallet_balance': parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2),
                                // 'renewal_date':new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
                                // 'is_subscribed': true
                            }
                        })
                        .then((wallet_updated)=>{

                          UserController.sendEmailRazorpay(user[0]._id, req.body.amount, req.body.transaction_id)
                          // Responder.success(res, wallet_updated)
                          callback(null,wallet_updated,app_settings);
                        })


                    },
                 
                    
                ], function (err, result) {
                    // result now equals 'done'
                    console.log('done')
                    // Responder.success(res, 'success')
                    // start dashboardBackend
                    // var reqParams= {params:{}};
                    // reqParams.params.id= req.body.user_id;
                    // var req= {body:{}};


                        var renewal_date=moment(new Date()).add(30, 'days');
                        renewal_date=moment(renewal_date).startOf('day').format('YYYY-MM-DDT00:00:01.000[Z]');
                        console.log('user_idrenewal_date ',renewal_date)

                          // find user
                              async.waterfall([
                              function(callback){
                                console.log('user_id dashboardBackend---1111')
                                User.aggregate([ 
                                      {
                                        $match: {
                                                  _id: ObjectId(req.body.user_id),
                                                  is_subscribed : false
                                                }
                                            
                                      },
                                      ])
                                .then((user)=>{
                                  console.log('length--- -',user.length);
                                  if(user.length > 0){
                                  console.log('dashboardBackend user--- ',user)
                                  callback(null,user);

                                }else{
                                  Responder.success(res, 'success')

                                }
                                })
                                .catch((err)=>Responder.operationFailed(res,err))
                              
                              },
                              function(user,callback){
                                console.log('user_id---222',user)
                                Settings.aggregate([ 
                                      {
                                        $match: {
                                                  'type': 'app_settings'
                                                }
                                            
                                      },
                                      ])
                                .then((app_settings)=>{
                                  // callback(null,app_settings,user);
                                  var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                                  callback(null,user,subscribeFees,app_settings);

                                })
                                .catch((err)=>Responder.operationFailed(res,err))
                                
                              },
                              function(user,subscribeFees,app_settings,callback){

                                if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
                                console.log('wwwwwwwwwww  a',user)
                                console.log('wwwwwwwwwww  b',subscribeFees)
                                console.log('wwwwwwwwwww  c',app_settings)

                                var tr = {};
                                tr.amount = parseFloat(subscribeFees).toFixed(2);
                                tr.transaction_reason = "Subscription Fee";
                                tr.reason = "Subscription Fee by server Deducted";
                                tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
                                tr.transaction_date = new Date();
                                tr.transaction_type = "DR";
                                tr.transaction_id = common.generateTransactionId();
                                tr.user_id = user[0]._id;
                                tr.total_amount = parseFloat(user[0].wallet_balance - tr.amount).toFixed(2);
                               console.log('amounttttttttttttt  ###',tr)
                               console.log('user.wallet_balance - tr.amount  ###',user[0].wallet_balance + 'dfddddd' + tr.amount)
                                Transaction.create(tr)
                                    .then((trnc) => {
                                      console.log("updated 2",subscribeFees);

                                      createPdf(null,'subscribeFees',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);

                                //     // send mail start
                                //     CommonSettings.find({type:'transactionSubscriptionFees'})
                                //  .then((res)=>
                                //  {
                                //   console.log('rrrrresponse')
                                //   console.log(res)
                                //   console.log('rrrrresponse')

                                //       var oldsubject=res[0].subject;
                                //       var userName= user[0].name;
                                //       var newSubject= oldsubject.replace("{name}",userName);

                                //       var oldBodySubject=res[0].emailBody;
                                //       var userName=user[0].name;
                                //       var newBodySubject= oldBodySubject.replace("{name}",userName);

                                //        Settings.findOne({ type: 'app_settings' })
                                //       .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                                //           var mailOptions = {
                                //             from: app_settings.billingEmail,
                                //             to: user[0].email,
                                //             subject:newSubject ,
                                //             html: newBodySubject,
                                //           };

                                //           transporter.sendMail(mailOptions, function(error, info){
                                //             if (error) {
                                //               console.log(error);
                                //             } else {
                                //               console.log('Email sent: ' + info.response);
                                //                 // send message start
                                //                var extServerOptionsPost = {
                                //                     hostname: "***************",
                                //                     path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                //                     method: 'POST',
                                //                     port: '80',
                                //                     headers: {
                                //                       'Content-Type': 'application/json'
                                //                     }
                                //                   }

                                //                   const token = otplib.authenticator.generate(secret);
                                //                   console.log(token);

                                //                   var reqPost = http.request(extServerOptionsPost, function (response) {

                                //                     response.on('data', function (data) {
                                //                       console.log("line no: 87 ", data);
                                //                     });
                                //                   });
                                //                   var body =
                                //                     {
                                //                       "smsContent": 'RS '+ subscribeFees +' Subscription Fee deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' with TR Id '+ req.body.transaction_id +'. Valid for 30 days. A/c Bal:RS '+ req.body.balance +'.00. Thanks www.swari.in',
                                //                       "routeId": "1",
                                //                       "mobileNumbers": user[0].phone_number,
                                //                       // "mobileNumbers": '9855621130,7009232617',
                                //                       "senderId": "SWARII",
                                //                       "signature": "signature",
                                //                       "smsContentType": "english"
                                //                     }
                                //                   reqPost.write(JSON.stringify(body));
                                //                   reqPost.end();
                                //                   reqPost.on('error', function (e) {
                                //                     console.error("line: 102 " + e);
                                //                   });
                                //               // send message end
                                //             }
                                //           }); 


                                //       })
                                //       .catch((err) => {})
                        

                                //  })
                                // .then((trc)=>{})
                                //  .catch((err)=>{}) 
                                //     // send mail end

                                       callback(null,user,app_settings,tr.total_amount,trnc);
                                       // callback(null,user,trnc,subscribeFees,app_settings);
                                    })
                                    .catch((err) => {
                                        console.log("cancellled999999",err);
                                        Responder.operationFailed(res,err);

                                    });
                                }else{

                                    Responder.success(res, 'not_enough_balance')
                                }
                              },
                              function(user,app_settings,subscribeFees,trnc,callback){
                                console.log('wallet_balance---',user)
                                console.log('renewal_date---',renewal_date)

                                // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
                                // if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
                                  console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
                                  User.findOneAndUpdate(
                                  {
                                      _id: user[0]._id
                                  }, 
                                  {
                                      $set: {
                                          'wallet_balance': subscribeFees,
                                          'renewal_date':renewal_date,
                                          'is_subscribed': true,
                                          'lastSubscribed': moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

                                      }
                                  })
                                  .then((wallet_updated)=>{
                                    // Responder.success(res, wallet_updated)
                                    // callback(null,wallet_updated,subscribeFees,app_settings);
                                       callback(null,user[0],app_settings);

                                  })
                                // }else{
                                    // Responder.success(res, 'not_enough_balance')

                                // }

                              },

                               // function(user,trnc,amount,app_settings,callback){
                               //  console.log('ttttttttt11 ',moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                               //  console.log('ttttttttt22 ',moment.utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                               //  console.log('ttttttttt33 ',moment().utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
                               //  // exit();

                               //              console.log("updated 3 called",parseFloat(user[0].wallet_balance - amount).toFixed(2));
                               //              User.update({
                               //                  _id: user._id
                               //              }, {
                               //                  $set: {
                               //                      wallet_balance: parseFloat(user.wallet_balance - amount).toFixed(2),
                               //                      lastSubscribed: moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")
                               //                  }
                               //              }).
                               //              then((trnc) => {
                               //                console.log("updated 3");
                               //                callback(null,user,app_settings);
                               //              }).catch((err) => {
                               //                console.log(err);
                               //                  console.log("cancellled");
                               //              });
                               //            },
                                          function(user,app_settings,callback){
                                            console.log('referal_code11',user);
                                            console.log('referal_code33',app_settings);
                                            User.find({
                                                referal_code: user.referred_by_code
                                            }).
                                            then((referred_by) => {
                                              console.log('referred_by-------- ',referred_by);
                                              callback(null,user,referred_by,app_settings);
                                            });
                                          },
                                          function(user,referred_by,app_settings,callback){
                                            Transaction.count({
                                                due_to_user: ObjectId(user._id),
                                                transaction_reason: 'Referral Commission'
                                            }).then((referred_by_count) => {
                                                console.log('referal_code44',referred_by_count);
                                                callback(null,user,referred_by,referred_by_count,app_settings);
                                            });
                                          },
                                          function(user,referred_by,referred_by_count,app_settings,callback){
                                            console.log('referred_by_count  --- ',referred_by_count); 
                                            // console.log(referred_by[0].wallet_balance + "internal total Balance check");
                                            console.log('referred_by_user',referred_by)
                                            console.log('app_settings----',app_settings)
                                            if(referred_by.length!=0){
                                              var tr = {};
                                              if (referred_by_count == 0) {
                                                  var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 30;
                                              } else {
                                                  var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 8;
                                              }
                                              // console.log('Referral Commission11',referred_by);
                                              // console.log('Referral Commission12',wb);
                                               console.log('Referral Commission13',subscribeFees);

                                              tr.amount = parseFloat(subscribeFees).toFixed(2);
                                              tr.transaction_reason = "Referral Commission";
                                              tr.reason = "Refe56rral Commission for " + referred_by[0].name;
                                              
                                              tr.due_to_user_name = user.name;
                                              tr.referred_by_name = referred_by[0].name;
                                              tr.due_to_user = user._id;
                                              tr.transaction_date = new Date();
                                              tr.transaction_type = "CR";
                                              var totalwb = parseFloat(referred_by[0].wallet_balance + subscribeFees).toFixed(2);
                                              console.log('walet balance  -563-- ', totalwb);
                                              tr.total_amount = parseFloat(totalwb).toFixed(2);
                                              tr.transaction_id = common.generateTransactionId();
                                              if (referred_by_count == 0) {
                                                  tr.subscribeFeePercentage = 30;
                                              } else {
                                                  tr.subscribeFeePercentage = 8;
                                              }
                                              tr.user_id = referred_by[0]._id;
                                              tr.referred_by_code = referred_by[0].referal_code;
                                              // console.log('Referral Commission1---------2',wb);




                                              Transaction.create(tr)
                                                  .then((trnc) => {
                                                      // console.log("success",trnc)

                                                      callback(null,referred_by,parseFloat(subscribeFees).toFixed(2),totalwb,user)
                                                  }).catch((err) => {
                                                      console.log("err_referral_commission  ", err);
                                                  })
                                            }else{
                                              callback(null,referred_by,null,null,user);
                                            }

                                          },
                                          function(referred_by,subscribeFees,totalwb,user,callback){
                                            console.log("846------   "+totalwb);
                                            if(referred_by.length!=0){
                                              User.update({
                                                  _id: referred_by[0]._id
                                              }, {
                                                  $set:{
                                                    wallet_balance: totalwb
                                                  }
                                                  // $inc: {
                                                  //     wallet_balance: subscribeFees
                                                  // }
                                              }).
                                              then((trnc) => {
                                                  callback(null);
                                                  console.log("success852",trnc)
                                                  User.aggregate([ 
                                                        {
                                                          $match: {
                                                                    _id: ObjectId(user._id)
                                                                  }
                                                              
                                                        },
                                                        ])
                                                  .then((user)=>{
                                                    Responder.success(res, user)
                                                  })

                                              }).catch((err) => {
                                                  console.log("cancellled");
                                              })
                                            }else{
                                              callback(null);
                                            }
                                          }

                              
                              
                          ], function (err, result) {
                              // result now equals 'done'
                              console.log('done')
                              Responder.success(res, 'success')

                              // userCallback();
                          });
  
                    // end dashboardBackend


                    // userCallback();
                });
              // end backendRazorpay
              // if else close for user not found
              }

             });

        }
        else{
          Responder.operationFailed(res, 'transaction not found')
        }
      });
      // check razorpay transaction end

          // User.findOne({ _id: req.params.userId })
          // .then((user) => Responder.success(res, user))
          // .catch(err => Responder.operationFailed(res, err))
  }

  static update(req, res) {
    console.log('ID from params>>>>>>>>>>>>:', req.params.id);
    console.log('ID in body------------------:', req.body.id);
    console.log('_id in body>>>>>>>>>>>>>>>>>>>>:', req.body._id);
    console.log('req.body-- ',req.body)
    if (req.body.location) {
      req.body.location.type= 'Point';
    }

    console.log(" ===========   live_location live_location ",req.body.live_location)
    // Add this logging
    console.log("Device info in update:", {
      device_id: req.body.device_id,
      device_platform: req.body.device_platform,
      device_info: req.body.device_info
    });

    if(!req.body.district) {      
      // req.body.district = '';
    }

    if(!req.body.live_location) {
      console.log(" ============= !!!! live_location   LIVE location updated");
      req.body.live_location = req.body.district;
    }
    
    delete req.body._id;
    if (!req.params.id)
      return Responder.operationFailed(res, { message: 'User Id Is Required.' })
    User.findOneAndUpdate({ _id: ObjectId(req.params.id) }, { $set: req.body },   { new: true, omitUndefined: true })
    .then((data) =>{ return User.findOne({_id:data._id })})  
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
    


  }


  static getSettings(req, res) {
   
    let data = {
      notification: req.body.notification,
      notification_sound: req.body.notification_sound
    }
    console.log("notification ",data, req.user._id);

    User.findOne({ _id: req.user._id})     
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
  }

  static updateSettings(req, res) {
   
    let data = {
      notification: req.body.notification,
      notification_sound: req.body.notification_sound
    }
    console.log("notification ",data);

    User.findOneAndUpdate({ _id: req.user._id }, { $set: data })
    .then((data) =>{ return User.findOne({_id:data._id })})  
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
  }


  static updateUserExpiry(req, res) {

    var extention =  req.body.expire;
    // var timeNow = new Date().getTime();
    // var currentDate = new Date(timeNow).toISOString().slice(0, 10);
    // console.log("my date",currentDate)
    // var unixTimestamp = (Math.round(new Date(currentDate+ ' 00:00:10.000').getTime()/1000))*1000;
    // var freetrialperiod = new Date(unixTimestamp + 1000 * 60 * 60 * (extention  * 24)).toISOString().slice(0, 10); //one month before

    // console.log('fffffffffffffffffff --- ',freetrialperiod)

    var freetrialperiod= moment.utc(req.body.renewal_date).add(extention, 'days').startOf('day').toDate()

    User.update({_id:req.params.id},{$set:{renewal_date:freetrialperiod}})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
           
  }

  static updateForWallet(req, res) {        
    var wb;
    User.findOneAndUpdate({_id:req.params.id },{$set:req.body},{ new: true, omitUndefined: true })      
      .then((usrs) => Responder.success(res,usrs))
     .catch((err) => Responder.operationFailed(res, err))
     //  User.findOne({_id:req.params.id })
     //  .then((usr) =>{ wb = usr.wallet_balance - req.body.boostFees; console.log(wb); return  User.findOneAndUpdate({ _id:usr._id },{$set:{'wallet_balance':wb}})})
     //  .then((data) =>{ return User.findOne({_id:data._id })})
     //  .then((usrs) => Responder.success(res,usrs))
     // .catch((err) => Responder.operationFailed(res, err))
    
    
  }

static stateUpdate(req, res){
      
      var extention =  req.body.renewal_date;
      var Obj={}
      if(req.body.startDate)
      {
        Obj={state:req.body.state,created_at: {$gte:req.body.startDate,$lte:req.body.endDate}};
      }
      else{
        Obj={state:req.body.state};
      }
      console.log('obj--',Obj)

     User.find(Obj)
       .then((users)=>{
          users.forEach(function (user, k) {
            if (user.renewal_date) {
              // console.log('user expiry -- ',user.renewal_date)

                // console.log('user expiry -- ',user)
                var freetrialperiod= moment.utc(user.renewal_date).add(extention + 1, 'days').startOf('day').toDate()
                console.log('freetrialperiod-- ',freetrialperiod)

                // var currentDate = new Date(user.renewal_date).toISOString().slice(0, 10);
                // console.log('user currentDate-- ',currentDate)

                // var unixTimestamp = (Math.round(new Date(currentDate+ ' 00:00:10.000').getTime()/1000))*1000;
                //  var freetrialperiod = new Date(unixTimestamp + 1000 * 60 * 60 * (extention  * 24)).toISOString().slice(0, 10); //one month getUsersByStateForEmail
                console.log('user phone_number-- ',user.phone_number)
                // var freetrialperiod= new Date(freetrialperiod);
                // console.log('user freetrialperiod-- ',freetrialperiod)
                 
                 User.update({phone_number:user.phone_number},{$set:{renewal_date:freetrialperiod}})
                  .then((val)=>
                    {
                      // console.log('updated')
                    Responder.success(res,val)
                      
                    }
                    )  
                  .catch((err)=>Responder.operationFailed(res,err))
            }
            else{
              console.log('user expiry --??????????? ')
            }
          }) 

       })
       .catch((err)=>Responder.operationFailed(res,err))

      // var extention =  req.body.renewal_date;
      // var timeNow = new Date().getTime();
      // var currentDate = new Date(timeNow).toISOString().slice(0, 10);
      // console.log("my date",currentDate)
      // var unixTimestamp = (Math.round(new Date(currentDate+ ' 00:00:10.000').getTime()/1000))*1000;
      // var freetrialperiod = new Date(unixTimestamp + 1000 * 60 * 60 * (extention  * 24)).toISOString().slice(0, 10); //one month before

      // console.log('fffffffffffffffffff --- ',freetrialperiod)
      // User.update({state:req.body.state},{$set:{renewal_date:freetrialperiod}},{multi:true})
      //  .then((val)=>Responder.success(res,val))
      //  .catch((err)=>Responder.operationFailed(res,err))
  }

  static updateForWalletWhenUserNotSubscribe(req, res) {
    var wb;
      User.findOne({_id:req.params.id })
      .then((usr) =>{ wb = usr.wallet_balance - req.body.subscribedFees; console.log(wb); return  User.findOneAndUpdate({ _id:usr._id },{$set:{'wallet_balance':wb,'renewal_date':req.body.renewal_date}})})
      .then((data) =>{ return User.findOne({_id:data._id })})
      .then((usrs) =>  Responder.success(res,usrs))
     .catch((err) => Responder.operationFailed(res, err))

     // if( req.body.disable =="account"){
     //  User.findOne({_id:req.params.id })
     //  .then((usr) =>{ return  User.findOneAndUpdate({ _id:usr._id },{$set:{'is_subscribed':req.body.is_subscribed}})})
     //  .then((data) =>{ return User.findOne({_id:data._id })})
     //  .then((usrs) => Responder.success(res,usrs))
     // .catch((err) => Responder.operationFailed(res, err))
     // }
    
    
  }

  static updateForReport(req, res) {
    console.log(req.params)
    console.log("qqqqqqqqqq", req.body)
    User.findOneAndUpdate({ _id: req.params.id }, { $addToSet: { 'report': req.body } })
      .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static updateForRate(req, res) {
    console.log(req.params)
    console.log("qqqqqqqqqq", req.body)
    User.findOneAndUpdate({ _id: req.params.id }, { $addToSet: { 'rating_review': req.body } })
      .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static remove(req, res) {
    console.log(req.body);
    User.deleteOne({ _id: req.body.id })
    .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static resendOtp(req, res) {

    const token = otplib.authenticator.generate(secret);
    console.log({token});

    User.find({'phone_number':req.body.phone_number})
      .then((userAlreadyCreated) => {

        User.findOneAndUpdate({ phone_number: req.body.phone_number }, { $set: { otp:token} })
          .then((data) =>{
            
          }) 

      });
      var body =
        {
          "smsContent":  token+" is the OTP for registration on SWARI. Thanks. www.swari.in "+req.body.appSignature,
          "routeId": "1",
          "mobileNumbers": req.body.phone_number,
          "senderId": "SWARII",
          "signature": "signature",
          "smsContentType": "english"
        }

        // body.templateId="1707169831386859678"; //Reg otp template
        //Send SMS using Txt Guru
      sendTXTGuruSMS("1707169831386859678",body);
      wpSendOtp(token, req.body.phone_number);
      return Responder.success(res, { success: true, message: "" }) 

  }

  static sendOtp(req, res) {

    const token = otplib.authenticator.generate(secret);
    console.log({token});


   	 //console.log(req.body)

      var location={ };
      location.coordinates = [74.8736788,31.6343083];

      req.body.location = location;

      User.find({'phone_number':req.body.phone_number})
      .then((userAlreadyCreated) => {
        console.log('userAlreadyCreated  --- ',userAlreadyCreated.length);
        if (userAlreadyCreated.length > 0 && !userAlreadyCreated.active_status){

         User.findOneAndUpdate({ phone_number: req.body.phone_number }, { $set: { otp:token} })
          .then((data) =>{
            
          }) 

        }
        else if (userAlreadyCreated.length > 0 && userAlreadyCreated.active_status){
        
        }
        else{
          User.create(req.body)
          .then((userCreated) => {
            console.log('userCreated  --- ',userCreated);
             User.findOneAndUpdate({ phone_number: req.body.phone_number }, { $set: { otp:token} })
              .then((data) =>{
                
              }) 
          });
        }
      });
     
      // User.findOneAndUpdate({ phone_number: req.body.phone_number }, { $set: { otp:req.body.otp} })
      // .then((data) =>{ console.log("succcess1100"); console.log(data); return User.findOne({_id:data._id })})  

      console.log(req.body)
      if (!req.body.phone_number)
        return Responder.operationFailed(res, { success: false, message: "Mobile number is mandatory." })

    /*  var extServerOptionsPost = {
        hostname: "***************",
        path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
        method: 'POST',
        port: '80',
        headers: {
          'Content-Type': 'application/json'
        }
      }

    
*/
      var body =
      {
        "smsContent":  token+" is the OTP for registration on SWARI. Thanks. www.swari.in "+req.body.appSignature,
        "routeId": "1",
        "mobileNumbers": req.body.phone_number,
        "senderId": "SWARII",
        "signature": "signature",
        "smsContentType": "english"
      }

      // body.templateId="1707169831386859678"; //Reg otp template
      //Send SMS using Txt Guru
      sendTXTGuruSMS("1707169831386859678",body);
      return Responder.success(res, { success: true, message: "" }) 
      
      /*
      var reqPost = http.request(extServerOptionsPost, function (response) {

      response.on('data', function (data) {
        console.log("line no: 87 ", data);
        return Responder.success(res, { success: true, message: token })        
      });
    });
   
    reqPost.write(JSON.stringify(body));
    reqPost.end();
    reqPost.on('error', function (e) {
      console.error("line: 102 " + e);
      return Responder.success(res, { success: false, message: "Some Error Occured." })
    });
    */

  }


  static sendMessageToSelectedUsers(req, res) {
    console.log('phone_number')
    // console.log(req.body)
    // var phone_number_list='';
    // req.body.phone_number_list.forEach(function (phone_number, k) { 
    //   phone_number_list + phone_number;               
    //   // if (user.title) {
    //   //   console.log('user'+user)
    //   //   // title=user.title;
        
    //   // }
    //   // else{
    //   //   console.log('aa'+user)
    //   //   phone_number_list= phone_number_list + ',' +user;
    //   // }
       
    // });

    console.log('phone_number_list  ',req.body.phone_number_list)

    // console.log(req.body)
    if (!req.body.phone_number_list)
      return Responder.operationFailed(res, { success: false, message: "Mobile number is mandatory." })

    console.log("hit")
    var extServerOptionsPost = {
      hostname: "***************",
      path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
      method: 'POST',
      port: '80',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const token = otplib.authenticator.generate(secret);
    console.log(token);

    var reqPost = http.request(extServerOptionsPost, function (response) {

      response.on('data', function (data) {
        console.log("line no: 87 ", data);
        return Responder.success(res, { success: true, message: token })
      });
    });

    console.log('req.body.title  --- ',req.body.title)
    var body = {
      "smsContent": req.body.title + '.Thanks. www.swari.in',
      "routeId": "1",
      "mobileNumbers": req.body.phone_number_list,
      // "mobileNumbers": '9855621130,7009232617',
      "senderId": "SWARII",
      "signature": "signature",
      "smsContentType": "english"
    }

    sendTXTGuruSMS("1707169831386859678",body);
    return Responder.success(res, { success: true, message: "" }) 
    reqPost.write(JSON.stringify(body));
    reqPost.end();
    reqPost.on('error', function (e) {
      console.error("line: 102 " + e);
      return Responder.success(res, { success: false, message: "Some Error Occured." })
    });

  }


  
  static checkRefferCodeExist(req, res) {
      User.count({ referal_code: req.body.referred_by_code })
      .then((count) => {
        console.log('ccccccccccccc ',count);
        if (count == 0) {
          Responder.success(res, {'count':0 })
        }else{
          Responder.success(res, {'count':1 } )
        }

      });

  }

  static checkOtpForgot(req, res) {
    console.log("!!!!!!!!!!")
    console.log(req.body)
    console.log("!!!!!!!!!!")
    if (!req.body.otp)
      return Responder.success(res, { message: "Please enter OTP." })
    console.log(req.body.otp);
    // const isValid = otplib.authenticator.check(req.body.otp, secret);
    // console.log(isValid);

      User.count({ phone_number: req.body.mobile, otp: req.body.otp })
      .then((isValid) =>{
        console.log('isValid--- ',isValid)
        
        if (isValid > 0) {
            User.findOneAndUpdate({ phone_number: req.body.mobile },{$set:{active_status: true,otp: 0}})
            .then((res)=>{
              
            });
            
            let userdetails = req.body;
            User.findOne({ phone_number: req.body.mobile })
              .then((user) => {
                Responder.success(res, user)

                    CommonSettings.find({type:'forgot_password'})
                       .then((res)=>
                       {
                        console.log('rrrrresponse')
                        console.log(res)
                        console.log('rrrrresponse')

                            var oldsubject=res[0].subject;
                            var userName=user.name;
                            var newSubject= oldsubject.replace("{name}",userName);

                            var oldBodySubject=res[0].emailBody;
                            var userName=user.name;
                            var newBodySubject= oldBodySubject.replace("{name}",userName);
                            newBodySubject= newBodySubject.replace("{name}",userName);


                             Settings.findOne({ type: 'app_settings' })
                              .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);


                                  var mailOptions = {
                                    from: 'Swari <'+app_settings.noReplyEmail+'>',
                                    to: user.email,
                                    subject:newSubject ,
                                    html: newBodySubject,
                                  };

                                  transporter.sendMail(mailOptions, function(error, info){
                                    if (error) {
                                      console.log(error);
                                    } else {
                                      console.log('Email sent: ' + info.response);
                                    }
                                  }); 

                                   })
                              .catch((err) => Responder.operationFailed(res, err))





                       })
                       .catch((err)=>Responder.operationFailed(res,err))

              })

          } else {
            return Responder.operationFailed(res, { success: false, message: "Invalid Otp or Otp is expired." });
          }
    }); 

  }

  static saveFcmToken(req, res) {   
    User.findOneAndUpdate({ _id: req.body.user_id }, { $set: { fcm_registration_token:req.body.fcm_registration_token,device_id:req.body.device_id,device_platform:req.body.device_platform,device_info:req.body.device_info} })
      .then((data) =>{ console.log("succcess1100"); return User.findOne({_id:data._id })})  
      .then((val) => { console.log("succcess110a"); console.log(val); Responder.success(res, val)} )
      .catch((err) => { console.log("succcess110f"); Responder.operationFailed(res, err)})
  }




  static checkOtp(req, res) {

    console.log(req.body)
    if (!req.body.otp) {
      return Responder.success(res, { message: "Please enter OTP." })
    }
    //console.log(req.body);
    // console.log('secret');
    // console.log(secret);
    // console.log('secret');
    // const isValid = otplib.authenticator.check(req.body.otp, secret);
    // console.log(isValid);

    


     User.count({ phone_number: req.body.mobile.phone_number, otp: parseInt(req.body.otp) })
      .then((isValid) =>{


        console.log('isValid--- ',isValid)
        

      // isValid = 0;
        if (isValid > 0) {
          let userdetails = req.body;

          User.count({ })
            .then((userCount) => {
              // userCount++
              var result           = '';
             var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
             var charactersLength = characters.length;
             for ( var i = 0; i < 4; i++ ) {
                result += characters.charAt(Math.floor(Math.random() * charactersLength));
             }


            // User.count({"referal_code": result + userCount})
            // .then((matchCount) => {
            //   if (matchCount > 0) {

            //   }else{
                
            //   }
            // });

           
              //anything here is executed after result is resolved
              // return result;

              function myPromise(){
                     return new Promise((resolve, reject) => {
                      
                        let rederCode = "";
                        var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                                console.log('000000000000  ');  

                        for (var i = 0; i < 6; i++){
                          rederCode += possible.charAt(Math.floor(Math.random() * possible.length));
                        }
                        
                         User
                    .count({'referal_code':rederCode}).exec()
                    .then(function(count) {

                      if(count>0){
                            console.log('000000000foundddd'  ,count + '  '+rederCode);  

                          myPromise();
                        }else{
                            console.log('1111111111111111222  ',rederCode);  

                          // return(result);
                        // resolve(rederCode);


                          // resolve(rederCode);

              

            console.log('1111111111111111115454  ',rederCode);

           // };

          req.body.mobile.referal_code = rederCode;
          console.log(req.body);

          userdetails.name = userdetails.mobile.name.toLowerCase();
          userdetails.email = userdetails.mobile.email.toLowerCase();

          if (!(userdetails.mobile.name && userdetails.mobile.password))
            return Responder.operationFailed(res, { message: "Please insert Mandatory fields." })
          
            User.findOne({ phone_number: userdetails.mobile.phone_number, active_status:true })
            .then((user) => {
              if (user) {
                return Responder.operationFailed(res, { message: "Mobile number is already exists. " })
              } else {
                console.log('uuuuuuuuuuuu---');
                console.log(userdetails.mobile);

                //  User.findOneAndUpdate({ phone_number: userdetails.mobile.phone_number },{$set:{active_status: true}}).
                //   then((result) =>{
                // console.log('uuuuuuuuuuuu11---',result);

                //   });

                  Settings.find({ })
                  .then((settings)=>{


                console.log('settings---',settings);

                //  var timeNow = new Date().getTime();

                // var currentDate = new Date(timeNow).toISOString().slice(0, 10);
                //  //oneDayInPast=oneDayInPast+1;
                //   //one month before
                //   console.log("my date",currentDate)
                //   var unixTimestamp = (Math.round(new Date(currentDate+ ' 00:00:10.000').getTime()/1000))*1000;
                  

                //   var TrailPeriod=unixTimestamp + 1000 * 60 * 60 * (admin[0].freetrialperiod * 24);
                //   console.log('TrailPeriod',TrailPeriod); 

                  // var freetrialperiod = new Date(unixTimestamp + 1000 * 60 * 60 * (admin[0].freetrialperiod * 24)).toISOString().slice(0, 10); //one month before

                   // var timeNow = new Date().getTime();
                // var ft =moment(new Date()).add(admin[0].freetrialperiod, 'days').toDate();
                // var freetrialperiod= moment().subtract(ft,'days').endOf('day').toString()

                // var freetrialperiod = moment(new Date(), "YYYY-MM-DD HH").format('YYYY-MMM-DD h:mm A');


                var freetrialperiod=moment(new Date()).add(settings[0].freetrialperiod,'days');
                freetrialperiod=moment(freetrialperiod).startOf('day').format('YYYY-MM-DDT00:00:01.000');

                console.log('freetrialperiod-------------------!!' , freetrialperiod)
                 
                // console.log('unixTimestamp -- - ',unixTimestamp);
                // console.log('freetrialperiod -- - ',freetrialperiod);

                // var currentDate = new Date(unixTimestamp).toISOString().slice(0, 10);
                    userdetails.mobile.created_at= moment(new Date()).toDate();
                    userdetails.mobile.is_subscribed= true;
                    userdetails.mobile.renewal_date= freetrialperiod;

                    userdetails.mobile.active_status= true;
                    userdetails.mobile.otp= 0;


                User.deleteOne({ phone_number: userdetails.mobile.phone_number })
                  .then((user) => {

                  });


                User.create(userdetails.mobile)
                 // User.findOneAndUpdate({ phone_number: userdetails.mobile.phone_number },{$set:{active_status: true,otp: 0}})
                 // User.findOneAndUpdate({ phone_number: userdetails.mobile.phone_number },{$set:userdetails.mobile})
                  .then((user) => {
                    console.log('upddddddddddddddddddd --- ',user);
                      User.aggregate([ 
                      {
                        $match: {
                                  _id: ObjectId(user._id) 
                                }
                            
                      }])
                      .then((userFound)=>
                     {
                      Responder.success(res, userFound);
                     });
                    
                    /////
                     

                     CommonSettings.find({type:'user_register'})
                     .then((res)=>
                     {
                      // console.log('rrrrresponse')
                      // console.log(res)
                      // console.log('rrrrresponse')

                          var oldsubject=res[0].subject;
                          var userName=user.name;

                          var newSubject= oldsubject.replace("{name}",userName);
                          newSubject= newSubject.replace("{name}",userName);

                          var oldBodySubject=res[0].emailBody;
                          var userName=user.name;
                         
                          var newBodySubject= oldBodySubject.replace("{name}",userName);
                          newBodySubject = newBodySubject.replace("{name}",userName);
                          newBodySubject = newBodySubject.replace("{name}",userName);
                          // newBodySubject = newBodySubject.replace


                          var mailOptions = {
                            from: 'Swari <<EMAIL>>',
                            to: userdetails.mobile.email,
                            subject:newSubject ,
                            html: newBodySubject,
                          };

                          transporter.sendMail(mailOptions, function(error, info){
                            if (error) {
                              console.log(error);
                            } else {
                              console.log('Email sent: ' + info.response);
                            }
                          }); 

                     })
                     .catch((err)=>Responder.operationFailed(res,err))
                  })
                  .catch((err) => Responder.operationFailed(res, err))
                  
                  });

              }
            })

                         }
                    });
                    });
              }

              myPromise();
            ///

            });


        } else {
          return Responder.operationFailed(res, { success: false, message: "Invalid OTP or OTP has expired." });
        }
    }) 

  }

   static registerUserByAdmin(req, res) {

      console.log(req.body)

      var location={ };
      location.coordinates = [74.8736788,31.6343083];

      req.body.location = location;

      // location:{
      //     coordinates : [],     
      //     type : {type:String, default:'Point'}, 
      // },


      function myPromise(){
     return new Promise((resolve, reject) => {
      
        let rederCode = "";
        var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                console.log('000000000000  ');  

        for (var i = 0; i < 6; i++){
          rederCode += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        
         User
    .count({'referal_code':rederCode}).exec()
    .then(function(count) {

      if(count>0){
            console.log('000000000foundddd'  ,count + '  '+rederCode);  

          myPromise();
        }else{
            console.log('1111111111111111222  ',rederCode);  

     
        let userdetails = req.body;
        req.body.referal_code = rederCode;
        console.log(req.body);
        userdetails.name = userdetails.name.toLowerCase();
        userdetails.email = userdetails.email.toLowerCase();
        if (!(userdetails.name && userdetails.password))
          return Responder.operationFailed(res, { message: "please insert mandatory fields." })
        User.findOne({ phone_number: userdetails.phone_number })
          .then((user) => {
            if (user) {
              return Responder.operationFailed(res, { message: "Mobile number is already exists. " })
            } else {
              console.log('uuuuuuuuuuuu---');
              console.log(userdetails);
              User.create(userdetails)
                .then((user) => {
                  Responder.success(res, user);
                  /////

                   CommonSettings.find({type:'user_register'})
                   .then((res)=>
                   {
                    console.log('rrrrresponse')
                    console.log(res)
                    console.log('rrrrresponse')

                        var oldsubject=res[0].subject;
                        var userName=user.name;
                        var newSubject= oldsubject.replace("{name}",userName);
                        newSubject= newSubject.replace("{name}",userName);

                        var oldBodySubject=res[0].emailBody;
                        var userName=user.name;
                        var newBodySubject= oldBodySubject.replace("{name}",userName);
                        newBodySubject= newBodySubject.replace("{name}",userName);
                        newBodySubject= newBodySubject.replace("{name}",userName);


                        var mailOptions = {
                          from: 'Swari <<EMAIL>>',
                          to: userdetails.mobile.email,
                          subject:newSubject ,
                          html: newBodySubject,
                        };

                        // transporter.sendMail(mailOptions, function(error, info){
                        //   if (error) {
                        //     console.log(error);
                        //   } else {
                        //     console.log('Email sent: ' + info.response);
                        //   }
                        // }); 

                   })
                   .catch((err)=>Responder.operationFailed(res,err))
                })
                .catch((err) => Responder.operationFailed(res, err))
            }
          })

        }

      })
    })
   }

   myPromise();



  }



  static getReview(req, res) {
    var users=[];
    console.log(req.params)
     User.findOne({_id:req.params.id})
     .then((user)=>{
      _.each(user.rating_review,rev=>{
        console.log('====length====',user.rating_review.length)
        getUserDataFunc(rev.ratedBy, function(user) {
          console.log("=====user====123===",user)
          rev.user_details = user;
          users.push(rev);
         Responder.success(res, users)})
          .catch((err) => Responder.operationFailed(res, err))
        });
       
      });



}

static checkReferCode(req, res) {
  var wb;
  console.log(req.params)
  console.log("refer",req.body)
  var tr = {};
  if(req.body.referred_by_code)
{
  User.findOne({ referal_code: req.body.referred_by_code })
  .then((usr) =>{ 
    wb = req.body.wallet_balance + usr.wallet_balance; console.log(wb);
       return User.find({ _id: usr._id })
       // return User.findOneAndUpdate({ _id: usr._id },{$set:{'wallet_balance':wb}})
      .then((data) =>{
            console.log("succcess1100")
      // tr.amount= req.body.wallet_balance;
      tr.amount= parseFloat(req.body.subscribeFees).toFixed(2);
      tr.transaction_reason= "Referral Commission";
      tr.reason= "Referral Commission for "+ req.body.referred_by_name;
      tr.due_to_user_name= req.body.due_to_user_name;
      tr.referred_by_name= req.body.referred_by_name;
      tr.due_to_user= req.body.due_to_user;
      tr.transaction_date = req.body.transaction_date;
      tr.transaction_type= "CR";
      // tr.total_amount= req.body.wallet_balance + req.body.wallet_balance;
      tr.transaction_id = Number(String(Math.random()).slice(2)) +Math.round( Date.now()).toString(36);
      //Referral Logic here for new users
      tr.subscribeFeePercentage =  req.body.subscribeFeePercentage;
      tr.user_id= usr._id;
      tr.referred_by_code= req.body.referred_by_code;
      console.log(tr);
      Transaction.create(tr)
       .then((trnc)=>{
        Responder.success(res, trnc)
        // Responder.success(res, usr)
        console.log("Transactionsuccess",trnc)
       }).catch((err) => {
           console.log("cancellled"); Responder.operationFailed(res, err)
         })
          })
      .catch((err) => {
           console.log("succcess110f"); Responder.operationFailed(res, err)
         })
    // return  User.findOneAndUpdate({ _id: usr._id },{$set:{'wallet_balance':wb}})
    // .then((usr1) =>{
    //   // tr.amount: req.body.wallet_balance;
    //   // tr.transaction_reason: "Referral Commission";
    //   // tr.transaction_type: "cash";
    //   // tr.user_id: usr._id;
    //   // Transaction.create(tr)
    //   //  .then((trnc)=>{
    //   //   Responder.success(res, usr)
    //   //  })
    //   });
    })

 .catch((err) => Responder.operationFailed(res, err))

} 

if(req.body.paytmMoney)
{
  User.findOne({ _id: req.body.id })
  .then((usr) =>{ wb = parseInt(req.body.paytmMoney) + usr.wallet_balance; console.log(wb); return  User.findOneAndUpdate({ _id: usr._id },{$set:{'wallet_balance':wb}})})
  .then((data) =>{ return User.findOne({_id:data._id })})
  .then((usrs) => Responder.success(res,usrs))
  .catch((err) => Responder.operationFailed(res, err))

} 

}

static checkAllUserReferCode(req, res) {

  console.log(req.params)
  User.aggregate([ 
              {
                $match: {
                          referred_by_code : req.params.id 
                        }
                    
              }])
  // User.find({ referred_by_code : req.params.id })
  .then((usr) =>{console.log(usr); Responder.success(res, usr)})
 .catch((err) => Responder.operationFailed(res, err))


}



static getSubscriptionFess(req, res) {
  console.log('req.params------con')
  console.log(req.params.id)

    User.findOne({ role: req.params.id })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
}



static getAdmin(req, res) {
  console.log('req.params------con')

    User.findOne({ role: 'admin' })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
}

static checkMobile(req,res){
  User.findOne({ phone_number: req.params.id, active_status:true })
      .then((user) =>{
          console.log('user---------',user);

        if (user) {
          Responder.success(res, user)
        } else {
          return Responder.success(res, { message: true })
        }
      })
      .catch((err) => Responder.operationFailed(res, err))
}

static checkEmail(req,res){
  User.findOne({ email: req.params.id, active_status:true  })
      .then((user) =>{
        if (user) {
          Responder.success(res, user)
        } else {
          return Responder.success(res, { message: true })
        }
      })
      .catch((err) => Responder.operationFailed(res, err))
}


static checkEmailForUser(req,res){
  User.findOne({ email: { $regex : "^" + req.body.email, $options: 'i'}, active_status:true , _id: {$ne:ObjectId(req.body.user_id)}  })
      .then((user) =>{
        if (user) {
          Responder.success(res, user)
        } else {
          return Responder.success(res, { message: true })
        }
      })
      .catch((err) => Responder.operationFailed(res, err))
}
  
static checkUserExist(req,res){
  console.log('iddddddddddddddddd');
  console.log(req.body._id);
  // User.findOne({ _id: req.body._id })
  //     .then((user) =>{
  //       if (user) {
  //         Responder.success(res, user)
  //       } else {
  //         return Responder.success(res, { message: false })
  //       }
  //     })
  //     .catch((err) => Responder.operationFailed(res, err))

      User.aggregate([ 
              {
                $match: {
                         _id: ObjectId(req.body._id)
                        }
                    
              },
               { $lookup:
                 {
                   from: 'vehicles',
                   localField: '_id',
                   foreignField: 'user_id',
                   as: 'vehicleList'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 

                    ])
         .then((user) =>{
            if (user) {
              var users =[];
              // users =user;
              // users.push({'currentDate':moment(new Date()).add(5.5,'hours').toDate()});
              // users.push({'user':user});

              // console.log('uuuuuuuser',users)
              Responder.success(res, user)
            } else {
              return Responder.success(res, { message: false })
            }
          })

}



static dashboardBackend(req,res){
  console.log('user_id---dashboardBackend ',req.params.id)
  var renewal_date=moment(new Date()).add(30, 'days');
  renewal_date=moment(renewal_date).startOf('day').format('YYYY-MM-DDT00:00:01.000[Z]');
  console.log('user_idrenewal_date ',renewal_date)

    // find user
        async.waterfall([
        function(callback){
          console.log('user_id dashboardBackend---1111')
          User.aggregate([ 
                {
                  $match: {
                            _id: ObjectId(req.params.id),
                            is_subscribed : false
                          }
                      
                },
                ])
          .then((user)=>{
            console.log('length--- -',user.length);
            if(user.length > 0){
            console.log('dashboardBackend user--- ',user)
            callback(null,user);

          }else{
            Responder.success(res, 'success')

          }
          })
          .catch((err)=>Responder.operationFailed(res,err))
        
        },
        function(user,callback){
          console.log('user_id---222',user)
          Settings.aggregate([ 
                {
                  $match: {
                            'type': 'app_settings'
                          }
                      
                },
                ])
          .then((app_settings)=>{
            // callback(null,app_settings,user);
            var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
            callback(null,user,subscribeFees,app_settings);

          })
          .catch((err)=>Responder.operationFailed(res,err))
          
        },
        function(user,subscribeFees,app_settings,callback){

          if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
          console.log('wwwwwwwwwww  a',user)
          console.log('wwwwwwwwwww  b',subscribeFees)
          console.log('wwwwwwwwwww  c',app_settings)

          var tr = {};
          tr.amount = parseFloat(subscribeFees).toFixed(2);
          tr.transaction_reason = "Subscription Fee";
          tr.reason = "Subscription Fee by server Deducted";
          tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
          tr.transaction_date = new Date();
          tr.transaction_type = "DR";
          tr.transaction_id = common.generateTransactionId();
          tr.user_id = user[0]._id;
          tr.total_amount = parseFloat(user[0].wallet_balance - tr.amount).toFixed(2);
         console.log('amounttttttttttttt  ###',tr)
         console.log('user.wallet_balance - tr.amount  ###',user[0].wallet_balance + 'dfddddd' + tr.amount)
          Transaction.create(tr)
              .then((trnc) => {
                console.log("updated 2",subscribeFees);

                createPdf(null,'subscribeFees',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);

          //     // send mail start
          //     CommonSettings.find({type:'transactionSubscriptionFees'})
          //  .then((res)=>
          //  {
          //   console.log('rrrrresponse')
          //   console.log(res)
          //   console.log('rrrrresponse')

          //       var oldsubject=res[0].subject;
          //       var userName= user[0].name;
          //       var newSubject= oldsubject.replace("{name}",userName);

          //       var oldBodySubject=res[0].emailBody;
          //       var userName=user[0].name;
          //       var newBodySubject= oldBodySubject.replace("{name}",userName);

          //        Settings.findOne({ type: 'app_settings' })
          //       .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

          //           var mailOptions = {
          //             from: app_settings.billingEmail,
          //             to: user[0].email,
          //             subject:newSubject ,
          //             html: newBodySubject,
          //           };

          //           transporter.sendMail(mailOptions, function(error, info){
          //             if (error) {
          //               console.log(error);
          //             } else {
          //               console.log('Email sent: ' + info.response);
          //                 // send message start
          //                var extServerOptionsPost = {
          //                     hostname: "***************",
          //                     path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
          //                     method: 'POST',
          //                     port: '80',
          //                     headers: {
          //                       'Content-Type': 'application/json'
          //                     }
          //                   }

          //                   const token = otplib.authenticator.generate(secret);
          //                   console.log(token);

          //                   var reqPost = http.request(extServerOptionsPost, function (response) {

          //                     response.on('data', function (data) {
          //                       console.log("line no: 87 ", data);
          //                     });
          //                   });
          //                   var body =
          //                     {
          //                       "smsContent": 'RS '+ subscribeFees +' Subscription Fee deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' with TR Id '+ req.body.transaction_id +'. Valid for 30 days. A/c Bal:RS '+ req.body.balance +'.00. Thanks www.swari.in',
          //                       "routeId": "1",
          //                       "mobileNumbers": user[0].phone_number,
          //                       // "mobileNumbers": '9855621130,7009232617',
          //                       "senderId": "SWARII",
          //                       "signature": "signature",
          //                       "smsContentType": "english"
          //                     }
          //                   reqPost.write(JSON.stringify(body));
          //                   reqPost.end();
          //                   reqPost.on('error', function (e) {
          //                     console.error("line: 102 " + e);
          //                   });
          //               // send message end
          //             }
          //           }); 


          //       })
          //       .catch((err) => {})
  

          //  })
          // .then((trc)=>{})
          //  .catch((err)=>{}) 
          //     // send mail end

                 callback(null,user,app_settings,tr.total_amount,trnc);
                 // callback(null,user,trnc,subscribeFees,app_settings);
              })
              .catch((err) => {
                  console.log("cancellled999999",err);
                  Responder.operationFailed(res,err);

              });
          }else{

              Responder.success(res, 'not_enough_balance')
          }
        },
        function(user,app_settings,subscribeFees,trnc,callback){
          console.log('wallet_balance---',user)
          console.log('renewal_date---',renewal_date)

          // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
          // if (!user[0].is_subscribed && user[0].wallet_balance >= subscribeFees) {
            console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
            User.findOneAndUpdate(
            {
                _id: user[0]._id
            }, 
            {
                $set: {
                    'wallet_balance': subscribeFees,
                    'renewal_date':renewal_date,
                    'is_subscribed': true,
                    'lastSubscribed': moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")

                }
            })
            .then((wallet_updated)=>{
              // Responder.success(res, wallet_updated)
              // callback(null,wallet_updated,subscribeFees,app_settings);
                 callback(null,user[0],app_settings);

            })
          // }else{
              // Responder.success(res, 'not_enough_balance')

          // }

        },

         // function(user,trnc,amount,app_settings,callback){
         //  console.log('ttttttttt11 ',moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
         //  console.log('ttttttttt22 ',moment.utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
         //  console.log('ttttttttt33 ',moment().utc().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"))
         //  // exit();

         //              console.log("updated 3 called",parseFloat(user[0].wallet_balance - amount).toFixed(2));
         //              User.update({
         //                  _id: user._id
         //              }, {
         //                  $set: {
         //                      wallet_balance: parseFloat(user.wallet_balance - amount).toFixed(2),
         //                      lastSubscribed: moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")
         //                  }
         //              }).
         //              then((trnc) => {
         //                console.log("updated 3");
         //                callback(null,user,app_settings);
         //              }).catch((err) => {
         //                console.log(err);
         //                  console.log("cancellled");
         //              });
         //            },
                    function(user,app_settings,callback){
                      console.log('referal_code11',user);
                      console.log('referal_code33',app_settings);
                      User.find({
                          referal_code: user.referred_by_code
                      }).
                      then((referred_by) => {
                        console.log('referred_by-------- ',referred_by);
                        callback(null,user,referred_by,app_settings);
                      });
                    },
                    function(user,referred_by,app_settings,callback){
                      Transaction.count({
                          due_to_user: ObjectId(user._id),
                          transaction_reason: 'Referral Commission'
                      }).then((referred_by_count) => {
                          console.log('referal_code44',referred_by_count);
                          callback(null,user,referred_by,referred_by_count,app_settings);
                      });
                    },
                    function(user,referred_by,referred_by_count,app_settings,callback){
                      console.log('referred_by_count  --- ',referred_by_count); 
                      // console.log(referred_by[0].wallet_balance + "internal total Balance check");
                      console.log('referred_by_user',referred_by)
                      console.log('app_settings----',app_settings)
                      if(referred_by.length!=0){
                        var tr = {};
                        //"Referral Commission
                        if (referred_by_count == 0) {
                            var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 30;
                        } else {
                            var subscribeFees = (app_settings[0].subscriptionFeesByAdmin / 100) * 8;
                        }
                        // console.log('Referral Commission11',referred_by);
                        // console.log('Referral Commission12',wb);
                         console.log('Referral Commission13',subscribeFees);

                        tr.amount = parseFloat(subscribeFees).toFixed(2);
                        tr.transaction_reason = "Referral Commission";
                        tr.reason = "Refe56rral Commission for " + referred_by[0].name;
                        
                        tr.due_to_user_name = user.name;
                        tr.referred_by_name = referred_by[0].name;
                        tr.due_to_user = user._id;
                        tr.transaction_date = new Date();
                        tr.transaction_type = "CR";
                        var totalwb = parseFloat(referred_by[0].wallet_balance + subscribeFees).toFixed(2);
                        console.log('walet balance  -563-- ', totalwb);
                        tr.total_amount = parseFloat(totalwb).toFixed(2);
                        tr.transaction_id = common.generateTransactionId();
                        if (referred_by_count == 0) {
                            tr.subscribeFeePercentage = 30;
                        } else {
                            tr.subscribeFeePercentage = 8;
                        }
                        tr.user_id = referred_by[0]._id;
                        tr.referred_by_code = referred_by[0].referal_code;
                        // console.log('Referral Commission1---------2',wb);




                        Transaction.create(tr)
                            .then((trnc) => {
                                // console.log("success",trnc)

                                callback(null,referred_by,parseFloat(subscribeFees).toFixed(2),totalwb,user)
                            }).catch((err) => {
                                console.log("err_referral_commission  ", err);
                            })
                      }else{
                        callback(null,referred_by,null,null,user);
                      }

                    },
                    function(referred_by,subscribeFees,totalwb,user,callback){
                      console.log("846------   "+totalwb);
                      if(referred_by.length!=0){
                        User.update({
                            _id: referred_by[0]._id
                        }, {
                            $set:{
                              wallet_balance: totalwb
                            }
                            // $inc: {
                            //     wallet_balance: subscribeFees
                            // }
                        }).
                        then((trnc) => {
                            callback(null);
                            console.log("success852",trnc)
                            User.aggregate([ 
                                  {
                                    $match: {
                                              _id: ObjectId(user._id)
                                            }
                                        
                                  },
                                  ])
                            .then((user)=>{
                              Responder.success(res, user)
                            })

                        }).catch((err) => {
                            console.log("cancellled");
                        })
                      }else{
                        callback(null);
                      }
                    }

        
        
    ], function (err, result) {
        // result now equals 'done'
        console.log('done')
        Responder.success(res, 'success')

        // userCallback();
    });
  

}



// boost trip process

static boostTripBackend(req,res){

   User.aggregate( [
      {
        $match: {
          _id: ObjectId(req.body.id)
        } 
      }
      ] )
      .then((user)=>{
        console.log('userfound??? ',user)
        if (user[0].is_subscribed) {
          // user is subscribed


        console.log('user_id---boost trip ',req.body.id)
        console.log('user_id---boost trip ',req.body.trip_id)
        
              async.waterfall([
              function(callback){
                console.log('user_id ---1111')
                User.aggregate([ 
                      {
                        $match: {
                                  _id: ObjectId(req.body.id),
                                  
                                }
                            
                      },
                      ])
                .then((user)=>{
                  console.log('length--- -',user.length);
                  if(user.length > 0){
                  console.log('boostbackend user--- ',user)
                  callback(null,user);

                }else{
                  Responder.success(res, 'success')

                }
                })
                .catch((err)=>Responder.operationFailed(res,err))
              
              },
              function(user,callback){
                console.log('user_id---222',user)
                Settings.aggregate([ 
                      {
                        $match: {
                                  'type': 'app_settings'
                                }
                            
                      },
                      ])
                .then((app_settings)=>{
                  console.log('app_settings>>>>>>>>',app_settings)
                  // callback(null,app_settings,user);

                  var boostFees= app_settings[0].tripBoostFeesByAdmin  + app_settings[0].boostTripGstAmount;
                  callback(null,user,boostFees,app_settings);

                }) 
                .catch((err)=>Responder.operationFailed(res,err))
                
              },
             function(user,boostFees,app_settings,callback){
                          console.log('boostFees',boostFees);

                          
                         Trip.findOne({_id:req.body.trip_id,
                                      'is_trip_boost':false
                                    }
                          )
                        .then((trip) =>{ 
                          console.log(trip); 
                          if(trip._id && (user[0].wallet_balance >= boostFees))
                          {  

                            console.log('callback11')
                           callback(null,user,boostFees,app_settings,trip);
                            console.log('callback22')

                         }
                         else{
                          console.log("trip is not exist or already boosted");
                          Responder.operationFailed(res,'trip is not exist or check your wallet balance')
                         }
                        })
                        .catch((err) => Responder.operationFailed(res, err))
        
          },

              function(user,boostFees,app_settings,trip,callback){
                console.log('trip details?>>',trip)
                if (user[0].wallet_balance >= boostFees) {
                console.log('wwwwwwwwwww  a',user)
                console.log('wwwwwwwwwww  b',boostFees)
                console.log('wwwwwwwwwww  c',app_settings)

                var tr = {};
                tr.amount = parseFloat(boostFees).toFixed(2);
                tr.transaction_reason = "Boost Fee";
                tr.reason = "Boost Fee";
                tr.gstAmount = parseFloat(app_settings[0].boostTripGstAmount).toFixed(2);
                tr.transaction_date = new Date();
                tr.transaction_type = "DR";
                tr.origin = trip.origin;
                tr.car_reg_no = trip.reg_no;
                tr.transaction_id = common.generateTransactionId();
                tr.user_id = user[0]._id;
                tr.total_amount = parseFloat(user[0].wallet_balance - tr.amount).toFixed(2);
               console.log('amounttttttttttttt  ###',tr)
               console.log('user.wallet_balance - tr.amount  ###',user[0].wallet_balance + 'dfddddd' + tr.amount)
                Transaction.create(tr)
                    .then((trnc) => {
                      console.log("updated 2",boostFees);
                      createPdf(tr.car_reg_no,'boostTrip',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, trip.origin, trip.destination , user[0]);

                       callback(null,user,app_settings,tr.total_amount);
                       // callback(null,user,trnc,subscribeFees,app_settings);
                    })
                    .catch((err) => {
                        console.log("cancellled999999",err);
                        Responder.operationFailed(res,err);

                    });
                }else{

                    Responder.success(res, 'not_enough_balance')
                }
              },
              function(user,app_settings,wallet_balance,callback){
                console.log('wallet_balance---',user)
                // console.log('renewal_date---',renewal_date)

                  // console.log('wwwwww ',user[0].wallet_balance +'ffff '+ boostFees)
                  User.findOneAndUpdate(
                  {
                      _id: user[0]._id
                  }, 
                  {
                      $set: {
                           'wallet_balance': wallet_balance,
                          // 'renewal_date':renewal_date,
                          // 'is_subscribed': true,
                          // 'lastSubscribed': moment().format("YYYY-MM-DDTHH:mm:ss.SSS[Z]")
                          //  'status': 'ACTIVE',
                           // 'trip_type': 'trip',

                      }
                  })
                  .then((wallet_updated)=>{
                    console.log('wallet_updated >> ',wallet_updated)
                    // Responder.success(res, wallet_updated)
                    // callback(null,wallet_updated,subscribeFees,app_settings);
                       callback(null,user,wallet_balance);

                  })
                // }else{
                    // Responder.success(res, 'not_enough_balance')

                // }

              },
              function(user,wallet_balance,callback){
                console.log('trip is_trip_boost---',req.body.trip_id);
                
                  Trip.findOneAndUpdate(
                  {
                      _id: req.body.trip_id
                  }, 
                  {
                      $set: {
                           'is_trip_boost': true,
                
                      }
                  })
                  .then((trip_update)=>{
                    console.log('trip_update >> ',trip_update)

                     Responder.success(res, trip_update)
                    
                  })
                
              },


              
              
          ], function (err, result) {
              // result now equals 'done'
              console.log('done')
              Responder.success(res, 'success')

              // userCallback();
          });
  
    }else{
        // user is not subscribed
        Responder.operationFailed(res,'user is not subscribed')
      }
    })
    .catch((err)=>Responder.operationFailed(res,err))

}







static backendRazorpay(req,res){
  console.log('backendRazorpay11 ')
  console.log('backendRazorpay22 ',req.body)
    // find user
      req.body.amount=  parseInt(req.body.amount);
        console.log('body backendRazorpay  ---',req.body)
        async.waterfall([
        function(callback){
          console.log('user_id---1111')
          User.aggregate([ 
                {
                  $match: {
                            _id: ObjectId(req.body.user_id),
                            // is_subscribed : false
                          }
                      
                },
                ])
          .then((user)=>{
            console.log('test user ',user)
            callback(null,user);
          })
          .catch((err)=>Responder.operationFailed(res,err))
        
        },
        function(user,callback){
          console.log('user_id---222',user)
          Settings.aggregate([ 
                {
                  $match: {
                            'type': 'app_settings'
                          }
                      
                },
                ])
          .then((app_settings)=>{
            callback(null,app_settings,user);
          })
          .catch((err)=>Responder.operationFailed(res,err))
          
        },
        function(app_settings,user,callback){
          console.log('Razorpay transaction')
          console.log('app_settings  ',app_settings)
           console.log("updated 0 called ",user);
          //deduct Subscription Fee by server
            var tr = {};
            tr.amount = parseFloat(req.body.amount).toFixed(2);
            tr.transaction_reason = "Funds Added";
            tr.reason = "Subscription Fee by server Deducted";
            // tr.gstAmount = parseFloat(app_settings[0].gstAmount).toFixed(2);
            tr.transaction_date = moment.tz('Asia/Kolkata').format('YYYY-MM-DDTHH:mm:ss.SSS');
            tr.transaction_type = "CR";
            tr.transaction_id = req.body.transaction_id;
            tr.user_id = req.body.user_id;
            tr.total_amount = parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2);


            Transaction.create(tr)
                .then((trnc) => {
                  console.log("updated tr 2",trnc);
                  // createPdf(null,'Razorpay',tr.transaction_id, tr.amount, tr.total_amount, tr.transaction_date, tr.gstAmount, null, null , user[0]);
                  callback(null,app_settings,user);
                }).catch((err) => {
                    console.log("cancellled-- ",err);
                    Responder.operationFailed(res,err);

                });
        
        },
        
        function(app_settings,user,callback){
          console.log('wallet_balance---',user)
          // var subscribeFees= app_settings[0].subscriptionFeesByAdmin + app_settings[0].gstAmount;
            // console.log('wwwwww ',user[0].wallet_balance +'ffff '+ subscribeFees)
            User.findOneAndUpdate(
            {
                _id: user[0]._id
            }, 
            {
                $set: {
                    'wallet_balance': parseFloat(user[0].wallet_balance + req.body.amount).toFixed(2),
                    // 'renewal_date':new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
                    // 'is_subscribed': true
                }
            })
            .then((wallet_updated)=>{
              // Responder.success(res, wallet_updated)
              callback(null,wallet_updated,app_settings);
            })


        },
     
        
    ], function (err, result) {
        // result now equals 'done'
        console.log('done')
        Responder.success(res, 'success')

        // userCallback();
    });
  

}


static currentDate(req,res){  
  var currentDate =[];
  currentDate.push({'currentDate': moment(new Date()).add(5.5,'hours').toDate() });
  Responder.success(res, currentDate)
}


static walletCheck(req,res){
  console.log("walletCheck-------",req.body);
  console.log(req.body);
  if (req.body.wallet_balance >= 500) {
    return Responder.success(res, { message: true })

  }
  else{
    return Responder.success(res, { message: false })

  }
  // User.findOne({ email: req.params.id })
  //     .then((user) =>{
  //       if (user) {
  //         Responder.success(res, user)
  //       } else {
  //         return Responder.success(res, { message: true })
  //       }
  //     })
  //     .catch((err) => Responder.operationFailed(res, err))
}


static getUsersByReferCode(req, res) {
  console.log('req.reeeeeeeeeee')
  console.log(req.user.referal_code)

    User.find({ referred_by_code: req.user.referal_code })
      .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
}


  static getUserById(req, res) {
    console.log('_idddddd'+req.params)
    User.findOne({ _id: req.params.id })
      .then((user) => Responder.success(res, user))
      .catch((err) => Responder.operationFailed(res, err))

  }

  static getUsersForSelectedDate(req, res) {
    console.log(req.body.startDate)
    
     let query = User.find({$or:[{
      "created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }
      // $or:[{created_at: { $gte: req.body.startDate } },{created_at: { $lte: req.body.endDate } }]
    }]}).sort({ 'created_at': -1 });

    let promise = query.exec();

        promise.then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))


    // User.find({$and:[{created_at: { $gte: req.body.startDate } },{created_at: { $lte: req.body.endDate } }]
    // })
    // .then((user) => Responder.success(res, user))
    // .catch((err) => Responder.operationFailed(res, err))

  }



  static sendPushNotification(req, res) {        
     const data = {
      title: req.body.title, // REQUIRED for Android
      topic: req.body.title, // REQUIRED for iOS (apn and gcm)           
      custom: {
          type: 'chat',
          sender: 'Triva',
      },          
      priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn         
      sound: 'jb',
      android_channel_id: 'test_channel', // Custom sound file without extension       
    };

    sendNotification(req.body.id_list,data);        
  }



    static sendPushNotificationToState(req, res) {
    console.log({body: req.body})
    console.log(req.body)
    var id_list=[];
    var title;

        console.log('id_list')
        console.log(id_list)

     const data = {
        title: req.body.title, // REQUIRED for Android
        topic: req.body.title, // REQUIRED for iOS (apn and gcm)            
        custom: {
            type: 'chat',
            sender: 'Triva',
        },
      
        priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn       
        sound: 'jb', 
        android_channel_id: 'test_channel',// Custom sound file without extension
      };
           
      sendNotification(req.body.id_list,data);
  }


  static sendPushNotificationToCity(req, res) {
    console.log('jjj'+req.body)
    console.log(req.body)
 

     const data = {
        title: req.body.title, // REQUIRED for Android
        topic: 'req.body.title', // REQUIRED for iOS (apn and gcm)           
        custom: {
            type: 'chat',
            sender: 'Triva',
        },       
        priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn
        sound: 'jb', 
        android_channel_id: 'test_channel',// Custom sound file without extension
      };
           
      sendNotification(req.body.id_list,data);

        // User.find({phone_number:'9855621130'},function(err,docs1){
          // console.log('docs1'+docs1)
          // if(docs1[0].fcm_registration_token!=""){
            // You can use it in node callback style
            /*push.send(req.body.id_list, data, (err, result) => {
                if (err) {
                    console.log(err[0].message);
                } else {
                    console.log(result[0].message);
                    Responder.success(res, 'success')

                }
            });
          // }
          console.log("%%%%%%%%%%%%%");
        // });
        */


  }


  static sendEmailToState(req, res) {
    Settings.findOne({ type: 'app_settings' })
    .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

    console.log('sendEmailToState'+req.body)
    console.log(req.body)
    var id_list=[];
    var title;
     req.body.email_list.forEach(function (user, k) {                
        if (user.title) {
          console.log('user'+user)
          title=user.title;
          
        } 
        else{
          id_list.push(user.id_list);
          console.log('ddddddddddddd')
        }
         
      });
        console.log('id_list')
        console.log(id_list)

          var helpers = require("./helpers/mailFormatForUser");
          var obj = {} // empty Object
          var key = 'data';
          var mailSender= app_settings.billingEmail;
          obj[key] = [];
          obj[key].push({'email':req.body.email_list});
          obj[key].push({'name':req.body.title});
          obj[key].push({'emailBody':req.body.emailBody});
          obj[key].push({'mailSender':mailSender});

          helpers.sendMail(obj);
          Responder.success(res, 'success')

          // }
          console.log("%%%%%%%%%%%%%");
        // });
      });

  }



  static sendEmailToSelectedUsers(req, res) {
    console.log('sendEmailToSelectedUsers'+req.body)
    console.log(req.body)

    Settings.findOne({ type: 'app_settings' })
    .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);


    
    var helpers = require("./helpers/mailFormatForUser");
    var obj = {} // empty Object
    var key = 'data';
    var mailSender= app_settings.billingEmail;
    obj[key] = [];
    obj[key].push({'email':req.body.email_list});
    obj[key].push({'name':req.body.title});
    obj[key].push({'emailBody':req.body.emailBody});
    obj[key].push({'mailSender':mailSender});

    console.log('obj')
    helpers.sendMail(obj);
    Responder.success(res, 'success')
        
  })

  }



  static sendMessageToState(req, res) {
    console.log('sendMessageToState'+req.body)
    console.log(req.body)
    var id_list=[];
    var title;
    var phone_number_list;
     req.body.forEach(function (user, k) {                
        if (user.title) {
          console.log('user'+user)
          title=user.title;
          
        }
        else{
          id_list.push(user.id_list);
          console.log('ddddddddddddd')
        }
         
      });
        console.log('id_list')
        console.log(id_list)

      id_list.forEach(function (user, k) {                
       
          console.log('aa'+user)
          phone_number_list= phone_number_list + ',' +user;
        
         
      });


         if (!id_list)
      return Responder.operationFailed(res, { success: false, message: "Mobile number is mandatory." })
    console.log("hit")
    var extServerOptionsPost = {
      hostname: "***************",
      path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
      method: 'POST',
      port: '80',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const token = otplib.authenticator.generate(secret);
    console.log(token);

    var reqPost = http.request(extServerOptionsPost, function (response) {

      response.on('data', function (data) {
        console.log("line no: 87 ", data);
        return Responder.success(res, { success: true, message: token })
      });
    });
    var body =
      {
        "smsContent": title+ '.Thanks. www.swari.in',
        "routeId": "1",
        "mobileNumbers": phone_number_list,
        "senderId": "SWARII",
        "signature": "signature",
        "smsContentType": "english"
      }
    reqPost.write(JSON.stringify(body));
    reqPost.end();
    reqPost.on('error', function (e) {
      console.error("line: 102 " + e);
      return Responder.success(res, { success: false, message: "Some Error Occured." })
    });

        


  }


  
  static filterUsers(req, res) {
    console.log('filterUsers'+req.body.phone_number);

    // User.find({$and: [{ 'name' : { $regex: /sad/i } }]})
    // 'name' : new RegExp(req.body.userName, 'm')
    // User.find({$and: [{ buisness_name : new RegExp(req.body.buisness_name, 'm')},{ name : new RegExp(req.body.name, 'm')},{ email : new RegExp(req.body.email, 'm')},{ district : new RegExp(req.body.district, 'm')},{ phone_number : new RegExp(req.body.phone_number, 'm')}]}) 
    // User.find({$and: [ {'role': {$ne: 'admin'}} ,{$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}},{ name : {$regex : "^" + req.body.userName,$options: 'i'}},{ email : {$regex : "^" + req.body.email,$options: 'i'}},{  district: {$regex : "^" + req.body.district,$options: 'i'}},{  phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},{  state: {$regex : "^" + req.body.state,$options: 'i'}}]}]}) 
    //   .then((user) =>{ console.log(user); Responder.success(res, user)})
    //   .catch((err) => Responder.operationFailed(res, err))
      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      if(!size) {
        size = 10;
      }
      console.log('size--'+size)

      
    let userCondition = {};

    if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

           userCondition = {
              $and: [ {'role': {$ne: 'admin'}},
                      {role:"normalUser"},
                      {active_status:true},

                      // {suspend:false},
                  {
                    // $or:[
                          // {
                            $and:[
                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                  // {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                            ]  
                          // },
                      // ]
                  }
                  ]
            }                        
        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

         userCondition = { 
              $and: [ {'role': {$ne: 'admin'}},
                      {role:"normalUser"},
                      {active_status:true},

                      // {suspend:false},
                  {
                    // $or:[
                          // {
                            $and:[
                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                {
                                  $or:[
                                        
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                        
                                    ]
                                } 
                            ],


                          // },
                      // ]
                  }
                  ]
            }                                        
        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

         userCondition = { 
                $and: [ {'role': {$ne: 'admin'}},
                        {role:"normalUser"},
                        {active_status:true},

                        // {suspend:false},
                    {
                        $and:[
                              { state: {$regex : "^" + req.body.state,$options: 'i'}},
                              { district: {$regex : "^" + req.body.district,$options: 'i'}},
                        ]

                    }
                    ]
              }                                          
        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

         userCondition = { 
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }                                         
        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

         userCondition = { 
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }                                       

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

         userCondition = { 
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                                      
        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

         userCondition = { 
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }                
        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

         userCondition = { 
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
              }                                         

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
         userCondition = {                              
                  $and: [ {'role': {$ne: 'admin'}},
                        {role:"normalUser"},
                        {active_status:true},

                        // {suspend:false},
                          

                  {
                    $or:[
                          
                          
                        { buisness_name : 
                          { $regex : "^" + req.body.buisness_name, $options: 'i'}
                        },
                        { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                        { email : {$regex : "^" + req.body.email,$options: 'i'}},
                        { district: {$regex : "^" + req.body.district,$options: 'i'}},
                        { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                        { state: {$regex : "^" + req.body.state,$options: 'i'}},
                        
                          
                      ]
                  }
                  ]
                
              }
                        
        }   
                                 

      if(req.body.checkDocs) {
      
        userCondition = { 
             documents: { $exists: true },             
             $expr: { $gt: [{ $size: "$documents" }, 1] }
        }
      }

          console.log("Filter condition ",userCondition);

        User.aggregate([ 
          {
            $match: userCondition
                
          },
          { $lookup:
             {
               from: 'vehicles',
               localField: 'vehical_id',
               foreignField: '_id',
               as: 'vehicleDetails'
             }
           },
           { $lookup:
             {
               from: 'vehicles',
               localField: '_id',
               foreignField: 'user_id',
               as: 'vehicleList'
             }
           },
           { 
            "$sort": { 
                "created_at": -1,
            } 
        }, 

        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))       
  }


  static filterReferralUsers(req, res) {
    console.log('filterReferralUsers'+req.body.phone_number);

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)



    if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                  // {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                {
                                                  $or:[
                                                       
                                                        
                                                      { buisness_name : 
                                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                                      },
                                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                      
                                                       
                                                    ]
                                                } 
                                            ],


                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]

                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
           User.aggregate([ 
                  {
                    $match: {
                               $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                        

                                {
                                  $or:[
                                       
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                       
                                    ]
                                }
                                ]
                              
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))



        }



    //    User.aggregate([ 
    //           {
    //             $match: {
    //                       $and: [ {'role': {$ne: 'admin'}},
    //                               {'role': 'referalUser'},
    //                       {
    //                         $or: [
    //                               { buisness_name : 
    //                                 { $regex : "^" + req.body.buisness_name, $options: 'i'}
    //                               },
    //                               { name : {$regex : "^" + req.body.userName,$options: 'i'}},
    //                               { email : {$regex : "^" + req.body.email,$options: 'i'}},
    //                               { district: {$regex : "^" + req.body.district,$options: 'i'}},
    //                               { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
    //                               { state: {$regex : "^" + req.body.state,$options: 'i'}}
    //                               ]
    //                       }
    //                       ]
                          
    //                     }
                    
    //           },
    //           { $lookup:
    //              {
    //                from: 'vehicles',
    //                localField: 'vehical_id',
    //                foreignField: '_id',
    //                as: 'vehicleDetails'
    //              }
    //            },
    //            { $lookup:
    //              {
    //                from: 'vehicles',
    //                localField: '_id',
    //                foreignField: 'user_id',
    //                as: 'vehicleList'
    //              }
    //            },
    //            { 
    //             "$sort": { 
    //                 "created_at": -1,
    //             } 
    //         }, 

    //                 ]).skip(size * (pageNo - 1)).limit(size)
    // .then((trc)=>Responder.success(res,trc))
    // .catch((err)=>Responder.operationFailed(res,err))

  }


  

  static filterSubAdminUsers(req, res) {
    console.log('filterSubAdminUsers'+req.body.phone_number);

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                  // {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                {
                                                  $or:[
                                                       
                                                        
                                                      { buisness_name : 
                                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                                      },
                                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                      
                                                       
                                                    ]
                                                } 
                                            ],


                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]

                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
           User.aggregate([ 
                  {
                    $match: {
                               $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                        

                                {
                                  $or:[
                                       
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                       
                                    ]
                                }
                                ]
                              
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))



        }



  }
  
  static getStateUsersFcmToken(req, res) {
    console.log('ssssssssssssss'+req.body.state);
    User.find({ "role":"normalUser" ,state: req.body.state })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  }

  static getUsersByStateForEmail(req, res) {
    console.log('getUsersByStateForEmail'+req.body.state);
    User.find({ "role":"normalUser" ,state: {$regex : "^" + req.body.state,$options: 'i'} })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  }


  static getUsersByStateForMessage(req, res) {
    console.log('getUsersByStateForMessage'+req.body.state);
    User.find({ state: {$regex : "^" + req.body.state,$options: 'i'} })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  }


  static getUsersByCityForEmail(req, res) {
    console.log('getUsersByCityForEmail'+req.body.city);

     User.aggregate([ 
              {
                    $match: {
                        "role":"normalUser",
                        district: {$regex : "^" + req.body.city,$options: 'i'},
                        active_status:true

                      }
                    
              }
              


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
   
  }

    // User.find({district: {$regex : "^" + req.body.city,$options: 'i'}})
    //   .then((user) =>{ console.log(user); Responder.success(res, user)})
    //   .catch((err) => Responder.operationFailed(res, err))
  // }

  static getUsersByCityForNotification(req, res) {
    console.log('getUsersByCityForNotification'+req.body.city);
    User.find({ "role":"normalUser" ,district: req.body.city })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  }


  static getAllUsersOnce(req, res) {
    // console.log('getAllUsersOnce'+req.body.state);
    User.find({ "role":"normalUser",active_status:true })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  }

  static getCityList(req, res) {

    console.log('getCityList',req.body.city);
    User.find({  }).distinct('district', {district: {$regex : "^" + req.body.city,$options: 'i'}})
      .then((user) =>{ console.log('user',user);
          // user.distinct('district')
          // .then((user) =>{ console.log(user); Responder.success(res, user)})
          // .catch((err) => Responder.operationFailed(res, err))
       Responder.success(res, user)
     })
      .catch((err) => Responder.operationFailed(res, err))
  } 

  static getReferuser(req, res) {
    console.log('getReferuser'+req.body.referred_by_code);
    console.log('getReferuser'+req.body.referred_by_code);
    User.find({ referal_code:req.body.referred_by_code })
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
  } 

            


static getToken(req,res){
  console.log("getToken-------",req.body.phone_number);
     if (!req.body.phone_number.phone_number || !req.body.phone_number.password) {
          return res.status(400).send({ 'msg': 'You need to send email and password' });
        }

       User.findOne({ phone_number: req.body.phone_number.phone_number }, (err, user) => {
          if (err) {
              return res.status(400).send({ 'msg': err });
          }
   
          if (!user) {
              return res.status(400).json({ 'msg': 'The user does not exist' });
          }
   
          user.comparePassword(req.body.phone_number.password, (err, isMatch) => {
              if (isMatch && !err) {

                let token = createToken(user)
                 myCache.set(user._id.toString(), token);

                  return res.status(200).json({
                      token,
                      user_details:user
                  });
              } else {
                  return res.status(400).json({ msg: 'Mobile Number and Password mismatch, Please Retry.' });
              }
          });
      });

}









  static registerUser(req,res){
     if (!req.body.phone_number || !req.body.password) {
        return res.status(400).json({ 'msg': 'You need to send email and password' });
    }

 
      var location={ };
      location.coordinates = [74.8736788,31.6343083];

      req.body.location = location;
    
      console.log("  ========   ", req.body);

      console.log(req.body);

    User.findOne({ phone_number: req.body.phone_number, active_status: true  }, (err, user) => {
          if (err) {
              return res.status(400).json({ 'msg': err });
          }
   
          if (user) {
              return res.status(400).json({ 'msg': 'The user already exists' });
          }
   
          User.findOne({ phone_number: req.body.phone_number, active_status: false  }, (err, user1) => {
             if(user1) {
              console.log("Already exists user ",user1);
              return res.status(201).json(user1);
             } else {
              let newUser = User(req.body);
              newUser.save((err, user) => {
                  if (err) {
                      return res.status(400).json({ 'msg': err });
                  }
                  console.log("user ", user);
                  return res.status(201).json(user);
              });
             }
          });
          
      });
  }


    static loginUser(req,res){
      // console.log(req.body);
    
      console.log(myCache.getStats());
        if (!req.body.phone_number || !req.body.password) {
          return res.status(400).send({ 'msg': 'You need to send email and password' });
        }

       User.findOne({ phone_number: req.body.phone_number }, (err, user) => {

        console.log("userid check",err,user);
          if (err) {
              return res.status(400).send({ 'msg': err });
          }
   
          if (!user) {
              return res.status(400).json({ 'msg': 'The user does not exist' });
          }

          if (user.suspend) {
            return res.status(400).json({ 'msg': 'Your account has been suspended. Contact support' });
        }
   
          user.comparePassword(req.body.password, (err, isMatch) => {
              if (isMatch && !err) {
                // if (user.role == 'referalUser') {
                //   return res.status(200).json({ user_details:'referalUser' }); 
                // }else{
                  var userToken =  createToken(user);
                 
                  // console.log(" =type of  ", typeof user._id.toString())
                  // console.log("]",myCache.get(user._id.toString()),"[");

                  myCache.set(user._id.toString(), userToken);

                  // console.log(" =$$$$$$$$$$$$$$$$$$$$$  =============|",user._id.toString(),"|");

                  // console.log(myCache.get(user._id.toString()));
                  // console.log(" =$$$$$$$$$$$$$$$$$$$$$  =============  ");
                  // console.log("]",myCache.get(user._id.toString()),"[");
                  // console.log("|",userToken,"|");
                  return res.status(200).json({
                      token: userToken,
                      user_details:user
                  });
                // }
              } else {
                  return res.status(400).json({ msg: 'Mobile Number and Password mismatch, Please Retry.' });
              }
          });
      });
  }




  static userCount(req, res) {

    User.count({'role':{$ne:'admin'},'role':{$ne:'subAdmin'},'active_status':true,'created_at': {$ne: null},}, function (err, count) {
      Responder.success(res,count);
      
    });
  }

  
  static userCountAllCount(req, res) {

    var counts={};
    User.count({'role':'normalUser','active_status':true}, function (err, normalUser) {
      counts.normalUser= normalUser;
      User.count({'role':'referalUser','active_status':true}, function (err, referalUser) {
        counts.referalUser= referalUser;
        Responder.success(res,counts);
      });
    });


  }  


  
  static getAppUsersCountForFirstTime(req, res) {
     User.count({'role': 'normalUser','active_status':true}, function (err, count) {
      Responder.success(res,count);
      
    });
  }


  static getAppUsersCount(req, res) {

   
     if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                  {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},
                                            ]  
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                    {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                  {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                {
                                                  $or:[
                                                       
                                                        
                                                      { buisness_name : 
                                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                                      },
                                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                      
                                                       
                                                    ]
                                                } 
                                            ],
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]

                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
           User.aggregate([ 
                  {
                    $match: {
                               $and: [ {'role': {$ne: 'admin'}},
                                      {role:"normalUser"},
                                      {active_status:true},
                                      // {suspend:false},
                                        

                                {
                                  $or:[
                                       
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                       
                                    ]
                                }
                                ]
                              
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))



        }

  }


  
  static getReferralUsersCountForFirstTime(req, res) {
    User.count({'role': 'referalUser','active_status':true}, function (err, count) {
      Responder.success(res,count);
      
    });

  }


  static getReferralUsersCount(req, res) {

    
    if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                  // {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                {
                                                  $or:[
                                                       
                                                        
                                                      { buisness_name : 
                                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                                      },
                                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                      
                                                       
                                                    ]
                                                } 
                                            ],


                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]

                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
           User.aggregate([ 
                  {
                    $match: {
                               $and: [ {'role': {$ne: 'admin'}},
                                      {role:"referalUser"},
                                      // {suspend:false},
                                        

                                {
                                  $or:[
                                       
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                       
                                    ]
                                }
                                ]
                              
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                         $group: {
                              _id: {
                              },
                              myCount: { $sum: 1 } ,
                            }
                    },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))



        }




  }


  
  static subAdminUsersCountForFirstTime(req, res) {
  
      User.aggregate([ 
              {
                $match: {
                          $and: [ {'role': {$ne: 'admin'}},
                                  {'role': {$ne: 'referalUser'}},
                                  {'role': {$ne: 'normalUser'}},
                                  {'role': 'subAdmin'},
                          // {
                          //   $or: [
                          //         { buisness_name : 
                          //           { $regex : "^" + req.body.buisness_name, $options: 'i'}
                          //         },
                          //         { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                          //         { email : {$regex : "^" + req.body.email,$options: 'i'}},
                          //         { district: {$regex : "^" + req.body.district,$options: 'i'}},
                          //         { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                          //         { state: {$regex : "^" + req.body.state,$options: 'i'}}
                          //         ]
                          // }
                          ]
                          
                        }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicles',
                   localField: '_id',
                   foreignField: 'user_id',
                   as: 'vehicleList'
                 }
               },
                  {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  static subAdminUsersCount(req, res) {

     if ((req.body.startDate != null && req.body.endDate != null) && 
          ( req.body.state == null && req.body.district == null) &&
            req.body.userName == null && req.body.state == null &&
            req.body.phone_number == null && req.body.email == null &&
            req.body.district == null && req.body.buisness_name == null
           ){
         console.log('cityyyyyyyyyyy1111111');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                  {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},
                                            ]  
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                    {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        if ((req.body.startDate != null && req.body.endDate != null) &&  
          ( 
            req.body.userName != null ||
            req.body.phone_number != null || req.body.email != null ||
             req.body.buisness_name != null
           )){
         console.log('cityyyyyyyyyyy11111112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                  {
                                            $and:[
                                                  {"created_at": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},

                                                {
                                                  $or:[
                                                       
                                                        
                                                      { buisness_name : 
                                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                                      },
                                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                      
                                                       
                                                    ]
                                                } 
                                            ],
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }
        else if ((req.body.startDate == null && req.body.endDate == null) &&
                   ( req.body.state != null && req.body.district != null) 

                   ) {
         console.log('cityyyyyyyyyyy222222222and');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]

                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state != null && req.body.district == null )) {
         console.log('cityyyyyyyyyyy222222222');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



      else if ((req.body.startDate != null && req.body.endDate != null )&&
                ( req.body.state == null && req.body.district != null )) {
         console.log('cityyyyyyyyyyy2222222224444');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    $or:[
                                         {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $or:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          },
                                      ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }



        else if ((req.body.startDate == null && req.body.endDate == null )&&
                ( req.body.state != null || req.body.district != null )) {
         console.log('cityyyyyyyyyyy22222222233');

           User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $or:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }

        else if ((req.body.startDate != null && req.body.endDate != null ) && 
                  (req.body.state != null && req.body.district != null)) {
         console.log('cityyyyyyyyyyy33333333333112');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                  {
                                    // $or:[
                                         // {
                                            $and:[
                                                  {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                                  {
                                                    $and:[
                                                          { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                                          { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                                    ]
                                                  }

                                            ]  
                                          // },
                                      // ]
                                  }
                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) &&
                   req.body.state != null && req.body.district != null) {
         console.log('cityyyyyyyyyyy33333333333');

            User.aggregate([ 
                  {
                    $match: {
                              $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},

                                      // {suspend:false},
                                    {
                                      $and:[
                                            { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                            { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      ]
                                    }

                                  ]
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))

        }


        else if ((req.body.startDate == null && req.body.endDate == null ) && 
                   req.body.state == null && req.body.district == null) {
         console.log('cityyyyyyyyyyy44444444444');
         
           User.aggregate([ 
                  {
                    $match: {
                               $and: [ {'role': {$ne: 'admin'}},
                                      {role:{$ne: "normalUser"}},
                                      {role:{$ne: "referalUser"}},
                                      {role:"subAdmin"},
                                      {active_status:true},
                                      // {suspend:false},
                                        

                                {
                                  $or:[
                                       
                                        
                                      { buisness_name : 
                                        { $regex : "^" + req.body.buisness_name, $options: 'i'}
                                      },
                                      { name : {$regex : "^" + req.body.userName,$options: 'i'}},
                                      { email : {$regex : "^" + req.body.email,$options: 'i'}},
                                      { district: {$regex : "^" + req.body.district,$options: 'i'}},
                                      { phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                      { state: {$regex : "^" + req.body.state,$options: 'i'}},
                                      
                                       
                                    ]
                                }
                                ]
                              
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))



        }
  }

static getUsersByMonth(req,res){


    User.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'created_at': {$ne: null},
                  'role':'normalUser',
                  'active_status':true
                  // 'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      myCount: { $sum: 1 } ,
                      
                        // total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}



static getAffiliateUsersByMonth(req,res){


    User.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'created_at': {$ne: null},
                  'role':'referalUser',
                  'active_status':true
                  // 'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      myCount: { $sum: 1 } ,
                      
                        // total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}



 static totalReferralIncome(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  static totalGst(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  // 'transaction_date': {$ne: null},
                  // 'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$gstAmount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }

  

  static totalBoostedIncome(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Boost Fee'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        total_gst: { $sum: { $add: ["$gstAmount"] } },
                        count: {$sum:1}
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  

  static getReferralIncomeByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }


  
  static getWithdrawTotal(req, res) {

    WithdrawWallet.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'date': {$ne: null},
                  $or:[
                    {'status': 'paid to bank'},
                    {'status': 'payout given to refferal user'}
                  ]
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }

  static getWithdrawRequestTotal(req, res) {

    WithdrawWallet.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'date': {$ne: null},
                  $or:[
                    {'status': 'waiting for aproval'},
                    {'status': 'waiting for remmittes'},
                  ]
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  
    static getWithdrawRequestTotalForSelectedMonth(req, res) {

      var moment = require('moment');
    // start today
    var start = moment(req.body.startDate).startOf('day');
    // end today
    var end = moment(req.body.enddate).endOf('day');

      WithdrawWallet.aggregate([ 
            {
                $match: {
                  $and:[
                  // 'date': { $gte : start, $lte:  end },
                    {
                      'date':{
                              "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                              "$lte": new Date(req.body.enddate+'T23:59:59Z') 
                            }
                    },
                  // 'date': {$ne: null},
                    {
                      $or:[
                      {'status': 'waiting for aproval'},
                      {'status': 'waiting for remmittes'},
                      ]
                    }
                  ],

                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }




  
  static getWithdrawAmountByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'date': {$ne: null},
                  'status': 'paid to bank'

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$date'
                        },
                        month: {
                          $month: '$date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  static getBoostTripGstTotal(req, res) {

     Transaction.aggregate([ 
            // {
            //     /* Filter out users who have not yet subscribed */
            //     $match: {
            //        "joined" is an ISODate field 
            //       'date': {$ne: null},
            //       // 'status': 'paid to bank'
            //     }
            // },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$gstAmount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  static getSubscriptionTotal(req, res) {

     Transaction.aggregate([ 
               {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  // 'date': {$ne: null},
                   'transaction_reason': 'Subscription Fee',

                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        total_gst : { $sum: { $add: ["$gstAmount"] } },
                        count : { $sum : 1 }
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  static getSubscriptionByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Subscription Fee'

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  static getBoostIncomeByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Boost Fee'

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$amount"] } },
                        total_gst: { $sum: { $add: ["$gstAmount"] } },
                        count: {$sum:1}
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  static getTotalGstByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  $or:[
                    {'transaction_reason': 'Subscription Fee'},
                    // {'transaction_reason': 'Boost Fee'},
                  ]


                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$gstAmount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }


  

  static getTotalTripBoostGstByMonth(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Boost Fee',

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$gstAmount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }

  
  static checkValidUser(req,res){
    
  }



  static getUserTotalReferralIncome(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'user_id': req.params.id,
                  'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  static getReferralIncomeByMonthGetMethod(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'user_id': req.params.id,
                  // 'due_to_user': req.params.id,

                  'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                      // myCount: { $sum: 1 } ,
                      
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



static getReferuserPagination(req, res) {

       var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)
    var users = [];

      User.aggregate([ 
              {
                    $match: {
                      referred_by_code : req.body.referal_code  
                      }
                    
              },
              { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
               },
               { 
                "$sort": { 
                    "lastSubscribed": -1,
                } 
            }, 
           


                    ]).skip(size * (pageNo - 1)).limit(size)
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))
}






static getreferuserGetMethod(req, res) {

      User.aggregate([ 
              {
                    $match: {
                      referred_by_code : req.params.id  
                      }
                    
              },
              { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
               }
           


                    ])
      .then((user) =>{ console.log(user); Responder.success(res, user)})
      .catch((err) => Responder.operationFailed(res, err))

 // var tranc=[];
   // var user=[];
    // User.find({ referred_by_code : req.params.id })
    // .then((trc)=> {tranc=trc; return Transaction.find({due_to_user:{$in:_.map(trc,'_id')}})})
    // .then((user)=>{console.log('aaaaaaaaa',user);var response=[];_.each(tranc,tr =>{
    //   console.log('tr'+tr)
    //   var trp={_id:tr._id,
    //     referred_by_code:tr.referred_by_code,
    //     name:tr.name,
    //     phone_number:tr.phone_number,
    //     email:tr.email,
    //     referal_code:tr.referal_code,
    //     renewal_date:tr.renewal_date,
    //     rating_review:tr.rating_review,
    //     report:tr.report,
    //     wallet_balance:tr.wallet_balance,
    //     is_subscribed:tr.is_subscribed,
    //     created_at:tr.created_at,
    //     suspend:tr.suspend,
       
    //   };
      
    //   _.each(user,usr=>{
    //     console.log('dd')
    //     console.log(usr)
        
    //     if(usr.due_to_user == trp._id){
      
    //       trp.transaction=usr;
    //       console.log(trp)

    //     }
    //   }) 
    //   response.push(trp);
    //   console.log(response)
    // });Responder.success(res,response) })
    // .catch((err)=>Responder.operationFailed(res,err))
     //  console.log(req.params)
     //  User.find({ referred_by_code : req.params.id })
     //  .then((usr) =>{console.log(usr); Responder.success(res, usr)})
     // .catch((err) => Responder.operationFailed(res, err))

  } 


  

  static userCountForMonth(req, res) {
    console.log(req.body.startDate)
    

    // User.count({$or:[{
    //   "created_at" : { $gte : new Date(req.body.startDate), $lte:  new Date(req.body.enddate) }
    //   // $or:[{created_at: { $gte: req.body.startDate } },{created_at: { $lte: req.body.endDate } }]
    // }]}, function (err, count) {
    //   Responder.success(res,count);
      
    // });

    var moment = require('moment');
    // start today
    var start = moment(req.body.startDate).startOf('day');
    // end today
    var end = moment(req.body.enddate).endOf('day');

    console.log('ssssssstarttt---',start);
    console.log('ssssssstarttt---',end);


    User.count({
                "created_at" : { $gte : start, $lte:  end },
                'role':{$ne:'admin'},
                'role':{$ne:'subAdmin'}
            
      // $or:[{created_at: { $gte: req.body.startDate } },{created_at: { $lte: req.body.endDate } }]
    }, function (err, count) {
      Responder.success(res,count);
      
    });

  }


  static userAllCountForMonth(req, res) {
    console.log(req.body.startDate)
    
    var moment = require('moment');
    // start today
    var start = moment(req.body.startDate).startOf('day');
    // end today
    var end = moment(req.body.enddate).endOf('day');

    console.log('ssssssstarttt---',start);
    console.log('ssssssstarttt---',end);

    var counts={};
    User.count({"created_at" : { $gte : start, $lte:  end },'role':'normalUser'}, function (err, normalUser) {
      counts.normalUser= normalUser;
      User.count({"created_at" : { $gte : start, $lte:  end },'role':'referalUser'}, function (err, referalUser) {
        counts.referalUser= referalUser;
        Responder.success(res,counts);
      });
    });

    // User.count({
    //             "created_at" : { $gte : start, $lte:  end },
    //             'role':'normalUser',
    //             'role':'referalUser'
            
    //   // $or:[{created_at: { $gte: req.body.startDate } },{created_at: { $lte: req.body.endDate } }]
    // }, function (err, count) {
    //   Responder.success(res,count);
      
    // });

  }


  static totalReferralIncomeForMonth(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'transaction_reason': 'Referral Commission'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  
  static totalBoostedIncomeForMonth(req, res) {

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'transaction_reason': 'Boost Fee'
                }
            },
            {
                 $group: {
                      _id: {
                        // year: {
                        //   $year: '$transaction_date'
                        // },
                        // month: {
                        //   $month: '$transaction_date',
                        // },
                        // due_to_user: {
                        //   $due_to_user: '$due_to_user'
                        // }
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        total_gst: { $sum: { $add: ["$gstAmount"] } },
                        count: {$sum:1}
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }

  

    static getWithdrawTotalForSelectedMonth(req, res) {

      WithdrawWallet.aggregate([ 
            {
                $match: {
                  'date': {$ne: null},
                  'date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'status': 'paid to bank'
                }
            },
            {
                 $group: {
                      _id: {
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                  
              
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }




  static getBoostTripGstTotalForSelectedMonth(req, res) {

     Transaction.aggregate([ 
              {   
                $match: {
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'transaction_reason': 'Boost Fee'

                }
              },

          
            {
                 $group: {
                      _id: {
                      },
                        total: { $sum: { $add: ["$gstAmount"] } },                   
                    } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  

  static totalGstForSelectedMonth(req, res) {

    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Boost Fee',
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                }
            },
            {
                 $group: {
                      _id: {
                      },
                        // total: { $sum: { $add: ["$amount"] } },
                        total_gst: { $sum: { $add: ["$gstAmount"] } },
                        count: {$sum:1},
                        total: { $sum: { $add: ["$gstAmount"] } },                    
                    }
                  
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  
  static getSubscriptionTotalForSelectedMonth(req, res) {

     Transaction.aggregate([ 
               {
                $match: {
                  'transaction_reason': 'Subscription Fee',
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  

                }
            },
            {
                 $group: {
                      _id: {
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        total_gst: {$sum :{$add : ["$gstAmount"]}},
                        // total_record: {$count : ['$amount']}
                        count: { $sum: 1 } 

                    }
                  
             // $project: { month:  
             //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
             //                { $month: '$transaction_date' }, -1] 
             //                }
             //            } 
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  
  static getUsersByMonthForSelectedMonth(req,res){


    User.aggregate([ 
            {
                $match: {
                  'role': 'referalUser',
                  'role': 'normalUser',
                  'created_at': {$ne: null},
                  'created_at': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                      },
                      myCount: { $sum: 1 } ,
                    }
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  static getReferalUsersByMonthForSelectedMonth(req,res){


    User.aggregate([ 
            {
                $match: {
                  'role': 'referalUser',
                  'created_at': {$ne: null},
                  'created_at': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                      },
                      myCount: { $sum: 1 } ,
                    }
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }

  
  static getReferralIncomeByMonthForSelectedMonth(req,res){


    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission',
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },


                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                       
                      },
                      
                        total: { $sum: { $add: ["$amount"] } },
                      
                    

                    }
                  
            
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }


  

  static getSubscriptionByMonthForSelectedMonth(req,res){


      Transaction.aggregate([ 
            {
                $match: {
                  'transaction_date': {$ne: null},
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'transaction_reason': 'Subscription Fee'

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                        
                      },
                      
                        total: { $sum: { $add: ["$amount"] } },                    

                    }
         
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  


   static getBoostIncomeByMonthForSelectedMonth(req,res){


      Transaction.aggregate([ 
            {
                $match: {
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Boost Fee',
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                  
                      },
                      
                        total: { $sum: { $add: ["$amount"] } },
                      
                    

                    }
                  
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }



  


  static getTotalGstByMonthForSelectedMonth(req,res){


      Transaction.aggregate([ 
            {
                $match: {
                  'transaction_date': {$ne: null},
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  $or:[
                    {'transaction_reason': 'Subscription Fee'},
                    // {'transaction_reason': 'Boost Fee'},
                  ]
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                      },
                      
                        total: { $sum: { $add: ["$gstAmount"] } },                    

                    }
                  
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }


  

  static getTotalTripBoostGstByMonthForSelectedMonth(req,res){


    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_date': {$ne: null},
                  'transaction_date': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                  'transaction_reason': 'Boost Fee'
                  

                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$transaction_date'
                        },
                        month: {
                          $month: '$transaction_date',
                        },
                       
                      },
                      
                        total: { $sum: { $add: ["$gstAmount"] } },                    

                    }
            },
            { 
                "$sort": { 
                    "_id.year": 1,
                    "_id.month": 1,
                    // "_id.day": 1 
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

  }


  static getIncomeForReferralUserByMonth(req,res){
    console.log('req.body.user_id'+req.body.user_id);
    Transaction.aggregate([ 
              
          
   
            {
                $match: {
                  'user_id': ObjectId(req.body.user_id),
                  'transaction_reason': 'Referral Commission'
                }
            },
             {
             $project: {
                transaction_date: { $dateToString: { format: "%Y-%m-%d", date: "$transaction_date" } },
                amount:1,
                due_to_user: 1,
                due_to_user_name: 1,
                reason: 1,
                referred_by_code: 1,
                referred_by_name: 1,
                status: 1,
                subscribeFeePercentage: 1,
                total_amount: 1,
                transaction_id: 1,
                transaction_reason: 1,
                transaction_type: 1,
                user_id: 1,
             }
           },
             
              { 
                "$sort": { 
                    "transaction_date": 1,
                } 
            },  

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}





static sendEmailAddBalance(req,res){

  User.findOne({ _id: req.body.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionCR'})
           .then((res)=>
           {
            console.log('rrrrresponse')
            console.log(res)
            console.log('rrrrresponse')

                var oldsubject1=res[0].subject;
                var userName= user.name;
                var newSubject1= oldsubject1.replace("{name}",userName);
                var newSubject= newSubject1.replace("{amount}",req.body.amount);

                var oldBodySubject1=res[0].emailBody;
                var userName=user.name;
                var newBodySubject1= oldBodySubject1.replace("{name}",userName);
                newBodySubject1= newBodySubject1.replace("{name}",userName);
                var newBodySubject= newBodySubject1.replace("{amount}",req.body.amount);


                Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);


                      var mailOptions = {
                        from: 'Swari <'+app_settings.billingEmail+'>',
                        to: user.email,
                        subject:newSubject ,
                        html: newBodySubject,
                      };

                      transporter.sendMail(mailOptions, function(error, info){
                        if (error) {
                          console.log(error);
                        } else {
                          console.log('Email sent: ' + info.response);

                             // send message start
                           var extServerOptionsPost = {
                                hostname: "***************",
                                path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                method: 'POST',
                                port: '80',
                                headers: {
                                  'Content-Type': 'application/json'
                                }
                              }

                              const token = otplib.authenticator.generate(secret);
                              console.log(token);

                              // var reqPost = http.request(extServerOptionsPost, function (response) {

                              //   response.on('data', function (data) {
                              //     console.log("line no: 87 ", data);
                              //   });
                              // });
                              var body = {
                                "smsContent": 'Rs. '+ req.body.amount +' is added to your wallet on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ req.body.transaction_id +'.  A/c Bal: Rs. '+ req.body.balance +'. Thanks. www.swari.in',
                                "routeId": "1",
                                "mobileNumbers": user.phone_number,
                                // "mobileNumbers": '9855621130,7009232617',
                                "senderId": "SWARII",
                                "signature": "signature",
                                "smsContentType": "english"
                              }

                              sendTXTGuruSMS("1507166609231240041",body);
                              return Responder.success(res, { success: true, message: "" })
                              // reqPost.write(JSON.stringify(body));
                              // reqPost.end();
                              // reqPost.on('error', function (e) {
                              //   console.error("line: 102 " + e);
                              // });
                          // send message end
                        }
                      });

                  })
                .catch((err) => Responder.operationFailed(res, err))



           })
          .then((trc)=>Responder.success(res,trc))
           .catch((err)=>Responder.operationFailed(res,err)) 
      } 
        )
      .catch((err) => Responder.operationFailed(res, err))

     

}




static sendEmailAddBalanceDR(req,res){

  User.findOne({ _id: req.body.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionDR'})
           .then((res)=>
           {
            console.log('rrrrresponse')
            console.log(res)
            console.log('rrrrresponse')

                var oldsubject1=res[0].subject;
                var userName= user.name;
                var newSubject1= oldsubject1.replace("{name}",userName);
                var newSubject= newSubject1.replace("{amount}",req.body.amount);

                var oldBodySubject1=res[0].emailBody;
                var userName=user.name;
                var newBodySubject1= oldBodySubject1.replace("{name}",userName);
                newBodySubject1= newBodySubject1.replace("{name}",userName);
                var newBodySubject= newBodySubject1.replace("{amount}",req.body.amount);

                 Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                      var mailOptions = {
                        from: 'Swari <'+app_settings.billingEmail+'>',
                        to: user.email,
                        subject:newSubject ,
                        html: newBodySubject,
                      };

                      transporter.sendMail(mailOptions, function(error, info){
                        if (error) {
                          console.log(error);
                        } else {
                          console.log('Email sent: ' + info.response);

                             // send message start
                           var extServerOptionsPost = {
                                hostname: "***************",
                                path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                method: 'POST',
                                port: '80',
                                headers: {
                                  'Content-Type': 'application/json'
                                }
                              }

                              const token = otplib.authenticator.generate(secret);
                              console.log(token);

                              var reqPost = http.request(extServerOptionsPost, function (response) {

                                response.on('data', function (data) {
                                  console.log("line no: 87 ", data);
                                });
                              });
                              var body =
                                {
                                  "smsContent": 'Rs. '+ req.body.amount +' deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ req.body.transaction_id +'.  A/c Bal: Rs. '+ req.body.balance +'. Thanks. www.swari.in',
                                  "routeId": "1",
                                  "mobileNumbers": user.phone_number,
                                  // "mobileNumbers": '9855621130,7009232617',
                                  "senderId": "SWARII",
                                  "signature": "signature",
                                  "smsContentType": "english"
                                }
                              reqPost.write(JSON.stringify(body));
                              reqPost.end();
                              reqPost.on('error', function (e) {
                                console.error("line: 102 " + e);
                              });
                          // send message end
                        }
                      }); 

                })
                .catch((err) => Responder.operationFailed(res, err))



           })
          .then((trc)=>Responder.success(res,trc))
           .catch((err)=>Responder.operationFailed(res,err)) 
      } 
        )
      .catch((err) => Responder.operationFailed(res, err))

     

}




static sendEmailBoostTrip(req,res){
  

  if(!req.body.amount || req.body.amount<=0 || req.body.amount=="") {
    console.log("Ignore boost trip as fee is ZERO ");
    Responder.success(res,[]);
    return;
  }

  User.findOne({ _id: req.body.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionBoostTrip'})
           .then((res)=>
           {
            console.log('rrrrresponse')
            console.log(res)
            console.log('rrrrresponse')

                var oldsubject1=res[0].subject;
                var userName= user.name;
                var newSubject1= oldsubject1.replace("{name}",userName);
                var newSubject= newSubject1.replace("{amount}",req.body.amount);

                var oldBodySubject1=res[0].emailBody;
                var userName=user.name;
                var newBodySubject1 = oldBodySubject1.replace("{name}",userName);
                newBodySubject1 = newBodySubject1.replace("{name}",userName);
                var newBodySubject= newBodySubject1.replace("{amount}",req.body.amount);


                  createPdfBoostTrip(req.body.transaction_id, req.body.amount, req.body.balance, req.body.date, req.body.gst, req.body.origin, req.body.destinationuser ,newSubject, user);
                    Settings.findOne({ type: 'app_settings' })
                    .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                      var mailOptions = {
                        from: 'Swari <'+app_settings.billingEmail+'>',
                        to: user.email,
                        subject:newSubject ,
                        html: newBodySubject,
                        // html: 'pdfDocs/'+'tr_id_'+req.body.transaction_id+'.pdf',
                        "attachments" : [
                          { // use URL as an attachment
                            filename: 'tr_id_'+req.body.transaction_id+'.pdf',
                            path: 'pdfDocs/'+'tr_id_'+req.body.transaction_id+'.pdf'
                          }
                        ]
                      };

                      transporter.sendMail(mailOptions, function(error, info){
                        if (error) {
                          console.log(error);
                        } else {
                          console.log('Email sent: ' + info.response);

                          // send message start
                           var extServerOptionsPost = {
                                hostname: "***************",
                                path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                method: 'POST',
                                port: '80',
                                headers: {
                                  'Content-Type': 'application/json'
                                }
                              }

                              const token = otplib.authenticator.generate(secret);
                              console.log(token);

                              var reqPost = http.request(extServerOptionsPost, function (response) {

                                response.on('data', function (data) {
                                  console.log("line no: 87 ", data);
                                });
                              });
                              var body =
                                {
                                  
                                  "smsContent": 'Boosting Trip fee Rs. '+ req.body.amount +' deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ req.body.transaction_id +'. A/c Bal: Rs. '+ req.body.balance +'. Thanks. www.swari.in',
                                  // "smsContent": 'pdfDocs/tr_id_'+'req.body.transaction_id.pdf',
                                  "routeId": "1",
                                  "mobileNumbers": user.phone_number,
                                  // "mobileNumbers": '9855621130,7009232617',
                                  "senderId": "SWARII",
                                  "signature": "signature",
                                  "smsContentType": "english"
                                }
                              /* 
                              reqPost.write(JSON.stringify(body));
                              reqPost.end();
                              reqPost.on('error', function (e) {
                                console.error("line: 102 " + e);
                              });
                              */
                          // send message end

                        }
                      }); 

                })
                .catch((err) => Responder.operationFailed(res, err))




           })
          .then((trc)=>Responder.success(res,trc))
           .catch((err)=>Responder.operationFailed(res,err)) 
      } 
        )
      .catch((err) => Responder.operationFailed(res, err))

     

}


  
static sendEmailSubscriptionFees(req,res){

  User.findOne({ _id: req.body.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionSubscriptionFees'})
           .then((res)=>
           {
            console.log('rrrrresponse')
            console.log(res)
            console.log('rrrrresponse')

                var oldsubject=res[0].subject;
                var userName= user.name;
                var newSubject= oldsubject.replace("{name}",userName);

                var oldBodySubject=res[0].emailBody;
                var userName=user.name;
                var newBodySubject= oldBodySubject.replace("{name}",userName);
                var newBodySubject= oldBodySubject.replace("{name}",userName);

                 Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                    var mailOptions = {
                      from: 'Swari <'+app_settings.billingEmail+'>',
                      to: user.email,
                      subject:newSubject ,
                      html: newBodySubject,
                    };

                    transporter.sendMail(mailOptions, function(error, info){
                      if (error) {
                        console.log(error);
                      } else {
                        console.log('Email sent: ' + info.response);
                          // send message start
                         var extServerOptionsPost = {
                              hostname: "***************",
                              path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                              method: 'POST',
                              port: '80',
                              headers: {
                                'Content-Type': 'application/json'
                              }
                            }

                            const token = otplib.authenticator.generate(secret);
                            console.log(token);

                            /*var reqPost = http.request(extServerOptionsPost, function (response) {

                              response.on('data', function (data) {
                                console.log("line no: 87 ", data);
                              });
                            });
                            */
                            var body =
                              {
                                "smsContent": 'Rs. '+ req.body.amount +' Subscription Fee deducted on '+ dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ req.body.transaction_id +'. Subscription valid for 30 days. A/c Bal: Rs. '+ req.body.balance +'. Thanks. www.swari.in',
                                "routeId": "1",
                                "mobileNumbers": user.phone_number,
                                // "mobileNumbers": '9855621130,7009232617',
                                "senderId": "SWARII",
                                "signature": "signature",
                                "smsContentType": "english"
                              }

                              sendTXTGuruSMS("1507166609163112979",body);
                              return Responder.success(res, { success: true, message: "" }) 
                            /*
                            reqPost.write(JSON.stringify(body));
                            reqPost.end();
                            reqPost.on('error', function (e) {
                              console.error("line: 102 " + e);
                            });
                            */
                        // send message end
                      }
                    }); 


                })
                .catch((err) => Responder.operationFailed(res, err))
  

           })
          .then((trc)=>Responder.success(res,trc))
           .catch((err)=>Responder.operationFailed(res,err)) 
      } 
        )
      .catch((err) => Responder.operationFailed(res, err))

     

}

static sendEmailSubscriptionFeesBackground(data){

  User.findOne({ _id: data.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionSubscriptionFees'})
           .then((res)=>
           {            
                var oldsubject=res[0].subject;
                var userName= user.name;
                oldsubject = oldsubject.replace("{name}",userName);
                oldsubject = oldsubject.replace("{name}",userName);
                oldsubject = oldsubject.replace("{amount}",data.amount);
                let newSubject = oldsubject.replace("{amount}",data.amount);

                var oldBodySubject=res[0].emailBody;
                var userName=user.name;
                var newBodySubject= oldBodySubject.replace("{name}",userName);
                newBodySubject = newBodySubject.replace("{amount}",data.amount);
                newBodySubject = newBodySubject.replace("{amount}",data.amount);

                Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);

                    var mailOptions = {
                      from: 'Swari <'+app_settings.billingEmail+'>',
                      to: user.email,
                      subject:newSubject ,
                      html: newBodySubject,
                    };

                    transporter.sendMail(mailOptions, function(error, info){
                      if (error) {
                        console.log(error);
                      } else {
                        console.log('Email sent: ' + info.response);
                          // send message start
                         var extServerOptionsPost = {
                              hostname: "***************",
                              path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                              method: 'POST',
                              port: '80',
                              headers: {
                                'Content-Type': 'application/json'
                              }
                            }

                            const token = otplib.authenticator.generate(secret);
                            console.log(token);

                            var reqPost = http.request(extServerOptionsPost, function (response) {

                              response.on('data', function (data) {
                                console.log("line no: 87 ", data);
                              });
                            });
                            var body =
                              {
                                "smsContent": 'Rs. '+ data.amount +' Subscription Fee deducted on '+ dateFormat(new Date(data.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID '+ data.transaction_id +'. Subscription valid for 30 days. A/c Bal: Rs. '+ user.wallet_balance +'. Thanks. www.swari.in',
                                "routeId": "1",
                                "mobileNumbers": user.phone_number,
                                // "mobileNumbers": '9855621130,7009232617',
                                "senderId": "SWARII",
                                "signature": "signature",
                                "smsContentType": "english"
                              }
                            reqPost.write(JSON.stringify(body));
                            reqPost.end();
                            reqPost.on('error', function (e) {
                              console.error("line: 102 " + e);
                            });
                        // send message end
                      }
                    }); 


                })                
  

           })
          .then((trc)=> console.log(trc) )
           .catch((err)=> console.error(err)) 
      } 
        )
      .catch((err) => console.error(err))     
}





  static sendEmailRazorpay(user_id, amount, transaction_id){
    // console.log('rrrrrrrrrrrrrrrrrrrrrrrrRRRRR',req.body);

  User.findOne({ _id: user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionRazorpay'})
           .then((res)=>
           {    
                var oldsubject1=res[0].subject;
                var userName= user.name;
                var newSubject1= oldsubject1.replace("{name}",userName);
                var newSubject= newSubject1.replace("{amount}",amount);

                var userName=user.name;
                var oldBodySubject1=res[0].emailBody;
                var newBodySubject1= oldBodySubject1.replace("{name}",userName);
                newBodySubject1= oldBodySubject1.replace("{name}",userName);
                newBodySubject1 =newBodySubject1.replace("{date}",dateFormat(new Date(), "dd-mm-yyyy h:MM:ss TT"))
                newBodySubject1 = newBodySubject1.replace("{transaction_id}",transaction_id)
                newBodySubject1 = newBodySubject1.replace("{amount}",amount);

                Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);


                        var mailOptions = {
                          from: 'Swari '+'<'+app_settings.billingEmail+'>',
                          to: user.email,
                          subject:newSubject ,
                          html: newBodySubject1,
                        };

                        transporter.sendMail(mailOptions, function(error, info){
                          if (error) {
                            console.log(error);
                          } else {
                            console.log('Email sent: ' + info.response);
                            // send message start
                            var extServerOptionsPost = {
                                  hostname: "***************",
                                  path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                                  method: 'POST',
                                  port: '80',
                                  headers: {
                                    'Content-Type': 'application/json'
                                  }
                            }

                            const token = otplib.authenticator.generate(secret);
                            console.log(token);

                            var reqPost = http.request(extServerOptionsPost, function (response) {

                              response.on('data', function (data) {
                                console.log("line no: 87 ", data);
                              });
                            });
                            var body =
                              {
                                // "smsContent": `Rs. ${req.body.amount} is added to your wallet on ${ext.date} Transaction ID ${ext.transaction_id}. A/c Bal: Rs ${ext.total}. Thanks. www.swari.in`,
                                "smsContent":  `Rs. ${amount} is added to your wallet on ${dateFormat(new Date(), "dd-mm-yyyy h:MM:ss TT")} Transaction ID ${transaction_id}.  A/c Bal: Rs. ${user.wallet_balance}. Thanks. www.swari.in`,
                                "routeId": "1",
                                "mobileNumbers": user.phone_number,
                                // "mobileNumbers": '9855621130,7009232617',
                                "senderId": "SWARII",
                                "signature": "signature",
                                "smsContentType": "english"
                              }
                            reqPost.write(JSON.stringify(body));
                            reqPost.end();
                            reqPost.on('error', function (e) {
                              console.error("line: 102 " + e);
                            });
                            // send message end
                          }
                        }); 
                  })
                .catch((err) => console.error("Razor pay email error"))
           })
          .then((trc)=>console.log("Email sent "))
           .catch((err)=>console.error("Razor pay email error")) 
      }
  ).catch((err) => Responder.operationFailed(res, err))
}


  static getSuspendedUsers(req,res){

        User.aggregate([ 
              {
                $match: {
                          suspend:true
                        }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicles',
                   localField: '_id',
                   foreignField: 'user_id',
                   as: 'vehicleList'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  
   static filterSuspendedUsers(req, res) {
    console.log('filterSuspendedUsers'+req.body.phone_number);

        if (req.body.role != null && req.body.phone_number == null) {
           User.aggregate([ 
                  {
                    $match: {
                              'role': {$ne: 'admin'} ,
                              'role': req.body.role ,
                              'suspend': true        
                                  
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
        }else if (req.body.role != null && req.body.phone_number != null) {
           User.aggregate([ 
                  {
                    $match: {
                              'role': {$ne: 'admin'} ,
                              'role': req.body.role ,
                              'suspend': true ,
                              'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                                     
                                  
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
        }
        else{
            User.aggregate([ 
                  {
                    $match: {
                              'role': {$ne: 'admin'} ,
                              'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                               'suspend': true        
                                  
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicles',
                       localField: '_id',
                       foreignField: 'user_id',
                       as: 'vehicleList'
                     }
                   },
                   { 
                    "$sort": { 
                        "created_at": -1,
                    } 
                }, 

                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
      }
    }



  static filterSubscribedUsers(req, res) {
    if (req.body.is_subscribed == 'Subscribed'&& req.body.phone_number != null) {
      console.log('Subscribed--------1')
      User.aggregate([ 
                {
                  $match: {
                            is_subscribed:true,
                            referred_by_code: req.body.referred_by_code,
                            phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'},

                          }
                      
                },
                { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
                },
                 { 
                  "$sort": { 
                      "lastSubscribed": -1,
                  } 
              }, 

                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }else if (req.body.is_subscribed == 'Subscribed'&& req.body.phone_number == null) {
      console.log('Subscribed--------12')
      User.aggregate([ 
                {
                  $match: {
                            is_subscribed:true,
                            referred_by_code: req.body.referred_by_code,

                          }
                      
                },
                { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
                },
                 { 
                  "$sort": { 
                      "lastSubscribed": -1,
                  } 
              }, 

                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
    else if (req.body.is_subscribed == 'Not Subscribed' && req.body.phone_number == null) {
      console.log('Subscribed--------2')
      User.aggregate([ 
                {
                  $match: {
                            is_subscribed:false,
                            referred_by_code: req.body.referred_by_code

                          }
                      
                },
                { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
                },
                 { 
                  "$sort": { 
                      "lastSubscribed": -1,
                  } 
              }, 

                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }else if (req.body.phone_number != null) {
      console.log('phone_number--------2')
      User.aggregate([ 
                {
                  $match: {
                            'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                            referred_by_code: req.body.referred_by_code

                          }
                      
                },
                { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
                },
                 { 
                  "$sort": { 
                      "lastSubscribed": -1,
                  } 
              }, 

                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }else{
      console.log('Subscribed--------3')
      User.aggregate([ 
                {
                  $match: {
                            $or:[
                              {is_subscribed:false},
                              {is_subscribed:true},
                            ],
                            referred_by_code: req.body.referred_by_code

                          }
                      
                },
                { $lookup:
                 {
                   from: 'transactions',
                   localField: '_id',
                   foreignField: 'due_to_user',
                   as: 'transactionsDetails'
                 }
                },
                 { 
                  "$sort": { 
                      "lastSubscribed": -1,
                  } 
              }, 

                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }

  }


static getUserTotalReferralUsers(req,res){


    User.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  // 'user_id': ObjectId(req.body.user_id),
                  // 'transaction_reason': 'Referral Commission' 
                  referal_code:req.body.referred_by_code                 
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                      },
                      myCount: { $sum: 1 } ,
                    }
            }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}



static getTotalReferralCommission(req,res){


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  'user_id': ObjectId(req.body.user_id),
                  'transaction_reason': 'Referral Commission'                  
                }
            },
            {
                 $group: {
                      _id: {
                        year: {
                          $year: '$created_at'
                        },
                        month: {
                          $month: '$created_at',
                        },
                      },
                        // total: { $sum: { $add: ["$amount"] } },

                    }
            }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}

static getTotalSMSBalance(req, res) {
  console.log('response ??? ', req.body);
  console.log('response ???11 ', req.params);

  const request = require('request');
  const username = 'navpreet.au'; // Replace with dynamic username if needed
  const password = 'Nav!@txt9'; // Replace with dynamic password if needed

  const apiUrl = `https://www.textguru.in/imobile/balancecheckapi.php?username=${username}&password=${password}`;

  request.get(apiUrl, function (err, resData, body) {
    console.log('rrrrrrrrrrr', err);
    console.log('rrrrrrrrrrres', resData);

    if (err) {
      return Responder.operationFailed(res, err);
    }

    try {
      // Assuming the body is a plain number
      const smsBalance = parseInt(body.trim(), 10);
      if (isNaN(smsBalance)) {
          console.error('Invalid SMS balance:', body);
          return Responder.operationFailed(res, 'Invalid SMS balance format');
      }
      Responder.success(res, { balance: smsBalance });
    } catch (parseError) {
      console.error('Error parsing SMS balance:', parseError);
      return Responder.operationFailed(res, 'Error parsing SMS balance');
    }
  });
}









  //  static getTotalSMSBalance(req,res){
  //    console.log('response ??? ',req.body)
  //    console.log('response ???11 ',req.params)
  //    

  //    
  //    var request=require('request');

  //    // request.get('http://bulksms.genx-infotech.com/rest/services/sendSMS/getClientRouteBalance?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',function(err,resData,body){
  //      //https://www.textguru.in/api/v22.0/balancecheckapi.php?username=navpreet.au&&password=Nav!@txt9
  //    request.get('https://www.textguru.in/imobile/balancecheckapi.php?username=navpreet.au&password=Nav!@txt9',function(err,resData,body){
  //        console.log('rrrrrrrrrrr',err)
  //        console.log('rrrrrrrrrrres',resData)
  //        Responder.success(res,resData)

  //        

  //      // if(err) //TODO: handle err
  //      // if(res.statusCode === 200 ) //etc
  //      //TODO Do something with response
  //    });

  //    // var options = {
  //    //   host: '***************',
  //    //   method:'GET',
  //    //   path: 'http://bulksms.genx-infotech.com/rest/services/sendSMS/getClientRouteBalance?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71'
  //    // };

  //    //     var reqPost = http.request(options, function (response) {
  //    //       response.on('data', function (data) {
  //    //         console.log("line no: 87 ", JSON.parse(data));
  //            

  //    //         setTimeout(() => {
  //    //           console.log('timeout beyond time');
  //    //           Responder.success(res,JSON.parse(data))

  //    //         }, 2000);


  //    //       });

  //    //         // response.on('end', (data) => {
  //    //         //   console.log('on end??',JSON.parse(data));
  //    //         // });
  //    //     });
  //    //  var body =
  //    //         {
  //              
  //    //           "smsContent": '9855621130',
  //    //           // "smsContent": 'pdfDocs/tr_id_'+'req.body.transaction_id.pdf',
  //    //           "routeId": "1",
  //    //           "mobileNumbers": '9855621130',
  //    //           // "mobileNumbers": '9855621130,7009232617',
  //    //           "senderId": "SWARII",
  //    //           "signature": "signature",
  //    //           "smsContentType": "english"
  //    //         }
  //    //       reqPost.write(JSON.stringify(body));
  //    //       reqPost.end();
  //  }



  
  static getreferuserCount(req,res){
       
    if (req.body.is_subscribed == 'Subscribed'&& req.body.phone_number != null) {
          console.log('Subscribed--------1')
          User.aggregate([ 
                    {
                      $match: {
                                is_subscribed:true,
                                referred_by_code: req.body.referred_by_code,
                                phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'},

                              }
                          
                    },
                    {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },

                          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))
        }else if (req.body.is_subscribed == 'Subscribed'&& req.body.phone_number == null) {
          console.log('Subscribed--------12')
          User.aggregate([ 
                    {
                      $match: {
                                is_subscribed:true,
                                referred_by_code: req.body.referred_by_code,

                              }
                          
                    },
                    {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  }, 

                          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))
        }
        else if (req.body.is_subscribed == 'Not Subscribed' && req.body.phone_number == null) {
          console.log('Subscribed--------2')
          User.aggregate([ 
                    {
                      $match: {
                                is_subscribed:false,
                                referred_by_code: req.body.referred_by_code

                              }
                          
                    },
                    {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },

                          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))
        }else if (req.body.phone_number != null) {
          console.log('phone_number--------2')
          User.aggregate([ 
                    {
                      $match: {
                                'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                                referred_by_code: req.body.referred_by_code

                              }
                          
                    },
                    {
                       $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  }, 
            ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))
        }else{
          console.log('Subscribed--------3')
          User.aggregate([ 
              {
                $match: {
                  $or:[
                    {is_subscribed:false},
                    {is_subscribed:true},
                  ],
                  referred_by_code: req.body.referred_by_code
                }  
              },
              {
              $group: {
                  _id: {                          
                },
                myCount: { $sum: 1 } ,
              }
            }, 
          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))
        }
  }


  
  static contaboSearch(req,res){
        
    var request=require('request');

    request.get('https://contabo.triva.in/api?limit=5&osm_tag=!place:postcode&q='+req.body.district,function(err,resData,body){
        console.log('rrrrrrrrrrr',err)
        console.log('rrrrrrrrrrres',resData)
        Responder.success(res,resData)

      // if(err) //TODO: handle err
      // if(res.statusCode === 200 ) //etc
      //TODO Do something with response
    });
  }


  static checkAppVersion(req,res){

    console.log(" checkAppVersion  =============   ", req.body.app_version);

    Settings.findOne({ type: 'app_settings' })
    .then((val) => {
      
      if(!val.appVersion) {
        return Responder.success(res,{'message':3})
      }

      let server_version = val.appVersion;
      
      if (server_version != req.body.app_version) {            
        console.log('greater required--');
        Responder.success(res,{'message':1})     
      } 
      else {
          console.log('else--');
          Responder.success(res,{'message':3})     

        }
      });
  }   

  static ___getAppConfig(req,res){

    const appConfig = {
      contabo: 'https://contabo.swari.in',
    }
    Responder.success(res, appConfig)     
  }    

  static getAppConfig(req,res){
    try {
    const appConfig = {
      contabo: 'https://contabo.swari.in',
    }
    Responder.success(res, appConfig) 
    }catch(err) {
      console.log(err);
    }
  }

}


