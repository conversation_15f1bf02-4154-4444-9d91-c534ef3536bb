import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const OfferRedeemedSchema = new Schema({
    user_id: ObjectId,
    offer_id: ObjectId,
    user_city: String,
    user_state: String,
    couponCode: String,
    created_at:{ type: Date, default: Date.now },
    redeemed_date: Date
});
// OfferRedeemedSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('OfferRedeemed', OfferRedeemedSchema);
