angular.module('addVehicleMaker.controllers', [])

    .controller('addVehicleMakerCtrl', function ($scope, APIService, $state,$rootScope) {



    	$scope.getVehicleMakers = function(){
            APIService.setData({ req_url: PrefixUrl + "/vehicleModel/getVehicleMakers"}).then(function (res) {
                console.log(res);
                $scope.vehicleMaker= res.data;
                // $scope.getALLCat=res.data;
             
            
            },function(er){
               
            })
    
        }


        $scope.addMaker = function(){

            const nameCapitalized = $scope.updatedata.maker.charAt(0).toUpperCase() + $scope.updatedata.maker.slice(1);
            $scope.updatedata.maker= nameCapitalized;
            
            APIService.setData({ req_url: PrefixUrl + "/vehicleMaker/create" ,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                 if (res.data == true) {
                    alert("Maker already exist")
                }else{ 
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    alert("Data is Add Successfully.")
                    location.reload();
                } 
                
            },function(er){
             
            })

        }


        $scope.UpdateShow = function(user){
            $scope.updatedata =user;
            $scope.showUpdate= true;
        }

        $scope.Update = function(){
          

            APIService.updateData({ req_url: PrefixUrl + "/vehicleMaker/update/"+$scope.updatedata._id,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                 if (res.data == true) {
                    alert("Maker already exist")
                }else{ 
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= false;
                    alert("Data is Updated Successfully.")
                    location.reload(); 
                }

            
            },function(er){
             
            })
        }


        $scope.UpdateRemove = function(object){
            if (confirm("Are you sure?")) {

              console.log('$scope.updatedata');
              console.log(object);

                APIService.updateData({ req_url: PrefixUrl + "/vehicleMaker/remove/"+object._id,data:$scope.updatedata}).then(function (res) {
                    console.log(res)
        
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= false;
                    alert("Data is Removed Successfully.")
                    location.reload(); 
                
                },function(er){
                 
                })
            }
        }


        $scope.getVehicleMakers();


})