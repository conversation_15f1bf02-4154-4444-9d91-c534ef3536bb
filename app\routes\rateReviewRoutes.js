import express from 'express';
import RateAndReviewController from '../controllers/rateAndReviewController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initrateReviewRoutes = () => {
  const rateReviewRoutes = express.Router();


  rateReviewRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  RateAndReviewController.show);
  rateReviewRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.page);
  rateReviewRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.create);
  rateReviewRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.update);
  rateReviewRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.remove);
  rateReviewRoutes.post('/reviewBy',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.reviewBy);
  rateReviewRoutes.delete('/removeReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.removeReview);
  rateReviewRoutes.post('/filterReview',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.filterReview);
  rateReviewRoutes.post('/getByPostMethod',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.getByPostMethod);
  rateReviewRoutes.post('/reviewCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.reviewCount);



  // rateReviewRoutes.get('/', RateAndReviewController.show);
  // rateReviewRoutes.get('/review/:id', RateAndReviewController.page);
  // rateReviewRoutes.post('/', RateAndReviewController.create);
  // rateReviewRoutes.put('/update/:vehicle_id', RateAndReviewController.update);
  // rateReviewRoutes.delete('/:vehicle_id', RateAndReviewController.remove);

  return rateReviewRoutes;
};

export default initrateReviewRoutes;
