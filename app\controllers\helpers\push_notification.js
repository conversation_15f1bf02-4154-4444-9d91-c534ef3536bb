const admin = require("firebase-admin");
const path = require("path");
const http = require("http");

// const path_to_private_key = "path/to/private_key.json";

const serviceAccountPath = path.resolve(
  __dirname,
  "../../..",
  "google-services.json"
);

// const fcm = new FCM(serviceAccountPath);

const serviceAccount = require(serviceAccountPath);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const sendNotificationOld = (tokens, data) => {
  console.log(" tokens ", tokens.length);
  // console.log(" tokens ", tokens);

  console.log(" payload ", data);

  const validTokens = tokens.filter((token) => token && token.trim() !== "");

  console.log("Valid tokens count:", validTokens.length);
  console.log("Valid tokens:", validTokens);
  console.log("Payload data:", data);

  if (validTokens.length === 0) {
    return Promise.reject(new Error("No valid device tokens found."));
  }

  const message = {
    notification: {
      title: data.title,
      body: data.body || data.title,
    },
    data: {
      sender: "Triva",
    },
    android: {
      priority: "high",
      notification: {
        sound: "jb",
        channelId: "test_channel",
      },
    },
    apns: {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          sound: "jb",
        },
      },
    },
    // tokens: validTokens, // List of device tokens
  };

  fcm
    .sendAll(validTokens, message)
    .then((e) => {
      console.log("Notification Sent ", e);
      console.log(" ===========================");
      console.log(e);
      console.log(" ================================================ ");
      console.log(e.responses[0].error);
    })
    .catch((err) => {
      console.log("Notification Failed ", err);
    });

  // admin.messaging().sendMulticast(message).then((e)=>{
  //   console.log("Notification Sent ",e);
  //   console.log(" ===========================")
  //   console.log(e);
  //   console.log(" ================================================ ")
  //   console.log(e.responses[0].error);

  // }).catch((err)=>{
  //   console.log("Notification Failed ",err);
  // });
};

const sendNotification = (tokens, payload, notification_sound) => {
  console.log(" tokens ", tokens.length);
  // console.log(" tokens ", tokens);

  console.log(" payload ", payload);

  const validTokens = tokens.filter((token) => token && token.trim() !== "");

  console.log("Valid tokens count:", validTokens.length);
  // console.log("Valid tokens:", validTokens);
  // console.log("Payload data:", payload);

  if (validTokens.length === 0) {
    return Promise.reject(new Error("No valid device tokens found."));
  }

  // Define the data to send
  const data = JSON.stringify({
    title: payload.title,
    body: payload.body || payload.title,
    data: payload.custom || {},
    tokens: validTokens,
    notification_sound: notification_sound!=undefined ? notification_sound:true,
  });

  // Set up the request options
  const options = {
    hostname: "localhost",
    port: 4000,
    path: "/send-notification",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Content-Length": data.length,
    },
  };

  // Make the request
  const req = http.request(options, (res) => {
    let responseData = "";

    // Receive data
    res.on("data", (chunk) => {
      responseData += chunk;
    });

    // Handle response end
    res.on("end", () => {
      console.log("Response:", responseData);
    });
  });

  // Handle error
  req.on("error", (error) => {
    console.error("Error:", error);
  });

  // Write data to request body
  req.write(data);

  // End the request
  req.end();
};
module.exports = {
  sendNotification,
};
