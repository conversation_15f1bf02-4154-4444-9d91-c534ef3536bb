/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.0.0-1 (2019-02-04)
 */
!function(){"use strict";var o=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},H=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}},q=function(e){return function(){return e}},$=function(e){return e};function d(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var e,t,n,r,i,a,u,s,c,l,f,m,g,p,h,v,b,y=function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,e)}},C=q(!1),x=q(!0),w=C,N=x,E=function(){return S},S=(r={fold:function(e,t){return e()},is:w,isSome:w,isNone:N,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:n,orThunk:t,map:E,ap:E,each:function(){},bind:E,flatten:E,exists:w,forall:N,filter:E,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:q("none()")},Object.freeze&&Object.freeze(r),r),k=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:N,isNone:w,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return k(e(n))},ap:function(e){return e.fold(E,function(e){return k(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:S},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(w,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},A={some:k,none:E,from:function(e){return null===e||e===undefined?S:k(e)}},T=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},R=T("string"),D=T("object"),B=T("array"),O=T("null"),_=T("boolean"),P=T("function"),I=T("number"),L=(i=Array.prototype.indexOf)===undefined?function(e,t){return X(e,t)}:function(e,t){return i.call(e,t)},M=function(e,t){return-1<L(e,t)},W=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},F=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},K=function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o,e)?n:r).push(a)}return{pass:n,fail:r}},U=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},z=function(e,t,n){return F(e,function(e){n=t(n,e)}),n},V=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return A.some(o)}return A.none()},j=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return A.some(n);return A.none()},X=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},Y=Array.prototype.push,G=function(e,t){return function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);Y.apply(t,e[n])}return t}(W(e,t))},J=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},Q=Array.prototype.slice,Z=function(e,t){return U(e,function(e){return!M(t,e)})},ee=function(e){return 0===e.length?A.none():A.some(e[0])},te=function(e){return 0===e.length?A.none():A.some(e[e.length-1])},ne=P(Array.from)?Array.from:function(e){return Q.call(e)},re="undefined"!=typeof window?window:Function("return this;")(),oe=function(e,t){return function(e,t){for(var n=t!==undefined&&null!==t?t:re,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n}(e.split("."),t)},ie={getOrDie:function(e,t){var n=oe(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}},ae=function(){return ie.getOrDie("URL")},ue={createObjectURL:function(e){return ae().createObjectURL(e)},revokeObjectURL:function(e){ae().revokeObjectURL(e)}},se=navigator,ce=se.userAgent,le=function(e){return"matchMedia"in window&&matchMedia(e).matches};g=/Android/.test(ce),u=(u=!(a=/WebKit/.test(ce))&&/MSIE/gi.test(ce)&&/Explorer/gi.test(se.appName))&&/MSIE (\w+)\./.exec(ce)[1],s=-1!==ce.indexOf("Trident/")&&(-1!==ce.indexOf("rv:")||-1!==se.appName.indexOf("Netscape"))&&11,c=-1!==ce.indexOf("Edge/")&&!u&&!s&&12,u=u||s||c,l=!a&&!s&&/Gecko/.test(ce),f=-1!==ce.indexOf("Mac"),m=/(iPad|iPhone)/.test(ce),p="FormData"in window&&"FileReader"in window&&"URL"in window&&!!ue.createObjectURL,h=le("only screen and (max-device-width: 480px)")&&(g||m),v=le("only screen and (min-width: 800px)")&&(g||m),b=-1!==ce.indexOf("Windows Phone"),c&&(a=!1);var fe,de={opera:!1,webkit:a,ie:u,gecko:l,mac:f,iOS:m,android:g,contentEditable:!m||p||534<=parseInt(ce.match(/AppleWebKit\/(\d*)/)[1],10),transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",caretAfter:8!==u,range:window.getSelection&&"Range"in window,documentMode:u&&!c?document.documentMode||7:10,fileApi:p,ceFalse:!1===u||8<u,cacheSuffix:null,container:null,overrideViewPort:null,experimentalShadowDom:!1,canHaveCSP:!1===u||11<u,desktop:!h&&!v,windowsPhone:b},me=window.Promise?window.Promise:function(){function r(e,t){return function(){e.apply(t,arguments)}}var e=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=function(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],l(e,r(o,this),r(u,this))},t=i.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)};function a(r){var o=this;null!==this._state?t(function(){var e=o._state?r.onFulfilled:r.onRejected;if(null!==e){var t;try{t=e(o._value)}catch(n){return void r.reject(n)}r.resolve(t)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void l(r(t,e),r(o,this),r(u,this))}this._state=!0,this._value=e,s.call(this)}catch(n){u.call(this,n)}}function u(e){this._state=!1,this._value=e,s.call(this)}function s(){for(var e=0,t=this._deferreds.length;e<t;e++)a.call(this,this._deferreds[e]);this._deferreds=null}function c(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function l(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(o){if(r)return;r=!0,n(o)}}return i.prototype["catch"]=function(e){return this.then(null,e)},i.prototype.then=function(n,r){var o=this;return new i(function(e,t){a.call(o,new c(n,r,e,t))})},i.all=function(){var s=Array.prototype.slice.call(1===arguments.length&&e(arguments[0])?arguments[0]:arguments);return new i(function(o,i){if(0===s.length)return o([]);var a=s.length;function u(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){u(t,e)},i)}s[t]=e,0==--a&&o(s)}catch(r){i(r)}}for(var e=0;e<s.length;e++)u(e,s[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i}(),ge=function(e,t){return"number"!=typeof t&&(t=0),setTimeout(e,t)},pe=function(e,t){return"number"!=typeof t&&(t=1),setInterval(e,t)},he=function(t,n){var r,e;return(e=function(){var e=arguments;clearTimeout(r),r=ge(function(){t.apply(this,e)},n)}).stop=function(){clearTimeout(r)},e},ve={requestAnimationFrame:function(e,t){fe?fe.then(e):fe=new me(function(e){t||(t=document.body),function(e,t){var n,r=window.requestAnimationFrame,o=["ms","moz","webkit"];for(n=0;n<o.length&&!r;n++)r=window[o[n]+"RequestAnimationFrame"];r||(r=function(e){window.setTimeout(e,0)}),r(e,t)}(e,t)}).then(e)},setTimeout:ge,setInterval:pe,setEditorTimeout:function(e,t,n){return ge(function(){e.removed||t()},n)},setEditorInterval:function(e,t,n){var r;return r=pe(function(){e.removed?clearInterval(r):t()},n)},debounce:he,throttle:he,clearInterval:function(e){return clearInterval(e)},clearTimeout:function(e){return clearTimeout(e)}},be=/^(?:mouse|contextmenu)|click/,ye={keyLocation:1,layerX:1,layerY:1,returnValue:1,webkitMovementX:1,webkitMovementY:1,keyIdentifier:1},Ce=function(){return!1},xe=function(){return!0},we=function(e,t,n,r){e.addEventListener?e.addEventListener(t,n,r||!1):e.attachEvent&&e.attachEvent("on"+t,n)},Ne=function(e,t,n,r){e.removeEventListener?e.removeEventListener(t,n,r||!1):e.detachEvent&&e.detachEvent("on"+t,n)},Ee=function(e,t){var n,r,o=t||{};for(n in e)ye[n]||(o[n]=e[n]);if(o.target||(o.target=o.srcElement||document),de.experimentalShadowDom&&(o.target=function(e,t){if(e.composedPath){var n=e.composedPath();if(n&&0<n.length)return n[0]}return t}(e,o.target)),e&&be.test(e.type)&&e.pageX===undefined&&e.clientX!==undefined){var i=o.target.ownerDocument||document,a=i.documentElement,u=i.body;o.pageX=e.clientX+(a&&a.scrollLeft||u&&u.scrollLeft||0)-(a&&a.clientLeft||u&&u.clientLeft||0),o.pageY=e.clientY+(a&&a.scrollTop||u&&u.scrollTop||0)-(a&&a.clientTop||u&&u.clientTop||0)}return o.preventDefault=function(){o.isDefaultPrevented=xe,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},o.stopPropagation=function(){o.isPropagationStopped=xe,e&&(e.stopPropagation?e.stopPropagation():e.cancelBubble=!0)},!(o.stopImmediatePropagation=function(){o.isImmediatePropagationStopped=xe,o.stopPropagation()})==((r=o).isDefaultPrevented===xe||r.isDefaultPrevented===Ce)&&(o.isDefaultPrevented=Ce,o.isPropagationStopped=Ce,o.isImmediatePropagationStopped=Ce),"undefined"==typeof o.metaKey&&(o.metaKey=!1),o},Se=function(e,t,n){var r=e.document,o={type:"ready"};if(n.domLoaded)t(o);else{var i=function(){return"complete"===r.readyState||"interactive"===r.readyState&&r.body},a=function(){Ne(e,"DOMContentLoaded",a),Ne(e,"load",a),n.domLoaded||(n.domLoaded=!0,t(o))},u=function(){i()&&(Ne(r,"readystatechange",u),a())},s=function(){try{r.documentElement.doScroll("left")}catch(e){return void ve.setTimeout(s)}a()};!r.addEventListener||de.ie&&de.ie<11?(we(r,"readystatechange",u),r.documentElement.doScroll&&e.self===e.top&&s()):i()?a():we(e,"DOMContentLoaded",a),we(e,"load",a)}},ke=function(){var m,g,p,h,v,b=this,y={};g="mce-data-"+(+new Date).toString(32),h="onmouseenter"in document.documentElement,p="onfocusin"in document.documentElement,v={mouseenter:"mouseover",mouseleave:"mouseout"},m=1,b.domLoaded=!1,b.events=y;var C=function(e,t){var n,r,o,i,a=y[t];if(n=a&&a[e.type])for(r=0,o=n.length;r<o;r++)if((i=n[r])&&!1===i.func.call(i.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return};b.bind=function(e,t,n,r){var o,i,a,u,s,c,l,f=window,d=function(e){C(Ee(e||f.event),o)};if(e&&3!==e.nodeType&&8!==e.nodeType){for(e[g]?o=e[g]:(o=m++,e[g]=o,y[o]={}),r=r||e,a=(t=t.split(" ")).length;a--;)c=d,s=l=!1,"DOMContentLoaded"===(u=t[a])&&(u="ready"),b.domLoaded&&"ready"===u&&"complete"===e.readyState?n.call(r,Ee({type:u})):(h||(s=v[u])&&(c=function(e){var t,n;if(t=e.currentTarget,(n=e.relatedTarget)&&t.contains)n=t.contains(n);else for(;n&&n!==t;)n=n.parentNode;n||((e=Ee(e||f.event)).type="mouseout"===e.type?"mouseleave":"mouseenter",e.target=t,C(e,o))}),p||"focusin"!==u&&"focusout"!==u||(l=!0,s="focusin"===u?"focus":"blur",c=function(e){(e=Ee(e||f.event)).type="focus"===e.type?"focusin":"focusout",C(e,o)}),(i=y[o][u])?"ready"===u&&b.domLoaded?n({type:u}):i.push({func:n,scope:r}):(y[o][u]=i=[{func:n,scope:r}],i.fakeName=s,i.capture=l,i.nativeHandler=c,"ready"===u?Se(e,c,b):we(e,s||u,c,l)));return e=i=0,n}},b.unbind=function(e,t,n){var r,o,i,a,u,s;if(!e||3===e.nodeType||8===e.nodeType)return b;if(r=e[g]){if(s=y[r],t){for(i=(t=t.split(" ")).length;i--;)if(o=s[u=t[i]]){if(n)for(a=o.length;a--;)if(o[a].func===n){var c=o.nativeHandler,l=o.fakeName,f=o.capture;(o=o.slice(0,a).concat(o.slice(a+1))).nativeHandler=c,o.fakeName=l,o.capture=f,s[u]=o}n&&0!==o.length||(delete s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture))}}else{for(u in s)o=s[u],Ne(e,o.fakeName||u,o.nativeHandler,o.capture);s={}}for(u in s)return b;delete y[r];try{delete e[g]}catch(d){e[g]=null}}return b},b.fire=function(e,t,n){var r;if(!e||3===e.nodeType||8===e.nodeType)return b;for((n=Ee(null,n)).type=t,n.target=e;(r=e[g])&&C(n,r),(e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow)&&!n.isPropagationStopped(););return b},b.clean=function(e){var t,n,r=b.unbind;if(!e||3===e.nodeType||8===e.nodeType)return b;if(e[g]&&r(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName)for(r(e),t=(n=e.getElementsByTagName("*")).length;t--;)(e=n[t])[g]&&r(e);return b},b.destroy=function(){y={}},b.cancel=function(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}};ke.Event=new ke,ke.Event.bind(window,"ready",function(){});var Te,Ae,Re,De,Be,Oe,_e,Pe,Ie,Le,Me,Fe,Ue,ze,Ve,je,He,qe,$e="sizzle"+-new Date,We=window.document,Ke=0,Xe=0,Ye=Tt(),Ge=Tt(),Je=Tt(),Qe=function(e,t){return e===t&&(Me=!0),0},Ze=typeof undefined,et={}.hasOwnProperty,tt=[],nt=tt.pop,rt=tt.push,ot=tt.push,it=tt.slice,at=tt.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},ut="[\\x20\\t\\r\\n\\f]",st="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ct="\\["+ut+"*("+st+")(?:"+ut+"*([*^$|!~]?=)"+ut+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+st+"))|)"+ut+"*\\]",lt=":("+st+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ct+")*)|.*)\\)|)",ft=new RegExp("^"+ut+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ut+"+$","g"),dt=new RegExp("^"+ut+"*,"+ut+"*"),mt=new RegExp("^"+ut+"*([>+~]|"+ut+")"+ut+"*"),gt=new RegExp("="+ut+"*([^\\]'\"]*?)"+ut+"*\\]","g"),pt=new RegExp(lt),ht=new RegExp("^"+st+"$"),vt={ID:new RegExp("^#("+st+")"),CLASS:new RegExp("^\\.("+st+")"),TAG:new RegExp("^("+st+"|[*])"),ATTR:new RegExp("^"+ct),PSEUDO:new RegExp("^"+lt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ut+"*(even|odd|(([+-]|)(\\d*)n|)"+ut+"*(?:([+-]|)"+ut+"*(\\d+)|))"+ut+"*\\)|)","i"),bool:new RegExp("^(?:checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped)$","i"),needsContext:new RegExp("^"+ut+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ut+"*((?:-\\d)?\\d*)"+ut+"*\\)|)(?=[^-]|$)","i")},bt=/^(?:input|select|textarea|button)$/i,yt=/^h\d$/i,Ct=/^[^{]+\{\s*\[native \w/,xt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,wt=/[+~]/,Nt=/'|\\/g,Et=new RegExp("\\\\([\\da-f]{1,6}"+ut+"?|("+ut+")|.)","ig"),St=function(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(r+65536):String.fromCharCode(r>>10|55296,1023&r|56320)};try{ot.apply(tt=it.call(We.childNodes),We.childNodes),tt[We.childNodes.length].nodeType}catch($N){ot={apply:tt.length?function(e,t){rt.apply(e,it.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}var kt=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m;if((t?t.ownerDocument||t:We)!==Ue&&Fe(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(u=(t=t||Ue).nodeType)&&9!==u)return[];if(Ve&&!r){if(o=xt.exec(e))if(a=o[1]){if(9===u){if(!(i=t.getElementById(a))||!i.parentNode)return n;if(i.id===a)return n.push(i),n}else if(t.ownerDocument&&(i=t.ownerDocument.getElementById(a))&&qe(t,i)&&i.id===a)return n.push(i),n}else{if(o[2])return ot.apply(n,t.getElementsByTagName(e)),n;if((a=o[3])&&Ae.getElementsByClassName)return ot.apply(n,t.getElementsByClassName(a)),n}if(Ae.qsa&&(!je||!je.test(e))){if(f=l=$e,d=t,m=9===u&&e,1===u&&"object"!==t.nodeName.toLowerCase()){for(c=Oe(e),(l=t.getAttribute("id"))?f=l.replace(Nt,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",s=c.length;s--;)c[s]=f+It(c[s]);d=wt.test(e)&&_t(t.parentNode)||t,m=c.join(",")}if(m)try{return ot.apply(n,d.querySelectorAll(m)),n}catch(g){}finally{l||t.removeAttribute("id")}}}return Pe(e.replace(ft,"$1"),t,n,r)};function Tt(){var n=[];return function r(e,t){return n.push(e+" ")>Re.cacheLength&&delete r[n.shift()],r[e+" "]=t}}function At(e){return e[$e]=!0,e}function Rt(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function Dt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function Bt(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function Ot(a){return At(function(i){return i=+i,At(function(e,t){for(var n,r=a([],e.length,i),o=r.length;o--;)e[n=r[o]]&&(e[n]=!(t[n]=e[n]))})})}function _t(e){return e&&typeof e.getElementsByTagName!==Ze&&e}for(Te in Ae=kt.support={},Be=kt.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},Fe=kt.setDocument=function(e){var t,s=e?e.ownerDocument||e:We,n=s.defaultView;return s!==Ue&&9===s.nodeType&&s.documentElement?(ze=(Ue=s).documentElement,Ve=!Be(s),n&&n!==function r(e){try{return e.top}catch(t){}return null}(n)&&(n.addEventListener?n.addEventListener("unload",function(){Fe()},!1):n.attachEvent&&n.attachEvent("onunload",function(){Fe()})),Ae.attributes=!0,Ae.getElementsByTagName=!0,Ae.getElementsByClassName=Ct.test(s.getElementsByClassName),Ae.getById=!0,Re.find.ID=function(e,t){if(typeof t.getElementById!==Ze&&Ve){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},Re.filter.ID=function(e){var t=e.replace(Et,St);return function(e){return e.getAttribute("id")===t}},Re.find.TAG=Ae.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==Ze)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],o=0,i=t.getElementsByTagName(e);if("*"!==e)return i;for(;n=i[o++];)1===n.nodeType&&r.push(n);return r},Re.find.CLASS=Ae.getElementsByClassName&&function(e,t){if(Ve)return t.getElementsByClassName(e)},He=[],je=[],Ae.disconnectedMatch=!0,je=je.length&&new RegExp(je.join("|")),He=He.length&&new RegExp(He.join("|")),t=Ct.test(ze.compareDocumentPosition),qe=t||Ct.test(ze.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):e.compareDocumentPosition&&16&e.compareDocumentPosition(r)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},Qe=t?function(e,t){if(e===t)return Me=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!Ae.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument===We&&qe(We,e)?-1:t===s||t.ownerDocument===We&&qe(We,t)?1:Le?at.call(Le,e)-at.call(Le,t):0:4&n?-1:1)}:function(e,t){if(e===t)return Me=!0,0;var n,r=0,o=e.parentNode,i=t.parentNode,a=[e],u=[t];if(!o||!i)return e===s?-1:t===s?1:o?-1:i?1:Le?at.call(Le,e)-at.call(Le,t):0;if(o===i)return Rt(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)u.unshift(n);for(;a[r]===u[r];)r++;return r?Rt(a[r],u[r]):a[r]===We?-1:u[r]===We?1:0},s):Ue},kt.matches=function(e,t){return kt(e,null,null,t)},kt.matchesSelector=function(e,t){if((e.ownerDocument||e)!==Ue&&Fe(e),t=t.replace(gt,"='$1']"),Ae.matchesSelector&&Ve&&(!He||!He.test(t))&&(!je||!je.test(t)))try{var n=(void 0).call(e,t);if(n||Ae.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch($N){}return 0<kt(t,Ue,null,[e]).length},kt.contains=function(e,t){return(e.ownerDocument||e)!==Ue&&Fe(e),qe(e,t)},kt.attr=function(e,t){(e.ownerDocument||e)!==Ue&&Fe(e);var n=Re.attrHandle[t.toLowerCase()],r=n&&et.call(Re.attrHandle,t.toLowerCase())?n(e,t,!Ve):undefined;return r!==undefined?r:Ae.attributes||!Ve?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},kt.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},kt.uniqueSort=function(e){var t,n=[],r=0,o=0;if(Me=!Ae.detectDuplicates,Le=!Ae.sortStable&&e.slice(0),e.sort(Qe),Me){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)e.splice(n[r],1)}return Le=null,e},De=kt.getText=function(e){var t,n="",r=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=De(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[r++];)n+=De(t);return n},(Re=kt.selectors={cacheLength:50,createPseudo:At,match:vt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Et,St),e[3]=(e[3]||e[4]||e[5]||"").replace(Et,St),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||kt.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&kt.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return vt.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pt.test(n)&&(t=Oe(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Et,St).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=Ye[e+" "];return t||(t=new RegExp("(^|"+ut+")"+e+"("+ut+"|$)"))&&Ye(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==Ze&&e.getAttribute("class")||"")})},ATTR:function(n,r,o){return function(e){var t=kt.attr(e,n);return null==t?"!="===r:!r||(t+="","="===r?t===o:"!="===r?t!==o:"^="===r?o&&0===t.indexOf(o):"*="===r?o&&-1<t.indexOf(o):"$="===r?o&&t.slice(-o.length)===o:"~="===r?-1<(" "+t+" ").indexOf(o):"|="===r&&(t===o||t.slice(0,o.length+1)===o+"-"))}},CHILD:function(m,e,t,g,p){var h="nth"!==m.slice(0,3),v="last"!==m.slice(-4),b="of-type"===e;return 1===g&&0===p?function(e){return!!e.parentNode}:function(e,t,n){var r,o,i,a,u,s,c=h!==v?"nextSibling":"previousSibling",l=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b;if(l){if(h){for(;c;){for(i=e;i=i[c];)if(b?i.nodeName.toLowerCase()===f:1===i.nodeType)return!1;s=c="only"===m&&!s&&"nextSibling"}return!0}if(s=[v?l.firstChild:l.lastChild],v&&d){for(u=(r=(o=l[$e]||(l[$e]={}))[m]||[])[0]===Ke&&r[1],a=r[0]===Ke&&r[2],i=u&&l.childNodes[u];i=++u&&i&&i[c]||(a=u=0)||s.pop();)if(1===i.nodeType&&++a&&i===e){o[m]=[Ke,u,a];break}}else if(d&&(r=(e[$e]||(e[$e]={}))[m])&&r[0]===Ke)a=r[1];else for(;(i=++u&&i&&i[c]||(a=u=0)||s.pop())&&((b?i.nodeName.toLowerCase()!==f:1!==i.nodeType)||!++a||(d&&((i[$e]||(i[$e]={}))[m]=[Ke,a]),i!==e)););return(a-=p)===g||a%g==0&&0<=a/g}}},PSEUDO:function(e,i){var t,a=Re.pseudos[e]||Re.setFilters[e.toLowerCase()]||kt.error("unsupported pseudo: "+e);return a[$e]?a(i):1<a.length?(t=[e,e,"",i],Re.setFilters.hasOwnProperty(e.toLowerCase())?At(function(e,t){for(var n,r=a(e,i),o=r.length;o--;)e[n=at.call(e,r[o])]=!(t[n]=r[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:At(function(e){var r=[],o=[],u=_e(e.replace(ft,"$1"));return u[$e]?At(function(e,t,n,r){for(var o,i=u(e,null,r,[]),a=e.length;a--;)(o=i[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return r[0]=e,u(r,null,n,o),!o.pop()}}),has:At(function(t){return function(e){return 0<kt(t,e).length}}),contains:At(function(t){return t=t.replace(Et,St),function(e){return-1<(e.textContent||e.innerText||De(e)).indexOf(t)}}),lang:At(function(n){return ht.test(n||"")||kt.error("unsupported lang: "+n),n=n.replace(Et,St).toLowerCase(),function(e){var t;do{if(t=Ve?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=window.location&&window.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===ze},focus:function(e){return e===Ue.activeElement&&(!Ue.hasFocus||Ue.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!Re.pseudos.empty(e)},header:function(e){return yt.test(e.nodeName)},input:function(e){return bt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:Ot(function(){return[0]}),last:Ot(function(e,t){return[t-1]}),eq:Ot(function(e,t,n){return[n<0?n+t:n]}),even:Ot(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:Ot(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:Ot(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:Ot(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=Re.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})Re.pseudos[Te]=Dt(Te);for(Te in{submit:!0,reset:!0})Re.pseudos[Te]=Bt(Te);function Pt(){}function It(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function Lt(a,e,t){var u=e.dir,s=t&&"parentNode"===u,c=Xe++;return e.first?function(e,t,n){for(;e=e[u];)if(1===e.nodeType||s)return a(e,t,n)}:function(e,t,n){var r,o,i=[Ke,c];if(n){for(;e=e[u];)if((1===e.nodeType||s)&&a(e,t,n))return!0}else for(;e=e[u];)if(1===e.nodeType||s){if((r=(o=e[$e]||(e[$e]={}))[u])&&r[0]===Ke&&r[1]===c)return i[2]=r[2];if((o[u]=i)[2]=a(e,t,n))return!0}}}function Mt(o){return 1<o.length?function(e,t,n){for(var r=o.length;r--;)if(!o[r](e,t,n))return!1;return!0}:o[0]}function Ft(e,t,n,r,o){for(var i,a=[],u=0,s=e.length,c=null!=t;u<s;u++)(i=e[u])&&(n&&!n(i,r,o)||(a.push(i),c&&t.push(u)));return a}function Ut(g,p,h,v,b,e){return v&&!v[$e]&&(v=Ut(v)),b&&!b[$e]&&(b=Ut(b,e)),At(function(e,t,n,r){var o,i,a,u=[],s=[],c=t.length,l=e||function m(e,t,n){for(var r=0,o=t.length;r<o;r++)kt(e,t[r],n);return n}(p||"*",n.nodeType?[n]:n,[]),f=!g||!e&&p?l:Ft(l,u,g,n,r),d=h?b||(e?g:c||v)?[]:t:f;if(h&&h(f,d,n,r),v)for(o=Ft(d,s),v(o,[],n,r),i=o.length;i--;)(a=o[i])&&(d[s[i]]=!(f[s[i]]=a));if(e){if(b||g){if(b){for(o=[],i=d.length;i--;)(a=d[i])&&o.push(f[i]=a);b(null,d=[],o,r)}for(i=d.length;i--;)(a=d[i])&&-1<(o=b?at.call(e,a):u[i])&&(e[o]=!(t[o]=a))}}else d=Ft(d===t?d.splice(c,d.length):d),b?b(null,t,d,r):ot.apply(t,d)})}function zt(e){for(var r,t,n,o=e.length,i=Re.relative[e[0].type],a=i||Re.relative[" "],u=i?1:0,s=Lt(function(e){return e===r},a,!0),c=Lt(function(e){return-1<at.call(r,e)},a,!0),l=[function(e,t,n){return!i&&(n||t!==Ie)||((r=t).nodeType?s(e,t,n):c(e,t,n))}];u<o;u++)if(t=Re.relative[e[u].type])l=[Lt(Mt(l),t)];else{if((t=Re.filter[e[u].type].apply(null,e[u].matches))[$e]){for(n=++u;n<o&&!Re.relative[e[n].type];n++);return Ut(1<u&&Mt(l),1<u&&It(e.slice(0,u-1).concat({value:" "===e[u-2].type?"*":""})).replace(ft,"$1"),t,u<n&&zt(e.slice(u,n)),n<o&&zt(e=e.slice(n)),n<o&&It(e))}l.push(t)}return Mt(l)}Pt.prototype=Re.filters=Re.pseudos,Re.setFilters=new Pt,Oe=kt.tokenize=function(e,t){var n,r,o,i,a,u,s,c=Ge[e+" "];if(c)return t?0:c.slice(0);for(a=e,u=[],s=Re.preFilter;a;){for(i in n&&!(r=dt.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(o=[])),n=!1,(r=mt.exec(a))&&(n=r.shift(),o.push({value:n,type:r[0].replace(ft," ")}),a=a.slice(n.length)),Re.filter)!(r=vt[i].exec(a))||s[i]&&!(r=s[i](r))||(n=r.shift(),o.push({value:n,type:i,matches:r}),a=a.slice(n.length));if(!n)break}return t?a.length:a?kt.error(e):Ge(e,u).slice(0)},_e=kt.compile=function(e,t){var n,r=[],o=[],i=Je[e+" "];if(!i){for(t||(t=Oe(e)),n=t.length;n--;)(i=zt(t[n]))[$e]?r.push(i):o.push(i);(i=Je(e,function a(h,v){var b=0<v.length,y=0<h.length,e=function(e,t,n,r,o){var i,a,u,s=0,c="0",l=e&&[],f=[],d=Ie,m=e||y&&Re.find.TAG("*",o),g=Ke+=null==d?1:Math.random()||.1,p=m.length;for(o&&(Ie=t!==Ue&&t);c!==p&&null!=(i=m[c]);c++){if(y&&i){for(a=0;u=h[a++];)if(u(i,t,n)){r.push(i);break}o&&(Ke=g)}b&&((i=!u&&i)&&s--,e&&l.push(i))}if(s+=c,b&&c!==s){for(a=0;u=v[a++];)u(l,f,t,n);if(e){if(0<s)for(;c--;)l[c]||f[c]||(f[c]=nt.call(r));f=Ft(f)}ot.apply(r,f),o&&!e&&0<f.length&&1<s+v.length&&kt.uniqueSort(r)}return o&&(Ke=g,Ie=d),l};return b?At(e):e}(o,r))).selector=e}return i},Pe=kt.select=function(e,t,n,r){var o,i,a,u,s,c="function"==typeof e&&e,l=!r&&Oe(e=c.selector||e);if(n=n||[],1===l.length){if(2<(i=l[0]=l[0].slice(0)).length&&"ID"===(a=i[0]).type&&Ae.getById&&9===t.nodeType&&Ve&&Re.relative[i[1].type]){if(!(t=(Re.find.ID(a.matches[0].replace(Et,St),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(i.shift().value.length)}for(o=vt.needsContext.test(e)?0:i.length;o--&&(a=i[o],!Re.relative[u=a.type]);)if((s=Re.find[u])&&(r=s(a.matches[0].replace(Et,St),wt.test(i[0].type)&&_t(t.parentNode)||t))){if(i.splice(o,1),!(e=r.length&&It(i)))return ot.apply(n,r),n;break}}return(c||_e(e,l))(r,t,!Ve,n,wt.test(e)&&_t(t.parentNode)||t),n},Ae.sortStable=$e.split("").sort(Qe).join("")===$e,Ae.detectDuplicates=!!Me,Fe(),Ae.sortDetached=!0;var Vt=Array.isArray,jt=function(e,t,n){var r,o;if(!e)return 0;if(n=n||e,e.length!==undefined){for(r=0,o=e.length;r<o;r++)if(!1===t.call(n,e[r],r,e))return 0}else for(r in e)if(e.hasOwnProperty(r)&&!1===t.call(n,e[r],r,e))return 0;return 1},Ht=function(e,t,n){var r,o;for(r=0,o=e.length;r<o;r++)if(t.call(n,e[r],r,e))return r;return-1},qt={isArray:Vt,toArray:function(e){var t,n,r=e;if(!Vt(e))for(r=[],t=0,n=e.length;t<n;t++)r[t]=e[t];return r},each:jt,map:function(n,r){var o=[];return jt(n,function(e,t){o.push(r(e,t,n))}),o},filter:function(n,r){var o=[];return jt(n,function(e,t){r&&!r(e,t,n)||o.push(e)}),o},indexOf:function(e,t){var n,r;if(e)for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},reduce:function(e,t,n,r){var o=0;for(arguments.length<3&&(n=e[0]);o<e.length;o++)n=t.call(r,n,e[o],o);return n},findIndex:Ht,find:function(e,t,n){var r=Ht(e,t,n);return-1!==r?e[r]:undefined},last:function(e){return e[e.length-1]}},$t=/^\s*|\s*$/g,Wt=function(e){return null===e||e===undefined?"":(""+e).replace($t,"")},Kt=function(e,t){return t?!("array"!==t||!qt.isArray(e))||typeof e===t:e!==undefined},Xt=function(e,n,r,o){o=o||this,e&&(r&&(e=e[r]),qt.each(e,function(e,t){if(!1===n.call(o,e,t,r))return!1;Xt(e,n,r,o)}))},Yt={trim:Wt,isArray:qt.isArray,is:Kt,toArray:qt.toArray,makeMap:function(e,t,n){var r;for(t=t||",","string"==typeof(e=e||[])&&(e=e.split(t)),n=n||{},r=e.length;r--;)n[e[r]]={};return n},each:qt.each,map:qt.map,grep:qt.filter,inArray:qt.indexOf,hasOwn:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},extend:function(e,t){for(var n,r,o,i=[],a=2;a<arguments.length;a++)i[a-2]=arguments[a];var u,s=arguments;for(n=1,r=s.length;n<r;n++)for(o in t=s[n])t.hasOwnProperty(o)&&(u=t[o])!==undefined&&(e[o]=u);return e},create:function(e,t,n){var r,o,i,a,u,s=this,c=0;if(e=/^((static) )?([\w.]+)(:([\w.]+))?/.exec(e),i=e[3].match(/(^|\.)(\w+)$/i)[2],!(o=s.createNS(e[3].replace(/\.\w+$/,""),n))[i]){if("static"===e[2])return o[i]=t,void(this.onCreate&&this.onCreate(e[2],e[3],o[i]));t[i]||(t[i]=function(){},c=1),o[i]=t[i],s.extend(o[i].prototype,t),e[5]&&(r=s.resolve(e[5]).prototype,a=e[5].match(/\.(\w+)$/i)[1],u=o[i],o[i]=c?function(){return r[a].apply(this,arguments)}:function(){return this.parent=r[a],u.apply(this,arguments)},o[i].prototype[i]=o[i],s.each(r,function(e,t){o[i].prototype[t]=r[t]}),s.each(t,function(e,t){r[t]?o[i].prototype[t]=function(){return this.parent=r[t],e.apply(this,arguments)}:t!==i&&(o[i].prototype[t]=e)})),s.each(t["static"],function(e,t){o[i][t]=e})}},walk:Xt,createNS:function(e,t){var n,r;for(t=t||window,e=e.split("."),n=0;n<e.length;n++)t[r=e[n]]||(t[r]={}),t=t[r];return t},resolve:function(e,t){var n,r;for(t=t||window,n=0,r=(e=e.split(".")).length;n<r&&(t=t[e[n]]);n++);return t},explode:function(e,t){return!e||Kt(e,"array")?e:qt.map(e.split(t||","),Wt)},_addCacheSuffix:function(e){var t=de.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Gt=document,Jt=Array.prototype.push,Qt=Array.prototype.slice,Zt=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,en=ke.Event,tn=Yt.makeMap("children,contents,next,prev"),nn=function(e){return void 0!==e},rn=function(e){return"string"==typeof e},on=function(e,t){var n,r,o;for(o=(t=t||Gt).createElement("div"),n=t.createDocumentFragment(),o.innerHTML=e;r=o.firstChild;)n.appendChild(r);return n},an=function(e,t,n,r){var o;if(rn(t))t=on(t,Cn(e[0]));else if(t.length&&!t.nodeType){if(t=pn.makeArray(t),r)for(o=t.length-1;0<=o;o--)an(e,t[o],n,r);else for(o=0;o<t.length;o++)an(e,t[o],n,r);return e}if(t.nodeType)for(o=e.length;o--;)n.call(e[o],t);return e},un=function(e,t){return e&&t&&-1!==(" "+e.className+" ").indexOf(" "+t+" ")},sn=function(e,t,n){var r,o;return t=pn(t)[0],e.each(function(){var e=this;n&&r===e.parentNode||(r=e.parentNode,o=t.cloneNode(!1),e.parentNode.insertBefore(o,e)),o.appendChild(e)}),e},cn=Yt.makeMap("fillOpacity fontWeight lineHeight opacity orphans widows zIndex zoom"," "),ln=Yt.makeMap("checked compact declare defer disabled ismap multiple nohref noshade nowrap readonly selected"," "),fn={"for":"htmlFor","class":"className",readonly:"readOnly"},dn={"float":"cssFloat"},mn={},gn={},pn=function(e,t){return new pn.fn.init(e,t)},hn=/^\s*|\s*$/g,vn=function(e){return null===e||e===undefined?"":(""+e).replace(hn,"")},bn=function(e,t){var n,r,o,i;if(e)if((n=e.length)===undefined){for(r in e)if(e.hasOwnProperty(r)&&(i=e[r],!1===t.call(i,r,i)))break}else for(o=0;o<n&&(i=e[o],!1!==t.call(i,o,i));o++);return e},yn=function(e,n){var r=[];return bn(e,function(e,t){n(t,e)&&r.push(t)}),r},Cn=function(e){return e?9===e.nodeType?e:e.ownerDocument:Gt};pn.fn=pn.prototype={constructor:pn,selector:"",context:null,length:0,init:function(e,t){var n,r,o=this;if(!e)return o;if(e.nodeType)return o.context=o[0]=e,o.length=1,o;if(t&&t.nodeType)o.context=t;else{if(t)return pn(e).attr(t);o.context=t=document}if(rn(e)){if(!(n="<"===(o.selector=e).charAt(0)&&">"===e.charAt(e.length-1)&&3<=e.length?[null,e,null]:Zt.exec(e)))return pn(t).find(e);if(n[1])for(r=on(e,Cn(t)).firstChild;r;)Jt.call(o,r),r=r.nextSibling;else{if(!(r=Cn(t).getElementById(n[2])))return o;if(r.id!==n[2])return o.find(e);o.length=1,o[0]=r}}else this.add(e,!1);return o},toArray:function(){return Yt.toArray(this)},add:function(e,t){var n,r,o=this;if(rn(e))return o.add(pn(e));if(!1!==t)for(n=pn.unique(o.toArray().concat(pn.makeArray(e))),o.length=n.length,r=0;r<n.length;r++)o[r]=n[r];else Jt.apply(o,pn.makeArray(e));return o},attr:function(t,n){var e,r=this;if("object"==typeof t)bn(t,function(e,t){r.attr(e,t)});else{if(!nn(n)){if(r[0]&&1===r[0].nodeType){if((e=mn[t])&&e.get)return e.get(r[0],t);if(ln[t])return r.prop(t)?t:undefined;null===(n=r[0].getAttribute(t,2))&&(n=undefined)}return n}this.each(function(){var e;if(1===this.nodeType){if((e=mn[t])&&e.set)return void e.set(this,n);null===n?this.removeAttribute(t,2):this.setAttribute(t,n,2)}})}return r},removeAttr:function(e){return this.attr(e,null)},prop:function(e,t){var n=this;if("object"==typeof(e=fn[e]||e))bn(e,function(e,t){n.prop(e,t)});else{if(!nn(t))return n[0]&&n[0].nodeType&&e in n[0]?n[0][e]:t;this.each(function(){1===this.nodeType&&(this[e]=t)})}return n},css:function(n,r){var e,o,i=this,t=function(e){return e.replace(/-(\D)/g,function(e,t){return t.toUpperCase()})},a=function(e){return e.replace(/[A-Z]/g,function(e){return"-"+e})};if("object"==typeof n)bn(n,function(e,t){i.css(e,t)});else if(nn(r))n=t(n),"number"!=typeof r||cn[n]||(r=r.toString()+"px"),i.each(function(){var e=this.style;if((o=gn[n])&&o.set)o.set(this,r);else{try{this.style[dn[n]||n]=r}catch(t){}null!==r&&""!==r||(e.removeProperty?e.removeProperty(a(n)):e.removeAttribute(n))}});else{if(e=i[0],(o=gn[n])&&o.get)return o.get(e);if(!e.ownerDocument.defaultView)return e.currentStyle?e.currentStyle[t(n)]:"";try{return e.ownerDocument.defaultView.getComputedStyle(e,null).getPropertyValue(a(n))}catch(u){return undefined}}return i},remove:function(){for(var e,t=this.length;t--;)e=this[t],en.clean(e),e.parentNode&&e.parentNode.removeChild(e);return this},empty:function(){for(var e,t=this.length;t--;)for(e=this[t];e.firstChild;)e.removeChild(e.firstChild);return this},html:function(e){var t,n=this;if(nn(e)){t=n.length;try{for(;t--;)n[t].innerHTML=e}catch(r){pn(n[t]).empty().append(e)}return n}return n[0]?n[0].innerHTML:""},text:function(e){var t,n=this;if(nn(e)){for(t=n.length;t--;)"innerText"in n[t]?n[t].innerText=e:n[0].textContent=e;return n}return n[0]?n[0].innerText||n[0].textContent:""},append:function(){return an(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.appendChild(e)})},prepend:function(){return an(this,arguments,function(e){(1===this.nodeType||this.host&&1===this.host.nodeType)&&this.insertBefore(e,this.firstChild)},!0)},before:function(){return this[0]&&this[0].parentNode?an(this,arguments,function(e){this.parentNode.insertBefore(e,this)}):this},after:function(){return this[0]&&this[0].parentNode?an(this,arguments,function(e){this.parentNode.insertBefore(e,this.nextSibling)},!0):this},appendTo:function(e){return pn(e).append(this),this},prependTo:function(e){return pn(e).prepend(this),this},replaceWith:function(e){return this.before(e).remove()},wrap:function(e){return sn(this,e)},wrapAll:function(e){return sn(this,e,!0)},wrapInner:function(e){return this.each(function(){pn(this).contents().wrapAll(e)}),this},unwrap:function(){return this.parent().each(function(){pn(this).replaceWith(this.childNodes)})},clone:function(){var e=[];return this.each(function(){e.push(this.cloneNode(!0))}),pn(e)},addClass:function(e){return this.toggleClass(e,!0)},removeClass:function(e){return this.toggleClass(e,!1)},toggleClass:function(o,i){var e=this;return"string"!=typeof o||(-1!==o.indexOf(" ")?bn(o.split(" "),function(){e.toggleClass(this,i)}):e.each(function(e,t){var n,r;(r=un(t,o))!==i&&(n=t.className,r?t.className=vn((" "+n+" ").replace(" "+o+" "," ")):t.className+=n?" "+o:o)})),e},hasClass:function(e){return un(this[0],e)},each:function(e){return bn(this,e)},on:function(e,t){return this.each(function(){en.bind(this,e,t)})},off:function(e,t){return this.each(function(){en.unbind(this,e,t)})},trigger:function(e){return this.each(function(){"object"==typeof e?en.fire(this,e.type,e):en.fire(this,e)})},show:function(){return this.css("display","")},hide:function(){return this.css("display","none")},slice:function(){return new pn(Qt.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},find:function(e){var t,n,r=[];for(t=0,n=this.length;t<n;t++)pn.find(e,this[t],r);return pn(r)},filter:function(n){return pn("function"==typeof n?yn(this.toArray(),function(e,t){return n(t,e)}):pn.filter(n,this.toArray()))},closest:function(n){var r=[];return n instanceof pn&&(n=n[0]),this.each(function(e,t){for(;t;){if("string"==typeof n&&pn(t).is(n)){r.push(t);break}if(t===n){r.push(t);break}t=t.parentNode}}),pn(r)},offset:function(e){var t,n,r,o,i=0,a=0;return e?this.css(e):((t=this[0])&&(r=(n=t.ownerDocument).documentElement,t.getBoundingClientRect&&(i=(o=t.getBoundingClientRect()).left+(r.scrollLeft||n.body.scrollLeft)-r.clientLeft,a=o.top+(r.scrollTop||n.body.scrollTop)-r.clientTop)),{left:i,top:a})},push:Jt,sort:[].sort,splice:[].splice},Yt.extend(pn,{extend:Yt.extend,makeArray:function(e){return(t=e)&&t===t.window||e.nodeType?[e]:Yt.toArray(e);var t},inArray:function(e,t){var n;if(t.indexOf)return t.indexOf(e);for(n=t.length;n--;)if(t[n]===e)return n;return-1},isArray:Yt.isArray,each:bn,trim:vn,grep:yn,find:kt,expr:kt.selectors,unique:kt.uniqueSort,text:kt.getText,contains:kt.contains,filter:function(e,t,n){var r=t.length;for(n&&(e=":not("+e+")");r--;)1!==t[r].nodeType&&t.splice(r,1);return t=1===t.length?pn.find.matchesSelector(t[0],e)?[t[0]]:[]:pn.find.matches(e,t)}});var xn=function(e,t,n){var r=[],o=e[t];for("string"!=typeof n&&n instanceof pn&&(n=n[0]);o&&9!==o.nodeType;){if(n!==undefined){if(o===n)break;if("string"==typeof n&&pn(o).is(n))break}1===o.nodeType&&r.push(o),o=o[t]}return r},wn=function(e,t,n,r){var o=[];for(r instanceof pn&&(r=r[0]);e;e=e[t])if(!n||e.nodeType===n){if(r!==undefined){if(e===r)break;if("string"==typeof r&&pn(e).is(r))break}o.push(e)}return o},Nn=function(e,t,n){for(e=e[t];e;e=e[t])if(e.nodeType===n)return e;return null};bn({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return xn(e,"parentNode")},next:function(e){return Nn(e,"nextSibling",1)},prev:function(e){return Nn(e,"previousSibling",1)},children:function(e){return wn(e.firstChild,"nextSibling",1)},contents:function(e){return Yt.toArray(("iframe"===e.nodeName?e.contentDocument||e.contentWindow.document:e).childNodes)}},function(e,r){pn.fn[e]=function(t){var n=[];return this.each(function(){var e=r.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(tn[e]||(n=pn.unique(n)),0===e.indexOf("parents")&&(n=n.reverse())),n=pn(n),t?n.filter(t):n}}),bn({parentsUntil:function(e,t){return xn(e,"parentNode",t)},nextUntil:function(e,t){return wn(e,"nextSibling",1,t).slice(1)},prevUntil:function(e,t){return wn(e,"previousSibling",1,t).slice(1)}},function(r,o){pn.fn[r]=function(t,e){var n=[];return this.each(function(){var e=o.call(n,this,t,n);e&&(pn.isArray(e)?n.push.apply(n,e):n.push(e))}),1<this.length&&(n=pn.unique(n),0!==r.indexOf("parents")&&"prevUntil"!==r||(n=n.reverse())),n=pn(n),e?n.filter(e):n}}),pn.fn.is=function(e){return!!e&&0<this.filter(e).length},pn.fn.init.prototype=pn.fn,pn.overrideDefaults=function(n){var r,o=function(e,t){return r=r||n(),0===arguments.length&&(e=r.element),t||(t=r.context),new o.fn.init(e,t)};return pn.extend(o,this),o};var En=function(n,r,e){bn(e,function(e,t){n[e]=n[e]||{},n[e][r]=t})};de.ie&&de.ie<8&&(En(mn,"get",{maxlength:function(e){var t=e.maxLength;return 2147483647===t?undefined:t},size:function(e){var t=e.size;return 20===t?undefined:t},"class":function(e){return e.className},style:function(e){var t=e.style.cssText;return 0===t.length?undefined:t}}),En(mn,"set",{"class":function(e,t){e.className=t},style:function(e,t){e.style.cssText=t}})),de.ie&&de.ie<9&&(dn["float"]="styleFloat",En(gn,"set",{opacity:function(e,t){var n=e.style;null===t||""===t?n.removeAttribute("filter"):(n.zoom=1,n.filter="alpha(opacity="+100*t+")")}})),pn.attrHooks=mn,pn.cssHooks=gn;var Sn,kn,Tn,An=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}return undefined}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return Dn(r(1),r(2))},Rn=function(){return Dn(0,0)},Dn=function(e,t){return{major:e,minor:t}},Bn={nu:Dn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Rn():An(e,n)},unknown:Rn},On="Firefox",_n=function(e,t){return function(){return t===e}},Pn=function(e){var t=e.current;return{current:t,version:e.version,isEdge:_n("Edge",t),isChrome:_n("Chrome",t),isIE:_n("IE",t),isOpera:_n("Opera",t),isFirefox:_n(On,t),isSafari:_n("Safari",t)}},In={unknown:function(){return Pn({current:undefined,version:Bn.unknown()})},nu:Pn,edge:q("Edge"),chrome:q("Chrome"),ie:q("IE"),opera:q("Opera"),firefox:q(On),safari:q("Safari")},Ln="Windows",Mn="Android",Fn="Solaris",Un="FreeBSD",zn=function(e,t){return function(){return t===e}},Vn=function(e){var t=e.current;return{current:t,version:e.version,isWindows:zn(Ln,t),isiOS:zn("iOS",t),isAndroid:zn(Mn,t),isOSX:zn("OSX",t),isLinux:zn("Linux",t),isSolaris:zn(Fn,t),isFreeBSD:zn(Un,t)}},jn={unknown:function(){return Vn({current:undefined,version:Bn.unknown()})},nu:Vn,windows:q(Ln),ios:q("iOS"),android:q(Mn),linux:q("Linux"),osx:q("OSX"),solaris:q(Fn),freebsd:q(Un)},Hn=function(e,t){var n=String(t).toLowerCase();return V(e,function(e){return e.search(n)})},qn=function(e,n){return Hn(e,n).map(function(e){var t=Bn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},$n=function(e,n){return Hn(e,n).map(function(e){var t=Bn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Wn=function(e,t){return-1!==e.indexOf(t)},Kn=function(e){return e.replace(/^\s+|\s+$/g,"")},Xn=function(e){return e.replace(/\s+$/g,"")},Yn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Gn=function(t){return function(e){return Wn(e,t)}},Jn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return Wn(e,"edge/")&&Wn(e,"chrome")&&Wn(e,"safari")&&Wn(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Yn],search:function(e){return Wn(e,"chrome")&&!Wn(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return Wn(e,"msie")||Wn(e,"trident")}},{name:"Opera",versionRegexes:[Yn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Gn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Gn("firefox")},{name:"Safari",versionRegexes:[Yn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(Wn(e,"safari")||Wn(e,"mobile/"))&&Wn(e,"applewebkit")}}],Qn=[{name:"Windows",search:Gn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return Wn(e,"iphone")||Wn(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Gn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Gn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Gn("linux"),versionRegexes:[]},{name:"Solaris",search:Gn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Gn("freebsd"),versionRegexes:[]}],Zn={browsers:q(Jn),oses:q(Qn)},er=function(e){var t,n,r,o,i,a,u,s,c,l,f,d=Zn.browsers(),m=Zn.oses(),g=qn(d,e).fold(In.unknown,In.nu),p=$n(m,e).fold(jn.unknown,jn.nu);return{browser:g,os:p,deviceType:(n=g,r=e,o=(t=p).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,s=o||a||u&&!0===/mobile/i.test(r),c=t.isiOS()||t.isAndroid(),l=c&&!s,f=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:q(o),isiPhone:q(i),isTablet:q(s),isPhone:q(l),isTouch:q(c),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:q(f)})}},tr={detect:(Sn=function(){var e=navigator.userAgent;return er(e)},Tn=!1,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Tn||(Tn=!0,kn=Sn.apply(null,e)),kn})},nr=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:q(e)}},rr={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return nr(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return nr(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return nr(n)},fromDom:nr,fromPoint:function(e,t,n){var r=e.dom();return A.from(r.elementFromPoint(t,n)).map(nr)}},or=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE),ir=(Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE),ar=Node.TEXT_NODE,ur=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(e){return e.dom().nodeName.toLowerCase()}),sr=function(t){return function(e){return e.dom().nodeType===t}},cr=sr(ir),lr=sr(ar),fr=Object.keys,dr=Object.hasOwnProperty,mr=function(e,t){for(var n=fr(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},gr=function(r,o){var i={};return mr(r,function(e,t){var n=o(e,t,r);i[n.k]=n.v}),i},pr=function(e,t){return hr(e,t)?A.some(e[t]):A.none()},hr=function(e,t){return dr.call(e,t)},vr=function(e,t,n){if(!(R(n)||_(n)||I(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},br=function(e,t,n){vr(e.dom(),t,n)},yr=function(e,t){var n=e.dom();mr(t,function(e,t){vr(n,t,e)})},Cr=function(e,t){var n=e.dom().getAttribute(t);return null===n?undefined:n},xr=function(e,t){e.dom().removeAttribute(t)},wr=function(e,t){var n,r,o=e.dom(),i=window.getComputedStyle(o).getPropertyValue(t),a=""!==i||(r=lr(n=e)?n.dom().parentNode:n.dom())!==undefined&&null!==r&&r.ownerDocument.body.contains(r)?i:Nr(o,t);return null===a?undefined:a},Nr=function(e,t){return e.style!==undefined?e.style.getPropertyValue(t):""},Er=function(e,t){var n=e.dom(),r=Nr(n,t);return A.from(r).filter(function(e){return 0<e.length})},Sr=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return F(t,function(e,t){r[e]=q(n[t])}),r}},kr=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},Tr=function(){return ie.getOrDie("Node")},Ar=function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)},Rr=function(e,t){return Ar(e,t,Tr().DOCUMENT_POSITION_CONTAINED_BY)},Dr=ir,Br=or,Or=function(e,t){var n=e.dom();if(n.nodeType!==Dr)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},_r=function(e){return e.nodeType!==Dr&&e.nodeType!==Br||0===e.childElementCount},Pr=function(e,t){return e.dom()===t.dom()},Ir=tr.detect().browser.isIE()?function(e,t){return Rr(e.dom(),t.dom())}:function(e,t){var n=e.dom(),r=t.dom();return n!==r&&n.contains(r)},Lr=function(e){return rr.fromDom(e.dom().ownerDocument)},Mr=function(e){var t=e.dom();return A.from(t.parentNode).map(rr.fromDom)},Fr=function(e){var t=e.dom();return A.from(t.previousSibling).map(rr.fromDom)},Ur=function(e){var t=e.dom();return A.from(t.nextSibling).map(rr.fromDom)},zr=function(e){return t=kr(e,Fr),(n=Q.call(t,0)).reverse(),n;var t,n},Vr=function(e){return kr(e,Ur)},jr=function(e){var t=e.dom();return W(t.childNodes,rr.fromDom)},Hr=function(e,t){var n=e.dom().childNodes;return A.from(n[t]).map(rr.fromDom)},qr=function(e){return Hr(e,0)},$r=function(e){return Hr(e,e.dom().childNodes.length-1)},Wr=(Sr("element","offset"),tr.detect().browser),Kr=function(e){return V(e,cr)},Xr={getPos:function(e,t,n){var r,o,i,a=0,u=0,s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===wr(rr.fromDom(e),"position"))return{x:a=(o=t.getBoundingClientRect()).left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,y:u=o.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop};for(r=t;r&&r!==n&&r.nodeType;)a+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!==n&&r.nodeType;)a-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;u+=(i=rr.fromDom(t),Wr.isFirefox()&&"table"===ur(i)?Kr(jr(i)).filter(function(e){return"caption"===ur(e)}).bind(function(o){return Kr(Vr(o)).map(function(e){var t=e.dom().offsetTop,n=o.dom().offsetTop,r=o.dom().offsetHeight;return t<=n?-r:0})}).getOr(0):0)}return{x:a,y:u}}},Yr=function(e){var n=A.none(),t=[],r=function(e){o()?a(e):t.push(e)},o=function(){return n.isSome()},i=function(e){F(e,a)},a=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){n=A.some(e),i(t),t=[]}),{get:r,map:function(n){return Yr(function(t){r(function(e){t(n(e))})})},isReady:o}},Gr={nu:Yr,pure:function(t){return Yr(function(e){e(t)})}},Jr=function(t){var e=function(e){var r;t((r=e,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this;setTimeout(function(){r.apply(n,e)},0)}))},n=function(){return Gr.nu(e)};return{map:function(r){return Jr(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return Jr(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return Jr(function(t){e(function(e){n.get(t)})})},toLazy:n,toCached:function(){var t=null;return Jr(function(e){null===t&&(t=n()),t.get(e)})},get:e}},Qr={nu:Jr,pure:function(t){return Jr(function(e){e(t)})}},Zr=function(a,e){return e(function(r){var o=[],i=0;0===a.length?r([]):F(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})})},eo=function(e){return Zr(e,Qr.nu)},to=function(n){return{is:function(e){return n===e},isValue:x,isError:C,getOr:q(n),getOrThunk:q(n),getOrDie:q(n),or:function(e){return to(n)},orThunk:function(e){return to(n)},fold:function(e,t){return t(n)},map:function(e){return to(e(n))},mapError:function(e){return to(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return A.some(n)}}},no=function(n){return{is:C,isValue:C,isError:x,getOr:$,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(n),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return no(n)},mapError:function(e){return no(e(n))},each:o,bind:function(e){return no(n)},exists:C,forall:x,toOption:A.none}},ro={value:to,error:no};function oo(e,u){var t=e,n=function(e,t,n,r){var o,i;if(e){if(!r&&e[t])return e[t];if(e!==u){if(o=e[n])return o;for(i=e.parentNode;i&&i!==u;i=i.parentNode)if(o=i[n])return o}}};this.current=function(){return t},this.next=function(e){return t=n(t,"firstChild","nextSibling",e)},this.prev=function(e){return t=n(t,"lastChild","previousSibling",e)},this.prev2=function(e){return t=function(e,t,n,r){var o,i,a;if(e){if(o=e[n],u&&o===u)return;if(o){if(!r)for(a=o[t];a;a=a[t])if(!a[t])return a;return o}if((i=e.parentNode)&&i!==u)return i}}(t,"lastChild","previousSibling",e)}}var io,ao,uo,so=function(t){var n;return function(e){return(n=n||function(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n}(t,q(!0))).hasOwnProperty(ur(e))}},co=so(["h1","h2","h3","h4","h5","h6"]),lo=so(["article","aside","details","div","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","p","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"]),fo=function(e){return cr(e)&&!lo(e)},mo=function(e){return cr(e)&&"br"===ur(e)},go=so(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),po=so(["ul","ol","dl"]),ho=so(["li","dd","dt"]),vo=so(["area","base","basefont","br","col","frame","hr","img","input","isindex","link","meta","param","embed","source","wbr","track"]),bo=so(["thead","tbody","tfoot"]),yo=so(["td","th"]),Co=so(["pre","script","textarea","style"]),xo=function(t){return function(e){return!!e&&e.nodeType===t}},wo=xo(1),No=function(e){var r=e.toLowerCase().split(" ");return function(e){var t,n;if(e&&e.nodeType)for(n=e.nodeName.toLowerCase(),t=0;t<r.length;t++)if(n===r[t])return!0;return!1}},Eo=function(t){return function(e){if(wo(e)){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},So=xo(3),ko=xo(8),To=xo(9),Ao=xo(11),Ro=No("br"),Do=Eo("true"),Bo=Eo("false"),Oo={isText:So,isElement:wo,isComment:ko,isDocument:To,isDocumentFragment:Ao,isBr:Ro,isContentEditableTrue:Do,isContentEditableFalse:Bo,matchNodeNames:No,hasPropValue:function(t,n){return function(e){return wo(e)&&e[t]===n}},hasAttribute:function(t,e){return function(e){return wo(e)&&e.hasAttribute(t)}},hasAttributeValue:function(t,n){return function(e){return wo(e)&&e.getAttribute(t)===n}},matchStyleValues:function(r,e){var o=e.toLowerCase().split(" ");return function(e){var t;if(wo(e))for(t=0;t<o.length;t++){var n=e.ownerDocument.defaultView.getComputedStyle(e,null);if((n?n.getPropertyValue(r):null)===o[t])return!0}return!1}},isBogus:function(e){return wo(e)&&e.hasAttribute("data-mce-bogus")},isBogusAll:function(e){return wo(e)&&"all"===e.getAttribute("data-mce-bogus")},isTable:function(e){return wo(e)&&"TABLE"===e.tagName}},_o=function(e){return e&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},Po=function(e,t){var n,r=t.childNodes;if(!Oo.isElement(t)||!_o(t)){for(n=r.length-1;0<=n;n--)Po(e,r[n]);if(!1===Oo.isDocument(t)){if(Oo.isText(t)&&0<t.nodeValue.length){var o=Yt.trim(t.nodeValue).length;if(e.isBlock(t.parentNode)||0<o)return;if(0===o&&(a=(i=t).previousSibling&&"SPAN"===i.previousSibling.nodeName,u=i.nextSibling&&"SPAN"===i.nextSibling.nodeName,a&&u))return}else if(Oo.isElement(t)&&(1===(r=t.childNodes).length&&_o(r[0])&&t.parentNode.insertBefore(r[0],t),r.length||vo(rr.fromDom(t))))return;e.remove(t)}var i,a,u;return t}},Io={trimNode:Po},Lo=Yt.makeMap,Mo=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Fo=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Uo=/[<>&\"\']/g,zo=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,Vo={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"};ao={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},uo={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"};var jo=function(e,t){var n,r,o,i={};if(e){for(e=e.split(","),t=t||10,n=0;n<e.length;n+=2)r=String.fromCharCode(parseInt(e[n],t)),ao[r]||(o="&"+e[n+1]+";",i[r]=o,i[o]=r);return i}};io=jo("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32);var Ho=function(e,t){return e.replace(t?Mo:Fo,function(e){return ao[e]||e})},qo=function(e,t){return e.replace(t?Mo:Fo,function(e){return 1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":ao[e]||"&#"+e.charCodeAt(0)+";"})},$o=function(e,t,n){return n=n||io,e.replace(t?Mo:Fo,function(e){return ao[e]||n[e]||e})},Wo={encodeRaw:Ho,encodeAllRaw:function(e){return(""+e).replace(Uo,function(e){return ao[e]||e})},encodeNumeric:qo,encodeNamed:$o,getEncodeFunc:function(e,t){var n=jo(t)||io,r=Lo(e.replace(/\+/g,","));return r.named&&r.numeric?function(e,t){return e.replace(t?Mo:Fo,function(e){return ao[e]!==undefined?ao[e]:n[e]!==undefined?n[e]:1<e.length?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";"})}:r.named?t?function(e,t){return $o(e,t,n)}:$o:r.numeric?qo:Ho},decode:function(e){return e.replace(zo,function(e,t){return t?65535<(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):Vo[t]||String.fromCharCode(t):uo[e]||io[e]||(n=e,(r=rr.fromTag("div").dom()).innerHTML=n,r.textContent||r.innerText||n);var n,r})}},Ko={},Xo={},Yo=Yt.makeMap,Go=Yt.each,Jo=Yt.extend,Qo=Yt.explode,Zo=Yt.inArray,ei=function(e,t){return(e=Yt.trim(e))?e.split(t||" "):[]},ti=function(e){var u,t,n,r,o,i,s={},a=function(e,t,n){var r,o,i,a=function(e,t){var n,r,o={};for(n=0,r=e.length;n<r;n++)o[e[n]]=t||{};return o};for(t=t||"","string"==typeof(n=n||[])&&(n=ei(n)),r=(e=ei(e)).length;r--;)i={attributes:a(o=ei([u,t].join(" "))),attributesOrder:o,children:a(n,Xo)},s[e[r]]=i},c=function(e,t){var n,r,o,i;for(n=(e=ei(e)).length,t=ei(t);n--;)for(r=s[e[n]],o=0,i=t.length;o<i;o++)r.attributes[t[o]]={},r.attributesOrder.push(t[o])};return Ko[e]?Ko[e]:(u="id accesskey class dir lang style tabindex title role",t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(u+=" contenteditable contextmenu draggable dropzone hidden spellcheck translate",t+=" article aside details dialog figure main header footer hgroup section nav",n+=" audio canvas command datalist mark meter output picture progress time wbr video ruby bdi keygen"),"html5-strict"!==e&&(u+=" xml:lang",n=[n,i="acronym applet basefont big font strike tt"].join(" "),Go(ei(i),function(e){a(e,"",n)}),t=[t,o="center dir isindex noframes"].join(" "),r=[t,n].join(" "),Go(ei(o),function(e){a(e,"",r)})),r=r||[t,n].join(" "),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",r),a("address dt dd div caption","",r),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",r),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",r),a("dl","","dt dd"),a("a","href target rel media hreflang type",n),a("q","cite",n),a("ins del","cite datetime",r),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",r),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[r,"param"].join(" ")),a("param","name value"),a("map","name",[r,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",r),a("th","colspan rowspan headers scope abbr",r),a("form","accept-charset action autocomplete enctype method name novalidate target",r),a("fieldset","disabled form name",[r,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?r:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[r,"li"].join(" ")),a("noscript","",r),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",r),a("mark rt rp summary bdi","",n),a("canvas","width height",r),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[r,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[r,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",r),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[r,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",r),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[r,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name")),"html5-strict"!==e&&(c("script","language xml:space"),c("style","xml:space"),c("object","declare classid code codebase codetype archive standby align border hspace vspace"),c("embed","align name hspace vspace"),c("param","valuetype type"),c("a","charset name rev shape coords"),c("br","clear"),c("applet","codebase archive code object alt name width height align hspace vspace"),c("img","name longdesc align border hspace vspace"),c("iframe","longdesc frameborder marginwidth marginheight scrolling align"),c("font basefont","size color face"),c("input","usemap align"),c("select","onchange"),c("textarea"),c("h1 h2 h3 h4 h5 h6 div p legend caption","align"),c("ul","type compact"),c("li","type"),c("ol dl menu dir","compact"),c("pre","width xml:space"),c("hr","align noshade size width"),c("isindex","prompt"),c("table","summary width frame rules cellspacing cellpadding align bgcolor"),c("col","width align char charoff valign"),c("colgroup","width align char charoff valign"),c("thead","align char charoff valign"),c("tr","align char charoff valign bgcolor"),c("th","axis align char charoff valign nowrap bgcolor width height"),c("form","accept"),c("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),c("tfoot","align char charoff valign"),c("tbody","align char charoff valign"),c("area","nohref"),c("body","background bgcolor text link vlink alink")),"html4"!==e&&(c("input button select textarea","autofocus"),c("input textarea","placeholder"),c("a","download"),c("link script img","crossorigin"),c("iframe","sandbox seamless allowfullscreen")),Go(ei("a form meter progress dfn"),function(e){s[e]&&delete s[e].children[e]}),delete s.caption.children.table,delete s.script,Ko[e]=s)},ni=function(e,n){var r;return e&&(r={},"string"==typeof e&&(e={"*":e}),Go(e,function(e,t){r[t]=r[t.toUpperCase()]="map"===n?Yo(e,/[, ]/):Qo(e,/[, ]/)})),r};function ri(i){var e,t,n,r,o,a,u,s,c,l,f,d,m,N={},g={},E=[],p={},h={},v=function(e,t,n){var r=i[e];return r?r=Yo(r,/[, ]/,Yo(r.toUpperCase(),/[, ]/)):(r=Ko[e])||(r=Yo(t," ",Yo(t.toUpperCase()," ")),r=Jo(r,n),Ko[e]=r),r};n=ti((i=i||{}).schema),!1===i.verify_html&&(i.valid_elements="*[*]"),e=ni(i.valid_styles),t=ni(i.invalid_styles,"map"),s=ni(i.valid_classes,"map"),r=v("whitespace_elements","pre script noscript style textarea video audio iframe object code"),o=v("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),a=v("short_ended_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),u=v("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls"),l=v("non_empty_elements","td th iframe video audio object script pre code",a),f=v("move_caret_before_on_enter_elements","table",l),d=v("text_block_elements","h1 h2 h3 h4 h5 h6 p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),c=v("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary",d),m=v("text_inline_elements","span strong b em i font strike u var cite dfn code mark q sup sub samp"),Go((i.special||"script noscript noframes noembed title style textarea xmp").split(" "),function(e){h[e]=new RegExp("</"+e+"[^>]*>","gi")});var S=function(e){return new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$")},b=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,g,p,h,v,b,y,C=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)\])?$/,x=/^([!\-])?(\w+[\\:]:\w+|[^=:<]+)?(?:([=:<])(.*))?$/,w=/[*?+]/;if(e)for(e=ei(e,","),N["@"]&&(h=N["@"].attributes,v=N["@"].attributesOrder),t=0,n=e.length;t<n;t++)if(i=C.exec(e[t])){if(g=i[1],c=i[2],p=i[3],s=i[5],a={attributes:d={},attributesOrder:m=[]},"#"===g&&(a.paddEmpty=!0),"-"===g&&(a.removeEmpty=!0),"!"===i[4]&&(a.removeEmptyAttrs=!0),h){for(b in h)d[b]=h[b];m.push.apply(m,v)}if(s)for(r=0,o=(s=ei(s,"|")).length;r<o;r++)if(i=x.exec(s[r])){if(u={},f=i[1],l=i[2].replace(/[\\:]:/g,":"),g=i[3],y=i[4],"!"===f&&(a.attributesRequired=a.attributesRequired||[],a.attributesRequired.push(l),u.required=!0),"-"===f){delete d[l],m.splice(Zo(m,l),1);continue}g&&("="===g&&(a.attributesDefault=a.attributesDefault||[],a.attributesDefault.push({name:l,value:y}),u.defaultValue=y),":"===g&&(a.attributesForced=a.attributesForced||[],a.attributesForced.push({name:l,value:y}),u.forcedValue=y),"<"===g&&(u.validValues=Yo(y,"?"))),w.test(l)?(a.attributePatterns=a.attributePatterns||[],u.pattern=S(l),a.attributePatterns.push(u)):(d[l]||m.push(l),d[l]=u)}h||"@"!==c||(h=d,v=m),p&&(a.outputName=c,N[p]=a),w.test(c)?(a.pattern=S(c),E.push(a)):N[c]=a}},y=function(e){N={},E=[],b(e),Go(n,function(e,t){g[t]=e.children})},C=function(e){var a=/^(~)?(.+)$/;e&&(Ko.text_block_elements=Ko.block_elements=null,Go(ei(e,","),function(e){var t=a.exec(e),n="~"===t[1],r=n?"span":"div",o=t[2];if(g[o]=g[r],p[o]=r,n||(c[o.toUpperCase()]={},c[o]={}),!N[o]){var i=N[r];delete(i=Jo({},i)).removeEmptyAttrs,delete i.removeEmpty,N[o]=i}Go(g,function(e,t){e[r]&&(g[t]=e=Jo({},g[t]),e[o]=e[r])})}))},x=function(e){var o=/^([+\-]?)(\w+)\[([^\]]+)\]$/;Ko[i.schema]=null,e&&Go(ei(e,","),function(e){var t,n,r=o.exec(e);r&&(n=r[1],t=n?g[r[2]]:g[r[2]]={"#comment":{}},t=g[r[2]],Go(ei(r[3],"|"),function(e){"-"===n?delete t[e]:t[e]={}}))})},w=function(e){var t,n=N[e];if(n)return n;for(t=E.length;t--;)if((n=E[t]).pattern.test(e))return n};return i.valid_elements?y(i.valid_elements):(Go(n,function(e,t){N[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},g[t]=e.children}),"html5"!==i.schema&&Go(ei("strong/b em/i"),function(e){e=ei(e,"/"),N[e[1]].outputName=e[0]}),Go(ei("ol ul sub sup blockquote span font a table tbody tr strong em b i"),function(e){N[e]&&(N[e].removeEmpty=!0)}),Go(ei("p h1 h2 h3 h4 h5 h6 th td pre div address caption li"),function(e){N[e].paddEmpty=!0}),Go(ei("span"),function(e){N[e].removeEmptyAttrs=!0})),C(i.custom_elements),x(i.valid_children),b(i.extended_valid_elements),x("+ol[ul|ol],+ul[ul|ol]"),Go({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},function(e,t){N[t]&&(N[t].parentsRequired=ei(e))}),i.invalid_elements&&Go(Qo(i.invalid_elements),function(e){N[e]&&delete N[e]}),w("span")||b("span[!data-mce-type|*]"),{children:g,elements:N,getValidStyles:function(){return e},getValidClasses:function(){return s},getBlockElements:function(){return c},getInvalidStyles:function(){return t},getShortEndedElements:function(){return a},getTextBlockElements:function(){return d},getTextInlineElements:function(){return m},getBoolAttrs:function(){return u},getElementRule:w,getSelfClosingElements:function(){return o},getNonEmptyElements:function(){return l},getMoveCaretBeforeOnEnterElements:function(){return f},getWhiteSpaceElements:function(){return r},getSpecialElements:function(){return h},isValidChild:function(e,t){var n=g[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:function(e,t){var n,r,o=w(e);if(o){if(!t)return!0;if(o.attributes[t])return!0;if(n=o.attributePatterns)for(r=n.length;r--;)if(n[r].pattern.test(e))return!0}return!1},getCustomElements:function(){return p},addValidElements:b,setValidElements:y,addCustomElements:C,addValidChildren:x}}var oi=function(e,t,n,r){var o=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+o(t)+o(n)+o(r)};function ii(y,e){var C,t,c,l,x=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,w=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,N=/\s*([^:]+):\s*([^;]+);?/g,E=/\s+$/,S={},k="\ufeff";for(y=y||{},e&&(c=e.getValidStyles(),l=e.getInvalidStyles()),t=("\\\" \\' \\; \\: ; : "+k).split(" "),C=0;C<t.length;C++)S[t[C]]=k+C,S[k+C]=t[C];return{toHex:function(e){return e.replace(x,oi)},parse:function(e){var t,n,r,o,i,a,u,s,c={},l=y.url_converter,f=y.url_converter_scope||this,d=function(e,t,n){var r,o,i,a;if((r=c[e+"-top"+t])&&(o=c[e+"-right"+t])&&(i=c[e+"-bottom"+t])&&(a=c[e+"-left"+t])){var u=[r,o,i,a];for(C=u.length-1;C--&&u[C]===u[C+1];);-1<C&&n||(c[e+t]=-1===C?u[0]:u.join(" "),delete c[e+"-top"+t],delete c[e+"-right"+t],delete c[e+"-bottom"+t],delete c[e+"-left"+t])}},m=function(e){var t,n=c[e];if(n){for(t=(n=n.split(" ")).length;t--;)if(n[t]!==n[0])return!1;return c[e]=n[0],!0}},g=function(e){return o=!0,S[e]},p=function(e,t){return o&&(e=e.replace(/\uFEFF[0-9]/g,function(e){return S[e]})),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e},h=function(e){return String.fromCharCode(parseInt(e.slice(1),16))},v=function(e){return e.replace(/\\[0-9a-f]+/gi,h)},b=function(e,t,n,r,o,i){if(o=o||i)return"'"+(o=p(o)).replace(/\'/g,"\\'")+"'";if(t=p(t||n||r),!y.allow_script_urls){var a=t.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(a))return"";if(!y.allow_svg_data_urls&&/^data:image\/svg/i.test(a))return""}return l&&(t=l.call(f,t,"style")),"url('"+t.replace(/\'/g,"\\'")+"')"};if(e){for(e=(e=e.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,g).replace(/\"[^\"]+\"|\'[^\']+\'/g,function(e){return e.replace(/[;:]/g,g)});t=N.exec(e);)if(N.lastIndex=t.index+t[0].length,n=t[1].replace(E,"").toLowerCase(),r=t[2].replace(E,""),n&&r){if(n=v(n),r=v(r),-1!==n.indexOf(k)||-1!==n.indexOf('"'))continue;if(!y.allow_script_urls&&("behavior"===n||/expression\s*\(|\/\*|\*\//.test(r)))continue;"font-weight"===n&&"700"===r?r="bold":"color"!==n&&"background-color"!==n||(r=r.toLowerCase()),r=(r=r.replace(x,oi)).replace(w,b),c[n]=o?p(r,!0):r}d("border","",!0),d("border","-width"),d("border","-color"),d("border","-style"),d("padding",""),d("margin",""),i="border",u="border-style",s="border-color",m(a="border-width")&&m(u)&&m(s)&&(c[i]=c[a]+" "+c[u]+" "+c[s],delete c[a],delete c[u],delete c[s]),"medium none"===c.border&&delete c.border,"none"===c["border-image"]&&delete c["border-image"]}return c},serialize:function(i,e){var t,n,r,o,a,u="",s=function(e){var t,n,r,o;if(t=c[e])for(n=0,r=t.length;n<r;n++)e=t[n],(o=i[e])&&(u+=(0<u.length?" ":"")+e+": "+o+";")};if(e&&c)s("*"),s(e);else for(t in i)!(n=i[t])||l&&(r=t,o=e,a=void 0,(a=l["*"])&&a[r]||(a=l[o])&&a[r])||(u+=(0<u.length?" ":"")+t+": "+n+";");return u}}}var ai,ui=Yt.each,si=Yt.grep,ci=de.ie,li=/^([a-z0-9],?)+$/i,fi=/^[ \t\r\n]*$/,di=function(n,r,o){var e={},i=r.keep_values,t={set:function(e,t,n){r.url_converter&&(t=r.url_converter.call(r.url_converter_scope||o(),t,n,e[0])),e.attr("data-mce-"+n,t).attr(n,t)},get:function(e,t){return e.attr("data-mce-"+t)||e.attr(t)}};return e={style:{set:function(e,t){null===t||"object"!=typeof t?(i&&e.attr("data-mce-style",t),e.attr("style",t)):e.css(t)},get:function(e){var t=e.attr("data-mce-style")||e.attr("style");return t=n.serialize(n.parse(t),e[0].nodeName)}}},i&&(e.href=e.src=t),e},mi=function(e,t){var n=t.attr("style"),r=e.serialize(e.parse(n),t[0].nodeName);r||(r=null),t.attr("data-mce-style",r)},gi=function(e,t){var n,r,o=0;if(e)for(n=e.nodeType,e=e.previousSibling;e;e=e.previousSibling)r=e.nodeType,(!t||3!==r||r!==n&&e.nodeValue.length)&&(o++,n=r);return o};function pi(a,u){var s,c=this;void 0===u&&(u={});var r={},i=window,o={},t=0,e=function j(g,p){void 0===p&&(p={});var h,v=0,b={};h=p.maxLoadTime||5e3;var y=function(e){g.getElementsByTagName("head")[0].appendChild(e)},n=function(e,t,n){var o,r,i,a,u=function(e){a.status=e,a.passed=[],a.failed=[],o&&(o.onload=null,o.onerror=null,o=null)},s=function(){for(var e=a.passed,t=e.length;t--;)e[t]();u(2)},c=function(){for(var e=a.failed,t=e.length;t--;)e[t]();u(3)},l=function(e,t){e()||((new Date).getTime()-i<h?ve.setTimeout(t):c())},f=function(){l(function(){for(var e,t,n=g.styleSheets,r=n.length;r--;)if((t=(e=n[r]).ownerNode?e.ownerNode:e.owningElement)&&t.id===o.id)return s(),!0},f)},d=function(){l(function(){try{var e=r.sheet.cssRules;return s(),!!e}catch(t){}},d)};if(e=Yt._addCacheSuffix(e),b[e]?a=b[e]:(a={passed:[],failed:[]},b[e]=a),t&&a.passed.push(t),n&&a.failed.push(n),1!==a.status)if(2!==a.status)if(3!==a.status){if(a.status=1,(o=g.createElement("link")).rel="stylesheet",o.type="text/css",o.id="u"+v++,o.async=!1,o.defer=!1,i=(new Date).getTime(),p.contentCssCors&&(o.crossOrigin="anonymous"),"onload"in o&&!((m=navigator.userAgent.match(/WebKit\/(\d*)/))&&parseInt(m[1],10)<536))o.onload=f,o.onerror=c;else{if(0<navigator.userAgent.indexOf("Firefox"))return(r=g.createElement("style")).textContent='@import "'+e+'"',d(),void y(r);f()}var m;y(o),o.href=e}else c();else s()},t=function(t){return Qr.nu(function(e){n(t,H(e,q(ro.value(t))),H(e,q(ro.error(t))))})},o=function(e){return e.fold($,$)};return{load:n,loadAll:function(e,n,r){eo(W(e,t)).get(function(e){var t=K(e,function(e){return e.isValue()});0<t.fail.length?r(t.fail.map(o)):n(t.pass.map(o))})}}}(a,{contentCssCors:u.contentCssCors}),l=[],f=u.schema?u.schema:ri({}),d=ii({url_converter:u.url_converter,url_converter_scope:u.url_converter_scope},u.schema),m=u.ownEvents?new ke(u.proxy):ke.Event,n=f.getBlockElements(),g=pn.overrideDefaults(function(){return{context:a,element:V.getRoot()}}),p=function(e){if(e&&a&&"string"==typeof e){var t=a.getElementById(e);return t&&t.id!==e?a.getElementsByName(e)[1]:t}return e},h=function(e){return"string"==typeof e&&(e=p(e)),g(e)},v=function(e,t,n){var r,o,i=h(e);return i.length&&(o=(r=s[t])&&r.get?r.get(i,t):i.attr(t)),void 0===o&&(o=n||""),o},b=function(e){var t=p(e);return t?t.attributes:[]},y=function(e,t,n){var r,o;""===n&&(n=null);var i=h(e);r=i.attr(t),i.length&&((o=s[t])&&o.set?o.set(i,n,t):i.attr(t,n),r!==n&&u.onSetAttrib&&u.onSetAttrib({attrElm:i,attrName:t,attrValue:n}))},C=function(){return u.root_element||a.body},x=function(e,t){return Xr.getPos(a.body,p(e),t)},w=function(e,t,n){var r=h(e);return n?r.css(t):("float"===(t=t.replace(/-(\D)/g,function(e,t){return t.toUpperCase()}))&&(t=de.ie&&de.ie<12?"styleFloat":"cssFloat"),r[0]&&r[0].style?r[0].style[t]:undefined)},N=function(e){var t,n;return e=p(e),t=w(e,"width"),n=w(e,"height"),-1===t.indexOf("px")&&(t=0),-1===n.indexOf("px")&&(n=0),{w:parseInt(t,10)||e.offsetWidth||e.clientWidth,h:parseInt(n,10)||e.offsetHeight||e.clientHeight}},E=function(e,t){var n;if(!e)return!1;if(!Array.isArray(e)){if("*"===t)return 1===e.nodeType;if(li.test(t)){var r=t.toLowerCase().split(/,/),o=e.nodeName.toLowerCase();for(n=r.length-1;0<=n;n--)if(r[n]===o)return!0;return!1}if(e.nodeType&&1!==e.nodeType)return!1}var i=Array.isArray(e)?e:[e];return 0<kt(t,i[0].ownerDocument||i[0],null,i).length},S=function(e,t,n,r){var o,i=[],a=p(e);for(r=r===undefined,n=n||("BODY"!==C().nodeName?C().parentNode:null),Yt.is(t,"string")&&(t="*"===(o=t)?function(e){return 1===e.nodeType}:function(e){return E(e,o)});a&&a!==n&&a.nodeType&&9!==a.nodeType;){if(!t||"function"==typeof t&&t(a)){if(!r)return[a];i.push(a)}a=a.parentNode}return r?i:null},k=function(e,t,n){var r=t;if(e)for("string"==typeof t&&(r=function(e){return E(e,t)}),e=e[n];e;e=e[n])if("function"==typeof r&&r(e))return e;return null},T=function(e,n,r){var o,t="string"==typeof e?p(e):e;if(!t)return!1;if(Yt.isArray(t)&&(t.length||0===t.length))return o=[],ui(t,function(e,t){e&&("string"==typeof e&&(e=p(e)),o.push(n.call(r,e,t)))}),o;var i=r||c;return n.call(i,t)},A=function(e,t){h(e).each(function(e,n){ui(t,function(e,t){y(n,t,e)})})},R=function(e,r){var t=h(e);ci?t.each(function(e,t){if(!1!==t.canHaveHTML){for(;t.firstChild;)t.removeChild(t.firstChild);try{t.innerHTML="<br>"+r,t.removeChild(t.firstChild)}catch(n){pn("<div></div>").html("<br>"+r).contents().slice(1).appendTo(t)}return r}}):t.html(r)},D=function(e,n,r,o,i){return T(e,function(e){var t="string"==typeof n?a.createElement(n):n;return A(t,r),o&&("string"!=typeof o&&o.nodeType?t.appendChild(o):"string"==typeof o&&R(t,o)),i?t:e.appendChild(t)})},B=function(e,t,n){return D(a.createElement(e),e,t,n,!0)},O=Wo.decode,_=Wo.encodeAllRaw,P=function(e,t){var n=h(e);return t?n.each(function(){for(var e;e=this.firstChild;)3===e.nodeType&&0===e.data.length?this.removeChild(e):this.parentNode.insertBefore(e,this)}).remove():n.remove(),1<n.length?n.toArray():n[0]},I=function(e,t,n){h(e).toggleClass(t,n).each(function(){""===this.className&&pn(this).attr("class",null)})},L=function(t,e,n){return T(e,function(e){return Yt.is(e,"array")&&(t=t.cloneNode(!0)),n&&ui(si(e.childNodes),function(e){t.appendChild(e)}),e.parentNode.replaceChild(t,e)})},M=function(){return a.createRange()},F=function(e,t,n,r){if(Yt.isArray(e)){for(var o=e.length;o--;)e[o]=F(e[o],t,n,r);return e}return!u.collect||e!==a&&e!==i||l.push([e,t,n,r]),m.bind(e,t,n,r||V)},U=function(e,t,n){var r;if(Yt.isArray(e)){for(r=e.length;r--;)e[r]=U(e[r],t,n);return e}if(l&&(e===a||e===i))for(r=l.length;r--;){var o=l[r];e!==o[0]||t&&t!==o[1]||n&&n!==o[2]||m.unbind(o[0],o[1],o[2])}return m.unbind(e,t,n)},z=function(e){if(e&&Oo.isElement(e)){var t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},V={doc:a,settings:u,win:i,files:o,stdMode:!0,boxModel:!0,styleSheetLoader:e,boundEvents:l,styles:d,schema:f,events:m,isBlock:function(e){if("string"==typeof e)return!!n[e];if(e){var t=e.nodeType;if(t)return!(1!==t||!n[e.nodeName])}return!1},$:g,$$:h,root:null,clone:function(t,e){if(!ci||1!==t.nodeType||e)return t.cloneNode(e);if(e)return null;var n=a.createElement(t.nodeName);return ui(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),n},getRoot:C,getViewPort:function(e){var t=e||i,n=t.document,r=n.documentElement;return{x:t.pageXOffset||r.scrollLeft,y:t.pageYOffset||r.scrollTop,w:t.innerWidth||r.clientWidth,h:t.innerHeight||r.clientHeight}},getRect:function(e){var t,n;return e=p(e),t=x(e),n=N(e),{x:t.x,y:t.y,w:n.w,h:n.h}},getSize:N,getParent:function(e,t,n){var r=S(e,t,n,!1);return r&&0<r.length?r[0]:null},getParents:S,get:p,getNext:function(e,t){return k(e,t,"nextSibling")},getPrev:function(e,t){return k(e,t,"previousSibling")},select:function(e,t){return kt(e,p(t)||u.root_element||a,[])},is:E,add:D,create:B,createHTML:function(e,t,n){var r,o="";for(r in o+="<"+e,t)t.hasOwnProperty(r)&&null!==t[r]&&"undefined"!=typeof t[r]&&(o+=" "+r+'="'+_(t[r])+'"');return void 0!==n?o+">"+n+"</"+e+">":o+" />"},createFragment:function(e){var t,n=a.createElement("div"),r=a.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)r.appendChild(t);return r},remove:P,setStyle:function(e,t,n){var r=h(e).css(t,n);u.update_styles&&mi(d,r)},getStyle:w,setStyles:function(e,t){var n=h(e).css(t);u.update_styles&&mi(d,n)},removeAllAttribs:function(e){return T(e,function(e){var t,n=e.attributes;for(t=n.length-1;0<=t;t--)e.removeAttributeNode(n.item(t))})},setAttrib:y,setAttribs:A,getAttrib:v,getPos:x,parseStyle:function(e){return d.parse(e)},serializeStyle:function(e,t){return d.serialize(e,t)},addStyle:function(e){var t,n;if(V!==pi.DOM&&a===document){if(r[e])return;r[e]=!0}(n=a.getElementById("mceDefaultStyles"))||((n=a.createElement("style")).id="mceDefaultStyles",n.type="text/css",(t=a.getElementsByTagName("head")[0]).firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(a.createTextNode(e))},loadCSS:function(e){var n;V===pi.DOM||a!==document?(e||(e=""),n=a.getElementsByTagName("head")[0],ui(e.split(","),function(e){var t;e=Yt._addCacheSuffix(e),o[e]||(o[e]=!0,t=B("link",{rel:"stylesheet",href:e}),n.appendChild(t))})):pi.DOM.loadCSS(e)},addClass:function(e,t){h(e).addClass(t)},removeClass:function(e,t){I(e,t,!1)},hasClass:function(e,t){return h(e).hasClass(t)},toggleClass:I,show:function(e){h(e).show()},hide:function(e){h(e).hide()},isHidden:function(e){return"none"===h(e).css("display")},uniqueId:function(e){return(e||"mce_")+t++},setHTML:R,getOuterHTML:function(e){var t="string"==typeof e?p(e):e;return Oo.isElement(t)?t.outerHTML:pn("<div></div>").append(pn(t).clone()).html()},setOuterHTML:function(e,t){h(e).each(function(){try{if("outerHTML"in this)return void(this.outerHTML=t)}catch(e){}P(pn(this).html(t),!0)})},decode:O,encode:_,insertAfter:function(e,t){var r=p(t);return T(e,function(e){var t,n;return t=r.parentNode,(n=r.nextSibling)?t.insertBefore(e,n):t.appendChild(e),e})},replace:L,rename:function(t,e){var n;return t.nodeName!==e.toUpperCase()&&(n=B(e),ui(b(t),function(e){y(n,e.nodeName,v(t,e.nodeName))}),L(n,t,!0)),n||t},findCommonAncestor:function(e,t){for(var n,r=e;r;){for(n=t;n&&r!==n;)n=n.parentNode;if(r===n)break;r=r.parentNode}return!r&&e.ownerDocument?e.ownerDocument.documentElement:r},toHex:function(e){return d.toHex(Yt.trim(e))},run:T,getAttribs:b,isEmpty:function(e,t){var n,r,o,i,a,u,s=0;if(e=e.firstChild){a=new oo(e,e.parentNode),t=t||(f?f.getNonEmptyElements():null),i=f?f.getWhiteSpaceElements():{};do{if(o=e.nodeType,Oo.isElement(e)){var c=e.getAttribute("data-mce-bogus");if(c){e=a.next("all"===c);continue}if(u=e.nodeName.toLowerCase(),t&&t[u]){if("br"!==u)return!1;s++,e=a.next();continue}for(n=(r=b(e)).length;n--;)if("name"===(u=r[n].nodeName)||"data-mce-bookmark"===u)return!1}if(8===o)return!1;if(3===o&&!fi.test(e.nodeValue))return!1;if(3===o&&e.parentNode&&i[e.parentNode.nodeName]&&fi.test(e.nodeValue))return!1;e=a.next()}while(e)}return s<=1},createRng:M,nodeIndex:gi,split:function(e,t,n){var r,o,i,a=M();if(e&&t)return a.setStart(e.parentNode,gi(e)),a.setEnd(t.parentNode,gi(t)),r=a.extractContents(),(a=M()).setStart(t.parentNode,gi(t)+1),a.setEnd(e.parentNode,gi(e)+1),o=a.extractContents(),(i=e.parentNode).insertBefore(Io.trimNode(V,r),e),n?i.insertBefore(n,e):i.insertBefore(t,e),i.insertBefore(Io.trimNode(V,o),e),P(e),n||t},bind:F,unbind:U,fire:function(e,t,n){return m.fire(e,t,n)},getContentEditable:z,getContentEditableParent:function(e){for(var t=C(),n=null;e&&e!==t&&null===(n=z(e));e=e.parentNode);return n},destroy:function(){if(l)for(var e=l.length;e--;){var t=l[e];m.unbind(t[0],t[1],t[2])}kt.setDocument&&kt.setDocument()},isChildOf:function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1},dumpRng:function(e){return"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset}};return s=di(d,u,function(){return V}),V}(ai=pi||(pi={})).DOM=ai(document),ai.nodeIndex=gi;var hi=pi,vi=hi.DOM,bi=Yt.each,yi=Yt.grep,Ci=function(e){return"function"==typeof e},xi=function(){var u={},o=[],s={},c=[],l=0,f=function(e,t,n){var r,o,i=vi;o=i.uniqueId(),(r=document.createElement("script")).id=o,r.type="text/javascript",r.src=Yt._addCacheSuffix(e),r.onload=function(){i.remove(o),r&&(r.onreadystatechange=r.onload=r=null),t()},r.onerror=function(){Ci(n)?n():"undefined"!=typeof console&&console.log&&console.log("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)};this.loadScript=function(e,t,n){f(e,t,n)},this.isDone=function(e){return 2===u[e]},this.markDone=function(e){u[e]=2},this.add=this.load=function(e,t,n,r){u[e]===undefined&&(o.push(e),u[e]=0),t&&(s[e]||(s[e]=[]),s[e].push({success:t,failure:r,scope:n||this}))},this.remove=function(e){delete u[e],delete s[e]},this.loadQueue=function(e,t,n){this.loadScripts(o,e,t,n)},this.loadScripts=function(n,e,t,r){var o,i=[],a=function(t,e){bi(s[e],function(e){Ci(e[t])&&e[t].call(e.scope)}),s[e]=undefined};c.push({success:e,failure:r,scope:t||this}),(o=function(){var e=yi(n);if(n.length=0,bi(e,function(e){2!==u[e]?3!==u[e]?1!==u[e]&&(u[e]=1,l++,f(e,function(){u[e]=2,l--,a("success",e),o()},function(){u[e]=3,l--,i.push(e),a("failure",e),o()})):a("failure",e):a("success",e)}),!l){var t=c.slice(0);c.length=0,bi(t,function(e){0===i.length?Ci(e.success)&&e.success.call(e.scope):Ci(e.failure)&&e.failure.call(e.scope,i)})}})()}};xi.ScriptLoader=new xi;var wi,Ni=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return Ni(n())}}},Ei={},Si=Ni("en"),ki={setCode:function(e){e&&Si.set(e)},getCode:function(){return Si.get()},add:function(e,t){var n=Ei[e];for(var r in n||(Ei[e]=n={}),t)n[r.toLowerCase()]=t[r]},translate:function(e){var t,n,r=Ei[Si.get()]||{},o=function(e){return P(e)?Object.prototype.toString.call(e):i(e)?"":""+e},i=function(e){return""===e||null===e||e===undefined},a=function(e){var t=o(e),n=t.toLowerCase();return hr(r,n)?o(r[n]):t},u=function(e){return e.replace(/{context:\w+}$/,"")},s=function(e){return e};if(i(e))return s("");if(D(t=e)&&hr(t,"raw"))return s(o(e.raw));if(B(n=e)&&1<n.length){var c=e.slice(1);return s(u(a(e[0]).replace(/\{([0-9]+)\}/g,function(e,t){return hr(c,t)?o(c[t]):e})))}return s(u(a(e)))},isRtl:function(){return pr(Ei,Si.get()).bind(function(e){return pr(e,"_dir")}).exists(function(e){return"rtl"===e})},hasCode:function(e){return hr(Ei,e)}},Ti=Yt.each;function Ai(){var r=this,o=[],a={},u={},i=[],s=function(e){var t;return u[e]&&(t=u[e].dependencies),t||[]},c=function(e,t){return"object"==typeof t?t:"string"==typeof e?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}},l=function(e,n,t,r){var o=s(e);Ti(o,function(e){var t=c(n,e);f(t.resource,t,undefined,undefined)}),t&&(r?t.call(r):t.call(xi))},f=function(e,t,n,r,o){if(!a[e]){var i="string"==typeof t?t:t.prefix+t.resource+t.suffix;0!==i.indexOf("/")&&-1===i.indexOf("://")&&(i=Ai.baseURL+"/"+i),a[e]=i.substring(0,i.lastIndexOf("/")),u[e]?l(e,t,n,r):xi.ScriptLoader.add(i,function(){return l(e,t,n,r)},r,o)}};return{items:o,urls:a,lookup:u,_listeners:i,get:function(e){return u[e]?u[e].instance:undefined},dependencies:s,requireLangPack:function(e,t){var n=ki.getCode();if(n&&!1!==Ai.languageLoad){if(t)if(-1!==(t=","+t+",").indexOf(","+n.substr(0,2)+","))n=n.substr(0,2);else if(-1===t.indexOf(","+n+","))return;xi.ScriptLoader.add(a[e]+"/langs/"+n+".js")}},add:function(t,e,n){o.push(e),u[t]={instance:e,dependencies:n};var r=K(i,function(e){return e.name===t});return i=r.fail,Ti(r.pass,function(e){e.callback()}),e},remove:function(e){delete a[e],delete u[e]},createUrl:c,addComponents:function(e,t){var n=r.urls[e];Ti(t,function(e){xi.ScriptLoader.add(n+"/"+e)})},load:f,waitFor:function(e,t){u.hasOwnProperty(e)?t():i.push({name:e,callback:t})}}}(wi=Ai||(Ai={})).PluginManager=wi(),wi.ThemeManager=wi();var Ri=function(t,n){Mr(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},Di=function(e,t){Ur(e).fold(function(){Mr(e).each(function(e){Oi(e,t)})},function(e){Ri(e,t)})},Bi=function(t,n){qr(t).fold(function(){Oi(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},Oi=function(e,t){e.dom().appendChild(t.dom())},_i=function(t,e){F(e,function(e){Oi(t,e)})},Pi=function(e){e.dom().textContent="",F(jr(e),function(e){Ii(e)})},Ii=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Li=function(e){var t,n=jr(e);0<n.length&&(t=e,F(n,function(e){Ri(t,e)})),Ii(e)},Mi=function(n,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null===o&&(o=setTimeout(function(){n.apply(null,e),o=null},r))}}},Fi=function(e,t){var n=Cr(e,t);return n===undefined||""===n?[]:n.split(" ")},Ui=function(e){return e.dom().classList!==undefined},zi=function(e,t){return o=t,i=Fi(n=e,r="class").concat([o]),br(n,r,i.join(" ")),!0;var n,r,o,i},Vi=function(e,t){return o=t,0<(i=U(Fi(n=e,r="class"),function(e){return e!==o})).length?br(n,r,i.join(" ")):xr(n,r),!1;var n,r,o,i},ji=function(e,t){Ui(e)?e.dom().classList.add(t):zi(e,t)},Hi=function(e){0===(Ui(e)?e.dom().classList:Fi(e,"class")).length&&xr(e,"class")},qi=function(e,t){return Ui(e)&&e.dom().classList.contains(t)},$i=function(e,t){var n=[];return F(jr(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat($i(e,t))}),n},Wi=function(e,t){return n=t,o=(r=e)===undefined?document:r.dom(),_r(o)?[]:W(o.querySelectorAll(n),rr.fromDom);var n,r,o};function Ki(e,t,n,r,o){return e(n,r)?A.some(n):P(o)&&o(n)?A.none():t(n,r,o)}var Xi,Yi=function(e,t,n){for(var r=e.dom(),o=P(n)?n:q(!1);r.parentNode;){r=r.parentNode;var i=rr.fromDom(r);if(t(i))return A.some(i);if(o(i))break}return A.none()},Gi=function(e,t,n){return Ki(function(e){return t(e)},Yi,e,t,n)},Ji=function(e,t,n){return Yi(e,function(e){return Or(e,t)},n)},Qi=function(e,t){return n=t,o=(r=e)===undefined?document:r.dom(),_r(o)?A.none():A.from(o.querySelector(n)).map(rr.fromDom);var n,r,o},Zi=function(e,t,n){return Ki(Or,Ji,e,t,n)},ea=q("mce-annotation"),ta=q("data-mce-annotation"),na=q("data-mce-annotation-uid"),ra=function(r,e){var t=r.selection.getRng(),n=rr.fromDom(t.startContainer),o=rr.fromDom(r.getBody()),i=e.fold(function(){return"."+ea()},function(e){return"["+ta()+'="'+e+'"]'}),a=Hr(n,t.startOffset).getOr(n),u=Zi(a,i,function(e){return Pr(e,o)}),s=function(e,t){return n=t,(r=e.dom())&&r.hasAttribute&&r.hasAttribute(n)?A.some(Cr(e,t)):A.none();var n,r};return u.bind(function(e){return s(e,""+na()).bind(function(n){return s(e,""+ta()).map(function(e){var t=oa(r,n);return{uid:n,name:e,elements:t}})})})},oa=function(e,t){var n=rr.fromDom(e.getBody());return Wi(n,"["+na()+'="'+t+'"]')},ia=function(i,e){var n,r,o,a=Ni({}),c=function(e,t){u(e,function(e){return t(e),e})},u=function(e,t){var n=a.get(),r=t(n.hasOwnProperty(e)?n[e]:{listeners:[],previous:Ni(A.none())});n[e]=r,a.set(n)},t=(n=function(){var e,t,n,r=a.get(),o=(e=fr(r),(n=Q.call(e,0)).sort(t),n);F(o,function(e){u(e,function(u){var s=u.previous.get();return ra(i,A.some(e)).fold(function(){var t;s.isSome()&&(c(t=e,function(e){F(e.listeners,function(e){return e(!1,t)})}),u.previous.set(A.none()))},function(e){var t,n,r,o=e.uid,i=e.name,a=e.elements;s.is(o)||(n=o,r=a,c(t=i,function(e){F(e.listeners,function(e){return e(!0,t,{uid:n,nodes:W(r,function(e){return e.dom()})})})}),u.previous.set(A.some(o)))}),{previous:u.previous,listeners:u.listeners}})})},r=30,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];null!==o&&clearTimeout(o),o=setTimeout(function(){n.apply(null,e),o=null},r)}});return i.on("remove",function(){t.cancel()}),i.on("nodeChange",function(){t.throttle()}),{addListener:function(e,t){u(e,function(e){return{previous:e.previous,listeners:e.listeners.concat([t])}})}}},aa=function(e,n){e.on("init",function(){e.serializer.addNodeFilter("span",function(e){F(e,function(t){var e;(e=t,A.from(e.attributes.map[ta()]).bind(n.lookup)).each(function(e){!1===e.persistent&&t.unwrap()})})})})},ua=0,sa=function(e,t){return rr.fromDom(e.dom().cloneNode(t))},ca=function(e){return sa(e,!1)},la=function(e){return sa(e,!0)},fa=function(e,t){var n,r,o=Lr(e).dom(),i=rr.fromDom(o.createDocumentFragment()),a=(n=t,(r=(o||document).createElement("div")).innerHTML=n,jr(rr.fromDom(r)));_i(i,a),Pi(e),Oi(e,i)},da="\ufeff",ma=function(e){return e===da},ga=da,pa=function(e){return e.replace(new RegExp(da,"g"),"")},ha=Oo.isElement,va=Oo.isText,ba=function(e){return va(e)&&(e=e.parentNode),ha(e)&&e.hasAttribute("data-mce-caret")},ya=function(e){return va(e)&&ma(e.data)},Ca=function(e){return ba(e)||ya(e)},xa=function(e){return e.firstChild!==e.lastChild||!Oo.isBr(e.firstChild)},wa=function(e){var t=e.container();return!(!e||!Oo.isText(t))&&(t.data.charAt(e.offset())===ga||e.isAtStart()&&ya(t.previousSibling))},Na=function(e){var t=e.container();return!(!e||!Oo.isText(t))&&(t.data.charAt(e.offset()-1)===ga||e.isAtEnd()&&ya(t.nextSibling))},Ea=function(e,t,n){var r,o,i;return(r=t.ownerDocument.createElement(e)).setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(((i=document.createElement("br")).setAttribute("data-mce-bogus","1"),i)),o=t.parentNode,n?o.insertBefore(r,t):t.nextSibling?o.insertBefore(r,t.nextSibling):o.appendChild(r),r},Sa=function(e){return va(e)&&e.data[0]===ga},ka=function(e){return va(e)&&e.data[e.data.length-1]===ga},Ta=function(e){return e&&e.hasAttribute("data-mce-caret")?(t=e.getElementsByTagName("br"),n=t[t.length-1],Oo.isBogus(n)&&n.parentNode.removeChild(n),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("_moz_abspos"),e):null;var t,n},Aa=Oo.isContentEditableTrue,Ra=Oo.isContentEditableFalse,Da=Oo.isBr,Ba=Oo.isText,Oa=Oo.matchNodeNames("script style textarea"),_a=Oo.matchNodeNames("img input textarea hr iframe video audio object"),Pa=Oo.matchNodeNames("table"),Ia=Ca,La=function(e){return!Ia(e)&&(Ba(e)?!Oa(e.parentNode):_a(e)||Da(e)||Pa(e)||Ma(e))},Ma=function(e){return!1===(t=e,Oo.isElement(t)&&"true"===t.getAttribute("unselectable"))&&Ra(e);var t},Fa=function(e,t){return La(e)&&function(e,t){for(e=e.parentNode;e&&e!==t;e=e.parentNode){if(Ma(e))return!1;if(Aa(e))return!0}return!0}(e,t)},Ua=Math.round,za=function(e){return e?{left:Ua(e.left),top:Ua(e.top),bottom:Ua(e.bottom),right:Ua(e.right),width:Ua(e.width),height:Ua(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0}},Va=function(e,t){return(e=za(e)).right=(t||(e.left=e.left+e.width),e.left),e.width=0,e},ja=function(e,t,n){return 0<=e&&e<=Math.min(t.height,n.height)/2},Ha=function(e,t){return e.bottom-e.height/2<t.top||!(e.top>t.bottom)&&ja(t.top-e.bottom,e,t)},qa=function(e,t){return e.top>t.bottom||!(e.bottom<t.top)&&ja(t.bottom-e.top,e,t)},$a=function(e){var t=e.startContainer,n=e.startOffset;return t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Wa=function(e,t){return 1===e.nodeType&&e.hasChildNodes()&&(t>=e.childNodes.length&&(t=e.childNodes.length-1),e=e.childNodes[t]),e},Ka=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Xa=function(e){return"string"==typeof e&&768<=e.charCodeAt(0)&&Ka.test(e)},Ya=function(e,t){for(var n=[],r=0;r<e.length;r++){var o=e[r];if(!o.isSome())return A.none();n.push(o.getOrDie())}return A.some(t.apply(null,n))},Ga=[].slice,Ja=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Ga.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(!n[t](e))return!1;return!0}},Qa=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=Ga.call(arguments);return function(e){for(var t=0;t<n.length;t++)if(n[t](e))return!0;return!1}},Za=Oo.isElement,eu=La,tu=Oo.matchStyleValues("display","block table"),nu=Oo.matchStyleValues("float","left right"),ru=Ja(Za,eu,y(nu)),ou=y(Oo.matchStyleValues("white-space","pre pre-line pre-wrap")),iu=Oo.isText,au=Oo.isBr,uu=hi.nodeIndex,su=Wa,cu=function(e){return"createRange"in e?e.createRange():hi.DOM.createRng()},lu=function(e){return e&&/[\r\n\t ]/.test(e)},fu=function(e){return!!e.setStart&&!!e.setEnd},du=function(e){var t,n=e.startContainer,r=e.startOffset;return!!(lu(e.toString())&&ou(n.parentNode)&&Oo.isText(n)&&(t=n.data,lu(t[r-1])||lu(t[r+1])))},mu=function(e){return 0===e.left&&0===e.right&&0===e.top&&0===e.bottom},gu=function(e){var t,n,r,o,i,a,u,s;return t=0<(n=e.getClientRects()).length?za(n[0]):za(e.getBoundingClientRect()),!fu(e)&&au(e)&&mu(t)?(i=(r=e).ownerDocument,a=cu(i),u=i.createTextNode("\xa0"),(s=r.parentNode).insertBefore(u,r),a.setStart(u,0),a.setEnd(u,1),o=za(a.getBoundingClientRect()),s.removeChild(u),o):mu(t)&&fu(e)?function(e){var t=e.startContainer,n=e.endContainer,r=e.startOffset,o=e.endOffset;if(t===n&&Oo.isText(n)&&0===r&&1===o){var i=e.cloneRange();return i.setEndAfter(n),gu(i)}return null}(e):t},pu=function(e,t){var n=Va(e,t);return n.width=1,n.right=n.left+1,n},hu=function(e){var t,n,r=[],o=function(e){var t,n;0!==e.height&&(0<r.length&&(t=e,n=r[r.length-1],t.left===n.left&&t.top===n.top&&t.bottom===n.bottom&&t.right===n.right)||r.push(e))},i=function(e,t){var n=cu(e.ownerDocument);if(t<e.data.length){if(Xa(e.data[t]))return r;if(Xa(e.data[t-1])&&(n.setStart(e,t),n.setEnd(e,t+1),!du(n)))return o(pu(gu(n),!1)),r}0<t&&(n.setStart(e,t-1),n.setEnd(e,t),du(n)||o(pu(gu(n),!1))),t<e.data.length&&(n.setStart(e,t),n.setEnd(e,t+1),du(n)||o(pu(gu(n),!0)))};if(iu(e.container()))return i(e.container(),e.offset()),r;if(Za(e.container()))if(e.isAtEnd())n=su(e.container(),e.offset()),iu(n)&&i(n,n.data.length),ru(n)&&!au(n)&&o(pu(gu(n),!1));else{if(n=su(e.container(),e.offset()),iu(n)&&i(n,0),ru(n)&&e.isAtEnd())return o(pu(gu(n),!1)),r;t=su(e.container(),e.offset()-1),ru(t)&&!au(t)&&(tu(t)||tu(n)||!ru(n))&&o(pu(gu(t),!1)),ru(n)&&o(pu(gu(n),!0))}return r};function vu(t,n,e){var r=function(){return e||(e=hu(vu(t,n))),e};return{container:q(t),offset:q(n),toRange:function(){var e;return(e=cu(t.ownerDocument)).setStart(t,n),e.setEnd(t,n),e},getClientRects:r,isVisible:function(){return 0<r().length},isAtStart:function(){return iu(t),0===n},isAtEnd:function(){return iu(t)?n>=t.data.length:n>=t.childNodes.length},isEqual:function(e){return e&&t===e.container()&&n===e.offset()},getNode:function(e){return su(t,e?n-1:n)}}}(Xi=vu||(vu={})).fromRangeStart=function(e){return Xi(e.startContainer,e.startOffset)},Xi.fromRangeEnd=function(e){return Xi(e.endContainer,e.endOffset)},Xi.after=function(e){return Xi(e.parentNode,uu(e)+1)},Xi.before=function(e){return Xi(e.parentNode,uu(e))},Xi.isAbove=function(e,t){return Ya([ee(t.getClientRects()),te(e.getClientRects())],Ha).getOr(!1)},Xi.isBelow=function(e,t){return Ya([te(t.getClientRects()),ee(e.getClientRects())],qa).getOr(!1)},Xi.isAtStart=function(e){return!!e&&e.isAtStart()},Xi.isAtEnd=function(e){return!!e&&e.isAtEnd()},Xi.isTextPosition=function(e){return!!e&&Oo.isText(e.container())},Xi.isElementPosition=function(e){return!1===Xi.isTextPosition(e)};var bu,yu,Cu,xu=vu,wu=Oo.isText,Nu=Oo.isBogus,Eu=hi.nodeIndex,Su=function(e){var t=e.parentNode;return Nu(t)?Su(t):t},ku=function(e){return e?qt.reduce(e.childNodes,function(e,t){return Nu(t)&&"BR"!==t.nodeName?e=e.concat(ku(t)):e.push(t),e},[]):[]},Tu=function(t){return function(e){return t===e}},Au=function(e){var t,r,n,o;return(wu(e)?"text()":e.nodeName.toLowerCase())+"["+(r=ku(Su(t=e)),n=qt.findIndex(r,Tu(t),t),r=r.slice(0,n+1),o=qt.reduce(r,function(e,t,n){return wu(t)&&wu(r[n-1])&&e++,e},0),r=qt.filter(r,Oo.matchNodeNames(t.nodeName)),(n=qt.findIndex(r,Tu(t),t))-o)+"]"},Ru=function(e,t){var n,r,o,i,a,u=[];return n=t.container(),r=t.offset(),wu(n)?o=function(e,t){for(;(e=e.previousSibling)&&wu(e);)t+=e.data.length;return t}(n,r):(r>=(i=n.childNodes).length?(o="after",r=i.length-1):o="before",n=i[r]),u.push(Au(n)),a=function(e,t,n){var r=[];for(t=t.parentNode;!(t===e||n&&n(t));t=t.parentNode)r.push(t);return r}(e,n),a=qt.filter(a,y(Oo.isBogus)),(u=u.concat(qt.map(a,function(e){return Au(e)}))).reverse().join("/")+","+o},Du=function(e,t){var n,r,o;return t?(t=(n=t.split(","))[0].split("/"),o=1<n.length?n[1]:"before",(r=qt.reduce(t,function(e,t){return(t=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t))?("text()"===t[1]&&(t[1]="#text"),n=e,r=t[1],o=parseInt(t[2],10),i=ku(n),i=qt.filter(i,function(e,t){return!wu(e)||!wu(i[t-1])}),(i=qt.filter(i,Oo.matchNodeNames(r)))[o]):null;var n,r,o,i},e))?wu(r)?function(e,t){for(var n,r=e,o=0;wu(r);){if(n=r.data.length,o<=t&&t<=o+n){e=r,t-=o;break}if(!wu(r.nextSibling)){e=r,t=n;break}o+=n,r=r.nextSibling}return wu(e)&&t>e.data.length&&(t=e.data.length),xu(e,t)}(r,parseInt(o,10)):(o="after"===o?Eu(r)+1:Eu(r),xu(r.parentNode,o)):null):null},Bu=function(e,t){Oo.isText(t)&&0===t.data.length&&e.remove(t)},Ou=function(e,t,n){var r,o,i,a,u,s,c;Oo.isDocumentFragment(n)?(i=e,a=t,u=n,s=A.from(u.firstChild),c=A.from(u.lastChild),a.insertNode(u),s.each(function(e){return Bu(i,e.previousSibling)}),c.each(function(e){return Bu(i,e.nextSibling)})):(r=e,o=n,t.insertNode(o),Bu(r,o.previousSibling),Bu(r,o.nextSibling))},_u=Oo.isContentEditableFalse,Pu=function(e,t,n,r,o){var i,a=r[o?"startContainer":"endContainer"],u=r[o?"startOffset":"endOffset"],s=[],c=0,l=e.getRoot();for(Oo.isText(a)?s.push(n?function(e,t,n){var r,o;for(o=e(t.data.slice(0,n)).length,r=t.previousSibling;r&&Oo.isText(r);r=r.previousSibling)o+=e(r.data).length;return o}(t,a,u):u):(u>=(i=a.childNodes).length&&i.length&&(c=1,u=Math.max(0,i.length-1)),s.push(e.nodeIndex(i[u],n)+c));a&&a!==l;a=a.parentNode)s.push(e.nodeIndex(a,n));return s},Iu=function(e,t,n){var r=0;return Yt.each(e.select(t),function(e){if("all"!==e.getAttribute("data-mce-bogus"))return e!==n&&void r++}),r},Lu=function(e,t){var n,r,o,i=t?"start":"end";n=e[i+"Container"],r=e[i+"Offset"],Oo.isElement(n)&&"TR"===n.nodeName&&(n=(o=n.childNodes)[Math.min(t?r:r-1,o.length-1)])&&(r=t?0:n.childNodes.length,e["set"+(t?"Start":"End")](n,r))},Mu=function(e){return Lu(e,!0),Lu(e,!1),e},Fu=function(e,t){var n;if(Oo.isElement(e)&&(e=Wa(e,t),_u(e)))return e;if(Ca(e)){if(Oo.isText(e)&&ba(e)&&(e=e.parentNode),n=e.previousSibling,_u(n))return n;if(n=e.nextSibling,_u(n))return n}},Uu=function(e,t,n){var r=n.getNode(),o=r?r.nodeName:null,i=n.getRng();if(_u(r)||"IMG"===o)return{name:o,index:Iu(n.dom,o,r)};var a,u,s,c,l,f,d,m=Fu((a=i).startContainer,a.startOffset)||Fu(a.endContainer,a.endOffset);return m?{name:o=m.tagName,index:Iu(n.dom,o,m)}:(u=e,c=t,l=i,f=(s=n).dom,(d={}).start=Pu(f,u,c,l,!0),s.isCollapsed()||(d.end=Pu(f,u,c,l,!1)),d)},zu=function(e,t,n){var r={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",r,"&#xFEFF;"):e.create("span",r)},Vu=function(e,t){var n=e.dom,r=e.getRng(),o=n.uniqueId(),i=e.isCollapsed(),a=e.getNode(),u=a.nodeName;if("IMG"===u)return{name:u,index:Iu(n,u,a)};var s=Mu(r.cloneRange());if(!i){s.collapse(!1);var c=zu(n,o+"_end",t);Ou(n,s,c)}(r=Mu(r)).collapse(!0);var l=zu(n,o+"_start",t);return Ou(n,r,l),e.moveToBookmark({id:o,keep:1}),{id:o}},ju={getBookmark:function(e,t,n){return 2===t?Uu(pa,n,e):3===t?(o=(r=e).getRng(),{start:Ru(r.dom.getRoot(),xu.fromRangeStart(o)),end:Ru(r.dom.getRoot(),xu.fromRangeEnd(o))}):t?{rng:e.getRng()}:Vu(e,!1);var r,o},getUndoBookmark:d(Uu,$,!0),getPersistentBookmark:Vu},Hu="_mce_caret",qu=function(e){return Oo.isElement(e)&&e.id===Hu},$u=function(e,t){for(;t&&t!==e;){if(t.id===Hu)return t;t=t.parentNode}return null},Wu=Oo.isElement,Ku=Oo.isText,Xu=function(e){var t=e.parentNode;t&&t.removeChild(e)},Yu=function(e,t){0===t.length?Xu(e):e.nodeValue=t},Gu=function(e){var t=pa(e);return{count:e.length-t.length,text:t}},Ju=function(e,t){return es(e),t},Qu=function(e,t){var n,r,o,i=t.container(),a=(n=ne(i.childNodes),r=e,o=L(n,r),-1===o?A.none():A.some(o)).map(function(e){return e<t.offset()?xu(i,t.offset()-1):t}).getOr(t);return es(e),a},Zu=function(e,t){return Ku(e)&&t.container()===e?(r=t,o=Gu((n=e).data.substr(0,r.offset())),i=Gu(n.data.substr(r.offset())),0<(a=o.text+i.text).length?(Yu(n,a),xu(n,r.offset()-o.count)):r):Ju(e,t);var n,r,o,i,a},es=function(e){if(Wu(e)&&Ca(e)&&(xa(e)?e.removeAttribute("data-mce-caret"):Xu(e)),Ku(e)){var t=pa(function(e){try{return e.nodeValue}catch(t){return""}}(e));Yu(e,t)}},ts={removeAndReposition:function(e,t){return xu.isTextPosition(t)?Zu(e,t):(n=e,(r=t).container()===n.parentNode?Qu(n,r):Ju(n,r));var n,r},remove:es},ns=tr.detect().browser,rs=Oo.isContentEditableFalse,os=function(e,t,n){var r,o,i,a,u,s=Va(t.getBoundingClientRect(),n);return i="BODY"===e.tagName?(r=e.ownerDocument.documentElement,o=e.scrollLeft||r.scrollLeft,e.scrollTop||r.scrollTop):(u=e.getBoundingClientRect(),o=e.scrollLeft-u.left,e.scrollTop-u.top),s.left+=o,s.right+=o,s.top+=i,s.bottom+=i,s.width=1,0<(a=t.offsetWidth-t.clientWidth)&&(n&&(a*=-1),s.left+=a,s.right+=a),s},is=function(a,u,e){var t,s,c=Ni(A.none()),l=function(){!function(e){var t,n,r,o,i;for(t=pn("*[contentEditable=false]",e),o=0;o<t.length;o++)r=(n=t[o]).previousSibling,ka(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(i.length-1,1)),r=n.nextSibling,Sa(r)&&(1===(i=r.data).length?r.parentNode.removeChild(r):r.deleteData(0,1))}(a),s&&(ts.remove(s),s=null),c.get().each(function(e){pn(e.caret).remove(),c.set(A.none())}),clearInterval(t)},f=function(){t=ve.setInterval(function(){e()?pn("div.mce-visual-caret",a).toggleClass("mce-visual-caret-hidden"):pn("div.mce-visual-caret",a).addClass("mce-visual-caret-hidden")},500)};return{show:function(t,e){var n,r,o;if(l(),o=e,Oo.isElement(o)&&/^(TD|TH)$/i.test(o.tagName))return null;if(!u(e))return s=function(e,t){var n,r,o;if(r=e.ownerDocument.createTextNode(ga),o=e.parentNode,t){if(n=e.previousSibling,va(n)){if(Ca(n))return n;if(ka(n))return n.splitText(n.data.length-1)}o.insertBefore(r,e)}else{if(n=e.nextSibling,va(n)){if(Ca(n))return n;if(Sa(n))return n.splitText(1),n}e.nextSibling?o.insertBefore(r,e.nextSibling):o.appendChild(r)}return r}(e,t),r=e.ownerDocument.createRange(),rs(s.nextSibling)?(r.setStart(s,0),r.setEnd(s,0)):(r.setStart(s,1),r.setEnd(s,1)),r;s=Ea("p",e,t),n=os(a,e,t),pn(s).css("top",n.top);var i=pn('<div class="mce-visual-caret" data-mce-bogus="all"></div>').css(n).appendTo(a)[0];return c.set(A.some({caret:i,element:e,before:t})),c.get().each(function(e){t&&pn(e.caret).addClass("mce-visual-caret-before")}),f(),(r=e.ownerDocument.createRange()).setStart(s,0),r.setEnd(s,0),r},hide:l,getCss:function(){return".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}"},reposition:function(){c.get().each(function(e){var t=os(a,e.element,e.before);pn(e.caret).css(t)})},destroy:function(){return ve.clearInterval(t)}}},as=function(){return ns.isIE()||ns.isEdge()||ns.isFirefox()},us=function(e){return rs(e)||Oo.isTable(e)&&as()},ss=(bu="\xa0",function(e){return bu===e}),cs=function(e){return/^[\r\n\t ]$/.test(e)},ls=function(e){return!cs(e)&&!ss(e)},fs=Oo.isContentEditableFalse,ds=Oo.matchStyleValues("display","block table table-cell table-caption list-item"),ms=Ca,gs=ba,ps=Oo.isElement,hs=La,vs=function(e){return 0<e},bs=function(e){return e<0},ys=function(e,t){for(var n;n=e(t);)if(!gs(n))return n;return null},Cs=function(e,t,n,r,o){var i=new oo(e,r);if(bs(t)){if((fs(e)||gs(e))&&n(e=ys(i.prev,!0)))return e;for(;e=ys(i.prev,o);)if(n(e))return e}if(vs(t)){if((fs(e)||gs(e))&&n(e=ys(i.next,!0)))return e;for(;e=ys(i.next,o);)if(n(e))return e}return null},xs=function(e,t){for(;e&&e!==t;){if(ds(e))return e;e=e.parentNode}return null},ws=function(e,t,n){return xs(e.container(),n)===xs(t.container(),n)},Ns=function(e,t){var n,r;return t?(n=t.container(),r=t.offset(),ps(n)?n.childNodes[r+e]:null):null},Es=function(e,t){var n=t.ownerDocument.createRange();return e?(n.setStartBefore(t),n.setEndBefore(t)):(n.setStartAfter(t),n.setEndAfter(t)),n},Ss=function(e,t,n){var r,o,i,a;for(o=e?"previousSibling":"nextSibling";n&&n!==t;){if(r=n[o],ms(r)&&(r=r[o]),fs(r)){if(a=n,xs(r,i=t)===xs(a,i))return r;break}if(hs(r))break;n=n.parentNode}return null},ks=d(Es,!0),Ts=d(Es,!1),As=function(e,t,n){var r,o,i,a,u=d(Ss,!0,t),s=d(Ss,!1,t);if(o=n.startContainer,i=n.startOffset,ba(o)){if(ps(o)||(o=o.parentNode),"before"===(a=o.getAttribute("data-mce-caret"))&&(r=o.nextSibling,us(r)))return ks(r);if("after"===a&&(r=o.previousSibling,us(r)))return Ts(r)}if(!n.collapsed)return n;if(Oo.isText(o)){if(ms(o)){if(1===e){if(r=s(o))return ks(r);if(r=u(o))return Ts(r)}if(-1===e){if(r=u(o))return Ts(r);if(r=s(o))return ks(r)}return n}if(ka(o)&&i>=o.data.length-1)return 1===e&&(r=s(o))?ks(r):n;if(Sa(o)&&i<=1)return-1===e&&(r=u(o))?Ts(r):n;if(i===o.data.length)return(r=s(o))?ks(r):n;if(0===i)return(r=u(o))?Ts(r):n}return n},Rs=function(e,t){var n=Ns(e,t);return fs(n)&&!Oo.isBogusAll(n)},Ds=function(e,t){return Oo.isTable(Ns(e,t))},Bs=function(e,t){return A.from(Ns(e?0:-1,t)).filter(fs)},Os=function(e,t,n){var r=As(e,t,n);return-1===e?vu.fromRangeStart(r):vu.fromRangeEnd(r)},_s=d(Rs,0),Ps=d(Rs,-1),Is=d(Ds,0),Ls=d(Ds,-1),Ms=function(n,r,o){return A.from(o.container()).filter(Oo.isText).exists(function(e){var t=n?0:-1;return r(e.data.charAt(o.offset()+t))})},Fs=d(Ms,!0,cs),Us=d(Ms,!1,cs),zs=function(e){return A.from(e.getNode()).map(rr.fromDom)},Vs=function(e,t){for(;t=e(t);)if(t.isVisible())return t;return t},js=function(e,t){var n=ws(e,t);return!(n||!Oo.isBr(e.getNode()))||n};(Cu=yu||(yu={}))[Cu.Backwards=-1]="Backwards",Cu[Cu.Forwards=1]="Forwards";var Hs=Oo.isContentEditableFalse,qs=Oo.isText,$s=Oo.isElement,Ws=Oo.isBr,Ks=La,Xs=function(e){return _a(e)||!!Ma(t=e)&&!0!==z(ne(t.getElementsByTagName("*")),function(e,t){return e||Aa(t)},!1);var t},Ys=Fa,Gs=function(e,t){return e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null},Js=function(e,t){if(vs(e)){if(Ks(t.previousSibling)&&!qs(t.previousSibling))return xu.before(t);if(qs(t))return xu(t,0)}if(bs(e)){if(Ks(t.nextSibling)&&!qs(t.nextSibling))return xu.after(t);if(qs(t))return xu(t,t.data.length)}return bs(e)?Ws(t)?xu.before(t):xu.after(t):xu.before(t)},Qs=function(e,t,n){var r,o,i,a,u;if(!$s(n)||!t)return null;if(t.isEqual(xu.after(n))&&n.lastChild){if(u=xu.after(n.lastChild),bs(e)&&Ks(n.lastChild)&&$s(n.lastChild))return Ws(n.lastChild)?xu.before(n.lastChild):u}else u=t;var s,c,l,f=u.container(),d=u.offset();if(qs(f)){if(bs(e)&&0<d)return xu(f,--d);if(vs(e)&&d<f.length)return xu(f,++d);r=f}else{if(bs(e)&&0<d&&(o=Gs(f,d-1),Ks(o)))return!Xs(o)&&(i=Cs(o,e,Ys,o))?qs(i)?xu(i,i.data.length):xu.after(i):qs(o)?xu(o,o.data.length):xu.before(o);if(vs(e)&&d<f.childNodes.length&&(o=Gs(f,d),Ks(o)))return Ws(o)?(s=n,(l=(c=o).nextSibling)&&Ks(l)?qs(l)?xu(l,0):xu.before(l):Qs(yu.Forwards,xu.after(c),s)):!Xs(o)&&(i=Cs(o,e,Ys,o))?qs(i)?xu(i,0):xu.before(i):qs(o)?xu(o,0):xu.after(o);r=o||u.getNode()}return(vs(e)&&u.isAtEnd()||bs(e)&&u.isAtStart())&&(r=Cs(r,e,q(!0),n,!0),Ys(r,n))?Js(e,r):(o=Cs(r,e,Ys,n),!(a=qt.last(U(function(e,t){for(var n=[];e&&e!==t;)n.push(e),e=e.parentNode;return n}(f,n),Hs)))||o&&a.contains(o)?o?Js(e,o):null:u=vs(e)?xu.after(a):xu.before(a))},Zs=function(t){return{next:function(e){return Qs(yu.Forwards,e,t)},prev:function(e){return Qs(yu.Backwards,e,t)}}},ec=function(e){return xu.isTextPosition(e)?0===e.offset():La(e.getNode())},tc=function(e){if(xu.isTextPosition(e)){var t=e.container();return e.offset()===t.data.length}return La(e.getNode(!0))},nc=function(e,t){return!xu.isTextPosition(e)&&!xu.isTextPosition(t)&&e.getNode()===t.getNode(!0)},rc=function(e,t,n){return e?!nc(t,n)&&(r=t,!(!xu.isTextPosition(r)&&Oo.isBr(r.getNode())))&&tc(t)&&ec(n):!nc(n,t)&&ec(t)&&tc(n);var r},oc=function(e,t,n){var r=Zs(t);return A.from(e?r.next(n):r.prev(n))},ic=function(e,t){var n,r,o,i,a,u=e?t.firstChild:t.lastChild;return Oo.isText(u)?A.some(xu(u,e?0:u.data.length)):u?La(u)?A.some(e?xu.before(u):(a=u,Oo.isBr(a)?xu.before(a):xu.after(a))):(r=t,o=u,i=(n=e)?xu.before(o):xu.after(o),oc(n,r,i)):A.none()},ac=d(oc,!0),uc=d(oc,!1),sc={fromPosition:oc,nextPosition:ac,prevPosition:uc,navigate:function(t,n,r){return oc(t,n,r).bind(function(e){return ws(r,e,n)&&rc(t,r,e)?oc(t,n,e):A.some(e)})},positionIn:ic,firstPositionIn:d(ic,!0),lastPositionIn:d(ic,!1)},cc=function(e,t){return!e.isBlock(t)||t.innerHTML||de.ie||(t.innerHTML='<br data-mce-bogus="1" />'),t},lc=function(e,t){return sc.lastPositionIn(e).fold(function(){return!1},function(e){return t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0})},fc=function(e,t,n){return!(!1!==t.hasChildNodes()||!$u(e,t)||(o=n,i=(r=t).ownerDocument.createTextNode(ga),r.appendChild(i),o.setStart(i,0),o.setEnd(i,0),0));var r,o,i},dc=function(e,t,n,r){var o,i,a,u,s=n[t?"start":"end"],c=e.getRoot();if(s){for(a=s[0],i=c,o=s.length-1;1<=o;o--){if(u=i.childNodes,fc(c,i,r))return!0;if(s[o]>u.length-1)return!!fc(c,i,r)||lc(i,r);i=u[s[o]]}3===i.nodeType&&(a=Math.min(s[0],i.nodeValue.length)),1===i.nodeType&&(a=Math.min(s[0],i.childNodes.length)),t?r.setStart(i,a):r.setEnd(i,a)}return!0},mc=function(e){return Oo.isText(e)&&0<e.data.length},gc=function(e,t,n){var r,o,i,a,u,s,c=e.get(n.id+"_"+t),l=n.keep;if(c){if(r=c.parentNode,o="start"===t?l?c.hasChildNodes()?(r=c.firstChild,1):mc(c.nextSibling)?(r=c.nextSibling,0):mc(c.previousSibling)?(r=c.previousSibling,c.previousSibling.data.length):(r=c.parentNode,e.nodeIndex(c)+1):e.nodeIndex(c):l?c.hasChildNodes()?(r=c.firstChild,1):mc(c.previousSibling)?(r=c.previousSibling,c.previousSibling.data.length):(r=c.parentNode,e.nodeIndex(c)):e.nodeIndex(c),u=r,s=o,!l){for(a=c.previousSibling,i=c.nextSibling,Yt.each(Yt.grep(c.childNodes),function(e){Oo.isText(e)&&(e.nodeValue=e.nodeValue.replace(/\uFEFF/g,""))});c=e.get(n.id+"_"+t);)e.remove(c,!0);a&&i&&a.nodeType===i.nodeType&&Oo.isText(a)&&!de.opera&&(o=a.nodeValue.length,a.appendData(i.nodeValue),e.remove(i),u=a,s=o)}return A.some(xu(u,s))}return A.none()},pc=function(e,t){var n,r,o,i,a,u,s,c,l,f,d,m,g,p,h,v,b=e.dom;if(t){if(v=t,Yt.isArray(v.start))return p=t,h=(g=b).createRng(),dc(g,!0,p,h)&&dc(g,!1,p,h)?A.some(h):A.none();if("string"==typeof t.start)return A.some((f=t,d=(l=b).createRng(),m=Du(l.getRoot(),f.start),d.setStart(m.container(),m.offset()),m=Du(l.getRoot(),f.end),d.setEnd(m.container(),m.offset()),d));if(t.hasOwnProperty("id"))return s=gc(o=b,"start",i=t),c=gc(o,"end",i),Ya([s,(a=c,u=s,a.isSome()?a:u)],function(e,t){var n=o.createRng();return n.setStart(cc(o,e.container()),e.offset()),n.setEnd(cc(o,t.container()),t.offset()),n});if(t.hasOwnProperty("name"))return n=b,r=t,A.from(n.select(r.name)[r.index]).map(function(e){var t=n.createRng();return t.selectNode(e),t});if(t.hasOwnProperty("rng"))return A.some(t.rng)}return A.none()},hc=function(e,t,n){return ju.getBookmark(e,t,n)},vc=function(t,e){pc(t,e).each(function(e){t.setRng(e)})},bc=function(e){return Oo.isElement(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type")},yc=function(e){return e&&/^(IMG)$/.test(e.nodeName)},Cc=function(e){return e&&3===e.nodeType&&/^([\t \r\n]+|)$/.test(e.nodeValue)},xc=function(e,t,n){return"color"!==n&&"backgroundColor"!==n||(t=e.toHex(t)),"fontWeight"===n&&700===t&&(t="bold"),"fontFamily"===n&&(t=t.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),""+t},wc={isInlineBlock:yc,moveStart:function(e,t,n){var r,o,i,a=n.startOffset,u=n.startContainer;if((n.startContainer!==n.endContainer||!yc(n.startContainer.childNodes[n.startOffset]))&&1===u.nodeType)for(a<(i=u.childNodes).length?r=new oo(u=i[a],e.getParent(u,e.isBlock)):(r=new oo(u=i[i.length-1],e.getParent(u,e.isBlock))).next(!0),o=r.current();o;o=r.next())if(3===o.nodeType&&!Cc(o))return n.setStart(o,0),void t.setRng(n)},getNonWhiteSpaceSibling:function(e,t,n){if(e)for(t=t?"nextSibling":"previousSibling",e=n?e:e[t];e;e=e[t])if(1===e.nodeType||!Cc(e))return e},isTextBlock:function(e,t){return t.nodeType&&(t=t.nodeName),!!e.schema.getTextBlockElements()[t.toLowerCase()]},isValid:function(e,t,n){return e.schema.isValidChild(t,n)},isWhiteSpaceNode:Cc,replaceVars:function(e,n){return"string"!=typeof e?e=e(n):n&&(e=e.replace(/%(\w+)/g,function(e,t){return n[t]||e})),e},isEq:function(e,t){return t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()},normalizeStyleValue:xc,getStyle:function(e,t,n){return xc(e,e.getStyle(t,n),n)},getTextDecoration:function(t,e){var n;return t.getParent(e,function(e){return(n=t.getStyle(e,"text-decoration"))&&"none"!==n}),n},getParents:function(e,t,n){return e.getParents(t,n,e.getRoot())}},Nc=bc,Ec=wc.getParents,Sc=wc.isWhiteSpaceNode,kc=wc.isTextBlock,Tc=function(e,t){for(void 0===t&&(t=3===e.nodeType?e.length:e.childNodes.length);e&&e.hasChildNodes();)(e=e.childNodes[t])&&(t=3===e.nodeType?e.length:e.childNodes.length);return{node:e,offset:t}},Ac=function(e,t){for(var n=t;n;){if(1===n.nodeType&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Rc=function(e,t,n,r){var o,i,a=n.nodeValue;return void 0===r&&(r=e?a.length:0),e?(o=a.lastIndexOf(" ",r),-1!==(o=(i=a.lastIndexOf("\xa0",r))<o?o:i)&&!t&&(o<r||!e)&&o<=a.length&&o++):(o=a.indexOf(" ",r),i=a.indexOf("\xa0",r),o=-1!==o&&(-1===i||o<i)?o:i),o},Dc=function(e,t,n,r,o,i){var a,u,s,c;if(3===n.nodeType){if(-1!==(s=Rc(o,i,n,r)))return{container:n,offset:s};c=n}for(a=new oo(n,e.getParent(n,e.isBlock)||t);u=a[o?"prev":"next"]();)if(3!==u.nodeType||Nc(u.parentNode)){if(e.isBlock(u)||wc.isEq(u,"BR"))break}else if(-1!==(s=Rc(o,i,c=u)))return{container:u,offset:s};if(c)return{container:c,offset:r=o?0:c.length}},Bc=function(e,t,n,r,o){var i,a,u,s;for(3===r.nodeType&&0===r.nodeValue.length&&r[o]&&(r=r[o]),i=Ec(e,r),a=0;a<i.length;a++)for(u=0;u<t.length;u++)if(!("collapsed"in(s=t[u])&&s.collapsed!==n.collapsed)&&e.is(i[a],s.selector))return i[a];return r},Oc=function(t,e,n,r){var o,i=t.dom,a=i.getRoot();if(e[0].wrapper||(o=i.getParent(n,e[0].block,a)),!o){var u=i.getParent(n,"LI,TD,TH");o=i.getParent(3===n.nodeType?n.parentNode:n,function(e){return e!==a&&kc(t,e)},u)}if(o&&e[0].wrapper&&(o=Ec(i,o,"ul,ol").reverse()[0]||o),!o)for(o=n;o[r]&&!i.isBlock(o[r])&&(o=o[r],!wc.isEq(o,"br")););return o||n},_c=function(e,t,n,r,o,i,a){var u,s,c,l,f,d;if(u=s=a?n:o,l=a?"previousSibling":"nextSibling",f=e.getRoot(),3===u.nodeType&&!Sc(u)&&(a?0<r:i<u.nodeValue.length))return u;for(;;){if(!t[0].block_expand&&e.isBlock(s))return s;for(c=s[l];c;c=c[l])if(!Nc(c)&&!Sc(c)&&("BR"!==(d=c).nodeName||!d.getAttribute("data-mce-bogus")||d.nextSibling))return s;if(s===f||s.parentNode===f){u=s;break}s=s.parentNode}return u},Pc=function(e,t,n,r){var o,i=t.startContainer,a=t.startOffset,u=t.endContainer,s=t.endOffset,c=e.dom;return 1===i.nodeType&&i.hasChildNodes()&&3===(i=Wa(i,a)).nodeType&&(a=0),1===u.nodeType&&u.hasChildNodes()&&3===(u=Wa(u,t.collapsed?s:s-1)).nodeType&&(s=u.nodeValue.length),i=Ac(c,i),u=Ac(c,u),(Nc(i.parentNode)||Nc(i))&&(i=Nc(i)?i:i.parentNode,3===(i=t.collapsed?i.previousSibling||i:i.nextSibling||i).nodeType&&(a=t.collapsed?i.length:0)),(Nc(u.parentNode)||Nc(u))&&(u=Nc(u)?u:u.parentNode,3===(u=t.collapsed?u.nextSibling||u:u.previousSibling||u).nodeType&&(s=t.collapsed?0:u.length)),t.collapsed&&((o=Dc(c,e.getBody(),i,a,!0,r))&&(i=o.container,a=o.offset),(o=Dc(c,e.getBody(),u,s,!1,r))&&(u=o.container,s=o.offset)),n[0].inline&&(u=r?u:function(e,t){var n=Tc(e,t);if(n.node){for(;n.node&&0===n.offset&&n.node.previousSibling;)n=Tc(n.node.previousSibling);n.node&&0<n.offset&&3===n.node.nodeType&&" "===n.node.nodeValue.charAt(n.offset-1)&&1<n.offset&&(e=n.node).splitText(n.offset-1)}return e}(u,s)),(n[0].inline||n[0].block_expand)&&(n[0].inline&&3===i.nodeType&&0!==a||(i=_c(c,n,i,a,u,s,!0)),n[0].inline&&3===u.nodeType&&s!==u.nodeValue.length||(u=_c(c,n,i,a,u,s,!1))),n[0].selector&&!1!==n[0].expand&&!n[0].inline&&(i=Bc(c,n,t,i,"previousSibling"),u=Bc(c,n,t,u,"nextSibling")),(n[0].block||n[0].selector)&&(i=Oc(e,n,i,"previousSibling"),u=Oc(e,n,u,"nextSibling"),n[0].block&&(c.isBlock(i)||(i=_c(c,n,i,a,u,s,!0)),c.isBlock(u)||(u=_c(c,n,i,a,u,s,!1)))),1===i.nodeType&&(a=c.nodeIndex(i),i=i.parentNode),1===u.nodeType&&(s=c.nodeIndex(u)+1,u=u.parentNode),{startContainer:i,startOffset:a,endContainer:u,endOffset:s}},Ic=Yt.each,Lc=function(e,t,o){var n,r,i,a,u,s,c,l=t.startContainer,f=t.startOffset,d=t.endContainer,m=t.endOffset;if(0<(c=e.select("td[data-mce-selected],th[data-mce-selected]")).length)Ic(c,function(e){o([e])});else{var g,p,h,v=function(e){var t;return 3===(t=e[0]).nodeType&&t===l&&f>=t.nodeValue.length&&e.splice(0,1),t=e[e.length-1],0===m&&0<e.length&&t===d&&3===t.nodeType&&e.splice(e.length-1,1),e},b=function(e,t,n){for(var r=[];e&&e!==n;e=e[t])r.push(e);return r},y=function(e,t){do{if(e.parentNode===t)return e;e=e.parentNode}while(e)},C=function(e,t,n){var r=n?"nextSibling":"previousSibling";for(u=(a=e).parentNode;a&&a!==t;a=u)u=a.parentNode,(s=b(a===e?a:a[r],r)).length&&(n||s.reverse(),o(v(s)))};if(1===l.nodeType&&l.hasChildNodes()&&(l=l.childNodes[f]),1===d.nodeType&&d.hasChildNodes()&&(p=m,h=(g=d).childNodes,--p>h.length-1?p=h.length-1:p<0&&(p=0),d=h[p]||g),l===d)return o(v([l]));for(n=e.findCommonAncestor(l,d),a=l;a;a=a.parentNode){if(a===d)return C(l,n,!0);if(a===n)break}for(a=d;a;a=a.parentNode){if(a===l)return C(d,n);if(a===n)break}r=y(l,n)||l,i=y(d,n)||d,C(l,r,!0),(s=b(r===l?r:r.nextSibling,"nextSibling",i===d?i.nextSibling:i)).length&&o(v(s)),C(d,i)}},Mc=function WN(n,r){var t=function(e){return n(e)?A.from(e.dom().nodeValue):A.none()},e=tr.detect().browser,o=e.isIE()&&10===e.version.major?function(e){try{return t(e)}catch($N){return A.none()}}:t;return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return o(e).getOr("")},getOption:o,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=t}}}(lr,"text"),Fc=function(e){return Mc.get(e)},Uc=function(r,o,i,a){return Mr(o).fold(function(){return"skipping"},function(e){return"br"===a||lr(n=o)&&"\ufeff"===Fc(n)?"valid":cr(t=o)&&qi(t,ea())?"existing":qu(o)?"caret":wc.isValid(r,i,a)&&wc.isValid(r,ur(e),i)?"valid":"invalid-child";var t,n})},zc=function(e,t,n,r){var o,i,a=t.uid,u=void 0===a?(o="mce-annotation",i=(new Date).getTime(),o+"_"+Math.floor(1e9*Math.random())+ ++ua+String(i)):a,s=function h(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&(n[r[o]]=e[r[o]])}return n}(t,["uid"]),c=rr.fromTag("span",e);ji(c,ea()),br(c,""+na(),u),br(c,""+ta(),n);var l,f=r(u,s),d=f.attributes,m=void 0===d?{}:d,g=f.classes,p=void 0===g?[]:g;return yr(c,m),l=c,F(p,function(e){ji(l,e)}),c},Vc=function(i,e,t,n,r){var a=[],u=zc(i.getDoc(),r,t,n),s=Ni(A.none()),c=function(){s.set(A.none())},l=function(e){F(e,o)},o=function(e){var t,n;switch(Uc(i,e,"span",ur(e))){case"invalid-child":c();var r=jr(e);l(r),c();break;case"valid":var o=s.get().getOrThunk(function(){var e=ca(u);return a.push(e),s.set(A.some(e)),e});Ri(t=e,n=o),Oi(n,t)}};return Lc(i.dom,e,function(e){var t;c(),t=W(e,rr.fromDom),l(t)}),a},jc=function(s,c,l,f){s.undoManager.transact(function(){var e,t,n,r,o=s.selection.getRng();if(o.collapsed&&(r=Pc(e=s,t=o,[{inline:!0}],3===(n=t).startContainer.nodeType&&n.startContainer.nodeValue.length>=n.startOffset&&"\xa0"===n.startContainer.nodeValue[n.startOffset]),t.setStart(r.startContainer,r.startOffset),t.setEnd(r.endContainer,r.endOffset),e.selection.setRng(t)),s.selection.getRng().collapsed){var i=zc(s.getDoc(),f,c,l.decorate);fa(i,"\xa0"),s.selection.getRng().insertNode(i.dom()),s.selection.select(i.dom())}else{var a=ju.getPersistentBookmark(s.selection,!1),u=s.selection.getRng();Vc(s,u,c,l.decorate,f),s.selection.moveToBookmark(a)}})};function Hc(s){var n,r=(n={},{register:function(e,t){n[e]={name:e,settings:t}},lookup:function(e){return n.hasOwnProperty(e)?A.from(n[e]).map(function(e){return e.settings}):A.none()}});aa(s,r);var o=ia(s);return{register:function(e,t){r.register(e,t)},annotate:function(t,n){r.lookup(t).each(function(e){jc(s,t,e,n)})},annotationChanged:function(e,t){o.addListener(e,t)},remove:function(e){ra(s,A.some(e)).each(function(e){var t=e.elements;F(t,Li)})},getAll:function(e){var t,n,r,o,i,a,u=(t=s,n=e,r=rr.fromDom(t.getBody()),o=Wi(r,"["+ta()+'="'+n+'"]'),i={},F(o,function(e){var t=Cr(e,na()),n=i.hasOwnProperty(t)?i[t]:[];i[t]=n.concat([e])}),i);return a=function(e){return W(e,function(e){return e.dom()})},gr(u,function(e,t,n){return{k:t,v:a(e,t,n)}})}}}var qc,$c=Object.prototype.hasOwnProperty,Wc=(qc=function(e,t){return t},function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)$c.call(o,i)&&(n[i]=qc(n[i],o[i]))}return n}),Kc=/^[ \t\r\n]*$/,Xc={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Yc=function(e,t,n){var r,o,i=n?"lastChild":"firstChild",a=n?"prev":"next";if(e[i])return e[i];if(e!==t){if(r=e[a])return r;for(o=e.parent;o&&o!==t;o=o.parent)if(r=o[a])return r}},Gc=function(){function a(e,t){this.name=e,1===(this.type=t)&&(this.attributes=[],this.attributes.map={})}return a.create=function(e,t){var n,r;if(n=new a(e,Xc[e]||1),t)for(r in t)n.attr(r,t[r]);return n},a.prototype.replace=function(e){return e.parent&&e.remove(),this.insert(e,this),this.remove(),this},a.prototype.attr=function(e,t){var n,r;if("string"!=typeof e){for(r in e)this.attr(r,e[r]);return this}if(n=this.attributes){if(t===undefined)return n.map[e];if(null===t){if(e in n.map)for(delete n.map[e],r=n.length;r--;)if(n[r].name===e)return n=n.splice(r,1),this;return this}if(e in n.map){for(r=n.length;r--;)if(n[r].name===e){n[r].value=t;break}}else n.push({name:e,value:t});return n.map[e]=t,this}},a.prototype.clone=function(){var e,t,n,r,o,i=new a(this.name,this.type);if(n=this.attributes){for((o=[]).map={},e=0,t=n.length;e<t;e++)"id"!==(r=n[e]).name&&(o[o.length]={name:r.name,value:r.value},o.map[r.name]=r.value);i.attributes=o}return i.value=this.value,i.shortEnded=this.shortEnded,i},a.prototype.wrap=function(e){return this.parent.insert(e,this),e.append(this),this},a.prototype.unwrap=function(){var e,t;for(e=this.firstChild;e;)t=e.next,this.insert(e,this,!0),e=t;this.remove()},a.prototype.remove=function(){var e=this.parent,t=this.next,n=this.prev;return e&&(e.firstChild===this?(e.firstChild=t)&&(t.prev=null):n.next=t,e.lastChild===this?(e.lastChild=n)&&(n.next=null):t.prev=n,this.parent=this.next=this.prev=null),this},a.prototype.append=function(e){var t;return e.parent&&e.remove(),t=this.lastChild,this.lastChild=t?((t.next=e).prev=t,e):this.firstChild=e,e.parent=this,e},a.prototype.insert=function(e,t,n){var r;return e.parent&&e.remove(),r=t.parent||this,n?(t===r.firstChild?r.firstChild=e:t.prev.next=e,e.prev=t.prev,(e.next=t).prev=e):(t===r.lastChild?r.lastChild=e:t.next.prev=e,e.next=t.next,(e.prev=t).next=e),e.parent=r,e},a.prototype.getAll=function(e){var t,n=[];for(t=this.firstChild;t;t=Yc(t,this))t.name===e&&n.push(t);return n},a.prototype.empty=function(){var e,t,n;if(this.firstChild){for(e=[],n=this.firstChild;n;n=Yc(n,this))e.push(n);for(t=e.length;t--;)(n=e[t]).parent=n.firstChild=n.lastChild=n.next=n.prev=null}return this.firstChild=this.lastChild=null,this},a.prototype.isEmpty=function(e,t,n){var r,o,i=this.firstChild;if(t=t||{},i)do{if(1===i.type){if(i.attributes.map["data-mce-bogus"])continue;if(e[i.name])return!1;for(r=i.attributes.length;r--;)if("name"===(o=i.attributes[r].name)||0===o.indexOf("data-mce-bookmark"))return!1}if(8===i.type)return!1;if(3===i.type&&!Kc.test(i.value))return!1;if(3===i.type&&i.parent&&t[i.parent.name]&&Kc.test(i.value))return!1;if(n&&n(i))return!1}while(i=Yc(i,this));return!0},a.prototype.walk=function(e){return Yc(this,null,e)},a}(),Jc=function(e,t,n){var r,o,i,a,u=1;for(a=e.getShortEndedElements(),(i=/<([!?\/])?([A-Za-z0-9\-_\:\.]+)((?:\s+[^"\'>]+(?:(?:"[^"]*")|(?:\'[^\']*\')|[^>]*))*|\/|\s+)>/g).lastIndex=r=n;o=i.exec(t);){if(r=i.lastIndex,"/"===o[1])u--;else if(!o[1]){if(o[2]in a)continue;u++}if(0===u)break}return r},Qc=function(e,t){var n=e.exec(t);if(n){var r=n[1],o=n[2];return"string"==typeof r&&"data-mce-bogus"===r.toLowerCase()?o:null}return null};function Zc(U,z){void 0===z&&(z=ri());var e=function(){};!1!==(U=U||{}).fix_self_closing&&(U.fix_self_closing=!0);var V=U.comment?U.comment:e,j=U.cdata?U.cdata:e,H=U.text?U.text:e,q=U.start?U.start:e,$=U.end?U.end:e,W=U.pi?U.pi:e,K=U.doctype?U.doctype:e;return{parse:function(e){var t,n,r,d,o,i,a,m,u,s,g,c,p,l,f,h,v,b,y,C,x,w,N,E,S,k,T,A,R,D=0,B=[],O=0,_=Wo.decode,P=Yt.makeMap("src,href,data,background,formaction,poster,xlink:href"),I=/((java|vb)script|mhtml):/i,L=function(e){var t,n;for(t=B.length;t--&&B[t].name!==e;);if(0<=t){for(n=B.length-1;t<=n;n--)(e=B[n]).valid&&$(e.name);B.length=t}},M=function(e,t,n,r,o){var i,a,u,s,c;if(n=(t=t.toLowerCase())in g?t:_(n||r||o||""),p&&!m&&0==(0===(u=t).indexOf("data-")||0===u.indexOf("aria-"))){if(!(i=b[t])&&y){for(a=y.length;a--&&!(i=y[a]).pattern.test(t););-1===a&&(i=null)}if(!i)return;if(i.validValues&&!(n in i.validValues))return}if(P[t]&&!U.allow_script_urls){var l=n.replace(/[\s\u0000-\u001F]+/g,"");try{l=decodeURIComponent(l)}catch(f){l=unescape(l)}if(I.test(l))return;if(c=l,!(s=U).allow_html_data_urls&&(/^data:image\//i.test(c)?!1===s.allow_svg_data_urls&&/^data:image\/svg\+xml/i.test(c):/^data:/i.test(c)))return}m&&(t in P||0===t.indexOf("on"))||(d.map[t]=n,d.push({name:t,value:n}))};for(S=new RegExp("<(?:(?:!--([\\w\\W]*?)--\x3e)|(?:!\\[CDATA\\[([\\w\\W]*?)\\]\\]>)|(?:!DOCTYPE([\\w\\W]*?)>)|(?:\\?([^\\s\\/<>]+) ?([\\w\\W]*?)[?/]>)|(?:\\/([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)>)|(?:([A-Za-z][A-Za-z0-9\\-_\\:\\.]*)((?:\\s+[^\"'>]+(?:(?:\"[^\"]*\")|(?:'[^']*')|[^>]*))*|\\/|\\s+)>))","g"),k=/([\w:\-]+)(?:\s*=\s*(?:(?:\"((?:[^\"])*)\")|(?:\'((?:[^\'])*)\')|([^>\s]+)))?/g,s=z.getShortEndedElements(),E=U.self_closing_elements||z.getSelfClosingElements(),g=z.getBoolAttrs(),p=U.validate,u=U.remove_internals,R=U.fix_self_closing,T=z.getSpecialElements(),N=e+">";t=S.exec(N);){if(D<t.index&&H(_(e.substr(D,t.index-D))),n=t[6])":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),L(n);else if(n=t[7]){if(t.index+t[0].length>e.length){H(_(e.substr(t.index))),D=t.index+t[0].length;continue}":"===(n=n.toLowerCase()).charAt(0)&&(n=n.substr(1)),c=n in s,R&&E[n]&&0<B.length&&B[B.length-1].name===n&&L(n);var F=Qc(k,t[8]);if(null!==F){if("all"===F){D=Jc(z,e,S.lastIndex),S.lastIndex=D;continue}f=!1}if(!p||(l=z.getElementRule(n))){if(f=!0,p&&(b=l.attributes,y=l.attributePatterns),(v=t[8])?((m=-1!==v.indexOf("data-mce-type"))&&u&&(f=!1),(d=[]).map={},v.replace(k,M)):(d=[]).map={},p&&!m){if(C=l.attributesRequired,x=l.attributesDefault,w=l.attributesForced,l.removeEmptyAttrs&&!d.length&&(f=!1),w)for(o=w.length;o--;)a=(h=w[o]).name,"{$uid}"===(A=h.value)&&(A="mce_"+O++),d.map[a]=A,d.push({name:a,value:A});if(x)for(o=x.length;o--;)(a=(h=x[o]).name)in d.map||("{$uid}"===(A=h.value)&&(A="mce_"+O++),d.map[a]=A,d.push({name:a,value:A}));if(C){for(o=C.length;o--&&!(C[o]in d.map););-1===o&&(f=!1)}if(h=d.map["data-mce-bogus"]){if("all"===h){D=Jc(z,e,S.lastIndex),S.lastIndex=D;continue}f=!1}}f&&q(n,d,c)}else f=!1;if(r=T[n]){r.lastIndex=D=t.index+t[0].length,D=(t=r.exec(e))?(f&&(i=e.substr(D,t.index-D)),t.index+t[0].length):(i=e.substr(D),e.length),f&&(0<i.length&&H(i,!0),$(n)),S.lastIndex=D;continue}c||(v&&v.indexOf("/")===v.length-1?f&&$(n):B.push({name:n,valid:f}))}else(n=t[1])?(">"===n.charAt(0)&&(n=" "+n),U.allow_conditional_comments||"[if"!==n.substr(0,3).toLowerCase()||(n=" "+n),V(n)):(n=t[2])?j(n.replace(/<!--|-->/g,"")):(n=t[3])?K(n):(n=t[4])&&W(n,t[5]);D=t.index+t[0].length}for(D<e.length&&H(_(e.substr(D))),o=B.length-1;0<=o;o--)(n=B[o]).valid&&$(n.name)}}}(Zc||(Zc={})).findEndTag=Jc;var el=Zc,tl=function(e,t){var n,r,o,i,a,u,s,c,l=t,f=/<(\w+) [^>]*data-mce-bogus="all"[^>]*>/g,d=e.schema;for(u=e.getTempAttrs(),s=l,c=new RegExp(["\\s?("+u.join("|")+')="[^"]+"'].join("|"),"gi"),l=s.replace(c,""),a=d.getShortEndedElements();i=f.exec(l);)r=f.lastIndex,o=i[0].length,n=a[i[1]]?r:el.findEndTag(d,l,r),l=l.substring(0,r-o)+l.substring(n),f.lastIndex=r-o;return pa(l)},nl={trimExternal:tl,trimInternal:tl},rl=function(e,t,n){var r=e.getParam(t,n);if(-1===r.indexOf("="))return r;var o=e.getParam(t,"","hash");return o.hasOwnProperty(e.id)?o[e.id]:n},ol=function(e){return e.getParam("iframe_attrs",{})},il=function(e){return e.getParam("doctype","<!DOCTYPE html>")},al=function(e){return e.getParam("document_base_url","")},ul=function(e){return rl(e,"body_id","tinymce")},sl=function(e){return rl(e,"body_class","")},cl=function(e){return e.getParam("content_security_policy","")},ll=function(e){return e.getParam("br_in_pre",!0)},fl=function(e){if(e.getParam("force_p_newlines",!1))return"p";var t=e.getParam("forced_root_block","p");return!1===t?"":!0===t?"p":t},dl=function(e){return e.getParam("forced_root_block_attrs",{})},ml=function(e){return e.getParam("br_newline_selector",".mce-toc h2,figcaption,caption")},gl=function(e){return e.getParam("no_newline_selector","")},pl=function(e){return e.getParam("keep_styles",!0)},hl=function(e){return e.getParam("end_container_on_empty_block",!1)},vl=function(e){return Yt.explode(e.getParam("font_size_style_values",""))},bl=function(e){return Yt.explode(e.getParam("font_size_classes",""))},yl=function(e){return e.getParam("images_dataimg_filter",q(!0),"function")},Cl=function(e){return e.getParam("automatic_uploads",!0,"boolean")},xl=function(e){return e.getParam("images_reuse_filename",!1,"boolean")},wl=function(e){return e.getParam("images_replace_blob_uris",!0,"boolean")},Nl=function(e){return e.getParam("images_upload_url","","string")},El=function(e){return e.getParam("images_upload_base_path","","string")},Sl=function(e){return e.getParam("images_upload_credentials",!1,"boolean")},kl=function(e){return e.getParam("images_upload_handler",null,"function")},Tl=function(e){return e.getParam("content_css_cors",!1,"boolean")},Al=function(e){return e.getParam("language","en","string")},Rl=function(e){return e.getParam("language_url","","string")},Dl=function(e){return e.getParam("indent_use_margin",!1)},Bl=function(e){return e.getParam("indentation","40px","string")},Ol=function(e){var t=e.settings.content_css;return R(t)?W(t.split(","),Kn):B(t)?t:!1===t||e.inline?[]:["default"]},_l=function(e,t,n){var r,o,i,a,u;if(t.format=t.format?t.format:"html",t.get=!0,t.getInner=!0,t.no_events||e.fire("BeforeGetContent",t),"raw"===t.format)r=Yt.trim(nl.trimExternal(e.serializer,n.innerHTML));else if("text"===t.format)r=pa(n.innerText||n.textContent);else{if("tree"===t.format)return e.serializer.serialize(n,t);i=(o=e).serializer.serialize(n,t),a=fl(o),u=new RegExp("^(<"+a+"[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/"+a+">[\r\n]*|<br \\/>[\r\n]*)$"),r=i.replace(u,"")}return"text"===t.format||Co(rr.fromDom(n))?t.content=r:t.content=Yt.trim(r),t.no_events||e.fire("GetContent",t),t.content},Pl=Yt.makeMap;function Il(e){var u,s,c,l,f,d=[];return u=(e=e||{}).indent,s=Pl(e.indent_before||""),c=Pl(e.indent_after||""),l=Wo.getEncodeFunc(e.entity_encoding||"raw",e.entities),f="html"===e.element_format,{start:function(e,t,n){var r,o,i,a;if(u&&s[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n"),d.push("<",e),t)for(r=0,o=t.length;r<o;r++)i=t[r],d.push(" ",i.name,'="',l(i.value,!0),'"');d[d.length]=!n||f?">":" />",n&&u&&c[e]&&0<d.length&&0<(a=d[d.length-1]).length&&"\n"!==a&&d.push("\n")},end:function(e){var t;d.push("</",e,">"),u&&c[e]&&0<d.length&&0<(t=d[d.length-1]).length&&"\n"!==t&&d.push("\n")},text:function(e,t){0<e.length&&(d[d.length]=t?e:l(e))},cdata:function(e){d.push("<![CDATA[",e,"]]>")},comment:function(e){d.push("\x3c!--",e,"--\x3e")},pi:function(e,t){t?d.push("<?",e," ",l(t),"?>"):d.push("<?",e,"?>"),u&&d.push("\n")},doctype:function(e){d.push("<!DOCTYPE",e,">",u?"\n":"")},reset:function(){d.length=0},getContent:function(){return d.join("").replace(/\n$/,"")}}}function Ll(t,g){void 0===g&&(g=ri());var p=Il(t);return(t=t||{}).validate=!("validate"in t)||t.validate,{serialize:function(e){var f,d;d=t.validate,f={3:function(e){p.text(e.value,e.raw)},8:function(e){p.comment(e.value)},7:function(e){p.pi(e.name,e.value)},10:function(e){p.doctype(e.value)},4:function(e){p.cdata(e.value)},11:function(e){if(e=e.firstChild)for(;m(e),e=e.next;);}},p.reset();var m=function(e){var t,n,r,o,i,a,u,s,c,l=f[e.type];if(l)l(e);else{if(t=e.name,n=e.shortEnded,r=e.attributes,d&&r&&1<r.length&&((a=[]).map={},c=g.getElementRule(e.name))){for(u=0,s=c.attributesOrder.length;u<s;u++)(o=c.attributesOrder[u])in r.map&&(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));for(u=0,s=r.length;u<s;u++)(o=r[u].name)in a.map||(i=r.map[o],a.map[o]=i,a.push({name:o,value:i}));r=a}if(p.start(e.name,r,n),!n){if(e=e.firstChild)for(;m(e),e=e.next;);p.end(t)}}};return 1!==e.type||t.inner?f[11](e):m(e),p.getContent()}}}var Ml=function(e,t){t(e),e.firstChild&&Ml(e.firstChild,t),e.next&&Ml(e.next,t)},Fl=function(e,t,n){var r=function(e,n,t){var r={},o={},i=[];for(var a in t.firstChild&&Ml(t.firstChild,function(t){F(e,function(e){e.name===t.name&&(r[e.name]?r[e.name].nodes.push(t):r[e.name]={filter:e,nodes:[t]})}),F(n,function(e){"string"==typeof t.attr(e.name)&&(o[e.name]?o[e.name].nodes.push(t):o[e.name]={filter:e,nodes:[t]})})}),r)r.hasOwnProperty(a)&&i.push(r[a]);for(var a in o)o.hasOwnProperty(a)&&i.push(o[a]);return i}(e,t,n);F(r,function(t){F(t.filter.callbacks,function(e){e(t.nodes,t.filter.name,{})})})},Ul=function(e){var t=Lr(e).dom();return e.dom()===t.activeElement},zl=function(t){return(e=Lr(t),n=e!==undefined?e.dom():document,A.from(n.activeElement).map(rr.fromDom)).filter(function(e){return t.dom().contains(e.dom())});var e,n},Vl=function(a){if(!B(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return F(a,function(e,r){var t=fr(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(n[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!B(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=fr(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!J(u,function(e){return M(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n},jl=(Vl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Vl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}])),Hl=Sr("start","soffset","finish","foffset"),ql=(jl.domRange,jl.relative,jl.exact,tr.detect().browser),$l=function(e,t){var n=lr(t)?Fc(t).length:jr(t).length+1;return n<e?n:e<0?0:e},Wl=function(e){return Hl(e.start(),$l(e.soffset(),e.start()),e.finish(),$l(e.foffset(),e.finish()))},Kl=function(e,t){return Ir(e,t)||Pr(e,t)},Xl=function(t){return function(e){return Kl(t,e.start())&&Kl(t,e.finish())}},Yl=function(e){return!0===e.inline||ql.isIE()},Gl=function(e){return Hl(rr.fromDom(e.startContainer),e.startOffset,rr.fromDom(e.endContainer),e.endOffset)},Jl=function(e){var t=e.getSelection();return(t&&0!==t.rangeCount?A.from(t.getRangeAt(0)):A.none()).map(Gl)},Ql=function(e){var t,n=(t=e.dom().ownerDocument.defaultView,rr.fromDom(t));return Jl(n.dom()).filter(Xl(e))},Zl=function(e,t){return A.from(t).filter(Xl(e)).map(Wl)},ef=function(e){var t=document.createRange();try{return t.setStart(e.start().dom(),e.soffset()),t.setEnd(e.finish().dom(),e.foffset()),A.some(t)}catch(n){return A.none()}},tf=function(e){return(e.bookmark?e.bookmark:A.none()).bind(d(Zl,rr.fromDom(e.getBody()))).bind(ef)},nf=function(e){var t=Yl(e)?Ql(rr.fromDom(e.getBody())):A.none();e.bookmark=t.isSome()?t:e.bookmark},rf=function(t){tf(t).each(function(e){t.selection.setRng(e)})},of=tf,af=function(t,e){return(n=e,n.collapsed?A.from(Wa(n.startContainer,n.startOffset)).map(rr.fromDom):A.none()).bind(function(e){return bo(e)?A.some(e):!1===Ir(t,e)?A.some(t):A.none()});var n},uf=function(t,e){af(rr.fromDom(t.getBody()),e).bind(function(e){return sc.firstPositionIn(e.dom())}).fold(function(){t.selection.normalize()},function(e){return t.selection.setRng(e.toRange())})},sf=function(e){if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},cf=function(e){var t,n=e.getBody();return n&&(t=rr.fromDom(n),Ul(t)||zl(t).isSome())},lf=function(e){return e.inline?cf(e):(t=e).iframeElement&&Ul(rr.fromDom(t.iframeElement));var t},ff=function(e){return e.editorManager.setActive(e)},df=function(e,t){e.removed||(t?ff(e):function(t){var e=t.selection,n=t.getBody(),r=e.getRng();t.quirks.refreshContentEditable();var o,i,a=(o=t,i=e.getNode(),o.dom.getParent(i,function(e){return"true"===o.dom.getContentEditable(e)}));if(t.$.contains(n,a))return sf(a),uf(t,r),ff(t);t.bookmark!==undefined&&!1===lf(t)&&of(t).each(function(e){t.selection.setRng(e),r=e}),t.inline||(de.opera||sf(n),t.getWin().focus()),(de.gecko||t.inline)&&(sf(n),uf(t,r)),ff(t)}(e))},mf=lf,gf=function(e){return e instanceof Gc},pf=function(e,t){var r;e.dom.setHTML(e.getBody(),t),mf(r=e)&&sc.firstPositionIn(r.getBody()).each(function(e){var t=e.getNode(),n=Oo.isTable(t)?sc.firstPositionIn(t).getOr(e):e;r.selection.setRng(n.toRange())})},hf=function(u,s,c){return void 0===c&&(c={}),c.format=c.format?c.format:"html",c.set=!0,c.content=gf(s)?"":s,gf(s)||c.no_events||(u.fire("BeforeSetContent",c),s=c.content),A.from(u.getBody()).fold(q(s),function(e){return gf(s)?function(e,t,n,r){Fl(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);var o=Ll({validate:e.validate},e.schema).serialize(n);return r.content=Co(rr.fromDom(t))?o:Yt.trim(o),pf(e,r.content),r.no_events||e.fire("SetContent",r),n}(u,e,s,c):(t=u,n=e,o=c,0===(r=s).length||/^\s+$/.test(r)?(a='<br data-mce-bogus="1">',"TABLE"===n.nodeName?r="<tr><td>"+a+"</td></tr>":/^(UL|OL)$/.test(n.nodeName)&&(r="<li>"+a+"</li>"),(i=fl(t))&&t.schema.isValidChild(n.nodeName.toLowerCase(),i.toLowerCase())?(r=a,r=t.dom.createHTML(i,t.settings.forced_root_block_attrs,r)):r||(r='<br data-mce-bogus="1">'),pf(t,r),t.fire("SetContent",o)):("raw"!==o.format&&(r=Ll({validate:t.validate},t.schema).serialize(t.parser.parse(r,{isRootContent:!0,insert:!0}))),o.content=Co(rr.fromDom(n))?r:Yt.trim(r),pf(t,o.content),o.no_events||t.fire("SetContent",o)),o.content);var t,n,r,o,i,a})},vf=function(e,t){return e.fire("PreProcess",t)},bf=function(e,t){return e.fire("PostProcess",t)},yf=function(e){return e.fire("remove")},Cf=function(e){return e.fire("detach")},xf=function(e,t){return e.fire("SwitchMode",{mode:t})},wf=function(e,t,n,r){e.fire("ObjectResizeStart",{target:t,width:n,height:r})},Nf=function(e,t,n,r){e.fire("ObjectResized",{target:t,width:n,height:r})},Ef=hi.DOM,Sf=function(e){return A.from(e).each(function(e){return e.destroy()})},kf=function(e){if(!e.removed){var t=e._selectionOverrides,n=e.editorUpload,r=e.getBody(),o=e.getElement();r&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&o&&Ef.remove(o.nextSibling),!e.inline&&r&&(i=e,Ef.setStyle(i.id,"display",i.orgDisplay)),yf(e),e.editorManager.remove(e),Cf(e),Ef.remove(e.getContainer()),Sf(t),Sf(n),e.destroy()}var i},Tf=function(e,t){var n,r,o,i=e.selection,a=e.dom;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),Sf(i),Sf(a)),(r=(n=e).formElement)&&(r._mceOldSubmit&&(r.submit=r._mceOldSubmit,r._mceOldSubmit=null),Ef.unbind(r,"submit reset",n.formEventDelegate)),(o=e).contentAreaContainer=o.formElement=o.container=o.editorContainer=null,o.bodyElement=o.contentDocument=o.contentWindow=null,o.iframeElement=o.targetElm=null,o.selection&&(o.selection=o.selection.win=o.selection.dom=o.selection.dom.doc=null),e.destroyed=!0):e.remove())},Af=Sr("sections","settings"),Rf=tr.detect(),Df=Rf.deviceType.isTouch(),Bf=Rf.deviceType.isPhone(),Of=["lists","autolink","autosave"],_f=Bf?{theme:"mobile"}:{},Pf=function(e){var t=B(e)?e.join(" "):e,n=W(R(t)?t.split(" "):[],Kn);return U(n,function(e){return 0<e.length})},If=function(n,e){var r,o,i,t=(r=function(e,t){return M(n,t)},o={},i={},mr(e,function(e,t){(r(e,t)?o:i)[t]=e}),{t:o,f:i});return Af(t.t,t.f)},Lf=function(e,t,n,r){var o,i,a=Pf(n.forced_plugins),u=Pf(r.plugins),s=e&&(o="mobile",t.sections().hasOwnProperty(o))?U(u,d(M,Of)):u,c=(i=s,[].concat(Pf(a)).concat(Pf(i)));return Yt.extend(r,{plugins:c.join(" ")})},Mf=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m=If(["mobile"],r),g=Yt.extend(t,n,m.settings(),(f=e,d=m.settings().inline,f&&!d?(u="mobile",s=_f,c=m.sections(),l=c.hasOwnProperty(u)?c[u]:{},Yt.extend({},s,l)):{}),{validate:!0,external_plugins:(o=n,i=m.settings(),a=i.external_plugins?i.external_plugins:{},o&&o.external_plugins?Yt.extend({},o.external_plugins,a):a)});return Lf(e,m,n,g)},Ff=function(e,t,n){return A.from(t.settings[n]).filter(e)},Uf=d(Ff,R),zf=function(e,t,n,r){var o,i,a,u=t in e.settings?e.settings[t]:n;return"hash"===r?(a={},"string"==typeof(i=u)?F(0<i.indexOf("=")?i.split(/[;,](?![^=;,]*(?:[;,]|$))/):i.split(","),function(e){var t=e.split("=");1<t.length?a[Yt.trim(t[0])]=Yt.trim(t[1]):a[Yt.trim(t[0])]=Yt.trim(t)}):a=i,a):"string"===r?Ff(R,e,t).getOr(n):"number"===r?Ff(I,e,t).getOr(n):"boolean"===r?Ff(_,e,t).getOr(n):"object"===r?Ff(D,e,t).getOr(n):"array"===r?Ff(B,e,t).getOr(n):"string[]"===r?Ff((o=R,function(e){return B(e)&&J(e,o)}),e,t).getOr(n):"function"===r?Ff(P,e,t).getOr(n):u},Vf=function(e,t){return t.dom()[e]},jf=function(e,t){return parseInt(wr(t,e),10)},Hf=d(Vf,"clientWidth"),qf=d(Vf,"clientHeight"),$f=d(jf,"margin-top"),Wf=d(jf,"margin-left"),Kf=function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m,g=rr.fromDom(e.getBody()),p=e.inline?g:(r=g,rr.fromDom(r.dom().ownerDocument.documentElement)),h=(o=e.inline,a=t,u=n,s=(i=p).dom().getBoundingClientRect(),{x:a-(o?s.left+i.dom().clientLeft+Wf(i):0),y:u-(o?s.top+i.dom().clientTop+$f(i):0)});return l=h.x,f=h.y,d=Hf(c=p),m=qf(c),0<=l&&0<=f&&l<=d&&f<=m},Xf=function(e){var t,n=e.inline?e.getBody():e.getContentAreaContainer();return(t=n,A.from(t).map(rr.fromDom)).map(function(e){return Ir(Lr(e),e)}).getOr(!1)};function Yf(n){var t,o=[],i=function(){var e=n.theme;return e&&e.getNotificationManagerImpl?e.getNotificationManagerImpl():function t(){var e=function(){throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,reposition:e,getArgs:e}}()},a=function(){0<o.length&&i().reposition(o)},u=function(t){j(o,function(e){return e===t}).each(function(e){o.splice(e,1)})},r=function(r){if(!n.removed&&Xf(n))return V(o,function(e){return t=i().getArgs(e),n=r,!(t.type!==n.type||t.text!==n.text||t.progressBar||t.timeout||n.progressBar||n.timeout);var t,n}).getOrThunk(function(){n.editorManager.setActive(n);var e,t=i().open(r,function(){u(t),a()});return e=t,o.push(e),a(),t})};return(t=n).on("SkinLoaded",function(){var e=t.settings.service_message;e&&r({text:e,type:"warning",timeout:0})}),t.on("ResizeEditor ResizeWindow NodeChange",function(){ve.requestAnimationFrame(a)}),t.on("remove",function(){F(o.slice(),function(e){i().close(e)})}),{open:r,close:function(){A.from(o[0]).each(function(e){i().close(e),u(e),a()})},getNotifications:function(){return o}}}function Gf(r){var n=[],o=function(){var e=r.theme;return e&&e.getWindowManagerImpl?e.getWindowManagerImpl():function t(){var e=function(){throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,alert:e,confirm:e,close:e,getParams:e,setParams:e}}()},i=function(e,t){return function(){return t?t.apply(e,arguments):undefined}},a=function(e){var t;n.push(e),t=e,r.fire("OpenWindow",{dialog:t})},u=function(t){var e;e=t,r.fire("CloseWindow",{dialog:e}),0===(n=U(n,function(e){return e!==t})).length&&r.focus()};return r.on("remove",function(){F(n,function(e){o().close(e)})}),{open:function(e,t){r.editorManager.setActive(r),nf(r);var n=o().open(e,t,u);return a(n),n},alert:function(e,t,n){o().alert(e,i(n||this,t))},confirm:function(e,t,n){o().confirm(e,i(n||this,t))},close:function(){A.from(n[n.length-1]).each(function(e){o().close(e),u(e)})}}}var Jf,Qf=Ai.PluginManager,Zf=function(e,t){var n=function(e,t){for(var n in Qf.urls)if(Qf.urls[n]+"/plugin"+t+".js"===e)return n;return null}(t,e.suffix);return n?ki.translate(["Failed to load plugin: {0} from url {1}",n,t]):ki.translate(["Failed to load plugin url: {0}",t])},ed=function(e,t){e.notificationManager.open({type:"error",text:t})},td=function(e,t){e._skinLoaded?ed(e,t):e.on("SkinLoaded",function(){ed(e,t)})},nd=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=window.console;r&&(r.error?r.error.apply(r,arguments):r.log.apply(r,arguments))},rd={pluginLoadError:function(e,t){td(e,Zf(e,t))},pluginInitError:function(e,t,n){var r=ki.translate(["Failed to initialize plugin: {0}",t]);nd(r,n),td(e,r)},uploadError:function(e,t){td(e,ki.translate(["Failed to upload image: {0}",t]))},displayError:td,initError:nd},od=(Jf={},{add:function(e,t){Jf[e]=t},get:function(e){return Jf[e]?Jf[e]:{icons:{}}}}),id=Ai.PluginManager,ad=Ai.ThemeManager;function ud(){return new(ie.getOrDie("XMLHttpRequest"))}function sd(u,s){var r={},n=function(e,r,o,t){var i,n;(i=ud()).open("POST",s.url),i.withCredentials=s.credentials,i.upload.onprogress=function(e){t(e.loaded/e.total*100)},i.onerror=function(){o("Image upload failed due to a XHR Transport error. Code: "+i.status)},i.onload=function(){var e,t,n;i.status<200||300<=i.status?o("HTTP Error: "+i.status):(e=JSON.parse(i.responseText))&&"string"==typeof e.location?r((t=s.basePath,n=e.location,t?t.replace(/\/$/,"")+"/"+n.replace(/^\//,""):n)):o("Invalid JSON: "+i.responseText)},(n=new FormData).append("file",e.blob(),e.filename()),i.send(n)},c=function(e,t){return{url:t,blobInfo:e,status:!0}},l=function(e,t){return{url:"",blobInfo:e,status:!1,error:t}},f=function(e,t){Yt.each(r[e],function(e){e(t)}),delete r[e]},o=function(e,n){return e=Yt.grep(e,function(e){return!u.isUploaded(e.blobUri())}),me.all(Yt.map(e,function(e){return u.isPending(e.blobUri())?(t=e.blobUri(),new me(function(e){r[t]=r[t]||[],r[t].push(e)})):(o=e,i=s.handler,a=n,u.markPending(o.blobUri()),new me(function(t){var n;try{var r=function(){n&&n.close()};i(o,function(e){r(),u.markUploaded(o.blobUri(),e),f(o.blobUri(),c(o,e)),t(c(o,e))},function(e){r(),u.removeFailed(o.blobUri()),f(o.blobUri(),l(o,e)),t(l(o,e))},function(e){e<0||100<e||(n||(n=a()),n.progressBar.value(e))})}catch(e){t(l(o,e.message))}}));var o,i,a,t}))};return!1===P(s.handler)&&(s.handler=n),{upload:function(e,t){return s.url||s.handler!==n?o(e,t):new me(function(e){e([])})}}}var cd=function(e){return ie.getOrDie("atob")(e)},ld=function(e){var t,n,r=decodeURIComponent(e).split(",");return(n=/data:([^;]+)/.exec(r[0]))&&(t=n[1]),{type:t,data:r[1]}},fd=function(a){return new me(function(e){var t,n,r,o=ld(a);try{t=cd(o.data)}catch($N){return void e(new Blob([]))}for(n=function i(e){return new(ie.getOrDie("Uint8Array"))(e)}(t.length),r=0;r<n.length;r++)n[r]=t.charCodeAt(r);e(new Blob([n],{type:o.type}))})},dd=function(e){return 0===e.indexOf("blob:")?(i=e,new me(function(e,t){var n=function(){t("Cannot convert "+i+" to Blob. Resource might not exist or is inaccessible.")};try{var r=ud();r.open("GET",i,!0),r.responseType="blob",r.onload=function(){200===this.status?e(this.response):n()},r.onerror=n,r.send()}catch(o){n()}})):0===e.indexOf("data:")?fd(e):null;var i},md=function(r){return new me(function(e){var t=function n(){return new(ie.getOrDie("FileReader"))}();t.onloadend=function(){e(t.result)},t.readAsDataURL(r)})},gd=ld,pd=0,hd=function(e){return(e||"blobid")+pd++},vd=function(n,r,o,t){var i,a;0!==r.src.indexOf("blob:")?(i=gd(r.src).data,(a=n.findFirst(function(e){return e.base64()===i}))?o({image:r,blobInfo:a}):dd(r.src).then(function(e){a=n.create(hd(),e,i),n.add(a),o({image:r,blobInfo:a})},function(e){t(e)})):(a=n.getByUri(r.src))?o({image:r,blobInfo:a}):dd(r.src).then(function(t){md(t).then(function(e){i=gd(e).data,a=n.create(hd(),t,i),n.add(a),o({image:r,blobInfo:a})})},function(e){t(e)})},bd=function(e){return e?ne(e.getElementsByTagName("img")):[]},yd=0,Cd={uuid:function(e){return e+yd+++(t=function(){return Math.round(4294967295*Math.random()).toString(36)},"s"+(new Date).getTime().toString(36)+t()+t()+t());var t}};function xd(o){var t,n,i=function v(){var n=[],o=function(e){var t,n,r;if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");return t=e.id||Cd.uuid("blobid"),n=e.name||t,{id:q(t),name:q(n),filename:q(n+"."+(r=e.blob.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}[r.toLowerCase()]||"dat")),blob:q(e.blob),base64:q(e.base64),blobUri:q(e.blobUri||ue.createObjectURL(e.blob)),uri:q(e.uri)}},t=function(t){return e(function(e){return e.id()===t})},e=function(e){return U(n,e)[0]};return{create:function(e,t,n,r){if(R(e))return o({id:e,name:r,blob:t,base64:n});if(D(e))return o(e);throw new Error("Unknown input type")},add:function(e){t(e.id())||n.push(e)},get:t,getByUri:function(t){return e(function(e){return e.blobUri()===t})},findFirst:e,removeByUri:function(t){n=U(n,function(e){return e.blobUri()!==t||(ue.revokeObjectURL(e.blobUri()),!1)})},destroy:function(){F(n,function(e){ue.revokeObjectURL(e.blobUri())}),n=[]}}}(),a=function b(){var n={},r=function(e,t){return{status:e,resultUri:t}},t=function(e){return e in n};return{hasBlobUri:t,getResultUri:function(e){var t=n[e];return t?t.resultUri:null},isPending:function(e){return!!t(e)&&1===n[e].status},isUploaded:function(e){return!!t(e)&&2===n[e].status},markPending:function(e){n[e]=r(1,null)},markUploaded:function(e,t){n[e]=r(2,t)},removeFailed:function(e){delete n[e]},destroy:function(){n={}}}}(),r=[],u=function(t){return function(e){return o.selection?t(e):[]}},s=function(e,t,n){for(var r=0;-1!==(r=e.indexOf(t,r))&&(e=e.substring(0,r)+n+e.substr(r+t.length),r+=n.length-t.length+1),-1!==r;);return e},c=function(e,t,n){return e=s(e,'src="'+t+'"','src="'+n+'"'),e=s(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},l=function(t,n){F(o.undoManager.data,function(e){"fragmented"===e.type?e.fragments=W(e.fragments,function(e){return c(e,t,n)}):e.content=c(e.content,t,n)})},f=function(){return o.notificationManager.open({text:o.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0})},d=function(e,t){i.removeByUri(e.src),l(e.src,t),o.$(e).attr({src:xl(o)?t+"?"+(new Date).getTime():t,"data-mce-src":o.convertURL(t,"src")})},m=function(n){return t||(t=sd(a,{url:Nl(o),basePath:El(o),credentials:Sl(o),handler:kl(o)})),p().then(u(function(r){var e;return e=W(r,function(e){return e.blobInfo}),t.upload(e,f).then(u(function(e){var t=W(e,function(e,t){var n=r[t].image;return e.status&&wl(o)?d(n,e.url):e.error&&rd.uploadError(o,e.error),{element:n,status:e.status}});return n&&n(t),t}))}))},e=function(e){if(Cl(o))return m(e)},g=function(t){return!1!==J(r,function(e){return e(t)})&&(0!==t.getAttribute("src").indexOf("data:")||yl(o)(t))},p=function(){return n||(n=function e(o,i){var a={};return{findAll:function(e,n){var t;n||(n=q(!0)),t=U(bd(e),function(e){var t=e.src;return!!de.fileApi&&!e.hasAttribute("data-mce-bogus")&&!e.hasAttribute("data-mce-placeholder")&&!(!t||t===de.transparentSrc)&&(0===t.indexOf("blob:")?!o.isUploaded(t)&&n(e):0===t.indexOf("data:")&&n(e))});var r=W(t,function(n){if(a[n.src])return new me(function(t){a[n.src].then(function(e){if("string"==typeof e)return e;t({image:n,blobInfo:e.blobInfo})})});var e=new me(function(e,t){vd(i,n,e,t)}).then(function(e){return delete a[e.image.src],e})["catch"](function(e){return delete a[n.src],e});return a[n.src]=e});return me.all(r)}}}(a,i)),n.findAll(o.getBody(),g).then(u(function(e){return e=U(e,function(e){return"string"!=typeof e||(rd.displayError(o,e),!1)}),F(e,function(e){l(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")}),e}))},h=function(e){return e.replace(/src="(blob:[^"]+)"/g,function(e,n){var t=a.getResultUri(n);if(t)return'src="'+t+'"';var r=i.getByUri(n);return r||(r=z(o.editorManager.get(),function(e,t){return e||t.editorUpload&&t.editorUpload.blobCache.getByUri(n)},null)),r?'src="data:'+r.blob().type+";base64,"+r.base64()+'"':e})};return o.on("setContent",function(){Cl(o)?e():p()}),o.on("RawSaveContent",function(e){e.content=h(e.content)}),o.on("getContent",function(e){e.source_view||"raw"===e.format||(e.content=h(e.content))}),o.on("PostRender",function(){o.parser.addNodeFilter("img",function(e){F(e,function(e){var t=e.attr("src");if(!i.getByUri(t)){var n=a.getResultUri(t);n&&e.attr("src",n)}})})}),{blobCache:i,addFilter:function(e){r.push(e)},uploadImages:m,uploadImagesAuto:e,scanForImages:p,destroy:function(){i.destroy(),a.destroy(),n=t=null}}}var wd=function(e,t,n){return Ir(t,e)?function(e,t){for(var n=P(t)?t:q(!1),r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,a=rr.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o}(e,function(e){return n(e)||Pr(e,t)}).slice(0,-1):[]},Nd=function(e,t){return wd(e,t,q(!1))},Ed=Nd,Sd=function(e,t){return[e].concat(Nd(e,t))},kd=function(e,t){return e.hasOwnProperty(t.nodeName)},Td=function(t,e,n){return r=Ed(rr.fromDom(n),rr.fromDom(e)),j(r,function(e){return kd(t,e.dom())}).isSome();var r},Ad=function(e,t){if(Oo.isText(t)){if(0===t.nodeValue.length)return!0;if(/^\s+$/.test(t.nodeValue)&&(!t.nextSibling||kd(e,t.nextSibling)))return!0}return!1},Rd=function(e){var t,n,r,o,i,a,u,s,c,l,f=e.dom,d=e.selection,m=e.schema,g=m.getBlockElements(),p=d.getStart(),h=e.getBody(),v=fl(e);if(p&&Oo.isElement(p)&&v&&(l=h.nodeName.toLowerCase(),m.isValidChild(l,v.toLowerCase())&&!Td(g,h,p))){for(n=(t=d.getRng()).startContainer,r=t.startOffset,o=t.endContainer,i=t.endOffset,c=mf(e),p=h.firstChild;p;)if(b=g,y=p,Oo.isText(y)||Oo.isElement(y)&&!kd(b,y)&&!bc(y)){if(Ad(g,p)){p=(u=p).nextSibling,f.remove(u);continue}a||(a=f.create(v,e.settings.forced_root_block_attrs),p.parentNode.insertBefore(a,p),s=!0),p=(u=p).nextSibling,a.appendChild(u)}else a=null,p=p.nextSibling;var b,y;s&&c&&(t.setStart(n,r),t.setEnd(o,i),d.setRng(t),e.nodeChanged())}},Dd=function(e){fl(e)&&e.on("NodeChange",d(Rd,e))},Bd=function(e,t){return e&&t&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset},Od=function(t){return qr(t).fold(q([t]),function(e){return[t].concat(Od(e))})},_d=function(t){return $r(t).fold(q([t]),function(e){return"br"===ur(e)?Fr(e).map(function(e){return[t].concat(_d(e))}).getOr([]):[t].concat(_d(e))})},Pd=function(o,e){return Ya([(i=e,a=i.startContainer,u=i.startOffset,Oo.isText(a)?0===u?A.some(rr.fromDom(a)):A.none():A.from(a.childNodes[u]).map(rr.fromDom)),(t=e,n=t.endContainer,r=t.endOffset,Oo.isText(n)?r===n.data.length?A.some(rr.fromDom(n)):A.none():A.from(n.childNodes[r-1]).map(rr.fromDom))],function(e,t){var n=V(Od(o),d(Pr,e)),r=V(_d(o),d(Pr,t));return n.isSome()&&r.isSome()}).getOr(!1);var t,n,r,i,a,u},Id=function(e,t,n,r){var o=n,i=new oo(n,o),a=e.schema.getNonEmptyElements();do{if(3===n.nodeType&&0!==Yt.trim(n.nodeValue).length)return void(r?t.setStart(n,0):t.setEnd(n,n.nodeValue.length));if(a[n.nodeName]&&!/^(TD|TH)$/.test(n.nodeName))return void(r?t.setStartBefore(n):"BR"===n.nodeName?t.setEndBefore(n):t.setEndAfter(n));if(de.ie&&de.ie<11&&e.isBlock(n)&&e.isEmpty(n))return void(r?t.setStart(n,0):t.setEnd(n,0))}while(n=r?i.next():i.prev());"BODY"===o.nodeName&&(r?t.setStart(o,0):t.setEnd(o,o.childNodes.length))},Ld=function(e){var t=e.selection.getSel();return t&&0<t.rangeCount};function Md(i){var r,o=[];"onselectionchange"in i.getDoc()||i.on("NodeChange Click MouseUp KeyUp Focus",function(e){var t,n;n={startContainer:(t=i.selection.getRng()).startContainer,startOffset:t.startOffset,endContainer:t.endContainer,endOffset:t.endOffset},"nodechange"!==e.type&&Bd(n,r)||i.fire("SelectionChange"),r=n}),i.on("contextmenu",function(){i.fire("SelectionChange")}),i.on("SelectionChange",function(){var e=i.selection.getStart(!0);!e||!de.range&&i.selection.isCollapsed()||Ld(i)&&!function(e){var t,n;if((n=i.$(e).parentsUntil(i.getBody()).add(e)).length===o.length){for(t=n.length;0<=t&&n[t]===o[t];t--);if(-1===t)return o=n,!0}return o=n,!1}(e)&&i.dom.isChildOf(e,i.getBody())&&i.nodeChanged({selectionChange:!0})}),i.on("MouseUp",function(e){!e.isDefaultPrevented()&&Ld(i)&&("IMG"===i.selection.getNode().nodeName?ve.setEditorTimeout(i,function(){i.nodeChanged()}):i.nodeChanged())}),this.nodeChanged=function(e){var t,n,r,o=i.selection;i.initialized&&o&&!i.settings.disable_nodechange&&!i.readonly&&(r=i.getBody(),(t=o.getStart(!0)||r).ownerDocument===i.getDoc()&&i.dom.isChildOf(t,r)||(t=r),n=[],i.dom.getParent(t,function(e){if(e===r)return!0;n.push(e)}),(e=e||{}).element=t,e.parents=n,i.fire("NodeChange",e))}}var Fd,Ud,zd=function(e){var t,n,r,o;return o=e.getBoundingClientRect(),n=(t=e.ownerDocument).documentElement,r=t.defaultView,{top:o.top+r.pageYOffset-n.clientTop,left:o.left+r.pageXOffset-n.clientLeft}},Vd=function(e,t){return n=(u=e).inline?zd(u.getBody()):{left:0,top:0},a=(i=e).getBody(),r=i.inline?{left:a.scrollLeft,top:a.scrollTop}:{left:0,top:0},{pageX:(o=function(e,t){if(t.target.ownerDocument===e.getDoc())return{left:t.pageX,top:t.pageY};var n,r,o,i,a,u=zd(e.getContentAreaContainer()),s=(r=(n=e).getBody(),o=n.getDoc().documentElement,i={left:r.scrollLeft,top:r.scrollTop},a={left:r.scrollLeft||o.scrollLeft,top:r.scrollTop||o.scrollTop},n.inline?i:a);return{left:t.pageX-u.left+s.left,top:t.pageY-u.top+s.top}}(e,t)).left-n.left+r.left,pageY:o.top-n.top+r.top};var n,r,o,i,a,u},jd=Oo.isContentEditableFalse,Hd=Oo.isContentEditableTrue,qd=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},$d=function(u,s){return function(e){if(0===e.button){var t=V(s.dom.getParents(e.target),Qa(jd,Hd)).getOr(null);if(i=s.getBody(),jd(a=t)&&a!==i){var n=s.dom.getPos(t),r=s.getBody(),o=s.getDoc().documentElement;u.element=t,u.screenX=e.screenX,u.screenY=e.screenY,u.maxX=(s.inline?r.scrollWidth:o.offsetWidth)-2,u.maxY=(s.inline?r.scrollHeight:o.offsetHeight)-2,u.relX=e.pageX-n.x,u.relY=e.pageY-n.y,u.width=t.offsetWidth,u.height=t.offsetHeight,u.ghost=function(e,t,n,r){var o=t.cloneNode(!0);e.dom.setStyles(o,{width:n,height:r}),e.dom.setAttrib(o,"data-mce-selected",null);var i=e.dom.create("div",{"class":"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return e.dom.setStyles(i,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:r}),e.dom.setStyles(o,{margin:0,boxSizing:"border-box"}),i.appendChild(o),i}(s,t,u.width,u.height)}}var i,a}},Wd=function(l,f){return function(e){if(l.dragging&&(s=(i=f).selection,c=s.getSel().getRangeAt(0).startContainer,a=3===c.nodeType?c.parentNode:c,u=l.element,a!==u&&!i.dom.isChildOf(a,u)&&!jd(a))){var t=(r=l.element,(o=r.cloneNode(!0)).removeAttribute("data-mce-selected"),o),n=f.fire("drop",{targetClone:t,clientX:e.clientX,clientY:e.clientY});n.isDefaultPrevented()||(t=n.targetClone,f.undoManager.transact(function(){qd(l.element),f.insertContent(f.dom.getOuterHTML(t)),f._selectionOverrides.hideFakeCaret()}))}var r,o,i,a,u,s,c;Kd(l)}},Kd=function(e){e.dragging=!1,e.element=null,qd(e.ghost)},Xd=function(e){var t,n,r,o,i,a,p,h,v,u,s,c={};t=hi.DOM,a=document,n=$d(c,e),p=c,h=e,v=ve.throttle(function(e,t){h._selectionOverrides.hideFakeCaret(),h.selection.placeCaretAt(e,t)},0),r=function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m=Math.max(Math.abs(e.screenX-p.screenX),Math.abs(e.screenY-p.screenY));if(p.element&&!p.dragging&&10<m){if(h.fire("dragstart",{target:p.element}).isDefaultPrevented())return;p.dragging=!0,h.focus()}if(p.dragging){var g=(f=p,{pageX:(d=Vd(h,e)).pageX-f.relX,pageY:d.pageY+5});c=p.ghost,l=h.getBody(),c.parentNode!==l&&l.appendChild(c),t=p.ghost,n=g,r=p.width,o=p.height,i=p.maxX,a=p.maxY,s=u=0,t.style.left=n.pageX+"px",t.style.top=n.pageY+"px",n.pageX+r>i&&(u=n.pageX+r-i),n.pageY+o>a&&(s=n.pageY+o-a),t.style.width=r-u+"px",t.style.height=o-s+"px",v(e.clientX,e.clientY)}},o=Wd(c,e),u=c,i=function(){u.dragging&&s.fire("dragend"),Kd(u)},(s=e).on("mousedown",n),e.on("mousemove",r),e.on("mouseup",o),t.bind(a,"mousemove",r),t.bind(a,"mouseup",i),e.on("remove",function(){t.unbind(a,"mousemove",r),t.unbind(a,"mouseup",i)})},Yd=function(e){var n;Xd(e),(n=e).on("drop",function(e){var t="undefined"!=typeof e.clientX?n.getDoc().elementFromPoint(e.clientX,e.clientY):null;(jd(t)||jd(n.dom.getContentEditableParent(t)))&&e.preventDefault()})},Gd=function(e){return z(e,function(e,t){return e.concat(function(t){var e=function(e){return W(e,function(e){return(e=za(e)).node=t,e})};if(Oo.isElement(t))return e(t.getClientRects());if(Oo.isText(t)){var n=t.ownerDocument.createRange();return n.setStart(t,0),n.setEnd(t,t.data.length),e(n.getClientRects())}}(t))},[])};(Ud=Fd||(Fd={}))[Ud.Up=-1]="Up",Ud[Ud.Down=1]="Down";var Jd=function(o,i,a,e,u,t){var n,s,c=0,l=[],r=function(e){var t,n,r;for(r=Gd([e]),-1===o&&(r=r.reverse()),t=0;t<r.length;t++)if(n=r[t],!a(n,s)){if(0<l.length&&i(n,qt.last(l))&&c++,n.line=c,u(n))return!0;l.push(n)}};return(s=qt.last(t.getClientRects()))&&(r(n=t.getNode()),function(e,t,n,r){for(;r=Cs(r,e,Fa,t);)if(n(r))return}(o,e,r,n)),l},Qd=d(Jd,Fd.Up,Ha,qa),Zd=d(Jd,Fd.Down,qa,Ha),em=function(n){return function(e){return t=n,e.line>t;var t}},tm=function(n){return function(e){return t=n,e.line===t;var t}},nm=Oo.isContentEditableFalse,rm=Cs,om=function(e,t){return Math.abs(e.left-t)},im=function(e,t){return Math.abs(e.right-t)},am=function(e,t){return e>=t.left&&e<=t.right},um=function(e,o){return qt.reduce(e,function(e,t){var n,r;return n=Math.min(om(e,o),im(e,o)),r=Math.min(om(t,o),im(t,o)),am(o,t)?t:am(o,e)?e:r===n&&nm(t.node)?t:r<n?t:e})},sm=function(e,t,n,r){for(;r=rm(r,e,Fa,t);)if(n(r))return},cm=function(e,t,n){var r,o,i,a,u,s,c,l=Gd(U(ne(e.getElementsByTagName("*")),us)),f=U(l,function(e){return n>=e.top&&n<=e.bottom});return(r=um(f,t))&&(r=um((a=e,c=function(t,e){var n;return n=U(Gd([e]),function(e){return!t(e,u)}),s=s.concat(n),0===n.length},(s=[]).push(u=r),sm(Fd.Up,a,d(c,Ha),u.node),sm(Fd.Down,a,d(c,qa),u.node),s),t))&&us(r.node)?(i=t,{node:(o=r).node,before:om(o,i)<im(o,i)}):null},lm=function(i,a,e){return!e.collapsed&&z(e.getClientRects(),function(e,t){return e||(o=a,(r=i)>=(n=t).left&&r<=n.right&&o>=n.top&&o<=n.bottom);var n,r,o},!1)},fm=Oo.isContentEditableTrue,dm=Oo.isContentEditableFalse,mm=function(e,t,n,r,o){return t._selectionOverrides.showCaret(e,n,r,o)},gm=function(e,t){var n,r;return e.fire("BeforeObjectSelected",{target:t}).isDefaultPrevented()?null:((r=(n=t).ownerDocument.createRange()).selectNode(n),r)},pm=function(e,t,n){var r=As(1,e.getBody(),t),o=xu.fromRangeStart(r),i=o.getNode();if(dm(i))return mm(1,e,i,!o.isAtEnd(),!1);var a=o.getNode(!0);if(dm(a))return mm(1,e,a,!1,!1);var u=e.dom.getParent(o.getNode(),function(e){return dm(e)||fm(e)});return dm(u)?mm(1,e,u,!1,n):null},hm=function(e,t,n){if(!t||!t.collapsed)return t;var r=pm(e,t,n);return r||t},vm=function(t){var e=Mi(function(){if(!t.removed&&t.getBody().contains(document.activeElement)&&t.selection.getRng().collapsed){var e=hm(t,t.selection.getRng(),!1);t.selection.setRng(e)}},0);t.on("focus",function(){e.throttle()}),t.on("blur",function(){e.cancel()})},bm={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,modifierPressed:function(e){return e.shiftKey||e.ctrlKey||e.altKey||this.metaKeyPressed(e)},metaKeyPressed:function(e){return de.mac?e.metaKey:e.ctrlKey&&!e.altKey}},ym=Oo.isContentEditableTrue,Cm=Oo.isContentEditableFalse,xm=Ps,wm=_s,Nm=function(e,t){for(var n=e.getBody();t&&t!==n;){if(ym(t)||Cm(t))return t;t=t.parentNode}return null},Em=function(g){var p,a=g.getBody(),o=is(g.getBody(),function(e){return g.dom.isBlock(e)},function(){return mf(g)}),h="sel-"+g.dom.uniqueId(),u=function(e){e&&g.selection.setRng(e)},s=function(){return g.selection.getRng()},v=function(e,t,n,r){return void 0===r&&(r=!0),g.fire("ShowCaret",{target:t,direction:e,before:n}).isDefaultPrevented()?null:(r&&g.selection.scrollIntoView(t,-1===e),o.show(n,t))},b=function(e,t){return t=As(e,a,t),-1===e?xu.fromRangeStart(t):xu.fromRangeEnd(t)},t=function(e){return Ca(e)||Sa(e)||ka(e)},y=function(e){return t(e.startContainer)||t(e.endContainer)},c=function(e,t){var n,r,o,i,a,u,s,c,l,f,d=g.$,m=g.dom;if(!e)return null;if(e.collapsed){if(!y(e))if(!1===t){if(c=b(-1,e),us(c.getNode(!0)))return v(-1,c.getNode(!0),!1,!1);if(us(c.getNode()))return v(-1,c.getNode(),!c.isAtEnd(),!1)}else{if(c=b(1,e),us(c.getNode()))return v(1,c.getNode(),!c.isAtEnd(),!1);if(us(c.getNode(!0)))return v(1,c.getNode(!0),!1,!1)}return null}return i=e.startContainer,a=e.startOffset,u=e.endOffset,3===i.nodeType&&0===a&&Cm(i.parentNode)&&(i=i.parentNode,a=m.nodeIndex(i),i=i.parentNode),1!==i.nodeType?null:(u===a+1&&(n=i.childNodes[a]),Cm(n)?(l=f=n.cloneNode(!0),(s=g.fire("ObjectSelected",{target:n,targetClone:l})).isDefaultPrevented()?null:(r=Qi(rr.fromDom(g.getBody()),"#"+h).fold(function(){return d([])},function(e){return d([e.dom()])}),l=s.targetClone,0===r.length&&(r=d('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>').attr("id",h)).appendTo(g.getBody()),e=g.dom.createRng(),l===f&&de.ie?(r.empty().append('<p style="font-size: 0" data-mce-bogus="all">\xa0</p>').append(l),e.setStartAfter(r[0].firstChild.firstChild),e.setEndAfter(l)):(r.empty().append("\xa0").append(l).append("\xa0"),e.setStart(r[0].firstChild,1),e.setEnd(r[0].lastChild,0)),r.css({top:m.getPos(n,g.getBody()).y}),r[0].focus(),(o=g.selection.getSel()).removeAllRanges(),o.addRange(e),F(Wi(rr.fromDom(g.getBody()),"*[data-mce-selected]"),function(e){xr(e,"data-mce-selected")}),n.setAttribute("data-mce-selected","1"),p=n,C(),e)):null)},l=function(){p&&(p.removeAttribute("data-mce-selected"),Qi(rr.fromDom(g.getBody()),"#"+h).each(Ii),p=null),Qi(rr.fromDom(g.getBody()),"#"+h).each(Ii),p=null},C=function(){o.hide()};return de.ceFalse&&function(){g.on("mouseup",function(e){var t=s();t.collapsed&&Kf(g,e.clientX,e.clientY)&&u(pm(g,t,!1))}),g.on("click",function(e){var t;(t=Nm(g,e.target))&&(Cm(t)&&(e.preventDefault(),g.focus()),ym(t)&&g.dom.isChildOf(t,g.selection.getNode())&&l())}),g.on("blur NewBlock",function(){l()}),g.on("ResizeWindow FullscreenStateChanged",function(){return o.reposition()});var n,r,i=function(e,t){var n,r,o=g.dom.getParent(e,g.dom.isBlock),i=g.dom.getParent(t,g.dom.isBlock);return!(!o||!g.dom.isChildOf(o,i)||!1!==Cm(Nm(g,o)))||o&&(n=o,r=i,!(g.dom.getParent(n,g.dom.isBlock)===g.dom.getParent(r,g.dom.isBlock)))&&function(e){var t=Zs(e);if(!e.firstChild)return!1;var n=xu.before(e.firstChild),r=t.next(n);return r&&!wm(r)&&!xm(r)}(o)};r=!1,(n=g).on("touchstart",function(){r=!1}),n.on("touchmove",function(){r=!0}),n.on("touchend",function(e){var t=Nm(n,e.target);Cm(t)&&(r||(e.preventDefault(),c(gm(n,t))))}),g.on("mousedown",function(e){var t,n=e.target;if((n===a||"HTML"===n.nodeName||g.dom.isChildOf(n,a))&&!1!==Kf(g,e.clientX,e.clientY))if(t=Nm(g,n))Cm(t)?(e.preventDefault(),c(gm(g,t))):(l(),ym(t)&&e.shiftKey||lm(e.clientX,e.clientY,g.selection.getRng())||(C(),g.selection.placeCaretAt(e.clientX,e.clientY)));else if(!1===us(n)){l(),C();var r=cm(a,e.clientX,e.clientY);if(r&&!i(e.target,r.node)){e.preventDefault();var o=v(1,r.node,r.before,!1);g.getBody().focus(),u(o)}}}),g.on("keypress",function(e){bm.modifierPressed(e)||(e.keyCode,Cm(g.selection.getNode())&&e.preventDefault())}),g.on("getSelectionRange",function(e){var t=e.range;if(p){if(!p.parentNode)return void(p=null);(t=t.cloneRange()).selectNode(p),e.range=t}}),g.on("setSelectionRange",function(e){var t;(t=c(e.range,e.forward))&&(e.range=t)}),g.on("AfterSetSelectionRange",function(e){var t,n=e.range;y(n)||"mcepastebin"===n.startContainer.parentNode.id||C(),t=n.startContainer.parentNode,g.dom.hasClass(t,"mce-offscreen-selection")||l()}),g.on("copy",function(e){var t,n=e.clipboardData;if(!e.isDefaultPrevented()&&e.clipboardData&&!de.ie){var r=(t=g.dom.get(h))?t.getElementsByTagName("*")[0]:t;r&&(e.preventDefault(),n.clearData(),n.setData("text/html",r.outerHTML),n.setData("text/plain",r.outerText))}}),Yd(g),vm(g)}(),{showCaret:v,showBlockCaretContainer:function(e){e.hasAttribute("data-mce-caret")&&(Ta(e),u(s()),g.selection.scrollIntoView(e[0]))},hideFakeCaret:C,destroy:function(){o.destroy(),p=null}}},Sm=0,km=2,Tm=1,Am=function(g,p){var e=g.length+p.length+2,h=new Array(e),v=new Array(e),c=function(e,t,n,r,o){var i=l(e,t,n,r);if(null===i||i.start===t&&i.diag===t-r||i.end===e&&i.diag===e-n)for(var a=e,u=n;a<t||u<r;)a<t&&u<r&&g[a]===p[u]?(o.push([0,g[a]]),++a,++u):r-n<t-e?(o.push([2,g[a]]),++a):(o.push([1,p[u]]),++u);else{c(e,i.start,n,i.start-i.diag,o);for(var s=i.start;s<i.end;++s)o.push([0,g[s]]);c(i.end,t,i.end-i.diag,r,o)}},b=function(e,t,n,r){for(var o=e;o-t<r&&o<n&&g[o]===p[o-t];)++o;return{start:e,end:o,diag:t}},l=function(e,t,n,r){var o=t-e,i=r-n;if(0===o||0===i)return null;var a,u,s,c,l,f=o-i,d=i+o,m=(d%2==0?d:d+1)/2;for(h[1+m]=e,v[1+m]=t+1,a=0;a<=m;++a){for(u=-a;u<=a;u+=2){for(s=u+m,u===-a||u!==a&&h[s-1]<h[s+1]?h[s]=h[s+1]:h[s]=h[s-1]+1,l=(c=h[s])-e+n-u;c<t&&l<r&&g[c]===p[l];)h[s]=++c,++l;if(f%2!=0&&f-a<=u&&u<=f+a&&v[s-f]<=h[s])return b(v[s-f],u+e-n,t,r)}for(u=f-a;u<=f+a;u+=2){for(s=u+m-f,u===f-a||u!==f+a&&v[s+1]<=v[s-1]?v[s]=v[s+1]-1:v[s]=v[s-1],l=(c=v[s]-1)-e+n-u;e<=c&&n<=l&&g[c]===p[l];)v[s]=c--,l--;if(f%2==0&&-a<=u&&u<=a&&v[s]<=h[s+f])return b(v[s],u+e-n,t,r)}}},t=[];return c(0,g.length,0,p.length,t),t},Rm=function(e){return Oo.isElement(e)?e.outerHTML:Oo.isText(e)?Wo.encodeRaw(e.data,!1):Oo.isComment(e)?"\x3c!--"+e.data+"--\x3e":""},Dm=function(e,t,n){var r=function(e){var t,n,r;for(r=document.createElement("div"),t=document.createDocumentFragment(),e&&(r.innerHTML=e);n=r.firstChild;)t.appendChild(n);return t}(t);if(e.hasChildNodes()&&n<e.childNodes.length){var o=e.childNodes[n];o.parentNode.insertBefore(r,o)}else e.appendChild(r)},Bm=function(e){return U(W(ne(e.childNodes),Rm),function(e){return 0<e.length})},Om=function(e,t){var n,r,o,i=W(ne(t.childNodes),Rm);return n=Am(i,e),r=t,o=0,F(n,function(e){e[0]===Sm?o++:e[0]===Tm?(Dm(r,e[1],o),o++):e[0]===km&&function(e,t){if(e.hasChildNodes()&&t<e.childNodes.length){var n=e.childNodes[t];n.parentNode.removeChild(n)}}(r,o)}),t},_m=Ni(A.none()),Pm=function(e){return{type:"fragmented",fragments:e,content:"",bookmark:null,beforeBookmark:null}},Im=function(e){return{type:"complete",fragments:null,content:e,bookmark:null,beforeBookmark:null}},Lm=function(e){return"fragmented"===e.type?e.fragments.join(""):e.content},Mm=function(e){var t=rr.fromTag("body",_m.get().getOrThunk(function(){var e=document.implementation.createHTMLDocument("undo");return _m.set(A.some(e)),e}));return fa(t,Lm(e)),F(Wi(t,"*[data-mce-bogus]"),Li),t.dom().innerHTML},Fm=function(n){var e,t,r;return e=Bm(n.getBody()),-1!==(t=(r=G(e,function(e){var t=nl.trimInternal(n.serializer,e);return 0<t.length?[t]:[]})).join("")).indexOf("</iframe>")?Pm(r):Im(t)},Um=function(e,t,n){"fragmented"===t.type?Om(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw"}),e.selection.moveToBookmark(n?t.beforeBookmark:t.bookmark)},zm=function(e,t){return!(!e||!t)&&(r=t,Lm(e)===Lm(r)||(n=t,Mm(e)===Mm(n)));var n,r};function Vm(u){var s,r,o=this,c=0,l=[],t=0,f=function(){return 0===t},i=function(e){f()&&(o.typing=e)},d=function(e){u.setDirty(e)},a=function(e){i(!1),o.add({},e)},n=function(){o.typing&&(i(!1),o.add())};return u.on("init",function(){o.add()}),u.on("BeforeExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&(n(),o.beforeChange())}),u.on("ExecCommand",function(e){var t=e.command;"Undo"!==t&&"Redo"!==t&&"mceRepaint"!==t&&a(e)}),u.on("ObjectResizeStart Cut",function(){o.beforeChange()}),u.on("SaveContent ObjectResized blur",a),u.on("DragEnd",a),u.on("KeyUp",function(e){var t=e.keyCode;e.isDefaultPrevented()||((33<=t&&t<=36||37<=t&&t<=40||45===t||e.ctrlKey)&&(a(),u.nodeChanged()),46!==t&&8!==t||u.nodeChanged(),r&&o.typing&&!1===zm(Fm(u),l[0])&&(!1===u.isDirty()&&(d(!0),u.fire("change",{level:l[0],lastLevel:null})),u.fire("TypingUndo"),r=!1,u.nodeChanged()))}),u.on("KeyDown",function(e){var t=e.keyCode;if(!e.isDefaultPrevented())if(33<=t&&t<=36||37<=t&&t<=40||45===t)o.typing&&a(e);else{var n=e.ctrlKey&&!e.altKey||e.metaKey;!(t<16||20<t)||224===t||91===t||o.typing||n||(o.beforeChange(),i(!0),o.add({},e),r=!0)}}),u.on("MouseDown",function(e){o.typing&&a(e)}),u.on("input",function(e){var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data)&&a(e)}),u.addShortcut("meta+z","","Undo"),u.addShortcut("meta+y,meta+shift+z","","Redo"),u.on("AddUndo Undo Redo ClearUndos",function(e){e.isDefaultPrevented()||u.nodeChanged()}),o={data:l,typing:!1,beforeChange:function(){f()&&(s=ju.getUndoBookmark(u.selection))},add:function(e,t){var n,r,o,i=u.settings;if(o=Fm(u),e=e||{},e=Yt.extend(e,o),!1===f()||u.removed)return null;if(r=l[c],u.fire("BeforeAddUndo",{level:e,lastLevel:r,originalEvent:t}).isDefaultPrevented())return null;if(r&&zm(r,e))return null;if(l[c]&&(l[c].beforeBookmark=s),i.custom_undo_redo_levels&&l.length>i.custom_undo_redo_levels){for(n=0;n<l.length-1;n++)l[n]=l[n+1];l.length--,c=l.length}e.bookmark=ju.getUndoBookmark(u.selection),c<l.length-1&&(l.length=c+1),l.push(e),c=l.length-1;var a={level:e,lastLevel:r,originalEvent:t};return u.fire("AddUndo",a),0<c&&(d(!0),u.fire("change",a)),e},undo:function(){var e;return o.typing&&(o.add(),o.typing=!1,i(!1)),0<c&&(e=l[--c],Um(u,e,!0),d(!0),u.fire("undo",{level:e})),e},redo:function(){var e;return c<l.length-1&&(e=l[++c],Um(u,e,!1),d(!0),u.fire("redo",{level:e})),e},clear:function(){l=[],c=0,o.typing=!1,o.data=l,u.fire("ClearUndos")},hasUndo:function(){return 0<c||o.typing&&l[0]&&!zm(Fm(u),l[0])},hasRedo:function(){return c<l.length-1&&!o.typing},transact:function(e){return n(),o.beforeChange(),o.ignore(e),o.add()},ignore:function(e){try{t++,e()}finally{t--}},extra:function(e,t){var n,r;o.transact(e)&&(r=l[c].bookmark,n=l[c-1],Um(u,n,!0),o.transact(t)&&(l[c-1].beforeBookmark=r))}}}var jm,Hm,qm=function(e){var t=Wi(e,"br"),n=U(function(e){for(var t=[],n=e.dom();n;)t.push(rr.fromDom(n)),n=n.lastChild;return t}(e).slice(-1),mo);t.length===n.length&&F(n,Ii)},$m=function(e){Pi(e),Oi(e,rr.fromHtml('<br data-mce-bogus="1">'))},Wm=function(n){$r(n).each(function(t){Fr(t).each(function(e){lo(n)&&mo(t)&&lo(e)&&Ii(t)})})},Km=wc.isEq,Xm=function(e,t,n){var r=e.formatter.get(n);if(r)for(var o=0;o<r.length;o++)if(!1===r[o].inherit&&e.dom.is(t,r[o].selector))return!0;return!1},Ym=function(t,e,n,r){var o=t.dom.getRoot();return e!==o&&(e=t.dom.getParent(e,function(e){return!!Xm(t,e,n)||e.parentNode===o||!!Qm(t,e,n,r,!0)}),Qm(t,e,n,r))},Gm=function(e,t,n){return!!Km(t,n.inline)||!!Km(t,n.block)||(n.selector?1===t.nodeType&&e.is(t,n.selector):void 0)},Jm=function(e,t,n,r,o,i){var a,u,s,c=n[r];if(n.onmatch)return n.onmatch(t,n,r);if(c)if("undefined"==typeof c.length){for(a in c)if(c.hasOwnProperty(a)){if(u="attributes"===r?e.getAttrib(t,a):wc.getStyle(e,t,a),o&&!u&&!n.exact)return;if((!o||n.exact)&&!Km(u,wc.normalizeStyleValue(e,wc.replaceVars(c[a],i),a)))return}}else for(s=0;s<c.length;s++)if("attributes"===r?e.getAttrib(t,c[s]):wc.getStyle(e,t,c[s]))return n;return n},Qm=function(e,t,n,r,o){var i,a,u,s,c=e.formatter.get(n),l=e.dom;if(c&&t)for(a=0;a<c.length;a++)if(i=c[a],Gm(e.dom,t,i)&&Jm(l,t,i,"attributes",o,r)&&Jm(l,t,i,"styles",o,r)){if(s=i.classes)for(u=0;u<s.length;u++)if(!e.dom.hasClass(t,s[u]))return;return i}},Zm={matchNode:Qm,matchName:Gm,match:function(e,t,n,r){var o;return r?Ym(e,r,t,n):(r=e.selection.getNode(),!!Ym(e,r,t,n)||!((o=e.selection.getStart())===r||!Ym(e,o,t,n)))},matchAll:function(r,o,i){var e,a=[],u={};return e=r.selection.getStart(),r.dom.getParent(e,function(e){var t,n;for(t=0;t<o.length;t++)n=o[t],!u[n]&&Qm(r,e,n,i)&&(u[n]=!0,a.push(n))},r.dom.getRoot()),a},canApply:function(e,t){var n,r,o,i,a,u=e.formatter.get(t),s=e.dom;if(u)for(n=e.selection.getStart(),r=wc.getParents(s,n),i=u.length-1;0<=i;i--){if(!(a=u[i].selector)||u[i].defaultBlock)return!0;for(o=r.length-1;0<=o;o--)if(s.is(r[o],a))return!0}return!1},matchesUnInheritedFormatSelector:Xm},eg=function(e,t){return e.splitText(t)},tg=function(e){var t=e.startContainer,n=e.startOffset,r=e.endContainer,o=e.endOffset;return t===r&&Oo.isText(t)?0<n&&n<t.nodeValue.length&&(t=(r=eg(t,n)).previousSibling,n<o?(t=r=eg(r,o-=n).previousSibling,o=r.nodeValue.length,n=0):o=0):(Oo.isText(t)&&0<n&&n<t.nodeValue.length&&(t=eg(t,n),n=0),Oo.isText(r)&&0<o&&o<r.nodeValue.length&&(o=(r=eg(r,o).previousSibling).nodeValue.length)),{startContainer:t,startOffset:n,endContainer:r,endOffset:o}},ng=function(e,t,n){if(0!==n){var r,o,i,a=e.data.slice(t,t+n),u=t+n>=e.data.length,s=0===t;e.replaceData(t,n,(o=s,i=u,z((r=a).split(""),function(e,t){return-1!==" \f\n\r\t\x0B".indexOf(t)||"\xa0"===t?e.previousCharIsSpace||""===e.str&&o||e.str.length===r.length-1&&i?{previousCharIsSpace:!1,str:e.str+"\xa0"}:{previousCharIsSpace:!0,str:e.str+" "}:{previousCharIsSpace:!1,str:e.str+t}},{previousCharIsSpace:!1,str:""}).str))}},rg=function(e,t){var n,r=e.data.slice(t),o=r.length-(n=r,n.replace(/^\s+/g,"")).length;return ng(e,t,o)},og=function(e,t){var n,r,o,i=rr.fromDom(e),a=rr.fromDom(t);return n=a,r="pre,code",o=d(Pr,i),Ji(n,r,o).isSome()},ig=function(e,t){return La(t)&&!1===(r=e,o=t,Oo.isText(o)&&/^[ \t\r\n]*$/.test(o.data)&&!1===og(r,o))||(n=t,Oo.isElement(n)&&"A"===n.nodeName&&n.hasAttribute("name"))||ag(t);var n,r,o},ag=Oo.hasAttribute("data-mce-bookmark"),ug=Oo.hasAttribute("data-mce-bogus"),sg=Oo.hasAttributeValue("data-mce-bogus","all"),cg=function(e){return function(e){var t,n,r=0;if(ig(e,e))return!1;if(!(n=e.firstChild))return!0;t=new oo(n,e);do{if(sg(n))n=t.next(!0);else if(ug(n))n=t.next();else if(Oo.isBr(n))r++,n=t.next();else{if(ig(e,n))return!1;n=t.next()}}while(n);return r<=1}(e.dom())},lg=function(e,t){return r=e,o=(n=t).container(),i=n.offset(),!1===xu.isTextPosition(n)&&o===r.parentNode&&i>xu.before(r).offset()?xu(t.container(),t.offset()-1):t;var n,r,o,i},fg=function(e){return La(e.previousSibling)?A.some((t=e.previousSibling,Oo.isText(t)?xu(t,t.data.length):xu.after(t))):e.previousSibling?sc.lastPositionIn(e.previousSibling):A.none();var t},dg=function(e){return La(e.nextSibling)?A.some((t=e.nextSibling,Oo.isText(t)?xu(t,0):xu.before(t))):e.nextSibling?sc.firstPositionIn(e.nextSibling):A.none();var t},mg=function(r,o){return fg(o).orThunk(function(){return dg(o)}).orThunk(function(){return e=r,t=o,n=xu.before(t.previousSibling?t.previousSibling:t.parentNode),sc.prevPosition(e,n).fold(function(){return sc.nextPosition(e,xu.after(t))},A.some);var e,t,n})},gg=function(n,r){return dg(r).orThunk(function(){return fg(r)}).orThunk(function(){return e=n,t=r,sc.nextPosition(e,xu.after(t)).fold(function(){return sc.prevPosition(e,xu.before(t))},A.some);var e,t})},pg=function(e,t,n){return(r=e,o=t,i=n,r?gg(o,i):mg(o,i)).map(d(lg,n));var r,o,i},hg=function(t,n,e){e.fold(function(){t.focus()},function(e){t.selection.setRng(e.toRange(),n)})},vg=function(e,t){return t&&e.schema.getBlockElements().hasOwnProperty(ur(t))},bg=function(e){if(cg(e)){var t=rr.fromHtml('<br data-mce-bogus="1">');return Pi(e),Oi(e,t),A.some(xu.before(t.dom()))}return A.none()},yg=function(e,t,l){var n=Fr(e).filter(function(e){return Oo.isText(e.dom())}),r=Ur(e).filter(function(e){return Oo.isText(e.dom())});return Ii(e),Ya([n,r,t],function(e,t,n){var r,o,i,a,u=e.dom(),s=t.dom(),c=u.data.length;return o=s,i=l,a=Xn((r=u).data).length,r.appendData(o.data),Ii(rr.fromDom(o)),i&&rg(r,a),n.container()===s?xu(u,c):n}).orThunk(function(){return l&&(n.each(function(e){return t=e.dom(),n=e.dom().length,r=t.data.slice(0,n),o=r.length-Xn(r).length,ng(t,n-o,o);var t,n,r,o}),r.each(function(e){return rg(e.dom(),0)})),t})},Cg=function(t,n,e,r){void 0===r&&(r=!0);var o,i,a=pg(n,t.getBody(),e.dom()),u=Yi(e,d(vg,t),(o=t.getBody(),function(e){return e.dom()===o})),s=yg(e,a,(i=e,hr(t.schema.getTextInlineElements(),ur(i))));t.dom.isEmpty(t.getBody())?(t.setContent(""),t.selection.setCursorLocation()):u.bind(bg).fold(function(){r&&hg(t,n,s)},function(e){r&&hg(t,n,A.some(e))})},xg=ga,wg="_mce_caret",Ng=function(e){return 0<function(e){for(var t=[];e;){if(3===e.nodeType&&e.nodeValue!==xg||1<e.childNodes.length)return[];1===e.nodeType&&t.push(e),e=e.firstChild}return t}(e).length},Eg=function(e){var t;if(e)for(e=(t=new oo(e,e)).current();e;e=t.next())if(3===e.nodeType)return e;return null},Sg=function(e){var t=rr.fromTag("span");return yr(t,{id:wg,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&Oi(t,rr.fromText(xg)),t},kg=function(e,t,n){void 0===n&&(n=!0);var r,o=e.dom,i=e.selection;if(Ng(t))Cg(e,!1,rr.fromDom(t),n);else{var a=i.getRng(),u=o.getParent(t,o.isBlock),s=((r=Eg(t))&&r.nodeValue.charAt(0)===xg&&r.deleteData(0,1),r);a.startContainer===s&&0<a.startOffset&&a.setStart(s,a.startOffset-1),a.endContainer===s&&0<a.endOffset&&a.setEnd(s,a.endOffset-1),o.remove(t,!0),u&&o.isEmpty(u)&&$m(rr.fromDom(u)),i.setRng(a)}},Tg=function(e,t,n){void 0===n&&(n=!0);var r=e.dom,o=e.selection;if(t)kg(e,t,n);else if(!(t=$u(e.getBody(),o.getStart())))for(;t=r.get(wg);)kg(e,t,!1)},Ag=function(e,t,n){var r=e.dom,o=r.getParent(n,d(wc.isTextBlock,e));o&&r.isEmpty(o)?n.parentNode.replaceChild(t,n):(qm(rr.fromDom(n)),r.isEmpty(n)?n.parentNode.replaceChild(t,n):r.insertAfter(t,n))},Rg=function(e,t){return e.appendChild(t),t},Dg=function(e,t){var n,r,o=(n=function(e,t){return Rg(e,t.cloneNode(!1))},r=t,function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)}(e,function(e){r=n(r,e)}),r);return Rg(o,o.ownerDocument.createTextNode(xg))},Bg=function(i){i.on("mouseup keydown",function(e){var t,n,r,o;t=i,n=e.keyCode,r=t.selection,o=t.getBody(),Tg(t,null,!1),8!==n&&46!==n||!r.isCollapsed()||r.getStart().innerHTML!==xg||Tg(t,$u(o,r.getStart())),37!==n&&39!==n||Tg(t,$u(o,r.getStart()))})},Og=function(e,t){return e.schema.getTextInlineElements().hasOwnProperty(ur(t))&&!qu(t.dom())&&!Oo.isBogus(t.dom())},_g={},Pg=qt.filter,Ig=qt.each;Hm=function(e){var t,n,r=e.selection.getRng();t=Oo.matchNodeNames("pre"),r.collapsed||(n=e.selection.getSelectedBlocks(),Ig(Pg(Pg(n,t),function(e){return t(e.previousSibling)&&-1!==qt.indexOf(n,e.previousSibling)}),function(e){var t,n;t=e.previousSibling,pn(n=e).remove(),pn(t).append("<br><br>").append(n.childNodes)}))},_g[jm="pre"]||(_g[jm]=[]),_g[jm].push(Hm);var Lg=function(e,t){Ig(_g[e],function(e){e(t)})},Mg=Yt.each,Fg=function(o){this.compare=function(e,t){if(e.nodeName!==t.nodeName)return!1;var n=function(n){var r={};return Mg(o.getAttribs(n),function(e){var t=e.nodeName.toLowerCase();0!==t.indexOf("_")&&"style"!==t&&0!==t.indexOf("data-")&&(r[t]=o.getAttrib(n,t))}),r},r=function(e,t){var n,r;for(r in e)if(e.hasOwnProperty(r)){if(void 0===(n=t[r]))return!1;if(e[r]!==n)return!1;delete t[r]}for(r in t)if(t.hasOwnProperty(r))return!1;return!0};return!(!r(n(e),n(t))||!r(o.parseStyle(o.getAttrib(e,"style")),o.parseStyle(o.getAttrib(t,"style")))||bc(e)||bc(t))}},Ug=/^(src|href|style)$/,zg=Yt.each,Vg=wc.isEq,jg=function(e,t,n){return e.isChildOf(t,n)&&t!==n&&!e.isBlock(n)},Hg=function(e,t,n){var r,o,i;return r=t[n?"startContainer":"endContainer"],o=t[n?"startOffset":"endOffset"],Oo.isElement(r)&&(i=r.childNodes.length-1,!n&&o&&o--,r=r.childNodes[i<o?i:o]),Oo.isText(r)&&n&&o>=r.nodeValue.length&&(r=new oo(r,e.getBody()).next()||r),Oo.isText(r)&&!n&&0===o&&(r=new oo(r,e.getBody()).prev()||r),r},qg=function(e,t,n,r){var o=e.create(n,r);return t.parentNode.insertBefore(o,t),o.appendChild(t),o},$g=function(e,t,n,r,o){var i=rr.fromDom(t),a=rr.fromDom(e.create(r,o)),u=n?Vr(i):zr(i);return _i(a,u),n?(Ri(i,a),Bi(a,i)):(Di(i,a),Oi(a,i)),a.dom()},Wg=function(e,t,n,r){return!(t=wc.getNonWhiteSpaceSibling(t,n,r))||"BR"===t.nodeName||e.isBlock(t)},Kg=function(e,n,r,o,i){var t,a,u,s,c,l,f,d,m,g,p,h,v,b,y=e.dom;if(c=y,!(Vg(l=o,(f=n).inline)||Vg(l,f.block)||(f.selector?Oo.isElement(l)&&c.is(l,f.selector):void 0)||(s=o,n.links&&"A"===s.tagName)))return!1;if("all"!==n.remove)for(zg(n.styles,function(e,t){e=wc.normalizeStyleValue(y,wc.replaceVars(e,r),t),"number"==typeof t&&(t=e,i=0),(n.remove_similar||!i||Vg(wc.getStyle(y,i,t),e))&&y.setStyle(o,t,""),u=1}),u&&""===y.getAttrib(o,"style")&&(o.removeAttribute("style"),o.removeAttribute("data-mce-style")),zg(n.attributes,function(e,t){var n;if(e=wc.replaceVars(e,r),"number"==typeof t&&(t=e,i=0),!i||Vg(y.getAttrib(i,t),e)){if("class"===t&&(e=y.getAttrib(o,t))&&(n="",zg(e.split(/\s+/),function(e){/mce\-\w+/.test(e)&&(n+=(n?" ":"")+e)}),n))return void y.setAttrib(o,t,n);"class"===t&&o.removeAttribute("className"),Ug.test(t)&&o.removeAttribute("data-mce-"+t),o.removeAttribute(t)}}),zg(n.classes,function(e){e=wc.replaceVars(e,r),i&&!y.hasClass(i,e)||y.removeClass(o,e)}),a=y.getAttribs(o),t=0;t<a.length;t++){var C=a[t].nodeName;if(0!==C.indexOf("_")&&0!==C.indexOf("data-"))return!1}return"none"!==n.remove?(d=e,g=n,h=(m=o).parentNode,v=d.dom,b=fl(d),g.block&&(b?h===v.getRoot()&&(g.list_block&&Vg(m,g.list_block)||zg(Yt.grep(m.childNodes),function(e){wc.isValid(d,b,e.nodeName.toLowerCase())?p?p.appendChild(e):(p=qg(v,e,b),v.setAttribs(p,d.settings.forced_root_block_attrs)):p=0})):v.isBlock(m)&&!v.isBlock(h)&&(Wg(v,m,!1)||Wg(v,m.firstChild,!0,1)||m.insertBefore(v.create("br"),m.firstChild),Wg(v,m,!0)||Wg(v,m.lastChild,!1,1)||m.appendChild(v.create("br")))),g.selector&&g.inline&&!Vg(g.inline,m)||v.remove(m,1),!0):void 0},Xg=Kg,Yg=function(s,c,l,e,f){var t,n,d=s.formatter.get(c),m=d[0],a=!0,u=s.dom,r=s.selection,i=function(e){var n,t,r,o,i,a,u=(n=s,t=e,r=c,o=l,i=f,zg(wc.getParents(n.dom,t.parentNode).reverse(),function(e){var t;a||"_start"===e.id||"_end"===e.id||(t=Zm.matchNode(n,e,r,o,i))&&!1!==t.split&&(a=e)}),a);return function(e,t,n,r,o,i,a,u){var s,c,l,f,d,m,g=e.dom;if(n){for(m=n.parentNode,s=r.parentNode;s&&s!==m;s=s.parentNode){for(c=g.clone(s,!1),d=0;d<t.length;d++)if(Kg(e,t[d],u,c,c)){c=0;break}c&&(l&&c.appendChild(l),f||(f=c),l=c)}!i||a.mixed&&g.isBlock(n)||(r=g.split(n,r)),l&&(o.parentNode.insertBefore(l,o),f.appendChild(o))}return r}(s,d,u,e,e,!0,m,l)},g=function(e){var t,n,r,o,i;if(Oo.isElement(e)&&u.getContentEditable(e)&&(o=a,a="true"===u.getContentEditable(e),i=!0),t=Yt.grep(e.childNodes),a&&!i)for(n=0,r=d.length;n<r&&!Kg(s,d[n],l,e,e);n++);if(m.deep&&t.length){for(n=0,r=t.length;n<r;n++)g(t[n]);i&&(a=o)}},p=function(e){var t,n=u.get(e?"_start":"_end"),r=n[e?"firstChild":"lastChild"];return bc(t=r)&&Oo.isElement(t)&&("_start"===t.id||"_end"===t.id)&&(r=r[e?"firstChild":"lastChild"]),Oo.isText(r)&&0===r.data.length&&(r=e?n.previousSibling||n.nextSibling:n.nextSibling||n.previousSibling),u.remove(n,!0),r},o=function(e){var t,n,r=e.commonAncestorContainer;if(e=Pc(s,e,d,!0),m.split){if(e=tg(e),(t=Hg(s,e,!0))!==(n=Hg(s,e))){if(/^(TR|TH|TD)$/.test(t.nodeName)&&t.firstChild&&(t="TR"===t.nodeName?t.firstChild.firstChild||t:t.firstChild||t),r&&/^T(HEAD|BODY|FOOT|R)$/.test(r.nodeName)&&/^(TH|TD)$/.test(n.nodeName)&&n.firstChild&&(n=n.firstChild||n),jg(u,t,n)){var o=A.from(t.firstChild).getOr(t);return i($g(u,o,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void p(!0)}if(jg(u,n,t))return o=A.from(n.lastChild).getOr(n),i($g(u,o,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void p(!1);t=qg(u,t,"span",{id:"_start","data-mce-type":"bookmark"}),n=qg(u,n,"span",{id:"_end","data-mce-type":"bookmark"}),i(t),i(n),t=p(!0),n=p()}else t=n=i(t);e.startContainer=t.parentNode?t.parentNode:t,e.startOffset=u.nodeIndex(t),e.endContainer=n.parentNode?n.parentNode:n,e.endOffset=u.nodeIndex(n)+1}Lc(u,e,function(e){zg(e,function(e){g(e),Oo.isElement(e)&&"underline"===s.dom.getStyle(e,"text-decoration")&&e.parentNode&&"underline"===wc.getTextDecoration(u,e.parentNode)&&Kg(s,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:"underline"}},null,e)})})};if(e)e.nodeType?((n=u.createRng()).setStartBefore(e),n.setEndAfter(e),o(n)):o(e);else if("false"!==u.getContentEditable(r.getNode()))r.isCollapsed()&&m.inline&&!u.select("td[data-mce-selected],th[data-mce-selected]").length?function(e,t,n,r){var o,i,a,u,s,c,l,f=e.dom,d=e.selection,m=[],g=d.getRng();for(o=g.startContainer,i=g.startOffset,3===(s=o).nodeType&&(i!==o.nodeValue.length&&(u=!0),s=s.parentNode);s;){if(Zm.matchNode(e,s,t,n,r)){c=s;break}s.nextSibling&&(u=!0),m.push(s),s=s.parentNode}if(c)if(u){a=d.getBookmark(),g.collapse(!0);var p=Pc(e,g,e.formatter.get(t),!0);p=tg(p),e.formatter.remove(t,n,p),d.moveToBookmark(a)}else{l=$u(e.getBody(),c);var h=Sg(!1).dom(),v=Dg(m,h);Ag(e,h,l||c),kg(e,l,!1),d.setCursorLocation(v,1),f.isEmpty(c)&&f.remove(c)}}(s,c,l,f):(t=ju.getPersistentBookmark(s.selection,!0),o(r.getRng()),r.moveToBookmark(t),m.inline&&Zm.match(s,c,l,r.getStart())&&wc.moveStart(u,r,r.getRng()),s.nodeChanged());else{e=r.getNode();for(var h=0,v=d.length;h<v&&(!d[h].ceFalseOverride||!Kg(s,d[h],l,e,e));h++);}},Gg=Yt.each,Jg=function(e){return e&&1===e.nodeType&&!bc(e)&&!qu(e)&&!Oo.isBogus(e)},Qg=function(e,t){var n;for(n=e;n;n=n[t]){if(3===n.nodeType&&0!==n.nodeValue.length)return e;if(1===n.nodeType&&!bc(n))return n}return e},Zg=function(e,t,n){var r,o,i=new Fg(e);if(t&&n&&(t=Qg(t,"previousSibling"),n=Qg(n,"nextSibling"),i.compare(t,n))){for(r=t.nextSibling;r&&r!==n;)r=(o=r).nextSibling,t.appendChild(o);return e.remove(n),Yt.each(Yt.grep(n.childNodes),function(e){t.appendChild(e)}),t}return n},ep=function(e,t,n){Gg(e.childNodes,function(e){Jg(e)&&(t(e)&&n(e),e.hasChildNodes()&&ep(e,t,n))})},tp=function(n,e){return d(function(e,t){return!(!t||!wc.getStyle(n,t,e))},e)},np=function(r,e,t){return d(function(e,t,n){r.setStyle(n,e,t),""===n.getAttribute("style")&&n.removeAttribute("style"),rp(r,n)},e,t)},rp=function(e,t){"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)},op=function(e,t){var n;1===t.nodeType&&t.parentNode&&1===t.parentNode.nodeType&&(n=wc.getTextDecoration(e,t.parentNode),e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null))},ip=function(n,e,r,o){Gg(e,function(t){Gg(n.dom.select(t.inline,o),function(e){Jg(e)&&Xg(n,t,r,e,t.exact?e:null)}),function(r,e,t){if(e.clear_child_styles){var n=e.links?"*:not(a)":"*";Gg(r.select(n,t),function(n){Jg(n)&&Gg(e.styles,function(e,t){r.setStyle(n,t,"")})})}}(n.dom,t,o)})},ap=function(e,t,n,r){(t.styles.color||t.styles.textDecoration)&&(Yt.walk(r,d(op,e),"childNodes"),op(e,r))},up=function(e,t,n,r){t.styles&&t.styles.backgroundColor&&ep(r,tp(e,"fontSize"),np(e,"backgroundColor",wc.replaceVars(t.styles.backgroundColor,n)))},sp=function(e,t,n,r){"sub"!==t.inline&&"sup"!==t.inline||(ep(r,tp(e,"fontSize"),np(e,"fontSize","")),e.remove(e.select("sup"===t.inline?"sub":"sup",r),!0))},cp=function(e,t,n,r){r&&!1!==t.merge_siblings&&(r=Zg(e,wc.getNonWhiteSpaceSibling(r),r),r=Zg(e,r,wc.getNonWhiteSpaceSibling(r,!0)))},lp=function(t,n,r,o,i){Zm.matchNode(t,i.parentNode,r,o)&&Xg(t,n,o,i)||n.merge_with_parents&&t.dom.getParent(i.parentNode,function(e){if(Zm.matchNode(t,e,r,o))return Xg(t,n,o,i),!0})},fp=function(a){var u=xu.fromRangeStart(a),s=xu.fromRangeEnd(a),c=a.commonAncestorContainer;return sc.fromPosition(!1,c,s).map(function(e){return!ws(u,s,c)&&ws(u,e,c)?(t=u.container(),n=u.offset(),r=e.container(),o=e.offset(),(i=document.createRange()).setStart(t,n),i.setEnd(r,o),i):a;var t,n,r,o,i}).getOr(a)},dp=function(e){return e.collapsed?e:fp(e)},mp=Yt.each,gp=function(g,p,h,r){var e,t,v=g.formatter.get(p),b=v[0],o=!r&&g.selection.isCollapsed(),i=g.dom,n=g.selection,y=function(n,e){if(e=e||b,n){if(e.onformat&&e.onformat(n,e,h,r),mp(e.styles,function(e,t){i.setStyle(n,t,wc.replaceVars(e,h))}),e.styles){var t=i.getAttrib(n,"style");t&&n.setAttribute("data-mce-style",t)}mp(e.attributes,function(e,t){i.setAttrib(n,t,wc.replaceVars(e,h))}),mp(e.classes,function(e){e=wc.replaceVars(e,h),i.hasClass(n,e)||i.addClass(n,e)})}},C=function(e,t){var n=!1;return!!b.selector&&(mp(e,function(e){if(!("collapsed"in e&&e.collapsed!==o))return i.is(t,e.selector)&&!qu(t)?(y(t,e),!(n=!0)):void 0}),n)},a=function(s,e,t,c){var l,f,d=[],m=!0;l=b.inline||b.block,f=s.create(l),y(f),Lc(s,e,function(e){var a,u=function(e){var t,n,r,o;if(o=m,t=e.nodeName.toLowerCase(),n=e.parentNode.nodeName.toLowerCase(),1===e.nodeType&&s.getContentEditable(e)&&(o=m,m="true"===s.getContentEditable(e),r=!0),wc.isEq(t,"br"))return a=0,void(b.block&&s.remove(e));if(b.wrapper&&Zm.matchNode(g,e,p,h))a=0;else{if(m&&!r&&b.block&&!b.wrapper&&wc.isTextBlock(g,t)&&wc.isValid(g,n,l))return e=s.rename(e,l),y(e),d.push(e),void(a=0);if(b.selector){var i=C(v,e);if(!b.inline||i)return void(a=0)}!m||r||!wc.isValid(g,l,t)||!wc.isValid(g,n,l)||!c&&3===e.nodeType&&1===e.nodeValue.length&&65279===e.nodeValue.charCodeAt(0)||qu(e)||b.inline&&s.isBlock(e)?(a=0,mp(Yt.grep(e.childNodes),u),r&&(m=o),a=0):(a||(a=s.clone(f,!1),e.parentNode.insertBefore(a,e),d.push(a)),a.appendChild(e))}};mp(e,u)}),!0===b.links&&mp(d,function(e){var t=function(e){"A"===e.nodeName&&y(e,b),mp(Yt.grep(e.childNodes),t)};t(e)}),mp(d,function(e){var t,n,r,o,i,a=function(e){var n=!1;return mp(e.childNodes,function(e){if((t=e)&&1===t.nodeType&&!bc(t)&&!qu(t)&&!Oo.isBogus(t))return n=e,!1;var t}),n};n=0,mp(e.childNodes,function(e){wc.isWhiteSpaceNode(e)||bc(e)||n++}),t=n,!(1<d.length)&&s.isBlock(e)||0!==t?(b.inline||b.wrapper)&&(b.exact||1!==t||((o=a(r=e))&&!bc(o)&&Zm.matchName(s,o,b)&&(i=s.clone(o,!1),y(i),s.replace(i,r,!0),s.remove(o,1)),e=i||r),ip(g,v,h,e),lp(g,b,p,h,e),up(s,b,h,e),sp(s,b,h,e),cp(s,b,h,e)):s.remove(e,1)})};if("false"!==i.getContentEditable(n.getNode())){if(b){if(r)r.nodeType?C(v,r)||((t=i.createRng()).setStartBefore(r),t.setEndAfter(r),a(i,Pc(g,t,v),0,!0)):a(i,r,0,!0);else if(o&&b.inline&&!i.select("td[data-mce-selected],th[data-mce-selected]").length)!function(e,t,n){var r,o,i,a,u,s,c=e.selection;a=(r=c.getRng(!0)).startOffset,s=r.startContainer.nodeValue,(o=$u(e.getBody(),c.getStart()))&&(i=Eg(o));var l,f,d=/[^\s\u00a0\u00ad\u200b\ufeff]/;s&&0<a&&a<s.length&&d.test(s.charAt(a))&&d.test(s.charAt(a-1))?(u=c.getBookmark(),r.collapse(!0),r=Pc(e,r,e.formatter.get(t)),r=tg(r),e.formatter.apply(t,n,r),c.moveToBookmark(u)):(o&&i.nodeValue===xg||(l=e.getDoc(),f=Sg(!0).dom(),i=(o=l.importNode(f,!0)).firstChild,r.insertNode(o),a=1),e.formatter.apply(t,n,o),c.setCursorLocation(i,a))}(g,p,h);else{var u=g.selection.getNode();g.settings.forced_root_block||!v[0].defaultBlock||i.getParent(u,i.isBlock)||gp(g,v[0].defaultBlock),g.selection.setRng(dp(g.selection.getRng())),e=ju.getPersistentBookmark(g.selection,!0),a(i,Pc(g,n.getRng(),v)),b.styles&&ap(i,b,h,u),n.moveToBookmark(e),wc.moveStart(i,n,n.getRng()),g.nodeChanged()}Lg(p,g)}}else{r=n.getNode();for(var s=0,c=v.length;s<c;s++)if(v[s].ceFalseOverride&&i.is(r,v[s].selector))return void y(r,v[s])}},pp={applyFormat:gp},hp=Yt.each,vp=function(e,t,n,r,o){var i,a,u,s,c,l,f,d;null===t.get()&&(a=e,u={},(i=t).set({}),a.on("NodeChange",function(n){var r=wc.getParents(a.dom,n.element),o={};r=Yt.grep(r,function(e){return 1===e.nodeType&&!e.getAttribute("data-mce-bogus")}),hp(i.get(),function(e,n){hp(r,function(t){return a.formatter.matchNode(t,n,{},e.similar)?(u[n]||(hp(e,function(e){e(!0,{node:t,format:n,parents:r})}),u[n]=e),o[n]=e,!1):!Zm.matchesUnInheritedFormatSelector(a,t,n)&&void 0})}),hp(u,function(e,t){o[t]||(delete u[t],hp(e,function(e){e(!1,{node:n.element,format:t,parents:r})}))})})),c=n,l=r,f=o,d=(s=t).get(),hp(c.split(","),function(e){d[e]||(d[e]=[],d[e].similar=f),d[e].push(l)}),s.set(d)},bp={get:function(r){var t={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"left"},inherit:!1,preview:!1,defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"left"},preview:"font-family font-size"}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size",defaultBlock:"div"},{selector:"img,table",collapsed:!1,styles:{"float":"right"},preview:"font-family font-size"}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li",styles:{textAlign:"justify"},inherit:!1,defaultBlock:"div",preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all"},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all"}],italic:[{inline:"em",remove:"all"},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all"}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all"}],strikethrough:[{inline:"span",styles:{textDecoration:"line-through"},exact:!0},{inline:"strike",remove:"all"}],forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},fontsize_class:{inline:"span",attributes:{"class":"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:function(){return!0},onformat:function(n,e,t){Yt.each(t,function(e,t){r.setAttrib(n,t,e)})}},removeformat:[{selector:"b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Yt.each("p h1 h2 h3 h4 h5 h6 div address pre div dt dd samp".split(/\s/),function(e){t[e]={block:e,remove:"all"}}),t}},yp=Yt.each,Cp=hi.DOM,xp=function(e,t){var n,o,r,m=t&&t.schema||ri({}),g=function(e){var t,n,r;return o="string"==typeof e?{name:e,classes:[],attrs:{}}:e,t=Cp.create(o.name),n=t,(r=o).classes.length&&Cp.addClass(n,r.classes.join(" ")),Cp.setAttribs(n,r.attrs),t},p=function(n,e,t){var r,o,i,a,u,s,c,l,f=0<e.length&&e[0],d=f&&f.name;if(u=d,s="string"!=typeof(a=n)?a.nodeName.toLowerCase():a,c=m.getElementRule(s),i=!(!(l=c&&c.parentsRequired)||!l.length)&&(u&&-1!==Yt.inArray(l,u)?u:l[0]))d===i?(o=e[0],e=e.slice(1)):o=i;else if(f)o=e[0],e=e.slice(1);else if(!t)return n;return o&&(r=g(o)).appendChild(n),t&&(r||(r=Cp.create("div")).appendChild(n),Yt.each(t,function(e){var t=g(e);r.insertBefore(t,n)})),p(r,e,o&&o.siblings)};return e&&e.length?(o=e[0],n=g(o),(r=Cp.create("div")).appendChild(p(n,e.slice(1),o.siblings)),r):""},wp=function(e){var t,a={classes:[],attrs:{}};return"*"!==(e=a.selector=Yt.trim(e))&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,function(e,t,n,r,o){switch(t){case"#":a.attrs.id=n;break;case".":a.classes.push(n);break;case":":-1!==Yt.inArray("checked disabled enabled read-only required".split(" "),n)&&(a.attrs[n]=n)}if("["===r){var i=o.match(/([\w\-]+)(?:\=\"([^\"]+))?/);i&&(a.attrs[i[1]]=i[2])}return""})),a.name=t||"div",a},Np=function(e){return e&&"string"==typeof e?(e=(e=e.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Yt.map(e.split(/(?:>|\s+(?![^\[\]]+\]))/),function(e){var t=Yt.map(e.split(/(?:~\+|~|\+)/),wp),n=t.pop();return t.length&&(n.siblings=t),n}).reverse()):[]},Ep=function(n,e){var t,r,o,i,a,u,s="";if(!1===(u=n.settings.preview_styles))return"";"string"!=typeof u&&(u="font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow");var c=function(e){return e.replace(/%(\w+)/g,"")};if("string"==typeof e){if(!(e=n.formatter.get(e)))return;e=e[0]}return"preview"in e&&!1===(u=e.preview)?"":(t=e.block||e.inline||"span",r=(i=Np(e.selector)).length?(i[0].name||(i[0].name=t),t=e.selector,xp(i,n)):xp([t],n),o=Cp.select(t,r)[0]||r.firstChild,yp(e.styles,function(e,t){(e=c(e))&&Cp.setStyle(o,t,e)}),yp(e.attributes,function(e,t){(e=c(e))&&Cp.setAttrib(o,t,e)}),yp(e.classes,function(e){e=c(e),Cp.hasClass(o,e)||Cp.addClass(o,e)}),n.fire("PreviewFormats"),Cp.setStyles(r,{position:"absolute",left:-65535}),n.getBody().appendChild(r),a=Cp.getStyle(n.getBody(),"fontSize",!0),a=/px$/.test(a)?parseInt(a,10):0,yp(u.split(" "),function(e){var t=Cp.getStyle(o,e,!0);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=Cp.getStyle(n.getBody(),e,!0),"#ffffff"===Cp.toHex(t).toLowerCase())||"color"===e&&"#000000"===Cp.toHex(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===a)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*a+"px"}"border"===e&&t&&(s+="padding:0 2px;"),s+=e+":"+t+";"}}),n.fire("AfterPreviewFormats"),Cp.remove(r),s)},Sp=function(e,t,n,r,o){var i=t.get(n);!Zm.match(e,n,r,o)||"toggle"in i[0]&&!i[0].toggle?pp.applyFormat(e,n,r,o):Yg(e,n,r,o)},kp=function(e){e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(var t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])};function Tp(e){var t=function o(e){var n={},r=function(e,t){e&&("string"!=typeof e?Yt.each(e,function(e,t){r(t,e)}):(B(t)||(t=[t]),Yt.each(t,function(e){"undefined"==typeof e.deep&&(e.deep=!e.selector),"undefined"==typeof e.split&&(e.split=!e.selector||e.inline),"undefined"==typeof e.remove&&e.selector&&!e.inline&&(e.remove="none"),e.selector&&e.inline&&(e.mixed=!0,e.block_expand=!0),"string"==typeof e.classes&&(e.classes=e.classes.split(/\s+/))}),n[e]=t))};return r(bp.get(e.dom)),r(e.settings.formats),{get:function(e){return e?n[e]:n},has:function(e){return hr(n,e)},register:r,unregister:function(e){return e&&n[e]&&delete n[e],n}}}(e),n=Ni(null);return kp(e),Bg(e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:d(pp.applyFormat,e),remove:d(Yg,e),toggle:d(Sp,e,t),match:d(Zm.match,e),matchAll:d(Zm.matchAll,e),matchNode:d(Zm.matchNode,e),canApply:d(Zm.canApply,e),formatChanged:d(vp,e,n),getCssText:d(Ep,e)}}var Ap={register:function(t,s,c){t.addAttributeFilter("data-mce-tabindex",function(e,t){for(var n,r=e.length;r--;)(n=e[r]).attr("tabindex",n.attributes.map["data-mce-tabindex"]),n.attr(t,null)}),t.addAttributeFilter("src,href,style",function(e,t){for(var n,r,o=e.length,i="data-mce-"+t,a=s.url_converter,u=s.url_converter_scope;o--;)(r=(n=e[o]).attributes.map[i])!==undefined?(n.attr(t,0<r.length?r:null),n.attr(i,null)):(r=n.attributes.map[t],"style"===t?r=c.serializeStyle(c.parseStyle(r),n.name):a&&(r=a.call(u,r,t,n.name)),n.attr(t,0<r.length?r:null))}),t.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)(n=(t=e[r]).attr("class"))&&(n=t.attr("class").replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),t.attr("class",0<n.length?n:null))}),t.addAttributeFilter("data-mce-type",function(e,t,n){for(var r,o=e.length;o--;)"bookmark"!==(r=e[o]).attributes.map["data-mce-type"]||n.cleanup||r.remove()}),t.addNodeFilter("noscript",function(e){for(var t,n=e.length;n--;)(t=e[n].firstChild)&&(t.value=Wo.decode(t.value))}),t.addNodeFilter("script,style",function(e,t){for(var n,r,o,i=e.length,a=function(e){return e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"")};i--;)r=(n=e[i]).firstChild?n.firstChild.value:"","script"===t?((o=n.attr("type"))&&n.attr("type","mce-no/type"===o?null:o.replace(/^mce\-/,"")),"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="// <![CDATA[\n"+a(r)+"\n// ]]>")):"xhtml"===s.element_format&&0<r.length&&(n.firstChild.value="\x3c!--\n"+a(r)+"\n--\x3e")}),t.addNodeFilter("#comment",function(e){for(var t,n=e.length;n--;)0===(t=e[n]).value.indexOf("[CDATA[")?(t.name="#cdata",t.type=4,t.value=t.value.replace(/^\[CDATA\[|\]\]$/g,"")):0===t.value.indexOf("mce:protected ")&&(t.name="#text",t.type=3,t.raw=!0,t.value=unescape(t.value).substr(14))}),t.addNodeFilter("xml:namespace,input",function(e,t){for(var n,r=e.length;r--;)7===(n=e[r]).type?n.remove():1===n.type&&("input"!==t||"type"in n.attributes.map||n.attr("type","text"))}),t.addAttributeFilter("data-mce-type",function(e){F(e,function(e){"format-caret"===e.attr("data-mce-type")&&(e.isEmpty(t.schema.getNonEmptyElements())?e.remove():e.unwrap())})}),t.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-type,data-mce-resize",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})},trimTrailingBr:function(e){var t,n,r=function(e){return e&&"br"===e.name};r(t=e.lastChild)&&r(n=t.prev)&&(t.remove(),n.remove())}},Rp={process:function(e,t,n){return f=n,(l=e)&&l.hasEventListeners("PreProcess")&&!f.no_events?(o=t,i=n,c=(r=e).dom,o=o.cloneNode(!0),(a=document.implementation).createHTMLDocument&&(u=a.createHTMLDocument(""),Yt.each("BODY"===o.nodeName?o.childNodes:[o],function(e){u.body.appendChild(u.importNode(e,!0))}),o="BODY"!==o.nodeName?u.body.firstChild:u.body,s=c.doc,c.doc=u),vf(r,Wc(i,{node:o})),s&&(c.doc=s),o):t;var r,o,i,a,u,s,c,l,f}},Dp=function(e,a,u){e.addNodeFilter("font",function(e){F(e,function(e){var t,n=a.parse(e.attr("style")),r=e.attr("color"),o=e.attr("face"),i=e.attr("size");r&&(n.color=r),o&&(n["font-family"]=o),i&&(n["font-size"]=u[parseInt(e.attr("size"),10)-1]),e.name="span",e.attr("style",a.serialize(n)),t=e,F(["color","face","size"],function(e){t.attr(e,null)})})})},Bp=function(e,t){var n,r=ii();t.convert_fonts_to_spans&&Dp(e,r,Yt.explode(t.font_size_legacy_values)),n=r,e.addNodeFilter("strike",function(e){F(e,function(e){var t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))})})},Op={register:function(e,t){t.inline_styles&&Bp(e,t)}},_p=function(e,t,n,r){(e.padd_empty_with_br||t.insert)&&n[r.name]?r.empty().append(new Gc("br",1)).shortEnded=!0:r.empty().append(new Gc("#text",3)).value="\xa0"},Pp=function(e){return Ip(e,"#text")&&"\xa0"===e.firstChild.value},Ip=function(e,t){return e&&e.firstChild&&e.firstChild===e.lastChild&&e.firstChild.name===t},Lp=function(r,e,t,n){return n.isEmpty(e,t,function(e){return t=e,(n=r.getElementRule(t.name))&&n.paddEmpty;var t,n})},Mp=function(e,t){return e&&(t[e.name]||"br"===e.name)},Fp=function(e,p){var h=e.schema;p.remove_trailing_brs&&e.addNodeFilter("br",function(e,t,n){var r,o,i,a,u,s,c,l,f=e.length,d=Yt.extend({},h.getBlockElements()),m=h.getNonEmptyElements(),g=h.getNonEmptyElements();for(d.body=1,r=0;r<f;r++)if(i=(o=e[r]).parent,d[o.parent.name]&&o===i.lastChild){for(u=o.prev;u;){if("span"!==(s=u.name)||"bookmark"!==u.attr("data-mce-type")){if("br"!==s)break;if("br"===s){o=null;break}}u=u.prev}o&&(o.remove(),Lp(h,m,g,i)&&(c=h.getElementRule(i.name))&&(c.removeEmpty?i.remove():c.paddEmpty&&_p(p,n,d,i)))}else{for(a=o;i&&i.firstChild===a&&i.lastChild===a&&!d[(a=i).name];)i=i.parent;a===i&&!0!==p.padd_empty_with_br&&((l=new Gc("#text",3)).value="\xa0",o.replace(l))}}),e.addAttributeFilter("href",function(e){var t,n,r,o=e.length;if(!p.allow_unsafe_link_target)for(;o--;)"a"===(t=e[o]).name&&"_blank"===t.attr("target")&&t.attr("rel",(n=t.attr("rel"),r=n?Yt.trim(n):"",/\b(noopener)\b/g.test(r)?r:r.split(" ").filter(function(e){return 0<e.length}).concat(["noopener"]).sort().join(" ")))}),p.allow_html_in_named_anchor||e.addAttributeFilter("id,name",function(e){for(var t,n,r,o,i=e.length;i--;)if("a"===(o=e[i]).name&&o.firstChild&&!o.attr("href"))for(r=o.parent,t=o.lastChild;n=t.prev,r.insert(t,o),t=n;);}),p.fix_list_elements&&e.addNodeFilter("ul,ol",function(e){for(var t,n,r=e.length;r--;)if("ul"===(n=(t=e[r]).parent).name||"ol"===n.name)if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{var o=new Gc("li",1);o.attr("style","list-style-type: none"),t.wrap(o)}}),p.validate&&h.getValidClasses()&&e.addAttributeFilter("class",function(e){for(var t,n,r,o,i,a,u,s=e.length,c=h.getValidClasses();s--;){for(n=(t=e[s]).attr("class").split(" "),i="",r=0;r<n.length;r++)o=n[r],u=!1,(a=c["*"])&&a[o]&&(u=!0),a=c[t.name],!u&&a&&a[o]&&(u=!0),u&&(i&&(i+=" "),i+=o);i.length||(i=null),t.attr("class",i)}})},Up=Yt.makeMap,zp=Yt.each,Vp=Yt.explode,jp=Yt.extend;function Hp(A,R){void 0===R&&(R=ri());var D={},B=[],O={},_={};(A=A||{}).validate=!("validate"in A)||A.validate,A.root_name=A.root_name||"body";var P=function(e){var t,n,r;(n=e.name)in D&&((r=O[n])?r.push(e):O[n]=[e]),t=B.length;for(;t--;)(n=B[t].name)in e.attributes.map&&((r=_[n])?r.push(e):_[n]=[e]);return e},e={schema:R,addAttributeFilter:function(e,n){zp(Vp(e),function(e){var t;for(t=0;t<B.length;t++)if(B[t].name===e)return void B[t].callbacks.push(n);B.push({name:e,callbacks:[n]})})},getAttributeFilters:function(){return[].concat(B)},addNodeFilter:function(e,n){zp(Vp(e),function(e){var t=D[e];t||(D[e]=t=[]),t.push(n)})},getNodeFilters:function(){var e=[];for(var t in D)D.hasOwnProperty(t)&&e.push({name:t,callbacks:D[t]});return e},filterNode:P,parse:function(e,a){var t,n,r,o,i,u,s,c,l,f,d,m=[];a=a||{},O={},_={},l=jp(Up("script,style,head,html,body,title,meta,param"),R.getBlockElements());var g,p=R.getNonEmptyElements(),h=R.children,v=A.validate,b="forced_root_block"in a?a.forced_root_block:A.forced_root_block,y=!1===(g=b)?"":!0===g?"p":g,C=R.getWhiteSpaceElements(),x=/^[ \t\r\n]+/,w=/[ \t\r\n]+$/,N=/[ \t\r\n]+/g,E=/^[ \t\r\n]+$/;f=C.hasOwnProperty(a.context)||C.hasOwnProperty(A.root_name);var S=function(e,t){var n,r=new Gc(e,t);return e in D&&((n=O[e])?n.push(r):O[e]=[r]),r},k=function(e){var t,n,r,o,i=R.getBlockElements();for(t=e.prev;t&&3===t.type;){if(0<(r=t.value.replace(w,"")).length)return void(t.value=r);if(n=t.next){if(3===n.type&&n.value.length){t=t.prev;continue}if(!i[n.name]&&"script"!==n.name&&"style"!==n.name){t=t.prev;continue}}o=t.prev,t.remove(),t=o}};t=el({validate:v,allow_script_urls:A.allow_script_urls,allow_conditional_comments:A.allow_conditional_comments,self_closing_elements:function(e){var t,n={};for(t in e)"li"!==t&&"p"!==t&&(n[t]=e[t]);return n}(R.getSelfClosingElements()),cdata:function(e){d.append(S("#cdata",4)).value=e},text:function(e,t){var n;f||(e=e.replace(N," "),Mp(d.lastChild,l)&&(e=e.replace(x,""))),0!==e.length&&((n=S("#text",3)).raw=!!t,d.append(n).value=e)},comment:function(e){d.append(S("#comment",8)).value=e},pi:function(e,t){d.append(S(e,7)).value=t,k(d)},doctype:function(e){d.append(S("#doctype",10)).value=e,k(d)},start:function(e,t,n){var r,o,i,a,u;if(i=v?R.getElementRule(e):{}){for((r=S(i.outputName||e,1)).attributes=t,r.shortEnded=n,d.append(r),(u=h[d.name])&&h[r.name]&&!u[r.name]&&m.push(r),o=B.length;o--;)(a=B[o].name)in t.map&&((s=_[a])?s.push(r):_[a]=[r]);l[e]&&k(r),n||(d=r),!f&&C[e]&&(f=!0)}},end:function(e){var t,n,r,o,i;if(n=v?R.getElementRule(e):{}){if(l[e]&&!f){if((t=d.firstChild)&&3===t.type)if(0<(r=t.value.replace(x,"")).length)t.value=r,t=t.next;else for(o=t.next,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.next,(0===r.length||E.test(r))&&(t.remove(),t=o),t=o;if((t=d.lastChild)&&3===t.type)if(0<(r=t.value.replace(w,"")).length)t.value=r,t=t.prev;else for(o=t.prev,t.remove(),t=o;t&&3===t.type;)r=t.value,o=t.prev,(0===r.length||E.test(r))&&(t.remove(),t=o),t=o}if(f&&C[e]&&(f=!1),n.removeEmpty&&Lp(R,p,C,d)&&!d.attributes.map.name&&!d.attr("id"))return i=d.parent,l[d.name]?d.empty().remove():d.unwrap(),void(d=i);n.paddEmpty&&(Pp(d)||Lp(R,p,C,d))&&_p(A,a,l,d),d=d.parent}}},R);var T=d=new Gc(a.context||A.root_name,11);if(t.parse(e),v&&m.length&&(a.context?a.invalid=!0:function(e){var t,n,r,o,i,a,u,s,c,l,f,d,m,g,p,h;for(d=Up("tr,td,th,tbody,thead,tfoot,table"),l=R.getNonEmptyElements(),f=R.getWhiteSpaceElements(),m=R.getTextBlockElements(),g=R.getSpecialElements(),t=0;t<e.length;t++)if((n=e[t]).parent&&!n.fixed)if(m[n.name]&&"li"===n.parent.name){for(p=n.next;p&&m[p.name];)p.name="li",p.fixed=!0,n.parent.insert(p,n.parent),p=p.next;n.unwrap(n)}else{for(o=[n],r=n.parent;r&&!R.isValidChild(r.name,n.name)&&!d[r.name];r=r.parent)o.push(r);if(r&&1<o.length){for(o.reverse(),i=a=P(o[0].clone()),c=0;c<o.length-1;c++){for(R.isValidChild(a.name,o[c].name)?(u=P(o[c].clone()),a.append(u)):u=a,s=o[c].firstChild;s&&s!==o[c+1];)h=s.next,u.append(s),s=h;a=u}Lp(R,l,f,i)?r.insert(n,o[0],!0):(r.insert(i,o[0],!0),r.insert(n,i)),r=o[0],(Lp(R,l,f,r)||Ip(r,"br"))&&r.empty().remove()}else if(n.parent){if("li"===n.name){if((p=n.prev)&&("ul"===p.name||"ul"===p.name)){p.append(n);continue}if((p=n.next)&&("ul"===p.name||"ul"===p.name)){p.insert(n,p.firstChild,!0);continue}n.wrap(P(new Gc("ul",1)));continue}R.isValidChild(n.parent.name,"div")&&R.isValidChild("div",n.name)?n.wrap(P(new Gc("div",1))):g[n.name]?n.empty().remove():n.unwrap()}}}(m)),y&&("body"===T.name||a.isRootContent)&&function(){var e,t,n=T.firstChild,r=function(e){e&&((n=e.firstChild)&&3===n.type&&(n.value=n.value.replace(x,"")),(n=e.lastChild)&&3===n.type&&(n.value=n.value.replace(w,"")))};if(R.isValidChild(T.name,y.toLowerCase())){for(;n;)e=n.next,3===n.type||1===n.type&&"p"!==n.name&&!l[n.name]&&!n.attr("data-mce-type")?(t||((t=S(y,1)).attr(A.forced_root_block_attrs),T.insert(t,n)),t.append(n)):(r(t),t=null),n=e;r(t)}}(),!a.invalid){for(c in O){for(s=D[c],i=(n=O[c]).length;i--;)n[i].parent||n.splice(i,1);for(r=0,o=s.length;r<o;r++)s[r](n,c,a)}for(r=0,o=B.length;r<o;r++)if((s=B[r]).name in _){for(i=(n=_[s.name]).length;i--;)n[i].parent||n.splice(i,1);for(i=0,u=s.callbacks.length;i<u;i++)s.callbacks[i](n,s.name,a)}}return T}};return Fp(e,A),Op.register(e,A),e}var qp=function(e,t,n){-1===Yt.inArray(t,n)&&(e.addAttributeFilter(n,function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)}),t.push(n))},$p=function(e,t,n){var r=pa(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||Co(rr.fromDom(t))?r:Yt.trim(r)},Wp=function(e,t,n){var r=n.selection?Wc({forced_root_block:!1},n):n,o=e.parse(t,r);return Ap.trimTrailingBr(o),o},Kp=function(e,t,n,r,o){var i,a,u,s,c=(i=r,Ll(t,n).serialize(i));return a=e,s=c,(u=o).no_events||!a?s:bf(a,Wc(u,{content:s})).content};function Xp(e,t){var n=function r(a,u){var s,c,l,e=["data-mce-selected"];return s=u&&u.dom?u.dom:hi.DOM,c=u&&u.schema?u.schema:ri(a),a.entity_encoding=a.entity_encoding||"named",a.remove_trailing_brs=!("remove_trailing_brs"in a)||a.remove_trailing_brs,l=Hp(a,c),Ap.register(l,a,s),{schema:c,addNodeFilter:l.addNodeFilter,addAttributeFilter:l.addAttributeFilter,serialize:function(e,t){var n=Wc({format:"html"},t||{}),r=Rp.process(u,e,n),o=$p(s,r,n),i=Wp(l,o,n);return"tree"===n.format?i:Kp(u,a,c,i,n)},addRules:function(e){c.addValidElements(e)},setRules:function(e){c.setValidElements(e)},addTempAttr:d(qp,l,e),getTempAttrs:function(){return e}}}(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs}}function Yp(e){return{getBookmark:d(hc,e),moveToBookmark:d(vc,e)}}(Yp||(Yp={})).isBookmarkNode=bc;var Gp,Jp,Qp=Yp,Zp=Oo.isContentEditableFalse,eh=Oo.isContentEditableTrue,th=function(r,a){var u,s,c,l,f,d,m,g,p,h,v,b,i,y,C,x,w,N=a.dom,E=Yt.each,S=a.getDoc(),k=document,T=Math.abs,A=Math.round,R=a.getBody();l={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]};var D=function(e){return e&&("IMG"===e.nodeName||a.dom.is(e,"figure.image"))},e=function(e){var t,n,r=e.target;t=e,n=a.selection.getRng(),!D(t.target)||lm(t.clientX,t.clientY,n)||e.isDefaultPrevented()||(e.preventDefault(),a.selection.select(r))},B=function(e){return a.dom.is(e,"figure.image")?e.querySelector("img"):e},O=function(e){var t=a.settings.object_resizing;return!1!==t&&!de.iOS&&("string"!=typeof t&&(t="table,img,figure.image,div"),"false"!==e.getAttribute("data-mce-resize")&&e!==a.getBody()&&Or(rr.fromDom(e),t))},_=function(e){var t,n,r,o;t=e.screenX-d,n=e.screenY-m,y=t*f[2]+h,C=n*f[3]+v,y=y<5?5:y,C=C<5?5:C,(D(u)&&!1!==a.settings.resize_img_proportional?!bm.modifierPressed(e):bm.modifierPressed(e)||D(u)&&f[2]*f[3]!=0)&&(T(t)>T(n)?(C=A(y*b),y=A(C/b)):(y=A(C/b),C=A(y*b))),N.setStyles(B(s),{width:y,height:C}),r=0<(r=f.startPos.x+t)?r:0,o=0<(o=f.startPos.y+n)?o:0,N.setStyles(c,{left:r,top:o,display:"block"}),c.innerHTML=y+" &times; "+C,f[2]<0&&s.clientWidth<=y&&N.setStyle(s,"left",g+(h-y)),f[3]<0&&s.clientHeight<=C&&N.setStyle(s,"top",p+(v-C)),(t=R.scrollWidth-x)+(n=R.scrollHeight-w)!=0&&N.setStyles(c,{left:r-t,top:o-n}),i||(wf(a,u,h,v),i=!0)},P=function(){i=!1;var e=function(e,t){t&&(u.style[e]||!a.schema.isValid(u.nodeName.toLowerCase(),e)?N.setStyle(B(u),e,t):N.setAttrib(B(u),e,t))};e("width",y),e("height",C),N.unbind(S,"mousemove",_),N.unbind(S,"mouseup",P),k!==S&&(N.unbind(k,"mousemove",_),N.unbind(k,"mouseup",P)),N.remove(s),N.remove(c),o(u),Nf(a,u,y,C),N.setAttrib(u,"style",N.getAttrib(u,"style")),a.nodeChanged()},o=function(e){var t,r,o,n,i;I(),M(),t=N.getPos(e,R),g=t.x,p=t.y,i=e.getBoundingClientRect(),r=i.width||i.right-i.left,o=i.height||i.bottom-i.top,u!==e&&(u=e,y=C=0),n=a.fire("ObjectSelected",{target:e}),O(e)&&!n.isDefaultPrevented()?E(l,function(n,e){var t;(t=N.get("mceResizeHandle"+e))&&N.remove(t),t=N.add(R,"div",{id:"mceResizeHandle"+e,"data-mce-bogus":"all","class":"mce-resizehandle",unselectable:!0,style:"cursor:"+e+"-resize; margin:0; padding:0"}),11===de.ie&&(t.contentEditable=!1),N.bind(t,"mousedown",function(e){var t;e.stopImmediatePropagation(),e.preventDefault(),d=(t=e).screenX,m=t.screenY,h=B(u).clientWidth,v=B(u).clientHeight,b=v/h,(f=n).startPos={x:r*n[0]+g,y:o*n[1]+p},x=R.scrollWidth,w=R.scrollHeight,s=u.cloneNode(!0),N.addClass(s,"mce-clonedresizable"),N.setAttrib(s,"data-mce-bogus","all"),s.contentEditable=!1,s.unSelectabe=!0,N.setStyles(s,{left:g,top:p,margin:0}),s.removeAttribute("data-mce-selected"),R.appendChild(s),N.bind(S,"mousemove",_),N.bind(S,"mouseup",P),k!==S&&(N.bind(k,"mousemove",_),N.bind(k,"mouseup",P)),c=N.add(R,"div",{"class":"mce-resize-helper","data-mce-bogus":"all"},h+" &times; "+v)}),n.elm=t,N.setStyles(t,{left:r*n[0]+g-t.offsetWidth/2,top:o*n[1]+p-t.offsetHeight/2})}):I(),u.setAttribute("data-mce-selected","1")},I=function(){var e,t;for(e in M(),u&&u.removeAttribute("data-mce-selected"),l)(t=N.get("mceResizeHandle"+e))&&(N.unbind(t),N.remove(t))},n=function(e){var t,n=function(e,t){if(e)do{if(e===t)return!0}while(e=e.parentNode)};i||a.removed||(E(N.select("img[data-mce-selected],hr[data-mce-selected]"),function(e){e.removeAttribute("data-mce-selected")}),t="mousedown"===e.type?e.target:r.getNode(),n(t=N.$(t).closest("table,img,figure.image,hr")[0],R)&&(F(),n(r.getStart(!0),t)&&n(r.getEnd(!0),t))?o(t):I())},L=function(e){return Zp(function(e,t){for(;t&&t!==e;){if(eh(t)||Zp(t))return t;t=t.parentNode}return null}(a.getBody(),e))},M=function(){for(var e in l){var t=l[e];t.elm&&(N.unbind(t.elm),delete t.elm)}},F=function(){try{a.getDoc().execCommand("enableObjectResizing",!1,!1)}catch(e){}};return a.on("init",function(){F(),de.ie&&11<=de.ie&&(a.on("mousedown click",function(e){var t=e.target,n=t.nodeName;i||!/^(TABLE|IMG|HR)$/.test(n)||L(t)||(2!==e.button&&a.selection.select(t,"TABLE"===n),"mousedown"===e.type&&a.nodeChanged())}),a.dom.bind(R,"mscontrolselect",function(e){var t=function(e){ve.setEditorTimeout(a,function(){a.selection.select(e)})};if(L(e.target))return e.preventDefault(),void t(e.target);/^(TABLE|IMG|HR)$/.test(e.target.nodeName)&&(e.preventDefault(),"IMG"===e.target.tagName&&t(e.target))}));var t=ve.throttle(function(e){a.composing||n(e)});a.on("nodechange ResizeEditor ResizeWindow drop FullscreenStateChanged",t),a.on("keyup compositionend",function(e){u&&"TABLE"===u.nodeName&&t(e)}),a.on("hide blur",I),a.on("contextmenu",e)}),a.on("remove",M),{isResizable:O,showResizeRect:o,hideResizeRect:I,updateResizeRect:n,destroy:function(){u=s=null}}},nh=function(e){for(var t=0,n=0,r=e;r&&r.nodeType;)t+=r.offsetLeft||0,n+=r.offsetTop||0,r=r.offsetParent;return{x:t,y:n}},rh=function(e,t,n){var r,o,i,a,u,s=e.dom,c=s.getRoot(),l=0;if(u={elm:t,alignToTop:n},e.fire("scrollIntoView",u),!u.isDefaultPrevented()&&Oo.isElement(t)){if(!1===n&&(l=t.offsetHeight),"BODY"!==c.nodeName){var f=e.selection.getScrollContainer();if(f)return r=nh(t).y-nh(f).y+l,a=f.clientHeight,void((r<(i=f.scrollTop)||i+a<r+25)&&(f.scrollTop=r<i?r:r-a+25))}o=s.getViewPort(e.getWin()),r=s.getPos(t).y+l,i=o.y,a=o.h,(r<o.y||i+a<r+25)&&e.getWin().scrollTo(0,r<i?r:r-a+25)}},oh=function(d,e){ee(vu.fromRangeStart(e).getClientRects()).each(function(e){var t,n,r,o,i,a,u,s,c,l=function(e){if(e.inline)return e.getBody().getBoundingClientRect();var t=e.getWin();return{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight,width:t.innerWidth,height:t.innerHeight}}(d),f={x:(i=t=l,a=n=e,a.left>i.left&&a.right<i.right?0:a.left<i.left?a.left-i.left:a.right-i.right),y:(r=t,o=n,o.top>r.top&&o.bottom<r.bottom?0:o.top<r.top?o.top-r.top:o.bottom-r.bottom)};s=0!==f.x?0<f.x?f.x+4:f.x-4:0,c=0!==f.y?0<f.y?f.y+4:f.y-4:0,(u=d).inline?(u.getBody().scrollLeft+=s,u.getBody().scrollTop+=c):u.getWin().scrollBy(s,c)})},ih=function(e){return Oo.isContentEditableTrue(e)||Oo.isContentEditableFalse(e)},ah=function(e,t,n){var r,o,i,a,u,s=n;if(s.caretPositionFromPoint)(o=s.caretPositionFromPoint(e,t))&&((r=n.createRange()).setStart(o.offsetNode,o.offset),r.collapse(!0));else if(n.caretRangeFromPoint)r=n.caretRangeFromPoint(e,t);else if(s.body.createTextRange){r=s.body.createTextRange();try{r.moveToPoint(e,t),r.collapse(!0)}catch(c){r=function(e,n,t){var r,o,i;if(r=t.elementFromPoint(e,n),o=t.body.createTextRange(),r&&"HTML"!==r.tagName||(r=t.body),o.moveToElementText(r),0<(i=(i=Yt.toArray(o.getClientRects())).sort(function(e,t){return(e=Math.abs(Math.max(e.top-n,e.bottom-n)))-(t=Math.abs(Math.max(t.top-n,t.bottom-n)))})).length){n=(i[0].bottom+i[0].top)/2;try{return o.moveToPoint(e,n),o.collapse(!0),o}catch(a){}}return null}(e,t,n)}return i=r,a=n.body,u=i&&i.parentElement?i.parentElement():null,Oo.isContentEditableFalse(function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(u,a,ih))?null:i}return r},uh=function(n,e){return W(e,function(e){var t=n.fire("GetSelectionRange",{range:e});return t.range!==e?t.range:e})},sh=function(e,t){var n=(t||document).createDocumentFragment();return F(e,function(e){n.appendChild(e.dom())}),rr.fromDom(n)},ch=Sr("element","width","rows"),lh=Sr("element","cells"),fh=Sr("x","y"),dh=function(e,t){var n=parseInt(Cr(e,t),10);return isNaN(n)?1:n},mh=function(e){return z(e,function(e,t){return t.cells().length>e?t.cells().length:e},0)},gh=function(e,t){for(var n=e.rows(),r=0;r<n.length;r++)for(var o=n[r].cells(),i=0;i<o.length;i++)if(Pr(o[i],t))return A.some(fh(i,r));return A.none()},ph=function(e,t,n,r,o){for(var i=[],a=e.rows(),u=n;u<=o;u++){var s=a[u].cells(),c=t<r?s.slice(t,r+1):s.slice(r,t+1);i.push(lh(a[u].element(),c))}return i},hh=function(e){var o=ch(ca(e),0,[]);return F(Wi(e,"tr"),function(n,r){F(Wi(n,"td,th"),function(e,t){!function(e,t,n,r,o){for(var i=dh(o,"rowspan"),a=dh(o,"colspan"),u=e.rows(),s=n;s<n+i;s++){u[s]||(u[s]=lh(la(r),[]));for(var c=t;c<t+a;c++)u[s].cells()[c]=s===n&&c===t?o:ca(o)}}(o,function(e,t,n){for(;r=t,o=n,i=void 0,((i=e.rows())[o]?i[o].cells():[])[r];)t++;var r,o,i;return t}(o,t,r),r,n,e)})}),ch(o.element(),mh(o.rows()),o.rows())},vh=function(e){return n=W((t=e).rows(),function(e){var t=W(e.cells(),function(e){var t=la(e);return xr(t,"colspan"),xr(t,"rowspan"),t}),n=ca(e.element());return _i(n,t),n}),r=ca(t.element()),o=rr.fromTag("tbody"),_i(o,n),Oi(r,o),r;var t,n,r,o},bh=function(l,e,t){return gh(l,e).bind(function(c){return gh(l,t).map(function(e){return t=l,r=e,o=(n=c).x(),i=n.y(),a=r.x(),u=r.y(),s=i<u?ph(t,o,i,a,u):ph(t,o,u,a,i),ch(t.element(),mh(s),s);var t,n,r,o,i,a,u,s})})},yh=function(e){var t=[];if(e)for(var n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},Ch=yh,xh=function(e){return G(e,function(e){var t=$a(e);return t?[rr.fromDom(t)]:[]})},wh=function(e){return 1<yh(e).length},Nh=function(e){return U(xh(e),yo)},Eh=function(e){return Wi(e,"td[data-mce-selected],th[data-mce-selected]")},Sh=function(e,t){var n=Eh(t),r=Nh(e);return 0<n.length?n:r},kh=Sh,Th=function(e){return Sh(Ch(e.selection.getSel()),rr.fromDom(e.getBody()))},Ah=function(n,t){return V(n,function(e){return"li"===ur(e)&&Pd(e,t)}).fold(q([]),function(e){return(t=n,V(t,function(e){return"ul"===ur(e)||"ol"===ur(e)})).map(function(e){return[rr.fromTag("li"),rr.fromTag(ur(e))]}).getOr([]);var t})},Rh=function(e,t){var n,r=rr.fromDom(t.commonAncestorContainer),o=Sd(r,e),i=U(o,function(e){return fo(e)||co(e)}),a=Ah(o,t),u=i.concat(a.length?a:ho(n=r)?Mr(n).filter(po).fold(q([]),function(e){return[n,e]}):po(n)?[n]:[]);return W(u,ca)},Dh=function(){return sh([])},Bh=function(e,t){return n=rr.fromDom(t.cloneContents()),r=Rh(e,t),o=z(r,function(e,t){return Oi(t,e),t},n),0<r.length?sh([o]):o;var n,r,o},Oh=function(e,o){return(t=e,n=o[0],Ji(n,"table",d(Pr,t))).bind(function(e){var t=o[0],n=o[o.length-1],r=hh(e);return bh(r,t,n).map(function(e){return sh([vh(e)])})}).getOrThunk(Dh);var t,n},_h=function(e,t){var n,r,o=kh(t,e);return 0<o.length?Oh(e,o):(n=e,0<(r=t).length&&r[0].collapsed?Dh():Bh(n,r[0]))},Ph=function(e,t){if(void 0===t&&(t={}),t.get=!0,t.format=t.format||"html",t.selection=!0,(t=e.fire("BeforeGetContent",t)).isDefaultPrevented())return e.fire("GetContent",t),t.content;if("text"===t.format)return c=e,A.from(c.selection.getRng()).map(function(e){return pa(e.toString())}).getOr("");t.getInner=!0;var n,r,o,i,a,u,s,c,l=(r=t,i=(n=e).selection.getRng(),a=n.dom.create("body"),u=n.selection.getSel(),s=uh(n,Ch(u)),i.cloneContents?(o=r.contextual?_h(rr.fromDom(n.getBody()),s).dom():i.cloneContents())&&a.appendChild(o):a.innerHTML=i.toString(),n.selection.serializer.serialize(a,r));return"tree"===t.format?l:(t.content=e.selection.isCollapsed()?"":l,e.fire("GetContent",t),t.content)},Ih=function(e,t,n){return null!==function(e,t,n){for(;e&&e!==t;){if(n(e))return e;e=e.parentNode}return null}(e,t,n)},Lh=function(e,t,n){return Ih(e,t,function(e){return e.nodeName===n})},Mh=function(e){return e&&"TABLE"===e.nodeName},Fh=function(e,t,n){for(var r=new oo(t,e.getParent(t.parentNode,e.isBlock)||e.getRoot());t=r[n?"prev":"next"]();)if(Oo.isBr(t))return!0},Uh=function(e,t,n,r,o){var i,a,u,s,c,l,f=e.getRoot(),d=e.schema.getNonEmptyElements();if(u=e.getParent(o.parentNode,e.isBlock)||f,r&&Oo.isBr(o)&&t&&e.isEmpty(u))return A.some(vu(o.parentNode,e.nodeIndex(o)));for(i=new oo(o,u);s=i[r?"prev":"next"]();){if("false"===e.getContentEditableParent(s)||(l=f,Ca(c=s)&&!1===Ih(c,l,qu)))return A.none();if(Oo.isText(s)&&0<s.nodeValue.length)return!1===Lh(s,f,"A")?A.some(vu(s,r?s.nodeValue.length:0)):A.none();if(e.isBlock(s)||d[s.nodeName.toLowerCase()])return A.none();a=s}return n&&a?A.some(vu(a,0)):A.none()},zh=function(e,t,n,r){var o,i,a,u,s,c,l,f,d,m,g=e.getRoot(),p=!1;if(o=r[(n?"start":"end")+"Container"],i=r[(n?"start":"end")+"Offset"],l=Oo.isElement(o)&&i===o.childNodes.length,s=e.schema.getNonEmptyElements(),c=n,Ca(o))return A.none();if(Oo.isElement(o)&&i>o.childNodes.length-1&&(c=!1),Oo.isDocument(o)&&(o=g,i=0),o===g){if(c&&(u=o.childNodes[0<i?i-1:0])){if(Ca(u))return A.none();if(s[u.nodeName]||Mh(u))return A.none()}if(o.hasChildNodes()){if(i=Math.min(!c&&0<i?i-1:i,o.childNodes.length-1),o=o.childNodes[i],i=Oo.isText(o)&&l?o.data.length:0,!t&&o===g.lastChild&&Mh(o))return A.none();if(function(e,t){for(;t&&t!==e;){if(Oo.isContentEditableFalse(t))return!0;t=t.parentNode}return!1}(g,o)||Ca(o))return A.none();if(o.hasChildNodes()&&!1===Mh(o)){a=new oo(u=o,g);do{if(Oo.isContentEditableFalse(u)||Ca(u)){p=!1;break}if(Oo.isText(u)&&0<u.nodeValue.length){i=c?0:u.nodeValue.length,o=u,p=!0;break}if(s[u.nodeName.toLowerCase()]&&(!(f=u)||!/^(TD|TH|CAPTION)$/.test(f.nodeName))){i=e.nodeIndex(u),o=u.parentNode,c||i++,p=!0;break}}while(u=c?a.next():a.prev())}}}return t&&(Oo.isText(o)&&0===i&&Uh(e,l,t,!0,o).each(function(e){o=e.container(),i=e.offset(),p=!0}),Oo.isElement(o)&&((u=o.childNodes[i])||(u=o.childNodes[i-1]),!u||!Oo.isBr(u)||(m="A",(d=u).previousSibling&&d.previousSibling.nodeName===m)||Fh(e,u,!1)||Fh(e,u,!0)||Uh(e,l,t,!0,u).each(function(e){o=e.container(),i=e.offset(),p=!0}))),c&&!t&&Oo.isText(o)&&i===o.nodeValue.length&&Uh(e,l,t,!1,o).each(function(e){o=e.container(),i=e.offset(),p=!0}),p?A.some(vu(o,i)):A.none()},Vh=function(e,t){var n=t.collapsed,r=t.cloneRange(),o=vu.fromRangeStart(t);return zh(e,n,!0,r).each(function(e){n&&vu.isAbove(o,e)||r.setStart(e.container(),e.offset())}),n||zh(e,n,!1,r).each(function(e){r.setEnd(e.container(),e.offset())}),n&&r.collapse(!0),Bd(t,r)?A.none():A.some(r)},jh=function(e){return 0===e.dom().length?(Ii(e),A.none()):A.some(e)},Hh=function(e,t,n){var r,o;if(o=t,(r=(r=n)||{format:"html"}).set=!0,r.selection=!0,r.content=o,(n=r).no_events||!(n=e.fire("BeforeSetContent",n)).isDefaultPrevented()){var i=e.selection.getRng();!function(r,e){var t=A.from(e.firstChild).map(rr.fromDom),n=A.from(e.lastChild).map(rr.fromDom);r.deleteContents(),r.insertNode(e);var o=t.bind(Fr).filter(lr).bind(jh),i=n.bind(Ur).filter(lr).bind(jh);Ya([o,t.filter(lr)],function(e,t){var n,r;n=t.dom(),r=e.dom().data,n.insertData(0,r),Ii(e)}),Ya([i,n.filter(lr)],function(e,t){var n=t.dom().length;t.dom().appendData(e.dom().data),r.setEnd(t.dom(),n),Ii(e)}),r.collapse(!1)}(i,i.createContextualFragment(n.content)),e.selection.setRng(i),oh(e,i),n.no_events||e.fire("SetContent",n)}else e.fire("SetContent",n)},qh=function(e,t,n,r,o){var i=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return A.from(i).map(rr.fromDom).map(function(e){return r&&t.collapsed?e:Hr(e,o(e,a)).getOr(e)}).bind(function(e){return cr(e)?A.some(e):Mr(e)}).map(function(e){return e.dom()}).getOr(e)},$h=function(e,t,n){return qh(e,t,!0,n,function(e,t){return Math.min(e.dom().childNodes.length,t)})},Wh=function(e,t,n){return qh(e,t,!1,n,function(e,t){return 0<t?t-1:t})},Kh=function(e,t){for(var n=e;e&&Oo.isText(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Xh=function(e,t,n){if(e&&e.hasOwnProperty(t)){var r=U(e[t],function(e){return e!==n});0===r.length?delete e[t]:e[t]=r}},Yh=function(e){return!!e.select},Gh=function(e){return!(!e||!e.ownerDocument)&&Ir(rr.fromDom(e.ownerDocument),rr.fromDom(e))},Jh=function(u,s,e,c){var n,t,l,f,r=function h(i,n){var a,u;return{selectorChangedWithUnbind:function(e,t){return a||(a={},u={},n.on("NodeChange",function(e){var n=e.element,r=i.getParents(n,null,i.getRoot()),o={};Yt.each(a,function(e,n){Yt.each(r,function(t){if(i.is(t,n))return u[n]||(Yt.each(e,function(e){e(!0,{node:t,selector:n,parents:r})}),u[n]=e),o[n]=e,!1})}),Yt.each(u,function(e,t){o[t]||(delete u[t],Yt.each(e,function(e){e(!1,{node:n,selector:t,parents:r})}))})})),a[e]||(a[e]=[]),a[e].push(t),{unbind:function(){Xh(a,e,t),Xh(u,e,t)}}}}}(u,c).selectorChangedWithUnbind,o=function(e,t){return Hh(c,e,t)},i=function(e){var t=m();t.collapse(!!e),a(t)},d=function(){return s.getSelection?s.getSelection():s.document.selection},m=function(){var e,t,n,r,o=function(e,t,n){try{return t.compareBoundaryPoints(e,n)}catch(r){return-1}};if(!s)return null;if(null==(r=s.document))return null;if(c.bookmark!==undefined&&!1===mf(c)){var i=of(c);if(i.isSome())return i.map(function(e){return uh(c,[e])[0]}).getOr(r.createRange())}try{(e=d())&&(t=0<e.rangeCount?e.getRangeAt(0):e.createRange?e.createRange():r.createRange())}catch(a){}return(t=uh(c,[t])[0])||(t=r.createRange?r.createRange():r.body.createTextRange()),t.setStart&&9===t.startContainer.nodeType&&t.collapsed&&(n=u.getRoot(),t.setStart(n,0),t.setEnd(n,0)),l&&f&&(0===o(t.START_TO_START,t,l)&&0===o(t.END_TO_END,t,l)?t=f:f=l=null),t},a=function(e,t){var n,r;if((o=e)&&(Yh(o)||Gh(o.startContainer)&&Gh(o.endContainer))){var o,i=Yh(e)?e:null;if(i){f=null;try{i.select()}catch(a){}}else{if(n=d(),e=c.fire("SetSelectionRange",{range:e,forward:t}).range,n){f=e;try{n.removeAllRanges(),n.addRange(e)}catch(a){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),l=0<n.rangeCount?n.getRangeAt(0):null}e.collapsed||e.startContainer!==e.endContainer||!n.setBaseAndExtent||de.ie||e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()&&(r=e.startContainer.childNodes[e.startOffset])&&"IMG"===r.tagName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(r,0,r,1)),c.fire("AfterSetSelectionRange",{range:e,forward:t})}}},g=function(){var e,t,n=d();return!(n&&n.anchorNode&&n.focusNode)||((e=u.createRng()).setStart(n.anchorNode,n.anchorOffset),e.collapse(!0),(t=u.createRng()).setStart(n.focusNode,n.focusOffset),t.collapse(!0),e.compareBoundaryPoints(e.START_TO_START,t)<=0)},p={bookmarkManager:null,controlSelection:null,dom:u,win:s,serializer:e,editor:c,collapse:i,setCursorLocation:function(e,t){var n=u.createRng();e?(n.setStart(e,t),n.setEnd(e,t),a(n),i(!1)):(Id(u,n,c.getBody(),!0),a(n))},getContent:function(e){return Ph(c,e)},setContent:o,getBookmark:function(e,t){return n.getBookmark(e,t)},moveToBookmark:function(e){return n.moveToBookmark(e)},select:function(e,t){var r,n,o;return(r=u,n=e,o=t,A.from(n).map(function(e){var t=r.nodeIndex(e),n=r.createRng();return n.setStart(e.parentNode,t),n.setEnd(e.parentNode,t+1),o&&(Id(r,n,e,!0),Id(r,n,e,!1)),n})).each(a),e},isCollapsed:function(){var e=m(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isForward:g,setNode:function(e){return o(u.getOuterHTML(e)),e},getNode:function(){return e=c.getBody(),(t=m())?(r=t.startContainer,o=t.endContainer,i=t.startOffset,a=t.endOffset,n=t.commonAncestorContainer,!t.collapsed&&(r===o&&a-i<2&&r.hasChildNodes()&&(n=r.childNodes[i]),3===r.nodeType&&3===o.nodeType&&(r=r.length===i?Kh(r.nextSibling,!0):r.parentNode,o=0===a?Kh(o.previousSibling,!1):o.parentNode,r&&r===o))?r:n&&3===n.nodeType?n.parentNode:n):e;var e,t,n,r,o,i,a},getSel:d,setRng:a,getRng:m,getStart:function(e){return $h(c.getBody(),m(),e)},getEnd:function(e){return Wh(c.getBody(),m(),e)},getSelectedBlocks:function(e,t){return function(e,t,n,r){var o,i,a=[];if(i=e.getRoot(),n=e.getParent(n||$h(i,t,t.collapsed),e.isBlock),r=e.getParent(r||Wh(i,t,t.collapsed),e.isBlock),n&&n!==i&&a.push(n),n&&r&&n!==r)for(var u=new oo(o=n,i);(o=u.next())&&o!==r;)e.isBlock(o)&&a.push(o);return r&&n!==r&&r!==i&&a.push(r),a}(u,m(),e,t)},normalize:function(){var e=m(),t=d();if(wh(t)||!Ld(c))return e;var n=Vh(u,e);return n.each(function(e){a(e,g())}),n.getOr(e)},selectorChanged:function(e,t){return r(e,t),p},selectorChangedWithUnbind:r,getScrollContainer:function(){for(var e,t=u.getRoot();t&&"BODY"!==t.nodeName;){if(t.scrollHeight>t.clientHeight){e=t;break}t=t.parentNode}return e},scrollIntoView:function(e,t){return rh(c,e,t)},placeCaretAt:function(e,t){return a(ah(e,t,c.getDoc()))},getBoundingClientRect:function(){var e=m();return e.collapsed?xu.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:function(){s=l=f=null,t.destroy()}};return n=Qp(p),t=th(p,c),p.bookmarkManager=n,p.controlSelection=t,p},Qh=Oo.isText,Zh=function(e){return Qh(e)&&e.data[0]===ga},ev=function(e){return Qh(e)&&e.data[e.data.length-1]===ga},tv=function(e){return e.ownerDocument.createTextNode(ga)},nv=function(e,t){return e?function(e){if(Qh(e.previousSibling))return ev(e.previousSibling)||e.previousSibling.appendData(ga),e.previousSibling;if(Qh(e))return Zh(e)||e.insertData(0,ga),e;var t=tv(e);return e.parentNode.insertBefore(t,e),t}(t):function(e){if(Qh(e.nextSibling))return Zh(e.nextSibling)||e.nextSibling.insertData(0,ga),e.nextSibling;if(Qh(e))return ev(e)||e.appendData(ga),e;var t=tv(e);return e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t),t}(t)},rv=d(nv,!0),ov=d(nv,!1),iv=function(e,t){return Oo.isText(e.container())?nv(t,e.container()):nv(t,e.getNode())},av=function(e,t){var n=t.get();return n&&e.container()===n&&ya(n)},uv=function(n,e){return e.fold(function(e){ts.remove(n.get());var t=rv(e);return n.set(t),A.some(xu(t,t.length-1))},function(e){return sc.firstPositionIn(e).map(function(e){if(av(e,n))return xu(n.get(),1);ts.remove(n.get());var t=iv(e,!0);return n.set(t),xu(t,1)})},function(e){return sc.lastPositionIn(e).map(function(e){if(av(e,n))return xu(n.get(),n.get().length-1);ts.remove(n.get());var t=iv(e,!1);return n.set(t),xu(t,t.length-1)})},function(e){ts.remove(n.get());var t=ov(e);return n.set(t),A.some(xu(t,1))})},sv=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,cv=function(e,t){if(!t)return t;var n=t.container(),r=t.offset();return e?ya(n)?Oo.isText(n.nextSibling)?xu(n.nextSibling,0):xu.after(n):wa(t)?xu(n,r+1):t:ya(n)?Oo.isText(n.previousSibling)?xu(n.previousSibling,n.previousSibling.data.length):xu.before(n):Na(t)?xu(n,r-1):t},lv={isInlineTarget:function(e,t){var n=Uf(e,"inline_boundaries_selector").getOr("a[href],code");return Or(rr.fromDom(t),n)},findRootInline:function(e,t,n){var r,o,i,a=(r=e,o=t,i=n,U(hi.DOM.getParents(i.container(),"*",o),r));return A.from(a[a.length-1])},isRtl:function(e){return"rtl"===hi.DOM.getStyle(e,"direction",!0)||(t=e.textContent,sv.test(t));var t},isAtZwsp:function(e){return wa(e)||Na(e)},normalizePosition:cv,normalizeForwards:d(cv,!0),normalizeBackwards:d(cv,!1),hasSameParentBlock:function(e,t,n){var r=xs(t,e),o=xs(n,e);return r&&r===o}},fv=function(e,t){for(var n=0;n<e.length;n++){var r=e[n].apply(null,t);if(r.isSome())return r}return A.none()},dv=Vl([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),mv=function(e,t){var n=xs(t,e);return n||e},gv=function(e,t,n){var r=lv.normalizeForwards(n),o=mv(t,r.container());return lv.findRootInline(e,o,r).fold(function(){return sc.nextPosition(o,r).bind(d(lv.findRootInline,e,o)).map(function(e){return dv.before(e)})},A.none)},pv=function(e,t){return null===$u(e,t)},hv=function(e,t,n){return lv.findRootInline(e,t,n).filter(d(pv,t))},vv=function(e,t,n){var r=lv.normalizeBackwards(n);return hv(e,t,r).bind(function(e){return sc.prevPosition(e,r).isNone()?A.some(dv.start(e)):A.none()})},bv=function(e,t,n){var r=lv.normalizeForwards(n);return hv(e,t,r).bind(function(e){return sc.nextPosition(e,r).isNone()?A.some(dv.end(e)):A.none()})},yv=function(e,t,n){var r=lv.normalizeBackwards(n),o=mv(t,r.container());return lv.findRootInline(e,o,r).fold(function(){return sc.prevPosition(o,r).bind(d(lv.findRootInline,e,o)).map(function(e){return dv.after(e)})},A.none)},Cv=function(e){return!1===lv.isRtl(wv(e))},xv=function(e,t,n){return fv([gv,vv,bv,yv],[e,t,n]).filter(Cv)},wv=function(e){return e.fold($,$,$,$)},Nv=function(e){return e.fold(q("before"),q("start"),q("end"),q("after"))},Ev=function(e){return e.fold(dv.before,dv.before,dv.after,dv.after)},Sv=function(n,e,r,t,o,i){return Ya([lv.findRootInline(e,r,t),lv.findRootInline(e,r,o)],function(e,t){return e!==t&&lv.hasSameParentBlock(r,e,t)?dv.after(n?e:t):i}).getOr(i)},kv=function(e,r){return e.fold(q(!0),function(e){return n=r,!(Nv(t=e)===Nv(n)&&wv(t)===wv(n));var t,n})},Tv=function(e,t){return e?t.fold(H(A.some,dv.start),A.none,H(A.some,dv.after),A.none):t.fold(A.none,H(A.some,dv.before),A.none,H(A.some,dv.end))},Av=function(a,u,s,c){var e=lv.normalizePosition(a,c),l=xv(u,s,e);return xv(u,s,e).bind(d(Tv,a)).orThunk(function(){return t=a,n=u,r=s,o=l,e=c,i=lv.normalizePosition(t,e),sc.fromPosition(t,r,i).map(d(lv.normalizePosition,t)).fold(function(){return o.map(Ev)},function(e){return xv(n,r,e).map(d(Sv,t,n,r,i,e)).filter(d(kv,o))}).filter(Cv);var t,n,r,o,e,i})},Rv=xv,Dv=Av,Bv=(d(Av,!1),d(Av,!0),Ev),Ov=function(e){return e.fold(dv.start,dv.start,dv.end,dv.end)},_v=function(e){return P(e.selection.getSel().modify)},Pv=function(e,t,n){var r=e?1:-1;return t.setRng(xu(n.container(),n.offset()+r).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0},Iv=function(e,t){var n=t.selection.getRng(),r=e?xu.fromRangeEnd(n):xu.fromRangeStart(n);return!!_v(t)&&(e&&wa(r)?Pv(!0,t.selection,r):!(e||!Na(r))&&Pv(!1,t.selection,r))},Lv=function(e,t){var n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},Mv=function(e){return!1!==e.settings.inline_boundaries},Fv=function(e,t){e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},Uv=function(t,e,n){return uv(e,n).map(function(e){return Lv(t,e),n})},zv=function(e,t,n){return function(){return!!Mv(t)&&Iv(e,t)}},Vv={move:function(a,u,s){return function(){return!!Mv(a)&&(t=a,n=u,e=s,r=t.getBody(),o=xu.fromRangeStart(t.selection.getRng()),i=d(lv.isInlineTarget,t),Dv(e,i,r,o).bind(function(e){return Uv(t,n,e)})).isSome();var t,n,e,r,o,i}},moveNextWord:d(zv,!0),movePrevWord:d(zv,!1),setupSelectedState:function(a){var u=Ni(null),s=d(lv.isInlineTarget,a);return a.on("NodeChange",function(e){var t,n,r,o,i;Mv(a)&&(t=s,n=a.dom,r=e.parents,o=U(n.select('*[data-mce-selected="inline-boundary"]'),t),i=U(r,t),F(Z(o,i),d(Fv,!1)),F(Z(i,o),d(Fv,!0)),function(e,t){if(e.selection.isCollapsed()&&!0!==e.composing&&t.get()){var n=xu.fromRangeStart(e.selection.getRng());xu.isTextPosition(n)&&!1===lv.isAtZwsp(n)&&(Lv(e,ts.removeAndReposition(t.get(),n)),t.set(null))}}(a,u),function(n,r,o,e){if(r.selection.isCollapsed()){var t=U(e,n);F(t,function(e){var t=xu.fromRangeStart(r.selection.getRng());Rv(n,r.getBody(),t).bind(function(e){return Uv(r,o,e)})})}}(s,a,u,e.parents))}),u},setCaretPosition:Lv},jv=Oo.isContentEditableFalse,Hv=$a,qv=Ps,$v=_s,Wv=function(e,t,n,r){var o=e===yu.Forwards,i=o?$v:qv;if(!r.collapsed){var a=Hv(r);if(jv(a))return mm(e,t,a,e===yu.Backwards,!0)}var u=ba(r.startContainer),s=Os(e,t.getBody(),r);if(i(s))return gm(t,s.getNode(!o));var c=lv.normalizePosition(o,n(s));if(!c)return u?r:null;if(i(c))return mm(e,t,c.getNode(!o),o,!0);var l=n(c);return l&&i(l)&&js(c,l)?mm(e,t,l.getNode(!o),o,!0):u?hm(t,c.toRange(),!0):null},Kv=function(e,t,n,r){var o,i,a,u,s,c,l,f,d;if(d=Hv(r),o=Os(e,t.getBody(),r),i=n(t.getBody(),em(1),o),a=U(i,tm(1)),s=qt.last(o.getClientRects()),($v(o)||Is(o))&&(d=o.getNode()),(qv(o)||Ls(o))&&(d=o.getNode(!0)),!s)return null;if(c=s.left,(u=um(a,c))&&jv(u.node))return l=Math.abs(c-u.left),f=Math.abs(c-u.right),mm(e,t,u.node,l<f,!0);if(d){var m=function(e,t,n,r){var o,i,a,u,s,c,l=Zs(t),f=[],d=0,m=function(e){return qt.last(e.getClientRects())};c=m(u=1===e?(o=l.next,i=qa,a=Ha,xu.after(r)):(o=l.prev,i=Ha,a=qa,xu.before(r)));do{if(u.isVisible()&&!a(s=m(u),c)){if(0<f.length&&i(s,qt.last(f))&&d++,(s=za(s)).position=u,s.line=d,n(s))return f;f.push(s)}}while(u=o(u));return f}(e,t.getBody(),em(1),d);if(u=um(U(m,tm(1)),c))return hm(t,u.position.toRange(),!0);if(u=qt.last(U(m,tm(0))))return hm(t,u.position.toRange(),!0)}},Xv=function(e,t,n){var r,o,i,a,u=Zs(e.getBody()),s=d(Vs,u.next),c=d(Vs,u.prev);if(n.collapsed&&e.settings.forced_root_block){if(!(r=e.dom.getParent(n.startContainer,"PRE")))return;(1===t?s(xu.fromRangeStart(n)):c(xu.fromRangeStart(n)))||(a=(i=e).dom.create(fl(i)),(!de.ie||11<=de.ie)&&(a.innerHTML='<br data-mce-bogus="1">'),o=a,1===t?e.$(r).after(o):e.$(r).before(o),e.selection.select(o,!0),e.selection.collapse())}},Yv=function(l,f){return function(){var e,t,n,r,o,i,a,u,s,c=(t=f,r=Zs((e=l).getBody()),o=d(Vs,r.next),i=d(Vs,r.prev),a=t?yu.Forwards:yu.Backwards,u=t?o:i,s=e.selection.getRng(),(n=Wv(a,e,u,s))?n:(n=Xv(e,a,s))||null);return!!c&&(l.selection.setRng(c),!0)}},Gv=function(u,s){return function(){var e,t,n,r,o,i,a=(r=(t=s)?1:-1,o=t?Zd:Qd,i=(e=u).selection.getRng(),(n=Kv(r,e,o,i))?n:(n=Xv(e,r,i))||null);return!!a&&(u.selection.setRng(a),!0)}};(Jp=Gp||(Gp={}))[Jp.Br=0]="Br",Jp[Jp.Block=1]="Block",Jp[Jp.Wrap=2]="Wrap",Jp[Jp.Eol=3]="Eol";var Jv=function(e,t){return e===yu.Backwards?t.reverse():t},Qv=function(e,t,n,r){for(var o,i,a,u,s,c,l=Zs(n),f=r,d=[];f&&(s=l,c=f,o=t===yu.Forwards?s.next(c):s.prev(c));){if(Oo.isBr(o.getNode(!1)))return t===yu.Forwards?{positions:Jv(t,d).concat([o]),breakType:Gp.Br,breakAt:A.some(o)}:{positions:Jv(t,d),breakType:Gp.Br,breakAt:A.some(o)};if(o.isVisible()){if(e(f,o)){var m=(i=t,a=f,u=o,Oo.isBr(u.getNode(i===yu.Forwards))?Gp.Br:!1===ws(a,u)?Gp.Block:Gp.Wrap);return{positions:Jv(t,d),breakType:m,breakAt:A.some(o)}}d.push(o),f=o}else f=o}return{positions:Jv(t,d),breakType:Gp.Eol,breakAt:A.none()}},Zv=function(n,r,o,e){return r(o,e).breakAt.map(function(e){var t=r(o,e).positions;return n===yu.Backwards?t.concat(e):[e].concat(t)}).getOr([])},eb=function(e,i){return z(e,function(e,o){return e.fold(function(){return A.some(o)},function(r){return Ya([ee(r.getClientRects()),ee(o.getClientRects())],function(e,t){var n=Math.abs(i-e.left);return Math.abs(i-t.left)<=n?o:r}).or(e)})},A.none())},tb=function(t,e){return ee(e.getClientRects()).bind(function(e){return eb(t,e.left)})},nb=d(Qv,vu.isAbove,-1),rb=d(Qv,vu.isBelow,1),ob=d(Zv,-1,nb),ib=d(Zv,1,rb),ab=function(e,t,n,r,o){var i,a,u,s,c=Wi(rr.fromDom(n),"td,th,caption").map(function(e){return e.dom()}),l=U((i=e,G(c,function(e){var t,n,r=(t=za(e.getBoundingClientRect()),n=-1,{left:t.left-n,top:t.top-n,right:t.right+2*n,bottom:t.bottom+2*n,width:t.width+n,height:t.height+n});return[{x:r.left,y:i(r),cell:e},{x:r.right,y:i(r),cell:e}]})),function(e){return t(e,o)});return(a=l,u=r,s=o,z(a,function(e,r){return e.fold(function(){return A.some(r)},function(e){var t=Math.sqrt(Math.abs(e.x-u)+Math.abs(e.y-s)),n=Math.sqrt(Math.abs(r.x-u)+Math.abs(r.y-s));return A.some(n<t?r:e)})},A.none())).map(function(e){return e.cell})},ub=d(ab,function(e){return e.bottom},function(e,t){return e.y<t}),sb=d(ab,function(e){return e.top},function(e,t){return e.y>t}),cb=function(t,n){return ee(n.getClientRects()).bind(function(e){return ub(t,e.left,e.top)}).bind(function(e){return tb((t=e,sc.lastPositionIn(t).map(function(e){return nb(t,e).positions.concat(e)}).getOr([])),n);var t})},lb=function(t,n){return te(n.getClientRects()).bind(function(e){return sb(t,e.left,e.top)}).bind(function(e){return tb((t=e,sc.firstPositionIn(t).map(function(e){return[e].concat(rb(t,e).positions)}).getOr([])),n);var t})},fb=function(e,t){e.selection.setRng(t),oh(e,t)},db=function(e,t,n){var r,o,i,a,u=e(t,n);return(a=u).breakType===Gp.Wrap&&0===a.positions.length||!Oo.isBr(n.getNode())&&(i=u).breakType===Gp.Br&&1===i.positions.length?(r=e,o=t,!u.breakAt.map(function(e){return r(o,e).breakAt.isSome()}).getOr(!1)):u.breakAt.isNone()},mb=d(db,nb),gb=d(db,rb),pb=function(e,t,n,r){var o,i,a,u,s=e.selection.getRng(),c=t?1:-1;if(as()&&(o=t,i=s,a=n,u=xu.fromRangeStart(i),sc.positionIn(!o,a).map(function(e){return e.isEqual(u)}).getOr(!1))){var l=mm(c,e,n,!t,!0);return fb(e,l),!0}return!1},hb=function(e,t){var n=t.getNode(e);return Oo.isElement(n)&&"TABLE"===n.nodeName?A.some(n):A.none()},vb=function(u,s,c){var e=hb(!!s,c),t=!1===s;e.fold(function(){return fb(u,c.toRange())},function(a){return sc.positionIn(t,u.getBody()).filter(function(e){return e.isEqual(c)}).fold(function(){return fb(u,c.toRange())},function(e){return n=s,o=a,t=c,void((i=fl(r=u))?r.undoManager.transact(function(){var e=rr.fromTag(i);yr(e,dl(r)),Oi(e,rr.fromTag("br")),n?Di(rr.fromDom(o),e):Ri(rr.fromDom(o),e);var t=r.dom.createRng();t.setStart(e.dom(),0),t.setEnd(e.dom(),0),fb(r,t)}):fb(r,t.toRange()));var n,r,o,t,i})})},bb=function(e,t,n,r){var o,i,a,u,s,c,l=e.selection.getRng(),f=xu.fromRangeStart(l),d=e.getBody();if(t||!mb(r,f))return!(!t||!gb(r,f))&&(o=d,m=lb(i=n,a=f).orThunk(function(){return ee(a.getClientRects()).bind(function(e){return eb(ib(o,xu.after(i)),e.left)})}).getOr(xu.after(i)),vb(e,t,m),!0);var m=(u=d,cb(s=n,c=f).orThunk(function(){return ee(c.getClientRects()).bind(function(e){return eb(ob(u,xu.before(s)),e.left)})}).getOr(xu.before(s)));return vb(e,t,m),!0},yb=function(t,n){return function(){return A.from(t.dom.getParent(t.selection.getNode(),"td,th")).bind(function(e){return A.from(t.dom.getParent(e,"table")).map(function(e){return pb(t,n,e)})}).getOr(!1)}},Cb=function(n,r){return function(){return A.from(n.dom.getParent(n.selection.getNode(),"td,th")).bind(function(t){return A.from(n.dom.getParent(t,"table")).map(function(e){return bb(n,r,e,t)})}).getOr(!1)}},xb=function(e){return M(["figcaption"],ur(e))},wb=function(e){var t=document.createRange();return t.setStartBefore(e.dom()),t.setEndBefore(e.dom()),t},Nb=function(e,t,n){n?Oi(e,t):Bi(e,t)},Eb=function(e,t,n,r){return""===t?(l=e,f=r,d=rr.fromTag("br"),Nb(l,d,f),wb(d)):(o=e,i=r,a=t,u=n,s=rr.fromTag(a),c=rr.fromTag("br"),yr(s,u),Oi(s,c),Nb(o,s,i),wb(c));var o,i,a,u,s,c,l,f,d},Sb=function(e,t,n){return t?(o=e.dom(),rb(o,n).breakAt.isNone()):(r=e.dom(),nb(r,n).breakAt.isNone());var r,o},kb=function(t,n){var e,r,o,i=rr.fromDom(t.getBody()),a=xu.fromRangeStart(t.selection.getRng()),u=fl(t),s=dl(t);return(e=a,r=i,o=d(Pr,r),Gi(rr.fromDom(e.container()),lo,o).filter(xb)).exists(function(){if(Sb(i,n,a)){var e=Eb(i,u,s,n);return t.selection.setRng(e),!0}return!1})},Tb=function(e,t){return function(){return!!e.selection.isCollapsed()&&kb(e,t)}},Ab=function(e,r){return G(W(e,function(e){return Wc({shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0,action:o},e)}),function(e){return t=e,(n=r).keyCode===t.keyCode&&n.shiftKey===t.shiftKey&&n.altKey===t.altKey&&n.ctrlKey===t.ctrlKey&&n.metaKey===t.metaKey?[e]:[];var t,n})},Rb=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,r)}},Db=function(e,t){return V(Ab(e,t),function(e){return e.action()})},Bb=function(i,a){i.on("keydown",function(e){var t,n,r,o;!1===e.isDefaultPrevented()&&(t=i,n=a,r=e,o=tr.detect().os,Db([{keyCode:bm.RIGHT,action:Yv(t,!0)},{keyCode:bm.LEFT,action:Yv(t,!1)},{keyCode:bm.UP,action:Gv(t,!1)},{keyCode:bm.DOWN,action:Gv(t,!0)},{keyCode:bm.RIGHT,action:yb(t,!0)},{keyCode:bm.LEFT,action:yb(t,!1)},{keyCode:bm.UP,action:Cb(t,!1)},{keyCode:bm.DOWN,action:Cb(t,!0)},{keyCode:bm.RIGHT,action:Vv.move(t,n,!0)},{keyCode:bm.LEFT,action:Vv.move(t,n,!1)},{keyCode:bm.RIGHT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:Vv.moveNextWord(t,n)},{keyCode:bm.LEFT,ctrlKey:!o.isOSX(),altKey:o.isOSX(),action:Vv.movePrevWord(t,n)},{keyCode:bm.UP,action:Tb(t,!1)},{keyCode:bm.DOWN,action:Tb(t,!0)}],r).each(function(e){r.preventDefault()}))})},Ob=function(e,t){return Ir(e,t)?Gi(t,function(e){return go(e)||ho(e)},(n=e,function(e){return Pr(n,rr.fromDom(e.dom().parentNode))})):A.none();var n},_b=function(e){var t,n,r;e.dom.isEmpty(e.getBody())&&(e.setContent(""),n=(t=e).getBody(),r=n.firstChild&&t.dom.isBlock(n.firstChild)?n.firstChild:n,t.selection.setCursorLocation(r,0))},Pb=function(i,a,u){return Ya([sc.firstPositionIn(u),sc.lastPositionIn(u)],function(e,t){var n=lv.normalizePosition(!0,e),r=lv.normalizePosition(!1,t),o=lv.normalizePosition(!1,a);return i?sc.nextPosition(u,o).map(function(e){return e.isEqual(r)&&a.isEqual(n)}).getOr(!1):sc.prevPosition(u,o).map(function(e){return e.isEqual(n)&&a.isEqual(r)}).getOr(!1)}).getOr(!0)},Ib=Sr("block","position"),Lb=Sr("from","to"),Mb=function(e,t){var n=rr.fromDom(e),r=rr.fromDom(t.container());return Ob(n,r).map(function(e){return Ib(e,t)})},Fb=function(o,i,e){var t=Mb(o,xu.fromRangeStart(e)),n=t.bind(function(e){return sc.fromPosition(i,o,e.position()).bind(function(e){return Mb(o,e).map(function(e){return t=o,n=i,r=e,Oo.isBr(r.position().getNode())&&!1===cg(r.block())?sc.positionIn(!1,r.block().dom()).bind(function(e){return e.isEqual(r.position())?sc.fromPosition(n,t,e).bind(function(e){return Mb(t,e)}):A.some(r)}).getOr(r):r;var t,n,r})})});return Ya([t,n],Lb).filter(function(e){return!1===Pr((r=e).from().block(),r.to().block())&&Mr((n=e).from().block()).bind(function(t){return Mr(n.to().block()).filter(function(e){return Pr(t,e)})}).isSome()&&(t=e,!1===Oo.isContentEditableFalse(t.from().block())&&!1===Oo.isContentEditableFalse(t.to().block()));var t,n,r})},Ub=function(e,t,n){return n.collapsed?Fb(e,t,n):A.none()},zb=function(e){var t,n=(t=jr(e),j(t,lo).fold(function(){return t},function(e){return t.slice(0,e)}));return F(n,Ii),n},Vb=function(e,t){var n=Sd(t,e);return V(n.reverse(),cg).each(Ii)},jb=function(e,t,n,r){if(cg(n))return $m(n),sc.firstPositionIn(n.dom());0===U(zr(r),function(e){return!cg(e)}).length&&cg(t)&&Ri(r,rr.fromTag("br"));var o=sc.prevPosition(n.dom(),xu.before(r.dom()));return F(zb(t),function(e){Ri(r,e)}),Vb(e,t),o},Hb=function(e,t,n){if(cg(n))return Ii(n),cg(t)&&$m(t),sc.firstPositionIn(t.dom());var r=sc.lastPositionIn(n.dom());return F(zb(t),function(e){Oi(n,e)}),Vb(e,t),r},qb=function(e,t){return Ir(t,e)?(n=Sd(e,t),A.from(n[n.length-1])):A.none();var n},$b=function(e,t){sc.positionIn(e,t.dom()).map(function(e){return e.getNode()}).map(rr.fromDom).filter(mo).each(Ii)},Wb=function(e,t,n){return $b(!0,t),$b(!1,n),qb(t,n).fold(d(Hb,e,t,n),d(jb,e,t,n))},Kb=function(e,t,n,r){return t?Wb(e,r,n):Wb(e,n,r)},Xb=function(t,n){var e,r=rr.fromDom(t.getBody());return(e=Ub(r.dom(),n,t.selection.getRng()).bind(function(e){return Kb(r,n,e.from().block(),e.to().block())})).each(function(e){t.selection.setRng(e.toRange())}),e.isSome()},Yb=function(e,t){var n=rr.fromDom(t),r=d(Pr,e);return Yi(n,yo,r).isSome()},Gb=function(e,t){var n,r,o=sc.prevPosition(e.dom(),xu.fromRangeStart(t)).isNone(),i=sc.nextPosition(e.dom(),xu.fromRangeEnd(t)).isNone();return!(Yb(n=e,(r=t).startContainer)||Yb(n,r.endContainer))&&o&&i},Jb=function(e){var n,r,o,t,i=rr.fromDom(e.getBody()),a=e.selection.getRng();return Gb(i,a)?((t=e).setContent(""),t.selection.setCursorLocation(),!0):(n=i,r=e.selection,o=r.getRng(),Ya([Ob(n,rr.fromDom(o.startContainer)),Ob(n,rr.fromDom(o.endContainer))],function(e,t){return!1===Pr(e,t)&&(o.deleteContents(),Kb(n,!0,e,t).each(function(e){r.setRng(e.toRange())}),!0)}).getOr(!1))},Qb=function(e,t){return!e.selection.isCollapsed()&&Jb(e)},Zb=function(e){return zs(e).exists(mo)},ey=function(e,t,n){var r=U(Sd(rr.fromDom(n.container()),t),lo),o=ee(r).getOr(t);return sc.fromPosition(e,o.dom(),n).filter(Zb)},ty=function(e,t){return zs(t).exists(mo)||ey(!0,e,t).isSome()},ny=function(e,t){return(n=t,A.from(n.getNode(!0)).map(rr.fromDom)).exists(mo)||ey(!1,e,t).isSome();var n},ry=d(ey,!1),oy=d(ey,!0),iy=Vl([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),ay=function(e,t,n,r){var o=r.getNode(!1===t);return Ob(rr.fromDom(e),rr.fromDom(n.getNode())).map(function(e){return cg(e)?iy.remove(e.dom()):iy.moveToElement(o)}).orThunk(function(){return A.some(iy.moveToElement(o))})},uy=function(u,s,c){return sc.fromPosition(s,u,c).bind(function(e){return a=e.getNode(),yo(rr.fromDom(a))||ho(rr.fromDom(a))?A.none():(t=u,o=e,i=function(e){return fo(rr.fromDom(e))&&!ws(r,o,t)},Bs(!(n=s),r=c).fold(function(){return Bs(n,o).fold(q(!1),i)},i)?A.none():s&&Oo.isContentEditableFalse(e.getNode())?ay(u,s,c,e):!1===s&&Oo.isContentEditableFalse(e.getNode(!0))?ay(u,s,c,e):s&&Ps(c)?A.some(iy.moveToPosition(e)):!1===s&&_s(c)?A.some(iy.moveToPosition(e)):A.none());var t,n,r,o,i,a})},sy=function(r,e,o){return i=e,a=o.getNode(!1===i),u=i?"after":"before",Oo.isElement(a)&&a.getAttribute("data-mce-caret")===u?(t=e,n=o.getNode(!1===e),t&&Oo.isContentEditableFalse(n.nextSibling)?A.some(iy.moveToElement(n.nextSibling)):!1===t&&Oo.isContentEditableFalse(n.previousSibling)?A.some(iy.moveToElement(n.previousSibling)):A.none()).fold(function(){return uy(r,e,o)},A.some):uy(r,e,o).bind(function(e){return t=r,n=o,e.fold(function(e){return A.some(iy.remove(e))},function(e){return A.some(iy.moveToElement(e))},function(e){return ws(n,e,t)?A.none():A.some(iy.moveToPosition(e))});var t,n});var t,n,i,a,u},cy=function(a,u){var e,t,n,r,o,i;return(e=a.getBody(),t=u,n=a.selection.getRng(),r=As(t?1:-1,e,n),o=xu.fromRangeStart(r),i=rr.fromDom(e),!1===t&&Ps(o)?A.some(iy.remove(o.getNode(!0))):t&&_s(o)?A.some(iy.remove(o.getNode())):!1===t&&_s(o)&&ny(i,o)?ry(i,o).map(function(e){return iy.remove(e.getNode())}):t&&Ps(o)&&ty(i,o)?oy(i,o).map(function(e){return iy.remove(e.getNode())}):sy(e,t,o)).map(function(e){return e.fold((o=a,i=u,function(e){return o._selectionOverrides.hideFakeCaret(),Cg(o,i,rr.fromDom(e)),!0}),(n=a,r=u,function(e){var t=r?xu.before(e):xu.after(e);return n.selection.setRng(t.toRange()),!0}),(t=a,function(e){return t.selection.setRng(e.toRange()),!0}));var t,n,r,o,i}).getOr(!1)},ly=function(e,t){var n,r=e.selection.getNode();return!!Oo.isContentEditableFalse(r)&&(n=rr.fromDom(e.getBody()),F(Wi(n,".mce-offscreen-selection"),Ii),Cg(e,t,rr.fromDom(e.selection.getNode())),_b(e),!0)},fy=function(e,t){return e.selection.isCollapsed()?cy(e,t):ly(e,t)},dy=function(e){var t,n=function(e,t){for(;t&&t!==e;){if(Oo.isContentEditableTrue(t)||Oo.isContentEditableFalse(t))return t;t=t.parentNode}return null}(e.getBody(),e.selection.getNode());return Oo.isContentEditableTrue(n)&&e.dom.isBlock(n)&&e.dom.isEmpty(n)&&(t=e.dom.create("br",{"data-mce-bogus":"1"}),e.dom.setHTML(n,""),n.appendChild(t),e.selection.setRng(xu.before(t).toRange())),!0},my=Ps,gy=_s,py=function(e,t,n,r,o,i){var a,u,s=mm(r,e,i.getNode(!o),o,!0);if(t.collapsed){var c=t.cloneRange();o?c.setEnd(s.startContainer,s.startOffset):c.setStart(s.endContainer,s.endOffset),c.deleteContents()}else t.deleteContents();return e.selection.setRng(s),a=e.dom,u=n,Oo.isText(u)&&0===u.data.length&&a.remove(u),!0},hy=function(e,t){return function(e,t){var n=e.selection.getRng();if(!Oo.isText(n.commonAncestorContainer))return!1;var r=t?yu.Forwards:yu.Backwards,o=Zs(e.getBody()),i=d(Vs,o.next),a=d(Vs,o.prev),u=t?i:a,s=t?gy:my,c=Os(r,e.getBody(),n),l=lv.normalizePosition(t,u(c));if(!l)return!1;if(s(l))return py(e,n,c.getNode(),r,t,l);var f=u(l);return!!(f&&s(f)&&js(l,f))&&py(e,n,c.getNode(),r,t,f)}(e,t)},vy=function(t,n){return function(e){return uv(n,e).map(function(e){return Vv.setCaretPosition(t,e),!0}).getOr(!1)}},by=function(r,o,i,a){var u=r.getBody(),s=d(lv.isInlineTarget,r);r.undoManager.ignore(function(){var e,t,n;r.selection.setRng((e=i,t=a,(n=document.createRange()).setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n)),r.execCommand("Delete"),Rv(s,u,xu.fromRangeStart(r.selection.getRng())).map(Ov).map(vy(r,o))}),r.nodeChanged()},yy=function(n,r,i,o){var e,t,a=(e=n.getBody(),t=o.container(),xs(t,e)||e),u=d(lv.isInlineTarget,n),s=Rv(u,a,o);return s.bind(function(e){return i?e.fold(q(A.some(Ov(e))),A.none,q(A.some(Bv(e))),A.none):e.fold(A.none,q(A.some(Bv(e))),A.none,q(A.some(Ov(e))))}).map(vy(n,r)).getOrThunk(function(){var t=sc.navigate(i,a,o),e=t.bind(function(e){return Rv(u,a,e)});return s.isSome()&&e.isSome()?lv.findRootInline(u,a,o).map(function(e){return o=e,!!Ya([sc.firstPositionIn(o),sc.lastPositionIn(o)],function(e,t){var n=lv.normalizePosition(!0,e),r=lv.normalizePosition(!1,t);return sc.nextPosition(o,n).map(function(e){return e.isEqual(r)}).getOr(!0)}).getOr(!0)&&(Cg(n,i,rr.fromDom(e)),!0);var o}).getOr(!1):e.bind(function(e){return t.map(function(e){return i?by(n,r,o,e):by(n,r,e,o),!0})}).getOr(!1)})},Cy=function(e,t,n){if(e.selection.isCollapsed()&&!1!==e.settings.inline_boundaries){var r=xu.fromRangeStart(e.selection.getRng());return yy(e,t,n,r)}return!1},xy=function(e){return 1===jr(e).length},wy=function(e,t,n,r){var o,i,a,u,s=d(Og,t),c=W(U(r,s),function(e){return e.dom()});if(0===c.length)Cg(t,e,n);else{var l=(o=n.dom(),i=c,a=Sg(!1),u=Dg(i,a.dom()),Ri(rr.fromDom(o),a),Ii(rr.fromDom(o)),xu(u,0));t.selection.setRng(l.toRange())}},Ny=function(r,o){var t,e=rr.fromDom(r.getBody()),n=rr.fromDom(r.selection.getStart()),i=U((t=Sd(n,e),j(t,lo).fold(q(t),function(e){return t.slice(0,e)})),xy);return te(i).map(function(e){var t,n=xu.fromRangeStart(r.selection.getRng());return!(!Pb(o,n,e.dom())||qu((t=e).dom())&&Ng(t.dom())||(wy(o,r,e,i),0))}).getOr(!1)},Ey=function(e,t){return!!e.selection.isCollapsed()&&Ny(e,t)},Sy=Sr("start","end"),ky=Sr("rng","table","cells"),Ty=Vl([{removeTable:["element"]},{emptyCells:["cells"]}]),Ay=function(e,t){return Zi(rr.fromDom(e),"td,th",t)},Ry=function(e,t){return Ji(e,"table",t)},Dy=function(e){return!1===Pr(e.start(),e.end())},By=function(e,n){return Ry(e.start(),n).bind(function(t){return Ry(e.end(),n).bind(function(e){return Pr(t,e)?A.some(t):A.none()})})},Oy=function(e){return Wi(e,"td,th")},_y=function(r,e){var t=Ay(e.startContainer,r),n=Ay(e.endContainer,r);return e.collapsed?A.none():Ya([t,n],Sy).fold(function(){return t.fold(function(){return n.bind(function(t){return Ry(t,r).bind(function(e){return ee(Oy(e)).map(function(e){return Sy(e,t)})})})},function(t){return Ry(t,r).bind(function(e){return te(Oy(e)).map(function(e){return Sy(t,e)})})})},function(e){return Py(r,e)?A.none():(n=r,Ry((t=e).start(),n).bind(function(e){return te(Oy(e)).map(function(e){return Sy(t.start(),e)})}));var t,n})},Py=function(e,t){return By(t,e).isSome()},Iy=function(e,t){var n,r,o,i,a=d(Pr,e);return(n=t,r=a,o=Ay(n.startContainer,r),i=Ay(n.endContainer,r),Ya([o,i],Sy).filter(Dy).filter(function(e){return Py(r,e)}).orThunk(function(){return _y(r,n)})).bind(function(e){return By(t=e,a).map(function(e){return ky(t,e,Oy(e))});var t})},Ly=function(e,t){return j(e,function(e){return Pr(e,t)})},My=function(n){return(r=n,Ya([Ly(r.cells(),r.rng().start()),Ly(r.cells(),r.rng().end())],function(e,t){return r.cells().slice(e,t+1)})).map(function(e){var t=n.cells();return e.length===t.length?Ty.removeTable(n.table()):Ty.emptyCells(e)});var r},Fy=function(e,t){return Iy(e,t).bind(My)},Uy=function(e,t){return F(t,$m),e.selection.setCursorLocation(t[0].dom(),0),!0},zy=function(e,t){return Cg(e,!1,t),!0},Vy=function(n,e,r,t){return Hy(e,t).fold(function(){return t=n,Fy(e,r).map(function(e){return e.fold(d(zy,t),d(Uy,t))});var t},function(e){return qy(n,e)}).getOr(!1)},jy=function(e,t){return V(Sd(t,e),yo)},Hy=function(e,t){return V(Sd(t,e),function(e){return"caption"===ur(e)})},qy=function(e,t){return $m(t),e.selection.setCursorLocation(t.dom(),0),A.some(!0)},$y=function(u,s,c,l,f){return sc.navigate(c,u.getBody(),f).bind(function(e){return r=l,o=c,i=f,a=e,sc.firstPositionIn(r.dom()).bind(function(t){return sc.lastPositionIn(r.dom()).map(function(e){return o?i.isEqual(t)&&a.isEqual(e):i.isEqual(e)&&a.isEqual(t)})}).getOr(!0)?qy(u,l):(t=l,n=e,Hy(s,rr.fromDom(n.getNode())).map(function(e){return!1===Pr(e,t)}));var t,n,r,o,i,a}).or(A.some(!0))},Wy=function(a,u,s,e){var c=xu.fromRangeStart(a.selection.getRng());return jy(s,e).bind(function(e){return cg(e)?qy(a,e):(t=a,n=s,r=u,o=e,i=c,sc.navigate(r,t.getBody(),i).bind(function(e){return jy(n,rr.fromDom(e.getNode())).map(function(e){return!1===Pr(e,o)})}));var t,n,r,o,i})},Ky=function(a,u,e){var s=rr.fromDom(a.getBody());return Hy(s,e).fold(function(){return Wy(a,u,s,e)},function(e){return t=a,n=u,r=s,o=e,i=xu.fromRangeStart(t.selection.getRng()),cg(o)?qy(t,o):$y(t,r,n,o,i);var t,n,r,o,i}).getOr(!1)},Xy=function(e,t){var n,r,o,i,a,u=rr.fromDom(e.selection.getStart(!0)),s=Th(e);return e.selection.isCollapsed()&&0===s.length?Ky(e,t,u):(n=e,r=u,o=rr.fromDom(n.getBody()),i=n.selection.getRng(),0!==(a=Th(n)).length?Uy(n,a):Vy(n,o,i,r))},Yy=function(o,i){o.on("keydown",function(e){var t,n,r;!1===e.isDefaultPrevented()&&(t=o,n=i,r=e,Db([{keyCode:bm.BACKSPACE,action:Rb(fy,t,!1)},{keyCode:bm.DELETE,action:Rb(fy,t,!0)},{keyCode:bm.BACKSPACE,action:Rb(hy,t,!1)},{keyCode:bm.DELETE,action:Rb(hy,t,!0)},{keyCode:bm.BACKSPACE,action:Rb(Cy,t,n,!1)},{keyCode:bm.DELETE,action:Rb(Cy,t,n,!0)},{keyCode:bm.BACKSPACE,action:Rb(Xy,t,!1)},{keyCode:bm.DELETE,action:Rb(Xy,t,!0)},{keyCode:bm.BACKSPACE,action:Rb(Qb,t,!1)},{keyCode:bm.DELETE,action:Rb(Qb,t,!0)},{keyCode:bm.BACKSPACE,action:Rb(Xb,t,!1)},{keyCode:bm.DELETE,action:Rb(Xb,t,!0)},{keyCode:bm.BACKSPACE,action:Rb(Ey,t,!1)},{keyCode:bm.DELETE,action:Rb(Ey,t,!0)}],r).each(function(e){r.preventDefault()}))}),o.on("keyup",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=o,n=e,Db([{keyCode:bm.BACKSPACE,action:Rb(dy,t)},{keyCode:bm.DELETE,action:Rb(dy,t)}],n))})},Gy=function(e){return A.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock))},Jy=function(e,t){var n,r,o,i=t,a=e.dom,u=e.schema.getMoveCaretBeforeOnEnterElements();if(t){if(/^(LI|DT|DD)$/.test(t.nodeName)){var s=function(e){for(;e;){if(1===e.nodeType||3===e.nodeType&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}}(t.firstChild);s&&/^(UL|OL|DL)$/.test(s.nodeName)&&t.insertBefore(a.doc.createTextNode("\xa0"),t.firstChild)}if(o=a.createRng(),t.normalize(),t.hasChildNodes()){for(n=new oo(t,t);r=n.current();){if(Oo.isText(r)){o.setStart(r,0),o.setEnd(r,0);break}if(u[r.nodeName.toLowerCase()]){o.setStartBefore(r),o.setEndBefore(r);break}i=r,r=n.next()}r||(o.setStart(i,0),o.setEnd(i,0))}else Oo.isBr(t)?t.nextSibling&&a.isBlock(t.nextSibling)?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)):(o.setStart(t,0),o.setEnd(t,0));e.selection.setRng(o),a.remove(void 0),e.selection.scrollIntoView(t)}},Qy=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},Zy=Gy,eC=function(e){return Gy(e).fold(q(""),function(e){return e.nodeName.toUpperCase()})},tC=function(e){return Gy(e).filter(function(e){return ho(rr.fromDom(e))}).isSome()},nC=function(e,t){return e&&e.parentNode&&e.parentNode.nodeName===t},rC=function(e){return e&&/^(OL|UL|LI)$/.test(e.nodeName)},oC=function(e){var t=e.parentNode;return/^(LI|DT|DD)$/.test(t.nodeName)?t:e},iC=function(e,t,n){for(var r=e[n?"firstChild":"lastChild"];r&&!Oo.isElement(r);)r=r[n?"nextSibling":"previousSibling"];return r===t},aC=function(e,t,n,r,o){var i=e.dom,a=e.selection.getRng();if(n!==e.getBody()){var u;rC(u=n)&&rC(u.parentNode)&&(o="LI");var s,c,l=o?t(o):i.create("BR");if(iC(n,r,!0)&&iC(n,r,!1))nC(n,"LI")?i.insertAfter(l,oC(n)):i.replace(l,n);else if(iC(n,r,!0))nC(n,"LI")?(i.insertAfter(l,oC(n)),l.appendChild(i.doc.createTextNode(" ")),l.appendChild(n)):n.parentNode.insertBefore(l,n);else if(iC(n,r,!1))i.insertAfter(l,oC(n));else{n=oC(n);var f=a.cloneRange();f.setStartAfter(r),f.setEndAfter(n);var d=f.extractContents();"LI"===o&&(c="LI",(s=d).firstChild&&s.firstChild.nodeName===c)?(l=d.firstChild,i.insertAfter(d,n)):(i.insertAfter(d,n),i.insertAfter(l,n))}i.remove(r),Jy(e,l)}},uC=function(e){e.innerHTML='<br data-mce-bogus="1">'},sC=function(e,t){return e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t},cC=function(e,t){return t&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&"true"!==e.getContentEditable(t)},lC=function(e,t,n){return!1===Oo.isText(t)?n:e?1===n&&t.data.charAt(n-1)===ga?0:n:n===t.data.length-1&&t.data.charAt(n)===ga?t.data.length:n},fC=function(e,t){var n,r,o=e.getRoot();for(n=t;n!==o&&"false"!==e.getContentEditable(n);)"true"===e.getContentEditable(n)&&(r=n),n=n.parentNode;return n!==o?r:o},dC=function(e,t){var n=fl(e);n&&n.toLowerCase()===t.tagName.toLowerCase()&&e.dom.setAttribs(t,dl(e))},mC=function(a,e){var t,u,s,i,c,n,r,o,l,f,d,m,g,p=a.dom,h=a.schema,v=h.getNonEmptyElements(),b=a.selection.getRng(),y=function(e){var t,n,r,o=s,i=h.getTextInlineElements();if(e||"TABLE"===f||"HR"===f?(t=p.create(e||m),dC(a,t)):t=c.cloneNode(!1),r=t,!1===pl(a))p.setAttrib(t,"style",null),p.setAttrib(t,"class",null);else do{if(i[o.nodeName]){if(qu(o))continue;n=o.cloneNode(!1),p.setAttrib(n,"id",""),t.hasChildNodes()?n.appendChild(t.firstChild):r=n,t.appendChild(n)}}while((o=o.parentNode)&&o!==u);return uC(r),t},C=function(e){var t,n,r,o;if(o=lC(e,s,i),Oo.isText(s)&&(e?0<o:o<s.nodeValue.length))return!1;if(s.parentNode===c&&g&&!e)return!0;if(e&&Oo.isElement(s)&&s===c.firstChild)return!0;if(sC(s,"TABLE")||sC(s,"HR"))return g&&!e||!g&&e;for(t=new oo(s,c),Oo.isText(s)&&(e&&0===o?t.prev():e||o!==s.nodeValue.length||t.next());n=t.current();){if(Oo.isElement(n)){if(!n.getAttribute("data-mce-bogus")&&(r=n.nodeName.toLowerCase(),v[r]&&"br"!==r))return!1}else if(Oo.isText(n)&&!/^[ \t\r\n]*$/.test(n.nodeValue))return!1;e?t.prev():t.next()}return!0},x=function(){r=/^(H[1-6]|PRE|FIGURE)$/.test(f)&&"HGROUP"!==d?y(m):y(),hl(a)&&cC(p,l)&&p.isEmpty(c)?r=p.split(l,c):p.insertAfter(r,c),Jy(a,r)};Vh(p,b).each(function(e){b.setStart(e.startContainer,e.startOffset),b.setEnd(e.endContainer,e.endOffset)}),s=b.startContainer,i=b.startOffset,m=fl(a),n=!(!e||!e.shiftKey);var w,N,E,S,k,T,A=!(!e||!e.ctrlKey);Oo.isElement(s)&&s.hasChildNodes()&&(g=i>s.childNodes.length-1,s=s.childNodes[Math.min(i,s.childNodes.length-1)]||s,i=g&&Oo.isText(s)?s.nodeValue.length:0),(u=fC(p,s))&&((m&&!n||!m&&n)&&(s=function(e,t,n,r,o){var i,a,u,s,c,l,f,d=t||"P",m=e.dom,g=fC(m,r);if(!(a=m.getParent(r,m.isBlock))||!cC(m,a)){if(l=(a=a||g)===e.getBody()||(f=a)&&/^(TD|TH|CAPTION)$/.test(f.nodeName)?a.nodeName.toLowerCase():a.parentNode.nodeName.toLowerCase(),!a.hasChildNodes())return i=m.create(d),dC(e,i),a.appendChild(i),n.setStart(i,0),n.setEnd(i,0),i;for(s=r;s.parentNode!==a;)s=s.parentNode;for(;s&&!m.isBlock(s);)s=(u=s).previousSibling;if(u&&e.schema.isValidChild(l,d.toLowerCase())){for(i=m.create(d),dC(e,i),u.parentNode.insertBefore(i,u),s=u;s&&!m.isBlock(s);)c=s.nextSibling,i.appendChild(s),s=c;n.setStart(r,o),n.setEnd(r,o)}}return r}(a,m,b,s,i)),c=p.getParent(s,p.isBlock),l=c?p.getParent(c.parentNode,p.isBlock):null,f=c?c.nodeName.toUpperCase():"","LI"!==(d=l?l.nodeName.toUpperCase():"")||A||(l=(c=l).parentNode,f=d),/^(LI|DT|DD)$/.test(f)&&p.isEmpty(c)?aC(a,y,l,c,m):m&&c===a.getBody()||(m=m||"P",ba(c)?(r=Ta(c),p.isEmpty(c)&&uC(c),Jy(a,r)):C()?x():C(!0)?(r=c.parentNode.insertBefore(y(),c),Jy(a,sC(c,"HR")?r:c)):((t=(k=b,T=k.cloneRange(),T.setStart(k.startContainer,lC(!0,k.startContainer,k.startOffset)),T.setEnd(k.endContainer,lC(!1,k.endContainer,k.endOffset)),T).cloneRange()).setEndAfter(c),o=t.extractContents(),S=o,F($i(rr.fromDom(S),lr),function(e){var t=e.dom();t.nodeValue=pa(t.nodeValue)}),function(e){for(;Oo.isText(e)&&(e.nodeValue=e.nodeValue.replace(/^[\r\n]+/,"")),e=e.firstChild;);}(o),r=o.firstChild,p.insertAfter(o,c),function(e,t,n){var r,o=n,i=[];if(o){for(;o=o.firstChild;){if(e.isBlock(o))return;Oo.isElement(o)&&!t[o.nodeName.toLowerCase()]&&i.push(o)}for(r=i.length;r--;)!(o=i[r]).hasChildNodes()||o.firstChild===o.lastChild&&""===o.firstChild.nodeValue?e.remove(o):(a=e,(u=o)&&"A"===u.nodeName&&a.isEmpty(u)&&e.remove(o));var a,u}}(p,v,r),w=p,(N=c).normalize(),(E=N.lastChild)&&!/^(left|right)$/gi.test(w.getStyle(E,"float",!0))||w.add(N,"br"),p.isEmpty(c)&&uC(c),r.normalize(),p.isEmpty(r)?(p.remove(r),x()):Jy(a,r)),p.setAttrib(r,"id",""),a.fire("NewBlock",{newBlock:r})))},gC=function(e,t,n){var r=e.create("span",{},"&nbsp;");n.parentNode.insertBefore(r,n),t.scrollIntoView(r),e.remove(r)},pC=function(e,t,n,r){var o=e.createRng();r?(o.setStartBefore(n),o.setEndBefore(n)):(o.setStartAfter(n),o.setEndAfter(n)),t.setRng(o)},hC=function(e,t){var n,r,o=e.selection,i=e.dom,a=o.getRng();Vh(i,a).each(function(e){a.setStart(e.startContainer,e.startOffset),a.setEnd(e.endContainer,e.endOffset)});var u=a.startOffset,s=a.startContainer;if(1===s.nodeType&&s.hasChildNodes()){var c=u>s.childNodes.length-1;s=s.childNodes[Math.min(u,s.childNodes.length-1)]||s,u=c&&3===s.nodeType?s.nodeValue.length:0}var l=i.getParent(s,i.isBlock),f=l?i.getParent(l.parentNode,i.isBlock):null,d=f?f.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==d||m||(l=f),s&&3===s.nodeType&&u>=s.nodeValue.length&&(function(e,t,n){for(var r,o=new oo(t,n),i=e.getNonEmptyElements();r=o.next();)if(i[r.nodeName.toLowerCase()]||0<r.length)return!0}(e.schema,s,l)||(n=i.create("br"),a.insertNode(n),a.setStartAfter(n),a.setEndAfter(n),r=!0)),n=i.create("br"),Ou(i,a,n),gC(i,o,n),pC(i,o,n,r),e.undoManager.add()},vC=function(e,t){var n=rr.fromTag("br");Ri(rr.fromDom(t),n),e.undoManager.add()},bC=function(e,t){yC(e.getBody(),t)||Di(rr.fromDom(t),rr.fromTag("br"));var n=rr.fromTag("br");Di(rr.fromDom(t),n),gC(e.dom,e.selection,n.dom()),pC(e.dom,e.selection,n.dom(),!1),e.undoManager.add()},yC=function(e,t){return n=xu.after(t),!!Oo.isBr(n.getNode())||sc.nextPosition(e,xu.after(t)).map(function(e){return Oo.isBr(e.getNode())}).getOr(!1);var n},CC=function(e){return e&&"A"===e.nodeName&&"href"in e},xC=function(e){return e.fold(q(!1),CC,CC,q(!1))},wC=function(e,t){t.fold(o,d(vC,e),d(bC,e),o)},NC=function(e,t){var n,r,o,i=(n=e,r=d(lv.isInlineTarget,n),o=xu.fromRangeStart(n.selection.getRng()),Rv(r,n.getBody(),o).filter(xC));i.isSome()?i.each(d(wC,e)):hC(e,t)},EC=function(e,t){return Zy(e).filter(function(e){return 0<t.length&&Or(rr.fromDom(e),t)}).isSome()},SC=function(e){return EC(e,ml(e))},kC=function(e){return EC(e,gl(e))},TC=Vl([{br:[]},{block:[]},{none:[]}]),AC=function(e,t){return kC(e)},RC=function(n){return function(e,t){return""===fl(e)===n}},DC=function(n){return function(e,t){return tC(e)===n}},BC=function(n,r){return function(e,t){return eC(e)===n.toUpperCase()===r}},OC=function(e){return BC("pre",e)},_C=function(n){return function(e,t){return ll(e)===n}},PC=function(e,t){return SC(e)},IC=function(e,t){return t},LC=function(e){var t=fl(e),n=Qy(e.dom,e.selection.getStart());return n&&e.schema.isValidChild(n.nodeName,t||"P")},MC=function(e,t){return function(n,r){return z(e,function(e,t){return e&&t(n,r)},!0)?A.some(t):A.none()}},FC=function(e,t){return fv([MC([AC],TC.none()),MC([BC("summary",!0)],TC.br()),MC([OC(!0),_C(!1),IC],TC.br()),MC([OC(!0),_C(!1)],TC.block()),MC([OC(!0),_C(!0),IC],TC.block()),MC([OC(!0),_C(!0)],TC.br()),MC([DC(!0),IC],TC.br()),MC([DC(!0)],TC.block()),MC([RC(!0),IC,LC],TC.block()),MC([RC(!0)],TC.br()),MC([PC],TC.br()),MC([RC(!1),IC],TC.br()),MC([LC],TC.block())],[e,!(!t||!t.shiftKey)]).getOr(TC.none())},UC=function(e,t){FC(e,t).fold(function(){NC(e,t)},function(){mC(e,t)},o)},zC=function(o){o.on("keydown",function(e){var t,n,r;e.keyCode===bm.ENTER&&(t=o,(n=e).isDefaultPrevented()||(n.preventDefault(),(r=t.undoManager).typing&&(r.typing=!1,r.add()),t.undoManager.transact(function(){!1===t.selection.isCollapsed()&&t.execCommand("Delete"),UC(t,n)})))})},VC=function(n,r){var e=r.container(),t=r.offset();return Oo.isText(e)?(e.insertData(t,n),A.some(vu(e,t+n.length))):zs(r).map(function(e){var t=rr.fromText(n);return r.isAtEnd()?Di(e,t):Ri(e,t),vu(t.dom(),n.length)})},jC=d(VC,"\xa0"),HC=d(VC," "),qC=function(t,n,r){var e=U(Sd(rr.fromDom(r.container()),n),lo);return ee(e).fold(function(){return sc.navigate(t,n.dom(),r).forall(function(e){return!1===ws(e,r,n.dom())})},function(e){return sc.navigate(t,e.dom(),r).isNone()})},$C=d(qC,!1),WC=d(qC,!0),KC=function(e){return vu.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd()},XC=function(e,t){var n=U(Sd(rr.fromDom(t.container()),e),lo);return ee(n).getOr(e)},YC=function(e,t){return KC(t)?Us(t):Us(t)||sc.prevPosition(XC(e,t).dom(),t).exists(Us)},GC=function(e,t){return KC(t)?Fs(t):Fs(t)||sc.nextPosition(XC(e,t).dom(),t).exists(Fs)},JC=function(e){return zs(e).bind(function(e){return Gi(e,cr)}).exists(function(e){return t=wr(e,"white-space"),M(["pre","pre-line","pre-wrap"],t);var t})},QC=function(e,t){return o=e,i=t,sc.prevPosition(o.dom(),i).isNone()||(n=e,r=t,sc.nextPosition(n.dom(),r).isNone())||$C(e,t)||WC(e,t)||ny(e,t)||ty(e,t);var n,r,o,i},ZC=function(e,t){return ss(e.charAt(t))},ex=function(e){var t=e.container();return Oo.isText(t)&&Wn(t.data,"\xa0")},tx=function(e,t,n){var r,o,i=vu(t,0);return ZC(n,0)&&(r=e,JC(o=i)||!($C(r,o)||ny(r,o)||YC(r,o)))?" "+n.slice(1):n},nx=function(e,t,n){var r,o,i=vu(t,n.length);return ZC(n,n.length-1)&&(r=e,JC(o=i)||!(WC(r,o)||ty(r,o)||GC(r,o)))?n.slice(0,-1)+" ":n},rx=function(i,e){return A.some(e).filter(ex).bind(function(e){var t,n=e.container(),r=n.nodeValue,o=tx(i,n,(t=nx(i,n,r),W(t.split(""),function(e,t,n){return ss(e)&&0<t&&t<n.length-1&&ls(n[t-1])&&ls(n[t+1])?" ":e}).join("")));return r!==o?(e.container().nodeValue=o,A.some(e)):A.none()})},ox=function(t){var e=rr.fromDom(t.getBody());t.selection.isCollapsed()&&rx(e,vu.fromRangeStart(t.selection.getRng())).each(function(e){t.selection.setRng(e.toRange())})},ix=function(r,o){return function(e){return t=r,!JC(n=e)&&(QC(t,n)||YC(t,n)||GC(t,n))?jC(o):HC(o);var t,n}},ax=function(e){var t,n,r=xu.fromRangeStart(e.selection.getRng()),o=rr.fromDom(e.getBody());if(e.selection.isCollapsed()){var i=d(lv.isInlineTarget,e),a=xu.fromRangeStart(e.selection.getRng());return Rv(i,e.getBody(),a).bind((n=o,function(e){return e.fold(function(e){return sc.prevPosition(n.dom(),xu.before(e))},function(e){return sc.firstPositionIn(e)},function(e){return sc.lastPositionIn(e)},function(e){return sc.nextPosition(n.dom(),xu.after(e))})})).bind(ix(o,r)).exists((t=e,function(e){return t.selection.setRng(e.toRange()),t.nodeChanged(),!0}))}return!1},ux=function(r){r.on("keydown",function(e){var t,n;!1===e.isDefaultPrevented()&&(t=r,n=e,Db([{keyCode:bm.SPACEBAR,action:Rb(ax,t)}],n).each(function(e){n.preventDefault()}))})},sx=function(e,t){var n;t.hasAttribute("data-mce-caret")&&(Ta(t),(n=e).selection.setRng(n.selection.getRng()),e.selection.scrollIntoView(t))},cx=function(e,t){var n,r=(n=e,Qi(rr.fromDom(n.getBody()),"*[data-mce-caret]").fold(q(null),function(e){return e.dom()}));if(r)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void sx(e,r)):void(xa(r)&&(sx(e,r),e.undoManager.add()))},lx=function(e){e.on("keyup compositionstart",d(cx,e))},fx=tr.detect().browser,dx=function(t){var e,n;e=t,n=Mi(function(){e.composing||ox(e)},0),fx.isIE()&&(e.on("keypress",function(e){n.throttle()}),e.on("remove",function(e){n.cancel()})),t.on("input",function(e){!1===e.isComposing&&ox(t)})},mx=function(e){var t=Vv.setupSelectedState(e);lx(e),Bb(e,t),Yy(e,t),zC(e),ux(e),dx(e)};function gx(u){var s,n,r,o=Yt.each,c=bm.BACKSPACE,l=bm.DELETE,f=u.dom,d=u.selection,e=u.settings,t=u.parser,i=de.gecko,a=de.ie,m=de.webkit,g="data:text/mce-internal,",p=a?"Text":"URL",h=function(e,t){try{u.getDoc().execCommand(e,!1,t)}catch(n){}},v=function(e){return e.isDefaultPrevented()},b=function(){u.shortcuts.add("meta+a",null,"SelectAll")},y=function(){u.on("keydown",function(e){if(!v(e)&&e.keyCode===c&&d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}})},C=function(){u.inline||(u.contentStyles.push("body {min-height: 150px}"),u.on("click",function(e){var t;if("HTML"===e.target.nodeName){if(11<de.ie)return void u.getBody().focus();t=u.selection.getRng(),u.getBody().focus(),u.selection.setRng(t),u.selection.normalize(),u.nodeChanged()}}))};return u.on("keydown",function(e){var t,n,r,o,i;if(!v(e)&&e.keyCode===bm.BACKSPACE&&(n=(t=d.getRng()).startContainer,r=t.startOffset,o=f.getRoot(),i=n,t.collapsed&&0===r)){for(;i&&i.parentNode&&i.parentNode.firstChild===i&&i.parentNode!==o;)i=i.parentNode;"BLOCKQUOTE"===i.tagName&&(u.formatter.toggle("blockquote",null,i),(t=f.createRng()).setStart(n,0),t.setEnd(n,0),d.setRng(t))}}),s=function(e){var t=f.create("body"),n=e.cloneContents();return t.appendChild(n),d.serializer.serialize(t,{format:"html"})},u.on("keydown",function(e){var t,n,r,o,i,a=e.keyCode;if(!v(e)&&(a===l||a===c)){if(t=u.selection.isCollapsed(),n=u.getBody(),t&&!f.isEmpty(n))return;if(!t&&(r=u.selection.getRng(),o=s(r),(i=f.createRng()).selectNode(u.getBody()),o!==s(i)))return;e.preventDefault(),u.setContent(""),n.firstChild&&f.isBlock(n.firstChild)?u.selection.setCursorLocation(n.firstChild,0):u.selection.setCursorLocation(n,0),u.nodeChanged()}}),de.windowsPhone||u.on("keyup focusin mouseup",function(e){bm.modifierPressed(e)||d.normalize()},!0),m&&(u.inline||f.bind(u.getDoc(),"mousedown mouseup",function(e){var t;if(e.target===u.getDoc().documentElement)if(t=d.getRng(),u.getBody().focus(),"mousedown"===e.type){if(Ca(t.startContainer))return;d.placeCaretAt(e.clientX,e.clientY)}else d.setRng(t)}),u.on("click",function(e){var t=e.target;/^(IMG|HR)$/.test(t.nodeName)&&"false"!==f.getContentEditableParent(t)&&(e.preventDefault(),u.selection.select(t),u.nodeChanged()),"A"===t.nodeName&&f.hasClass(t,"mce-item-anchor")&&(e.preventDefault(),d.select(t))}),e.forced_root_block&&u.on("init",function(){h("DefaultParagraphSeparator",fl(u))}),u.on("init",function(){u.dom.bind(u.getBody(),"submit",function(e){e.preventDefault()})}),y(),t.addNodeFilter("br",function(e){for(var t=e.length;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()}),de.iOS?(u.inline||u.on("keydown",function(){document.activeElement===document.body&&u.getWin().focus()}),C(),u.on("click",function(e){var t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)}),u.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")):b()),11<=de.ie&&(C(),y()),de.ie&&(b(),h("AutoUrlDetect",!1),u.on("dragstart",function(e){var t,n,r;(t=e).dataTransfer&&(u.selection.isCollapsed()&&"IMG"===t.target.tagName&&d.select(t.target),0<(n=u.selection.getContent()).length&&(r=g+escape(u.id)+","+escape(n),t.dataTransfer.setData(p,r)))}),u.on("drop",function(e){if(!v(e)){var t=(i=e).dataTransfer&&(a=i.dataTransfer.getData(p))&&0<=a.indexOf(g)?(a=a.substr(g.length).split(","),{id:unescape(a[0]),html:unescape(a[1])}):null;if(t&&t.id!==u.id){e.preventDefault();var n=ah(e.x,e.y,u.getDoc());d.setRng(n),r=t.html,o=!0,u.queryCommandSupported("mceInsertClipboardContent")?u.execCommand("mceInsertClipboardContent",!1,{content:r,internal:o}):u.execCommand("mceInsertContent",!1,r)}}var r,o,i,a})),i&&(u.on("keydown",function(e){if(!v(e)&&e.keyCode===c){if(!u.getBody().getElementsByTagName("hr").length)return;if(d.isCollapsed()&&0===d.getRng().startOffset){var t=d.getNode(),n=t.previousSibling;if("HR"===t.nodeName)return f.remove(t),void e.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(f.remove(n),e.preventDefault())}}}),Range.prototype.getClientRects||u.on("mousedown",function(e){if(!v(e)&&"HTML"===e.target.nodeName){var t=u.getBody();t.blur(),ve.setEditorTimeout(u,function(){t.focus()})}}),n=function(){var e=f.getAttribs(d.getStart().cloneNode(!1));return function(){var t=d.getStart();t!==u.getBody()&&(f.setAttrib(t,"style",null),o(e,function(e){t.setAttributeNode(e.cloneNode(!0))}))}},r=function(){return!d.isCollapsed()&&f.getParent(d.getStart(),f.isBlock)!==f.getParent(d.getEnd(),f.isBlock)},u.on("keypress",function(e){var t;if(!v(e)&&(8===e.keyCode||46===e.keyCode)&&r())return t=n(),u.getDoc().execCommand("delete",!1,null),t(),e.preventDefault(),!1}),f.bind(u.getDoc(),"cut",function(e){var t;!v(e)&&r()&&(t=n(),ve.setEditorTimeout(u,function(){t()}))}),e.readonly||u.on("BeforeExecCommand MouseDown",function(){h("StyleWithCSS",!1),h("enableInlineTableEditing",!1),e.object_resizing||h("enableObjectResizing",!1)}),u.on("SetContent ExecCommand",function(e){"setcontent"!==e.type&&"mceInsertLink"!==e.command||o(f.select("a"),function(e){var t=e.parentNode,n=f.getRoot();if(t.lastChild===e){for(;t&&!f.isBlock(t);){if(t.parentNode.lastChild!==t||t===n)return;t=t.parentNode}f.add(t,"br",{"data-mce-bogus":1})}})}),u.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}"),de.mac&&u.on("keydown",function(e){!bm.metaKeyPressed(e)||e.shiftKey||37!==e.keyCode&&39!==e.keyCode||(e.preventDefault(),u.selection.getSel().modify("move",37===e.keyCode?"backward":"forward","lineboundary"))}),y()),{refreshContentEditable:function(){},isHidden:function(){var e;return!i||u.removed?0:!(e=u.selection.getSel())||!e.rangeCount||0===e.rangeCount}}}var px=function(e){return Oo.isElement(e)&&go(rr.fromDom(e))},hx=function(t){t.on("click",function(e){3<=e.detail&&function(e){var t=e.selection.getRng(),n=vu.fromRangeStart(t),r=vu.fromRangeEnd(t);if(vu.isElementPosition(n)){var o=n.container();px(o)&&sc.firstPositionIn(o).each(function(e){return t.setStart(e.container(),e.offset())})}vu.isElementPosition(r)&&(o=n.container(),px(o)&&sc.lastPositionIn(o).each(function(e){return t.setEnd(e.container(),e.offset())})),e.selection.setRng(dp(t))}(t)})},vx=function(e){var t,n;(t=e).on("click",function(e){t.dom.getParent(e.target,"details")&&e.preventDefault()}),(n=e).parser.addNodeFilter("details",function(e){F(e,function(e){e.attr("data-mce-open",e.attr("open")),e.attr("open","open")})}),n.serializer.addNodeFilter("details",function(e){F(e,function(e){var t=e.attr("data-mce-open");e.attr("open",R(t)?t:null),e.attr("data-mce-open",null)})})},bx=hi.DOM,yx=function(e){var t;e.bindPendingEventDelegates(),e.initialized=!0,e.fire("init"),e.focus(!0),e.nodeChanged({initial:!0}),e.execCallback("init_instance_callback",e),(t=e).settings.auto_focus&&ve.setEditorTimeout(t,function(){var e;(e=!0===t.settings.auto_focus?t:t.editorManager.get(t.settings.auto_focus)).destroyed||e.focus()},100)},Cx=function(t,e){var n,r,u,o,i,a,s,c,l,f=t.settings,d=t.getElement(),m=t.getDoc();f.inline||(t.getElement().style.visibility=t.orgVisibility),e||t.inline||(m.open(),m.write(t.iframeHTML),m.close()),t.inline&&(t.on("remove",function(){var e=this.getBody();bx.removeClass(e,"mce-content-body"),bx.removeClass(e,"mce-edit-focus"),bx.setAttrib(e,"contentEditable",null)}),bx.addClass(d,"mce-content-body"),t.contentDocument=m=f.content_document||document,t.contentWindow=f.content_window||window,t.bodyElement=d,t.contentAreaContainer=d,f.content_document=f.content_window=null,f.root_name=d.nodeName.toLowerCase()),(n=t.getBody()).disabled=!0,t.readonly=f.readonly,t.readonly||(t.inline&&"static"===bx.getStyle(n,"position",!0)&&(n.style.position="relative"),n.contentEditable=t.getParam("content_editable_state",!0)),n.disabled=!1,t.editorUpload=xd(t),t.schema=ri(f),t.dom=hi(m,{keep_values:!0,url_converter:t.convertURL,url_converter_scope:t,hex_colors:f.force_hex_style_colors,class_filter:f.class_filter,update_styles:!0,root_element:t.inline?t.getBody():null,collect:function(){return t.inline},schema:t.schema,contentCssCors:Tl(t),onSetAttrib:function(e){t.fire("SetAttrib",e)}}),t.parser=((o=Hp((u=t).settings,u.schema)).addAttributeFilter("src,href,style,tabindex",function(e,t){for(var n,r,o,i=e.length,a=u.dom;i--;)if(r=(n=e[i]).attr(t),o="data-mce-"+t,!n.attributes.map[o]){if(0===r.indexOf("data:")||0===r.indexOf("blob:"))continue;"style"===t?((r=a.serializeStyle(a.parseStyle(r),n.name)).length||(r=null),n.attr(o,r),n.attr(t,r)):"tabindex"===t?(n.attr(o,r),n.attr(t,null)):n.attr(o,u.convertURL(r,t,n.name))}}),o.addNodeFilter("script",function(e){for(var t,n,r=e.length;r--;)0!==(n=(t=e[r]).attr("type")||"no/type").indexOf("mce-")&&t.attr("type","mce-"+n)}),o.addNodeFilter("#cdata",function(e){for(var t,n=e.length;n--;)(t=e[n]).type=8,t.name="#comment",t.value="[CDATA["+t.value+"]]"}),o.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",function(e){for(var t,n=e.length,r=u.schema.getNonEmptyElements();n--;)(t=e[n]).isEmpty(r)&&0===t.getAll("br").length&&(t.append(new Gc("br",1)).shortEnded=!0)}),o),t.serializer=Xp(f,t),t.selection=Jh(t.dom,t.getWin(),t.serializer,t),t.annotator=Hc(t),t.formatter=Tp(t),t.undoManager=Vm(t),t._nodeChangeDispatcher=new Md(t),t._selectionOverrides=Em(t),vx(t),hx(t),mx(t),Dd(t),t.fire("PreInit"),f.browser_spellcheck||f.gecko_spellcheck||(m.body.spellcheck=!1,bx.setAttrib(n,"spellcheck","false")),t.quirks=gx(t),t.fire("PostRender"),f.directionality&&(n.dir=f.directionality),f.protect&&t.on("BeforeSetContent",function(t){Yt.each(f.protect,function(e){t.content=t.content.replace(e,function(e){return"\x3c!--mce:protected "+escape(e)+"--\x3e"})})}),t.on("SetContent",function(){t.addVisual(t.getBody())}),t.load({initial:!0,format:"html"}),t.startContent=t.getContent({format:"raw"}),t.on("compositionstart compositionend",function(e){t.composing="compositionstart"===e.type}),0<t.contentStyles.length&&(r="",Yt.each(t.contentStyles,function(e){r+=e+"\r\n"}),t.dom.addStyle(r)),(i=t,i.inline?bx.styleSheetLoader:i.dom.styleSheetLoader).loadAll(t.contentCSS,function(e){yx(t)},function(e){yx(t)}),f.content_style&&(a=t,s=f.content_style,c=rr.fromDom(a.getDoc().head),l=rr.fromTag("style"),br(l,"type","text/css"),Oi(l,rr.fromText(s)),Oi(c,l))},xx=hi.DOM,wx=function(e,t){var n,r,o,i,a=e.editorManager.translate("Rich Text Area. Press ALT-0 for help."),u=(n=e.id,r=a,t.height,o=ol(e),i=rr.fromTag("iframe"),yr(i,o),yr(i,{id:n+"_ifr",frameBorder:"0",allowTransparency:"true",title:r}),ji(i,"tox-edit-area__iframe"),i).dom();u.onload=function(){u.onload=null,e.fire("load")};var s,c,l,f,d=function(e,t){if(document.domain!==window.location.hostname&&de.ie&&de.ie<12){var n=Cd.uuid("mce");e[n]=function(){Cx(e)};var r='javascript:(function(){document.open();document.domain="'+document.domain+'";var ed = window.parent.tinymce.get("'+e.id+'");document.write(ed.iframeHTML);document.close();ed.'+n+"(true);})()";return xx.setAttrib(t,"src",r),!0}return!1}(e,u);return e.contentAreaContainer=t.iframeContainer,e.iframeElement=u,e.iframeHTML=(f=il(s=e)+"<html><head>",al(s)!==s.documentBaseUrl&&(f+='<base href="'+s.documentBaseURI.getURI()+'" />'),f+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />',c=ul(s),l=sl(s),cl(s)&&(f+='<meta http-equiv="Content-Security-Policy" content="'+cl(s)+'" />'),f+='</head><body id="'+c+'" class="mce-content-body '+l+'" data-id="'+s.id+'"><br></body></html>'),xx.add(t.iframeContainer,u),d},Nx=function(e,t){var n=wx(e,t);t.editorContainer&&(xx.get(t.editorContainer).style.display=e.orgDisplay,e.hidden=xx.isHidden(t.editorContainer)),e.getElement().style.display="none",xx.setAttrib(e.id,"aria-hidden","true"),n||Cx(e)},Ex=function(e){var t,n,r,o,i;e.contentCSS=e.contentCSS.concat((n=Ol(t=e),r=t.editorManager.baseURL+"/skins/content",o="content"+t.editorManager.suffix+".css",i=!0===t.inline,W(n,function(e){return/^[a-z0-9\-]+$/i.test(e)&&!i?r+"/"+e+"/"+o:t.documentBaseURI.toAbsolute(e)})))},Sx=hi.DOM,kx=function(t,n,e){var r=id.get(e),o=id.urls[e]||t.documentBaseUrl.replace(/\/$/,"");if(e=Yt.trim(e),r&&-1===Yt.inArray(n,e)){if(Yt.each(id.dependencies(e),function(e){kx(t,n,e)}),t.plugins[e])return;try{var i=new r(t,o,t.$);(t.plugins[e]=i).init&&(i.init(t,o),n.push(e))}catch($N){rd.pluginInitError(t,e,$N)}}},Tx=function(e){return e.replace(/^\-/,"")},Ax=function(e){return{editorContainer:e,iframeContainer:e}},Rx=function(e){var t,n,r=e.getElement();return e.inline?Ax(null):(t=r,n=Sx.create("div"),Sx.insertAfter(n,t),Ax(n))},Dx=function(e){var t,n,r,o;e.fire("ScriptsLoaded"),function(e){var t=e.settings.theme;if(R(t)){e.settings.theme=Tx(t);var n=ad.get(t);e.theme=new n(e,ad.urls[t]),e.theme.init&&e.theme.init(e,ad.urls[t]||e.documentBaseUrl.replace(/\/$/,""),e.$)}else e.theme={}}(e),t=e,n=[],Yt.each(t.settings.plugins.split(/[ ,]/),function(e){kx(t,n,Tx(e))}),r=e,o=Yt.trim(r.settings.icons),mr(od.get(o).icons,function(e,t){r.ui.registry.addIcon(t,e)});var i,a,u,s,c,l,f=(c=(i=e).settings,l=i.getElement(),i.orgDisplay=l.style.display,R(c.theme)?i.theme.renderUI():P(c.theme)?(u=(a=i).getElement(),(s=a.settings.theme(a,u)).editorContainer.nodeType&&(s.editorContainer.id=s.editorContainer.id||a.id+"_parent"),s.iframeContainer&&s.iframeContainer.nodeType&&(s.iframeContainer.id=s.iframeContainer.id||a.id+"_iframecontainer"),s.height=s.iframeHeight?s.iframeHeight:u.offsetHeight,s):Rx(i));return e.editorContainer=f.editorContainer?f.editorContainer:null,Ex(e),e.inline?Cx(e):Nx(e,f)},Bx=hi.DOM,Ox=function(e){return"-"===e.charAt(0)},_x=function(a,u){var s=xi.ScriptLoader;!function(e,t,n,r){var o=t.settings,i=o.theme;if(R(i)){if(!Ox(i)&&!ad.urls.hasOwnProperty(i)){var a=o.theme_url;a?ad.load(i,t.documentBaseURI.toAbsolute(a)):ad.load(i,"themes/"+i+"/theme"+n+".js")}e.loadQueue(function(){ad.waitFor(i,r)})}else r()}(s,a,u,function(){var e,t,n,r,o,i;e=s,n=Al(t=a),r=Rl(t),!1===ki.hasCode(n)&&"en"!==n&&(""!==r?e.add(r):e.add(t.editorManager.baseURL+"/langs/"+n+".js")),function(e,t){var n=e.icons;if(R(n)){var r=t.editorManager.baseURL+"/icons/"+Yt.trim(n)+"/icons.js";xi.ScriptLoader.add(r)}}(a.settings,a),o=a.settings,i=u,Yt.isArray(o.plugins)&&(o.plugins=o.plugins.join(" ")),Yt.each(o.external_plugins,function(e,t){id.load(t,e),o.plugins+=" "+t}),Yt.each(o.plugins.split(/[ ,]/),function(e){if((e=Yt.trim(e))&&!id.urls[e])if(Ox(e)){e=e.substr(1,e.length);var t=id.dependencies(e);Yt.each(t,function(e){var t={prefix:"plugins/",resource:e,suffix:"/plugin"+i+".js"};e=id.createUrl(t,e),id.load(e.resource,e)})}else id.load(e,{prefix:"plugins/",resource:e,suffix:"/plugin"+i+".js"})}),s.loadQueue(function(){a.removed||Dx(a)},a,function(e){rd.pluginLoadError(a,e[0]),a.removed||Dx(a)})})},Px=function(t){var e=t.settings,n=t.id;ki.setCode(Al(t));var r=function(){Bx.unbind(window,"ready",r),t.render()};if(ke.Event.domLoaded){if(t.getElement()&&de.contentEditable){e.inline?t.inline=!0:(t.orgVisibility=t.getElement().style.visibility,t.getElement().style.visibility="hidden");var o=t.getElement().form||Bx.getParent(n,"form");o&&(t.formElement=o,e.hidden_input&&!/TEXTAREA|INPUT/i.test(t.getElement().nodeName)&&(Bx.insertAfter(Bx.create("input",{type:"hidden",name:n}),n),t.hasHiddenInput=!0),t.formEventDelegate=function(e){t.fire(e.type,e)},Bx.bind(o,"submit reset",t.formEventDelegate),t.on("reset",function(){t.setContent(t.startContent,{format:"raw"})}),!e.submit_patch||o.submit.nodeType||o.submit.length||o._mceOldSubmit||(o._mceOldSubmit=o.submit,o.submit=function(){return t.editorManager.triggerSave(),t.setDirty(!1),o._mceOldSubmit(o)})),t.windowManager=Gf(t),t.notificationManager=Yf(t),"xml"===e.encoding&&t.on("GetContent",function(e){e.save&&(e.content=Bx.encode(e.content))}),e.add_form_submit_trigger&&t.on("submit",function(){t.initialized&&t.save()}),e.add_unload_trigger&&(t._beforeUnload=function(){!t.initialized||t.destroyed||t.isHidden()||t.save({format:"raw",no_events:!0,set_dirty:!1})},t.editorManager.on("BeforeUnload",t._beforeUnload)),t.editorManager.add(t),_x(t,t.suffix)}}else Bx.bind(window,"ready",r)},Ix=function(e,t,n){try{e.getDoc().execCommand(t,!1,n)}catch(r){}},Lx=function(e,t,n){var r,o;qi(e,t)&&!1===n?(o=t,Ui(r=e)?r.dom().classList.remove(o):Vi(r,o),Hi(r)):n&&ji(e,t)},Mx=function(e,t){Lx(rr.fromDom(e.getBody()),"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e.readonly=!0,e.getBody().contentEditable="false"):(e.readonly=!1,e.getBody().contentEditable="true",Ix(e,"StyleWithCSS",!1),Ix(e,"enableInlineTableEditing",!1),Ix(e,"enableObjectResizing",!1),e.focus(),e.nodeChanged())},Fx=function(e){return e.readonly?"readonly":"design"},Ux=function(e){return Yt.grep(e.childNodes,function(e){return"LI"===e.nodeName})},zx=function(e){return e&&e.firstChild&&e.firstChild===e.lastChild&&("\xa0"===(t=e.firstChild).data||Oo.isBr(t));var t},Vx=function(e){return 0<e.length&&(!(t=e[e.length-1]).firstChild||zx(t))?e.slice(0,-1):e;var t},jx=function(e,t){var n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Hx=function(e,t){var n=xu.after(e),r=Zs(t).prev(n);return r?r.toRange():null},qx=function(t,e,n){var r,o,i,a,u=t.parentNode;return Yt.each(e,function(e){u.insertBefore(e,t)}),r=t,o=n,i=xu.before(r),(a=Zs(o).next(i))?a.toRange():null},$x=function(e,t){var n,r,o,i,a,u,s=t.firstChild,c=t.lastChild;return s&&"meta"===s.name&&(s=s.next),c&&"mce_marker"===c.attr("id")&&(c=c.prev),r=c,u=(n=e).getNonEmptyElements(),r&&(r.isEmpty(u)||(o=r,n.getBlockElements()[o.name]&&(a=o).firstChild&&a.firstChild===a.lastChild&&("br"===(i=o.firstChild).name||"\xa0"===i.value)))&&(c=c.prev),!(!s||s!==c||"ul"!==s.name&&"ol"!==s.name)},Wx=function(e,o,i,t){var n,r,a,u,s,c,l,f,d,m,g,p,h,v,b,y,C,x,w,N=(n=o,r=t,c=e.serialize(r),l=n.createFragment(c),u=(a=l).firstChild,s=a.lastChild,u&&"META"===u.nodeName&&u.parentNode.removeChild(u),s&&"mce_marker"===s.id&&s.parentNode.removeChild(s),a),E=jx(o,i.startContainer),S=Vx(Ux(N.firstChild)),k=o.getRoot(),T=function(e){var t=xu.fromRangeStart(i),n=Zs(o.getRoot()),r=1===e?n.prev(t):n.next(t);return!r||jx(o,r.getNode())!==E};return T(1)?qx(E,S,k):T(2)?(f=E,d=S,m=k,o.insertAfter(d.reverse(),f),Hx(d[0],m)):(p=S,h=k,v=g=E,y=(b=i).cloneRange(),C=b.cloneRange(),y.setStartBefore(v),C.setEndAfter(v),x=[y.cloneContents(),C.cloneContents()],(w=g.parentNode).insertBefore(x[0],g),Yt.each(p,function(e){w.insertBefore(e,g)}),w.insertBefore(x[1],g),w.removeChild(g),Hx(p[p.length-1],h))},Kx=function(e,t){return!!jx(e,t)},Xx=Oo.matchNodeNames("td th"),Yx=function(e,t){var n,r,o=e.selection.getRng(),i=o.startContainer,a=o.startOffset;o.collapsed&&(n=i,r=a,Oo.isText(n)&&"\xa0"===n.nodeValue[r-1])&&Oo.isText(i)&&(i.insertData(a-1," "),i.deleteData(a,1),o.setStart(i,a),o.setEnd(i,a),e.selection.setRng(o)),e.selection.setContent(t)},Gx=function(e,t,n){var r,o,i,a,u,s,c,l,f,d,m,g=e.selection,p=e.dom;if(/^ | $/.test(t)&&(t=function(e,t){var n,r;n=e.startContainer,r=e.startOffset;var o=function(e){return n[e]&&3===n[e].nodeType};return 3===n.nodeType&&(0<r?t=t.replace(/^&nbsp;/," "):o("previousSibling")||(t=t.replace(/^ /,"&nbsp;")),r<n.length?t=t.replace(/&nbsp;(<br>|)$/," "):o("nextSibling")||(t=t.replace(/(&nbsp;| )(<br>|)$/,"&nbsp;"))),t}(g.getRng(),t)),r=e.parser,m=n.merge,o=Ll({validate:e.settings.validate},e.schema),d='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;&#x200B;</span>',s={content:t,format:"html",selection:!0,paste:n.paste},(s=e.fire("BeforeSetContent",s)).isDefaultPrevented())e.fire("SetContent",{content:s.content,format:"html",selection:!0,paste:n.paste});else{-1===(t=s.content).indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,d);var h,v,b,y,C,x,w=(l=g.getRng()).startContainer||(l.parentElement?l.parentElement():null),N=e.getBody();w===N&&g.isCollapsed()&&p.isBlock(N.firstChild)&&(h=e,(v=N.firstChild)&&!h.schema.getShortEndedElements()[v.nodeName])&&p.isEmpty(N.firstChild)&&((l=p.createRng()).setStart(N.firstChild,0),l.setEnd(N.firstChild,0),g.setRng(l)),g.isCollapsed()||(e.selection.setRng(dp(e.selection.getRng())),e.getDoc().execCommand("Delete",!1,null),b=e.selection.getRng(),y=t,C=b.startContainer,x=b.startOffset,3===C.nodeType&&b.collapsed&&("\xa0"===C.data[x]?(C.deleteData(x,1),/[\u00a0| ]$/.test(y)||(y+=" ")):"\xa0"===C.data[x-1]&&(C.deleteData(x-1,1),/[\u00a0| ]$/.test(y)||(y=" "+y))),t=y);var E,S,k,T={context:(i=g.getNode()).nodeName.toLowerCase(),data:n.data,insert:!0};if(u=r.parse(t,T),!0===n.paste&&$x(e.schema,u)&&Kx(p,i))return l=Wx(o,p,e.selection.getRng(),u),e.selection.setRng(l),void e.fire("SetContent",s);if(function(e){for(var t=e;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")}(u),"mce_marker"===(f=u.lastChild).attr("id"))for(f=(c=f).prev;f;f=f.walk(!0))if(3===f.type||!p.isBlock(f.name)){e.schema.isValidChild(f.parent.name,"span")&&f.parent.insert(c,f,"br"===f.name);break}if(e._selectionOverrides.showBlockCaretContainer(i),T.invalid){for(Yx(e,d),i=g.getNode(),a=e.getBody(),9===i.nodeType?i=f=a:f=i;f!==a;)f=(i=f).parentNode;t=i===a?a.innerHTML:p.getOuterHTML(i),t=o.serialize(r.parse(t.replace(/<span (id="mce_marker"|id=mce_marker).+?<\/span>/i,function(){return o.serialize(u)}))),i===a?p.setHTML(a,t):p.setOuterHTML(i,t)}else!function(e,t,n){if("all"===n.getAttribute("data-mce-bogus"))n.parentNode.insertBefore(e.dom.createFragment(t),n);else{var r=n.firstChild,o=n.lastChild;!r||r===o&&"BR"===r.nodeName?e.dom.setHTML(n,t):Yx(e,t)}}(e,t=o.serialize(u),i);!function(e,t){var n=e.schema.getTextInlineElements(),r=e.dom;if(t){var o=e.getBody(),i=new Fg(r);Yt.each(r.select("*[data-mce-fragment]"),function(e){for(var t=e.parentNode;t&&t!==o;t=t.parentNode)n[e.nodeName.toLowerCase()]&&i.compare(t,e)&&r.remove(e,!0)})}}(e,m),function(n,e){var t,r,o,i,a,u=n.dom,s=n.selection;if(e){if(n.selection.scrollIntoView(e),t=function(e){for(var t=n.getBody();e&&e!==t;e=e.parentNode)if("false"===n.dom.getContentEditable(e))return e;return null}(e))return u.remove(e),s.select(t);var c=u.createRng();(i=e.previousSibling)&&3===i.nodeType?(c.setStart(i,i.nodeValue.length),de.ie||(a=e.nextSibling)&&3===a.nodeType&&(i.appendData(a.data),a.parentNode.removeChild(a))):(c.setStartBefore(e),c.setEndBefore(e)),r=u.getParent(e,u.isBlock),u.remove(e),r&&u.isEmpty(r)&&(n.$(r).empty(),c.setStart(r,0),c.setEnd(r,0),Xx(r)||r.getAttribute("data-mce-fragment")||!(o=function(e){var t=xu.fromRangeStart(e);if(t=Zs(n.getBody()).next(t))return t.toRange()}(c))?u.add(r,u.create("br",{"data-mce-bogus":"1"})):(c=o,u.remove(r))),s.setRng(c)}}(e,p.get("mce_marker")),E=e.getBody(),Yt.each(E.getElementsByTagName("*"),function(e){e.removeAttribute("data-mce-fragment")}),S=e.dom,k=e.selection.getStart(),A.from(S.getParent(k,"td,th")).map(rr.fromDom).each(Wm),e.fire("SetContent",s),e.addVisual()}},Jx=function(e,t){var n,r,o="string"!=typeof(n=t)?(r=Yt.extend({paste:n.paste,data:{paste:n.paste}},n),{content:n.content,details:r}):{content:n,details:{}};Gx(e,o.content,o.details)},Qx=function(e,t){e.getDoc().execCommand(t,!1,null)},Zx=function(e){fy(e,!1)||hy(e,!1)||Cy(e,!1)||Xb(e,!1)||Xy(e)||Qb(e,!1)||Ey(e,!1)||(Qx(e,"Delete"),_b(e))},ew=function(e){fy(e,!0)||hy(e,!0)||Cy(e,!0)||Xb(e,!0)||Xy(e)||Qb(e,!0)||Ey(e,!0)||Qx(e,"ForwardDelete")},tw=function(s){return function(u,e){return A.from(e).map(rr.fromDom).filter(cr).bind(function(e){return(r=s,o=u,i=e.dom(),a=function(e){return Er(e,r)},Gi(rr.fromDom(i),function(e){return a(e).isSome()},function(e){return Pr(rr.fromDom(o),e)}).bind(a)).or((t=s,n=e.dom(),A.from(hi.DOM.getStyle(n,t,!0))));var t,n,r,o,i,a}).getOr("")}},nw={getFontSize:tw("font-size"),getFontFamily:H(function(e){return e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")},tw("font-family")),toPt:function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,r=t||0,o=Math.pow(10,r),Math.round(n*o)/o+"pt"):e;var n,r,o}},rw=function(e){return sc.firstPositionIn(e.getBody()).map(function(e){var t=e.container();return Oo.isText(t)?t.parentNode:t})},ow=function(o){return A.from(o.selection.getRng()).bind(function(e){var t,n,r=o.getBody();return n=r,(t=e).startContainer===n&&0===t.startOffset?A.none():A.from(o.selection.getStart(!0))})},iw=function(e,t){if(/^[0-9\.]+$/.test(t)){var n=parseInt(t,10);if(1<=n&&n<=7){var r=vl(e),o=bl(e);return o?o[n-1]||t:r[n-1]||t}return t}return t},aw=function(e){var t=parseInt(e,10);return isNaN(t)?0:t},uw=function(e,t){return(e||"table"===ur(t)?"margin":"padding")+("rtl"===wr(t,"direction")?"-right":"-left")},sw=function(e){var r,t=lw(e);return!0!==e.readonly&&(1<t.length||(r=e,J(t,function(e){var t=uw(Dl(r),e),n=Er(e,t).map(aw).getOr(0);return"false"!==r.dom.getContentEditable(e.dom())&&0<n})))},cw=function(e){return po(e)||ho(e)},lw=function(e){return U(W(e.selection.getSelectedBlocks(),rr.fromDom),function(e){return!cw(e)&&!Mr(e).map(cw).getOr(!1)&&Gi(e,function(e){return Oo.isContentEditableTrue(e.dom())||Oo.isContentEditableFalse(e.dom())}).exists(function(e){return Oo.isContentEditableTrue(e.dom())})})},fw=function(e,t){var n=e.dom,r=e.selection,o=e.formatter,i=Bl(e),a=/[a-z%]+$/i.exec(i)[0],u=parseInt(i,10),s=Dl(e),c=fl(e);e.queryCommandState("InsertUnorderedList")||e.queryCommandState("InsertOrderedList")||""!==c||n.getParent(r.getNode(),n.isBlock)||o.apply("div"),F(lw(e),function(e){!function(e,t,n,r,o,i){var a=uw(n,rr.fromDom(i));if("outdent"===t){var u=Math.max(0,aw(i.style[a])-r);e.setStyle(i,a,u?u+o:"")}else u=aw(i.style[a])+r+o,e.setStyle(i,a,u)}(n,t,s,u,a,e.dom())})},dw=Yt.each,mw=Yt.extend,gw=Yt.map,pw=Yt.inArray;function hw(s){var o,i,a,t,c={state:{},exec:{},value:{}};s.on("PreInit",function(){o=s.dom,i=s.selection,a=s.formatter});var e=function(e,n){n=n||"exec",dw(e,function(t,e){dw(e.toLowerCase().split(","),function(e){c[n][e]=t})})},n=function(e,t,n){e=e.toLowerCase(),c.value[e]=function(){return t.call(n||s)}};mw(this,{execCommand:function(t,n,r,e){var o,i,a=!1;if(!s.removed){if(/^(mceAddUndoLevel|mceEndUndoLevel|mceBeginUndoLevel|mceRepaint)$/.test(t)||e&&e.skip_focus?rf(s):s.focus(),(e=s.fire("BeforeExecCommand",{command:t,ui:n,value:r})).isDefaultPrevented())return!1;if(i=t.toLowerCase(),o=c.exec[i])return o(i,n,r),s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;if(dw(s.plugins,function(e){if(e.execCommand&&e.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!(a=!0)}),a)return a;if(s.theme&&s.theme.execCommand&&s.theme.execCommand(t,n,r))return s.fire("ExecCommand",{command:t,ui:n,value:r}),!0;try{a=s.getDoc().execCommand(t,n,r)}catch(u){}return!!a&&(s.fire("ExecCommand",{command:t,ui:n,value:r}),!0)}},queryCommandState:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.state[e])return t(e);try{return s.getDoc().queryCommandState(e)}catch(n){}return!1}},queryCommandValue:function(e){var t;if(!s.quirks.isHidden()&&!s.removed){if(e=e.toLowerCase(),t=c.value[e])return t(e);try{return s.getDoc().queryCommandValue(e)}catch(n){}}},queryCommandSupported:function(e){if(e=e.toLowerCase(),c.exec[e])return!0;try{return s.getDoc().queryCommandSupported(e)}catch(t){}return!1},addCommands:e,addCommand:function(e,o,i){e=e.toLowerCase(),c.exec[e]=function(e,t,n,r){return o.call(i||s,t,n,r)}},addQueryStateHandler:function(e,t,n){e=e.toLowerCase(),c.state[e]=function(){return t.call(n||s)}},addQueryValueHandler:n,hasCustomCommand:function(e){return e=e.toLowerCase(),!!c.exec[e]}});var u=function(e,t,n){return t===undefined&&(t=!1),n===undefined&&(n=null),s.getDoc().execCommand(e,t,n)},r=function(e){return a.match(e)},l=function(e,t){a.toggle(e,t?{value:t}:undefined),s.nodeChanged()},f=function(e){t=i.getBookmark(e)},d=function(){i.moveToBookmark(t)};e({"mceResetDesignMode,mceBeginUndoLevel":function(){},"mceEndUndoLevel,mceAddUndoLevel":function(){s.undoManager.add()},"Cut,Copy,Paste":function(e){var t,n=s.getDoc();try{u(e)}catch(o){t=!0}if("paste"!==e||n.queryCommandEnabled(e)||(t=!0),t||!n.queryCommandSupported(e)){var r=s.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");de.mac&&(r=r.replace(/Ctrl\+/g,"\u2318+")),s.notificationManager.open({text:r,type:"error"})}},unlink:function(){if(i.isCollapsed()){var e=s.dom.getParent(s.selection.getStart(),"a");e&&s.dom.remove(e,!0)}else a.remove("link")},"JustifyLeft,JustifyCenter,JustifyRight,JustifyFull,JustifyNone":function(e){var t=e.substring(7);"full"===t&&(t="justify"),dw("left,center,right,justify".split(","),function(e){t!==e&&a.remove("align"+e)}),"none"!==t&&l("align"+t)},"InsertUnorderedList,InsertOrderedList":function(e){var t,n;u(e),(t=o.getParent(i.getNode(),"ol,ul"))&&(n=t.parentNode,/^(H[1-6]|P|ADDRESS|PRE)$/.test(n.nodeName)&&(f(),o.split(n,t),d()))},"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){l(e)},"ForeColor,HiliteColor":function(e,t,n){l(e,n)},FontName:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontname",{value:iw(r,o)}),r.nodeChanged()},FontSize:function(e,t,n){var r,o;o=n,(r=s).formatter.toggle("fontsize",{value:iw(r,o)}),r.nodeChanged()},RemoveFormat:function(e){a.remove(e)},mceBlockQuote:function(){l("blockquote")},FormatBlock:function(e,t,n){return l(n||"p")},mceCleanup:function(){var e=i.getBookmark();s.setContent(s.getContent()),i.moveToBookmark(e)},mceRemoveNode:function(e,t,n){var r=n||i.getNode();r!==s.getBody()&&(f(),s.dom.remove(r,!0),d())},mceSelectNodeDepth:function(e,t,n){var r=0;o.getParent(i.getNode(),function(e){if(1===e.nodeType&&r++===n)return i.select(e),!1},s.getBody())},mceSelectNode:function(e,t,n){i.select(n)},mceInsertContent:function(e,t,n){Jx(s,n)},mceInsertRawHTML:function(e,t,n){i.setContent("tiny_mce_marker");var r=s.getContent();s.setContent(r.replace(/tiny_mce_marker/g,function(){return n}))},mceInsertNewLine:function(e,t,n){UC(s,n)},mceToggleFormat:function(e,t,n){l(n)},mceSetContent:function(e,t,n){s.setContent(n)},"Indent,Outdent":function(e){fw(s,e)},mceRepaint:function(){},InsertHorizontalRule:function(){s.execCommand("mceInsertContent",!1,"<hr />")},mceToggleVisualAid:function(){s.hasVisual=!s.hasVisual,s.addVisual()},mceReplaceContent:function(e,t,n){s.execCommand("mceInsertContent",!1,n.replace(/\{\$selection\}/g,i.getContent({format:"text"})))},mceInsertLink:function(e,t,n){var r;"string"==typeof n&&(n={href:n}),r=o.getParent(i.getNode(),"a"),n.href=n.href.replace(" ","%20"),r&&n.href||a.remove("link"),n.href&&a.apply("link",n,r)},selectAll:function(){var e=o.getParent(i.getStart(),Oo.isContentEditableTrue);if(e){var t=o.createRng();t.selectNodeContents(e),i.setRng(t)}},"delete":function(){Zx(s)},forwardDelete:function(){ew(s)},mceNewDocument:function(){s.setContent("")},InsertLineBreak:function(e,t,n){return NC(s,n),!0}});var m=function(n){return function(){var e=i.isCollapsed()?[o.getParent(i.getNode(),o.isBlock)]:i.getSelectedBlocks(),t=gw(e,function(e){return!!a.matchNode(e,n)});return-1!==pw(t,!0)}};e({JustifyLeft:m("alignleft"),JustifyCenter:m("aligncenter"),JustifyRight:m("alignright"),JustifyFull:m("alignjustify"),"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":function(e){return r(e)},mceBlockQuote:function(){return r("blockquote")},Outdent:function(){return sw(s)},"InsertUnorderedList,InsertOrderedList":function(e){var t=o.getParent(i.getNode(),"ul,ol");return t&&("insertunorderedlist"===e&&"UL"===t.tagName||"insertorderedlist"===e&&"OL"===t.tagName)}},"state"),e({Undo:function(){s.undoManager.undo()},Redo:function(){s.undoManager.redo()}}),n("FontName",function(){return ow(t=s).fold(function(){return rw(t).map(function(e){return nw.getFontFamily(t.getBody(),e)}).getOr("")},function(e){return nw.getFontFamily(t.getBody(),e)});var t},this),n("FontSize",function(){return ow(t=s).fold(function(){return rw(t).map(function(e){return nw.getFontSize(t.getBody(),e)}).getOr("")},function(e){return nw.getFontSize(t.getBody(),e)});var t},this)}var vw=Yt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend"," "),bw=function(a){var u,s,c=this,l={},f=function(){return!1},d=function(){return!0};u=(a=a||{}).scope||c,s=a.toggleEvent||f;var r=function(e,t,n,r){var o,i,a;if(!1===t&&(t=f),t)for(t={func:t},r&&Yt.extend(t,r),a=(i=e.toLowerCase().split(" ")).length;a--;)e=i[a],(o=l[e])||(o=l[e]=[],s(e,!0)),n?o.unshift(t):o.push(t);return c},m=function(e,t){var n,r,o,i,a;if(e)for(n=(i=e.toLowerCase().split(" ")).length;n--;){if(e=i[n],r=l[e],!e){for(o in l)s(o,!1),delete l[o];return c}if(r){if(t)for(a=r.length;a--;)r[a].func===t&&(r=r.slice(0,a).concat(r.slice(a+1)),l[e]=r);else r.length=0;r.length||(s(e,!1),delete l[e])}}else{for(e in l)s(e,!1);l={}}return c};c.fire=function(e,t){var n,r,o,i;if(e=e.toLowerCase(),(t=t||{}).type=e,t.target||(t.target=u),t.preventDefault||(t.preventDefault=function(){t.isDefaultPrevented=d},t.stopPropagation=function(){t.isPropagationStopped=d},t.stopImmediatePropagation=function(){t.isImmediatePropagationStopped=d},t.isDefaultPrevented=f,t.isPropagationStopped=f,t.isImmediatePropagationStopped=f),a.beforeFire&&a.beforeFire(t),n=l[e])for(r=0,o=n.length;r<o;r++){if((i=n[r]).once&&m(e,i.func),t.isImmediatePropagationStopped())return t.stopPropagation(),t;if(!1===i.func.call(u,t))return t.preventDefault(),t}return t},c.on=r,c.off=m,c.once=function(e,t,n){return r(e,t,n,{once:!0})},c.has=function(e){return e=e.toLowerCase(),!(!l[e]||0===l[e].length)}};bw.isNative=function(e){return!!vw[e.toLowerCase()]};var yw,Cw=function(n){return n._eventDispatcher||(n._eventDispatcher=new bw({scope:n,toggleEvent:function(e,t){bw.isNative(e)&&n.toggleNativeEvent&&n.toggleNativeEvent(e,t)}})),n._eventDispatcher},xw={fire:function(e,t,n){if(this.removed&&"remove"!==e&&"detach"!==e)return t;if(t=Cw(this).fire(e,t,n),!1!==n&&this.parent)for(var r=this.parent();r&&!t.isPropagationStopped();)r.fire(e,t,!1),r=r.parent();return t},on:function(e,t,n){return Cw(this).on(e,t,n)},off:function(e,t){return Cw(this).off(e,t)},once:function(e,t){return Cw(this).once(e,t)},hasEventListeners:function(e){return Cw(this).has(e)}},ww=hi.DOM,Nw=function(e,t){return"selectionchange"===t?e.getDoc():!e.inline&&/^mouse|touch|click|contextmenu|drop|dragover|dragend/.test(t)?e.getDoc().documentElement:e.settings.event_root?(e.eventRoot||(e.eventRoot=ww.select(e.settings.event_root)[0]),e.eventRoot):e.getBody()},Ew=function(e,t,n){var r;(r=e).hidden||r.readonly?!0===e.readonly&&n.preventDefault():e.fire(t,n)},Sw=function(i,a){var e,t;if(i.delegates||(i.delegates={}),!i.delegates[a]&&!i.removed)if(e=Nw(i,a),i.settings.event_root){if(yw||(yw={},i.editorManager.on("removeEditor",function(){var e;if(!i.editorManager.activeEditor&&yw){for(e in yw)i.dom.unbind(Nw(i,e));yw=null}})),yw[a])return;t=function(e){for(var t=e.target,n=i.editorManager.get(),r=n.length;r--;){var o=n[r].getBody();(o===t||ww.isChildOf(t,o))&&Ew(n[r],a,e)}},yw[a]=t,ww.bind(e,a,t)}else t=function(e){Ew(i,a,e)},ww.bind(e,a,t),i.delegates[a]=t},kw={bindPendingEventDelegates:function(){var t=this;Yt.each(t._pendingNativeEvents,function(e){Sw(t,e)})},toggleNativeEvent:function(e,t){var n=this;"focus"!==e&&"blur"!==e&&(t?n.initialized?Sw(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&(n.dom.unbind(Nw(n,e),e,n.delegates[e]),delete n.delegates[e]))},unbindAllNativeEvents:function(){var e,t=this,n=t.getBody(),r=t.dom;if(t.delegates){for(e in t.delegates)t.dom.unbind(Nw(t,e),e,t.delegates[e]);delete t.delegates}!t.inline&&n&&r&&(n.onload=null,r.unbind(t.getWin()),r.unbind(t.getDoc())),r&&(r.unbind(n),r.unbind(t.getContainer()))}},Tw=kw=Yt.extend({},xw,kw),Aw=Yt.each,Rw=Yt.explode,Dw={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},Bw=Yt.makeMap("alt,ctrl,shift,meta,access");function Ow(i){var a={},r=[],u=function(e){var t,n,r={};for(n in Aw(Rw(e,"+"),function(e){e in Bw?r[e]=!0:/^[0-9]{2,}$/.test(e)?r.keyCode=parseInt(e,10):(r.charCode=e.charCodeAt(0),r.keyCode=Dw[e]||e.toUpperCase().charCodeAt(0))}),t=[r.keyCode],Bw)r[n]?t.push(n):r[n]=!1;return r.id=t.join(","),r.access&&(r.alt=!0,de.mac?r.ctrl=!0:r.shift=!0),r.meta&&(de.mac?r.meta=!0:(r.ctrl=!0,r.meta=!1)),r},s=function(e,t,n,r){var o;return(o=Yt.map(Rw(e,">"),u))[o.length-1]=Yt.extend(o[o.length-1],{func:n,scope:r||i}),Yt.extend(o[0],{desc:i.translate(t),subpatterns:o.slice(1)})},o=function(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)},c=function(e){return e.func?e.func.call(e.scope):null};i.on("keyup keypress keydown",function(t){var e,n;((n=t).altKey||n.ctrlKey||n.metaKey||"keydown"===(e=t).type&&112<=e.keyCode&&e.keyCode<=123)&&!t.isDefaultPrevented()&&(Aw(a,function(e){if(o(t,e))return r=e.subpatterns.slice(0),"keydown"===t.type&&c(e),!0}),o(t,r[0])&&(1===r.length&&"keydown"===t.type&&c(r[0]),r.shift()))}),this.add=function(e,n,r,o){var t;return"string"==typeof(t=r)?r=function(){i.execCommand(t,!1,null)}:Yt.isArray(t)&&(r=function(){i.execCommand(t[0],t[1],t[2])}),Aw(Rw(Yt.trim(e.toLowerCase())),function(e){var t=s(e,n,r,o);a[t.id]=t}),!0},this.remove=function(e){var t=s(e);return!!a[t.id]&&(delete a[t.id],!0)}}var _w=Yt.each,Pw=Yt.trim,Iw="source protocol authority userInfo user password host port relative path directory file query anchor".split(" "),Lw={ftp:21,http:80,https:443,mailto:25},Mw=function(r,e){var t,n,o=this;if(r=Pw(r),t=(e=o.settings=e||{}).base_uri,/^([\w\-]+):([^\/]{2})/i.test(r)||/^\s*#/.test(r))o.source=r;else{var i=0===r.indexOf("//");0!==r.indexOf("/")||i||(r=(t&&t.protocol||"http")+"://mce_host"+r),/^[\w\-]*:?\/\//.test(r)||(n=e.base_uri?e.base_uri.path:new Mw(document.location.href).directory,r=""==e.base_uri.protocol?"//mce_host"+o.toAbsPath(n,r):(r=/([^#?]*)([#?]?.*)/.exec(r),(t&&t.protocol||"http")+"://mce_host"+o.toAbsPath(n,r[1])+r[2])),r=r.replace(/@@/g,"(mce_at)"),r=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(r),_w(Iw,function(e,t){var n=r[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n}),t&&(o.protocol||(o.protocol=t.protocol),o.userInfo||(o.userInfo=t.userInfo),o.port||"mce_host"!==o.host||(o.port=t.port),o.host&&"mce_host"!==o.host||(o.host=t.host),o.source=""),i&&(o.protocol="")}};Mw.prototype={setPath:function(e){e=/^(.*?)\/?(\w+)?$/.exec(e),this.path=e[0],this.directory=e[1],this.file=e[2],this.source="",this.getURI()},toRelative:function(e){var t;if("./"===e)return e;if("mce_host"!==(e=new Mw(e,{base_uri:this})).host&&this.host!==e.host&&e.host||this.port!==e.port||this.protocol!==e.protocol&&""!==e.protocol)return e.getURI();var n=this.getURI(),r=e.getURI();return n===r||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===r?n:(t=this.toRelPath(this.path,e.path),e.query&&(t+="?"+e.query),e.anchor&&(t+="#"+e.anchor),t)},toAbsolute:function(e,t){return(e=new Mw(e,{base_uri:this})).getURI(t&&this.isSameOrigin(e))},isSameOrigin:function(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;var t=Lw[this.protocol];if(t&&(this.port||t)==(e.port||t))return!0}return!1},toRelPath:function(e,t){var n,r,o,i=0,a="";if(e=(e=e.substring(0,e.lastIndexOf("/"))).split("/"),n=t.split("/"),e.length>=n.length)for(r=0,o=e.length;r<o;r++)if(r>=n.length||e[r]!==n[r]){i=r+1;break}if(e.length<n.length)for(r=0,o=n.length;r<o;r++)if(r>=e.length||e[r]!==n[r]){i=r+1;break}if(1===i)return t;for(r=0,o=e.length-(i-1);r<o;r++)a+="../";for(r=i-1,o=n.length;r<o;r++)a+=r!==i-1?"/"+n[r]:n[r];return a},toAbsPath:function(e,t){var n,r,o,i=0,a=[];for(r=/\/$/.test(t)?"/":"",e=e.split("/"),t=t.split("/"),_w(e,function(e){e&&a.push(e)}),e=a,n=t.length-1,a=[];0<=n;n--)0!==t[n].length&&"."!==t[n]&&(".."!==t[n]?0<i?i--:a.push(t[n]):i++);return 0!==(o=(n=e.length-i)<=0?a.reverse().join("/"):e.slice(0,n).join("/")+"/"+a.reverse().join("/")).indexOf("/")&&(o="/"+o),r&&o.lastIndexOf("/")!==o.length-1&&(o+=r),o},getURI:function(e){var t,n=this;return n.source&&!e||(t="",e||(n.protocol?t+=n.protocol+"://":t+="//",n.userInfo&&(t+=n.userInfo+"@"),n.host&&(t+=n.host),n.port&&(t+=":"+n.port)),n.path&&(t+=n.path),n.query&&(t+="?"+n.query),n.anchor&&(t+="#"+n.anchor),n.source=t),n.source}},Mw.parseDataUri=function(e){var t,n;return e=decodeURIComponent(e).split(","),(n=/data:([^;]+)/.exec(e[0]))&&(t=n[1]),{type:t,data:e[1]}},Mw.getDocumentBaseUrl=function(e){var t;return t=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?e.href:e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),t};var Fw=hi.DOM,Uw=Yt.extend,zw=Yt.each,Vw=Yt.resolve,jw=de.ie,Hw=function(e,t,n){var r,o,i,a,u,s,c,l=this,f=l.documentBaseUrl=n.documentBaseURL,d=n.baseURI;r=l,o=e,i=f,a=n.defaultSettings,u=t,c={id:o,theme:"silver",popup_css:"",plugins:"",document_base_url:i,add_form_submit_trigger:!0,submit_patch:!0,add_unload_trigger:!0,convert_urls:!0,relative_urls:!0,remove_script_host:!0,object_resizing:!0,doctype:"<!DOCTYPE html>",visual:!0,font_size_style_values:"xx-small,x-small,small,medium,large,x-large,xx-large",font_size_legacy_values:"xx-small,small,medium,large,x-large,xx-large,300%",forced_root_block:"p",hidden_input:!0,render_ui:!0,inline_styles:!0,convert_fonts_to_spans:!0,indent:"simple",indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist",entity_encoding:"named",url_converter:(s=r).convertURL,url_converter_scope:s,ie7_compat:!0},t=Mf(Df,c,a,u),l.settings=t,Ai.languageLoad=t.language_load,Ai.baseURL=n.baseURL,l.id=e,l.setDirty(!1),l.plugins={},l.documentBaseURI=new Mw(t.document_base_url,{base_uri:d}),l.baseURI=d,l.contentCSS=[],l.contentStyles=[],l.shortcuts=new Ow(l),l.loadedCSS={},l.editorCommands=new hw(l),l.suffix=n.suffix,l.editorManager=n,l.inline=t.inline,l.buttons={},l.menuItems={},t.cache_suffix&&(de.cacheSuffix=t.cache_suffix.replace(/^[\?\&]+/,"")),!1===t.override_viewport&&(de.overrideViewPort=!1);var m,g,p,h,v,b,y,C,x=(g={},p={},h={},v={},b={},y={},{addButton:(C=function(n,r){return function(e,t){return n[e.toLowerCase()]=Wc({type:r},t)}})(m={},"button"),addToggleButton:C(m,"togglebutton"),addMenuButton:C(m,"menubutton"),addSplitButton:C(m,"splitbutton"),addMenuItem:C(g,"menuitem"),addNestedMenuItem:C(g,"nestedmenuitem"),addToggleMenuItem:C(g,"togglemenuitem"),addAutocompleter:C(p,"autocompleter"),addContextMenu:C(v,"contextmenu"),addContextToolbar:C(b,"contexttoolbar"),addContextForm:C(b,"contextform"),addSidebar:C(y,"sidebar"),addIcon:function(e,t){return h[e.toLowerCase()]=t},getAll:function(){return{buttons:m,menuItems:g,icons:h,popups:p,contextMenus:v,contextToolbars:b,sidebars:y}}});l.ui={registry:x},n.fire("SetupEditor",{editor:l}),l.execCallback("setup",l),l.$=pn.overrideDefaults(function(){return{context:l.inline?l.getBody():l.getDoc(),element:l.getBody()}})};Uw(Hw.prototype={render:function(){Px(this)},focus:function(e){df(this,e)},hasFocus:function(){return mf(this)},execCallback:function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r,o=this.settings[e];if(o)return this.callbackLookup&&(r=this.callbackLookup[e])&&(o=r.func,r=r.scope),"string"==typeof o&&(r=(r=o.replace(/\.\w+$/,""))?Vw(r):0,o=Vw(o),this.callbackLookup=this.callbackLookup||{},this.callbackLookup[e]={func:o,scope:r}),o.apply(r||this,Array.prototype.slice.call(arguments,1))},translate:function(e){return ki.translate(e)},getParam:function(e,t,n){return zf(this,e,t,n)},nodeChanged:function(e){this._nodeChangeDispatcher.nodeChanged(e)},addButton:function(){throw new Error("editor.addButton has been removed in tinymce 5x, use editor.ui.registry.addButton or editor.ui.registry.addToggleButton or editor.ui.registry.addSplitButton instead")},addSidebar:function(){throw new Error("editor.addSidebar has been removed in tinymce 5x, use editor.ui.registry.addSidebar instead")},addMenuItem:function(){throw new Error("editor.addMenuItem has been removed in tinymce 5x, use editor.ui.registry.addMenuItem instead")},addContextToolbar:function(){throw new Error("editor.addContextToolbar has been removed in tinymce 5x, use editor.ui.registry.addContextToolbar instead")},addCommand:function(e,t,n){this.editorCommands.addCommand(e,t,n)},addQueryStateHandler:function(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)},addQueryValueHandler:function(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)},addShortcut:function(e,t,n,r){this.shortcuts.add(e,t,n,r)},execCommand:function(e,t,n,r){return this.editorCommands.execCommand(e,t,n,r)},queryCommandState:function(e){return this.editorCommands.queryCommandState(e)},queryCommandValue:function(e){return this.editorCommands.queryCommandValue(e)},queryCommandSupported:function(e){return this.editorCommands.queryCommandSupported(e)},show:function(){this.hidden&&(this.hidden=!1,this.inline?this.getBody().contentEditable=!0:(Fw.show(this.getContainer()),Fw.hide(this.id)),this.load(),this.fire("show"))},hide:function(){var e=this,t=e.getDoc();e.hidden||(jw&&t&&!e.inline&&t.execCommand("SelectAll"),e.save(),e.inline?(e.getBody().contentEditable=!1,e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(Fw.hide(e.getContainer()),Fw.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.fire("hide"))},isHidden:function(){return!!this.hidden},setProgressState:function(e,t){this.fire("ProgressState",{state:e,time:t})},load:function(e){var t,n=this.getElement();return this.removed?"":n?((e=e||{}).load=!0,t=this.setContent(n.value!==undefined?n.value:n.innerHTML,e),e.element=n,e.no_events||this.fire("LoadContent",e),e.element=n=null,t):void 0},save:function(e){var t,n,r=this,o=r.getElement();if(o&&r.initialized&&!r.removed)return(e=e||{}).save=!0,e.element=o,e.content=r.getContent(e),e.no_events||r.fire("SaveContent",e),"raw"===e.format&&r.fire("RawSaveContent",e),t=e.content,/TEXTAREA|INPUT/i.test(o.nodeName)?o.value=t:(!e.is_removing&&r.inline||(o.innerHTML=t),(n=Fw.getParent(r.id,"form"))&&zw(n.elements,function(e){if(e.name===r.id)return e.value=t,!1})),e.element=o=null,!1!==e.set_dirty&&r.setDirty(!1),t},setContent:function(e,t){return hf(this,e,t)},getContent:function(e){return t=this,void 0===(n=e)&&(n={}),A.from(t.getBody()).fold(q("tree"===n.format?new Gc("body",11):""),function(e){return _l(t,n,e)});var t,n},insertContent:function(e,t){t&&(e=Uw({content:e},t)),this.execCommand("mceInsertContent",!1,e)},isDirty:function(){return!this.isNotDirty},setDirty:function(e){var t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.fire("dirty")},setMode:function(e){var t,n;(n=e)!==Fx(t=this)&&(t.initialized?Mx(t,"readonly"===n):t.on("init",function(){Mx(t,"readonly"===n)}),xf(t,n))},getContainer:function(){return this.container||(this.container=Fw.get(this.editorContainer||this.id+"_parent")),this.container},getContentAreaContainer:function(){return this.contentAreaContainer},getElement:function(){return this.targetElm||(this.targetElm=Fw.get(this.id)),this.targetElm},getWin:function(){var e;return this.contentWindow||(e=this.iframeElement)&&(this.contentWindow=e.contentWindow),this.contentWindow},getDoc:function(){var e;return this.contentDocument||(e=this.getWin())&&(this.contentDocument=e.document),this.contentDocument},getBody:function(){var e=this.getDoc();return this.bodyElement||(e?e.body:null)},convertURL:function(e,t,n){var r=this.settings;return r.urlconverter_callback?this.execCallback("urlconverter_callback",e,n,!0,t):!r.convert_urls||n&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length?e:r.relative_urls?this.documentBaseURI.toRelative(e):e=this.documentBaseURI.toAbsolute(e,r.remove_script_host)},addVisual:function(e){var n,r=this,o=r.settings,i=r.dom;e=e||r.getBody(),r.hasVisual===undefined&&(r.hasVisual=o.visual),zw(i.select("table,a",e),function(e){var t;switch(e.nodeName){case"TABLE":return n=o.visual_table_class||"mce-item-table",void((t=i.getAttrib(e,"border"))&&"0"!==t||!r.hasVisual?i.removeClass(e,n):i.addClass(e,n));case"A":return void(i.getAttrib(e,"href")||(t=i.getAttrib(e,"name")||e.id,n=o.visual_anchor_class||"mce-item-anchor",t&&r.hasVisual?i.addClass(e,n):i.removeClass(e,n)))}}),r.fire("VisualAid",{element:e,hasVisual:r.hasVisual})},remove:function(){kf(this)},destroy:function(e){Tf(this,e)},uploadImages:function(e){return this.editorUpload.uploadImages(e)},_scanForImages:function(){return this.editorUpload.scanForImages()}},Tw);var qw,$w,Ww,Kw={isEditorUIElement:function(e){return-1!==e.className.toString().indexOf("tox-")||-1!==e.className.toString().indexOf("mce-")}},Xw=function(n,e){var t,r;tr.detect().browser.isIE()?(r=n).on("focusout",function(){nf(r)}):(t=e,n.on("mouseup touchend",function(e){t.throttle()})),n.on("keyup nodechange",function(e){var t;"nodechange"===(t=e).type&&t.selectionChange||nf(n)})},Yw=function(e){var t,n,r,o=Mi(function(){nf(e)},0);e.inline&&(t=e,n=o,r=function(){n.throttle()},hi.DOM.bind(document,"mouseup",r),t.on("remove",function(){hi.DOM.unbind(document,"mouseup",r)})),e.on("init",function(){Xw(e,o)}),e.on("remove",function(){o.cancel()})},Gw=hi.DOM,Jw=function(e){return Kw.isEditorUIElement(e)},Qw=function(t,e){var n=t?t.settings.custom_ui_selector:"";return null!==Gw.getParent(e,function(e){return Jw(e)||!!n&&t.dom.is(e,n)})},Zw=function(r,e){var t=e.editor;Yw(t),t.on("focusin",function(){var e=r.focusedEditor;e!==this&&(e&&e.fire("blur",{focusedEditor:this}),r.setActive(this),(r.focusedEditor=this).fire("focus",{blurredEditor:e}),this.focus(!0))}),t.on("focusout",function(){var t=this;ve.setEditorTimeout(t,function(){var e=r.focusedEditor;Qw(t,function(){try{return document.activeElement}catch(e){return document.body}}())||e!==t||(t.fire("blur",{focusedEditor:null}),r.focusedEditor=null)})}),qw||(qw=function(e){var t,n=r.activeEditor;t=e.target,n&&t.ownerDocument===document&&(t===document.body||Qw(n,t)||r.focusedEditor!==n||(n.fire("blur",{focusedEditor:null}),r.focusedEditor=null))},Gw.bind(document,"focusin",qw))},eN=function(e,t){e.focusedEditor===t.editor&&(e.focusedEditor=null),e.activeEditor||(Gw.unbind(document,"focusin",qw),qw=null)},tN=function(e){e.on("AddEditor",d(Zw,e)),e.on("RemoveEditor",d(eN,e))},nN=hi.DOM,rN=Yt.explode,oN=Yt.each,iN=Yt.extend,aN=0,uN=!1,sN=[],cN=[],lN=function(t){var n=t.type;oN(Ww.get(),function(e){switch(n){case"scroll":e.fire("ScrollWindow",t);break;case"resize":e.fire("ResizeWindow",t)}})},fN=function(e){e!==uN&&(e?pn(window).on("resize scroll",lN):pn(window).off("resize scroll",lN),uN=e)},dN=function(t){var e=cN;delete sN[t.id];for(var n=0;n<sN.length;n++)if(sN[n]===t){sN.splice(n,1);break}return cN=U(cN,function(e){return t!==e}),Ww.activeEditor===t&&(Ww.activeEditor=0<cN.length?cN[0]:null),Ww.focusedEditor===t&&(Ww.focusedEditor=null),e.length!==cN.length};iN(Ww={defaultSettings:{},$:pn,majorVersion:"5",minorVersion:"0.0",releaseDate:"2019-02-04",editors:sN,i18n:ki,activeEditor:null,settings:{},setup:function(){var e,t,n,r,o="";if(t=Mw.getDocumentBaseUrl(document.location),/^[^:]+:\/\/\/?[^\/]+\//.test(t)&&(t=t.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(t)||(t+="/")),n=window.tinymce||window.tinyMCEPreInit)e=n.base||n.baseURL,o=n.suffix;else{for(var i=document.getElementsByTagName("script"),a=0;a<i.length;a++){var u=(r=i[a].src).substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==u.indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/"));break}}!e&&document.currentScript&&(-1!==(r=document.currentScript.src).indexOf(".min")&&(o=".min"),e=r.substring(0,r.lastIndexOf("/")))}this.baseURL=new Mw(t).toAbsolute(e),this.documentBaseURL=t,this.baseURI=new Mw(this.baseURL),this.suffix=o,tN(this)},overrideDefaults:function(e){var t,n;(t=e.base_url)&&(this.baseURL=new Mw(this.documentBaseURL).toAbsolute(t.replace(/\/+$/,"")),this.baseURI=new Mw(this.baseURL)),n=e.suffix,e.suffix&&(this.suffix=n);var r=(this.defaultSettings=e).plugin_base_urls;for(var o in r)Ai.PluginManager.urls[o]=r[o]},init:function(r){var n,u,s=this;u=Yt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");var c=function(e){var t=e.id;return t||(t=(t=e.name)&&!nN.get(t)?e.name:nN.uniqueId(),e.setAttribute("id",t)),t},l=function(e,t){return t.constructor===RegExp?t.test(e.className):nN.hasClass(e,t)},f=function(e){n=e},e=function(){var o,i=0,a=[],n=function(e,t,n){var r=new Hw(e,t,s);a.push(r),r.on("init",function(){++i===o.length&&f(a)}),r.targetElm=r.targetElm||n,r.render()};nN.unbind(window,"ready",e),function(e){var t=r[e];t&&t.apply(s,Array.prototype.slice.call(arguments,2))}("onpageload"),o=pn.unique(function(t){var e,n=[];if(de.ie&&de.ie<11)return rd.initError("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tinymce.com/docs/get-started/system-requirements/"),[];if(t.types)return oN(t.types,function(e){n=n.concat(nN.select(e.selector))}),n;if(t.selector)return nN.select(t.selector);if(t.target)return[t.target];switch(t.mode){case"exact":0<(e=t.elements||"").length&&oN(rN(e),function(t){var e;(e=nN.get(t))?n.push(e):oN(document.forms,function(e){oN(e.elements,function(e){e.name===t&&(t="mce_editor_"+aN++,nN.setAttrib(e,"id",t),n.push(e))})})});break;case"textareas":case"specific_textareas":oN(nN.select("textarea"),function(e){t.editor_deselector&&l(e,t.editor_deselector)||t.editor_selector&&!l(e,t.editor_selector)||n.push(e)})}return n}(r)),r.types?oN(r.types,function(t){Yt.each(o,function(e){return!nN.is(e,t.selector)||(n(c(e),iN({},r,t),e),!1)})}):(Yt.each(o,function(e){var t;(t=s.get(e.id))&&t.initialized&&!(t.getContainer()||t.getBody()).parentNode&&(dN(t),t.unbindAllNativeEvents(),t.destroy(!0),t.removed=!0,t=null)}),0===(o=Yt.grep(o,function(e){return!s.get(e.id)})).length?f([]):oN(o,function(e){var t;t=e,r.inline&&t.tagName.toLowerCase()in u?rd.initError("Could not initialize inline editor on invalid inline target element",e):n(c(e),r,e)}))};return s.settings=r,nN.bind(window,"ready",e),new me(function(t){n?t(n):f=function(e){t(e)}})},get:function(t){return 0===arguments.length?cN.slice(0):R(t)?V(cN,function(e){return e.id===t}).getOr(null):I(t)&&cN[t]?cN[t]:null},add:function(e){var t=this;return sN[e.id]===e||(null===t.get(e.id)&&("length"!==e.id&&(sN[e.id]=e),sN.push(e),cN.push(e)),fN(!0),t.activeEditor=e,t.fire("AddEditor",{editor:e}),$w||($w=function(){t.fire("BeforeUnload")},nN.bind(window,"beforeunload",$w))),e},createEditor:function(e,t){return this.add(new Hw(e,t,this))},remove:function(e){var t,n,r=this;if(e){if(!R(e))return n=e,O(r.get(n.id))?null:(dN(n)&&r.fire("RemoveEditor",{editor:n}),0===cN.length&&nN.unbind(window,"beforeunload",$w),n.remove(),fN(0<cN.length),n);oN(nN.select(e),function(e){(n=r.get(e.id))&&r.remove(n)})}else for(t=cN.length-1;0<=t;t--)r.remove(cN[t])},execCommand:function(e,t,n){var r=this.get(n);switch(e){case"mceAddEditor":return this.get(n)||new Hw(n,this.settings,this).render(),!0;case"mceRemoveEditor":return r&&r.remove(),!0;case"mceToggleEditor":return r?r.isHidden()?r.show():r.hide():this.execCommand("mceAddEditor",0,n),!0}return!!this.activeEditor&&this.activeEditor.execCommand(e,t,n)},triggerSave:function(){oN(cN,function(e){e.save()})},addI18n:function(e,t){ki.add(e,t)},translate:function(e){return ki.translate(e)},setActive:function(e){var t=this.activeEditor;this.activeEditor!==e&&(t&&t.fire("deactivate",{relatedTarget:e}),e.fire("activate",{relatedTarget:t})),this.activeEditor=e}},xw),Ww.setup();var mN,gN=Ww;function pN(n){return{walk:function(e,t){return Lc(n,e,t)},split:tg,normalize:function(t){return Vh(n,t).fold(q(!1),function(e){return t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0})}}}(mN=pN||(pN={})).compareRanges=Bd,mN.getCaretRangeFromPoint=ah,mN.getSelectedNode=$a,mN.getNode=Wa;var hN,vN,bN=pN,yN=Math.min,CN=Math.max,xN=Math.round,wN=function(e,t,n){var r,o,i,a,u,s;return r=t.x,o=t.y,i=e.w,a=e.h,u=t.w,s=t.h,"b"===(n=(n||"").split(""))[0]&&(o+=s),"r"===n[1]&&(r+=u),"c"===n[0]&&(o+=xN(s/2)),"c"===n[1]&&(r+=xN(u/2)),"b"===n[3]&&(o-=a),"r"===n[4]&&(r-=i),"c"===n[3]&&(o-=xN(a/2)),"c"===n[4]&&(r-=xN(i/2)),NN(r,o,i,a)},NN=function(e,t,n,r){return{x:e,y:t,w:n,h:r}},EN={inflate:function(e,t,n){return NN(e.x-t,e.y-n,e.w+2*t,e.h+2*n)},relativePosition:wN,findBestRelativePosition:function(e,t,n,r){var o,i;for(i=0;i<r.length;i++)if((o=wN(e,t,r[i])).x>=n.x&&o.x+o.w<=n.w+n.x&&o.y>=n.y&&o.y+o.h<=n.h+n.y)return r[i];return null},intersect:function(e,t){var n,r,o,i;return n=CN(e.x,t.x),r=CN(e.y,t.y),o=yN(e.x+e.w,t.x+t.w),i=yN(e.y+e.h,t.y+t.h),o-n<0||i-r<0?null:NN(n,r,o-n,i-r)},clamp:function(e,t,n){var r,o,i,a,u,s,c,l,f,d;return u=e.x,s=e.y,c=e.x+e.w,l=e.y+e.h,f=t.x+t.w,d=t.y+t.h,r=CN(0,t.x-u),o=CN(0,t.y-s),i=CN(0,c-f),a=CN(0,l-d),u+=r,s+=o,n&&(c+=r,l+=o,u-=i,s-=a),NN(u,s,(c-=i)-u,(l-=a)-s)},create:NN,fromClientRect:function(e){return NN(e.left,e.top,e.width,e.height)}},SN=Yt.each,kN=Yt.extend,TN=function(){};TN.extend=hN=function(n){var e,t,r,o=this.prototype,i=function(){var e,t,n;if(!vN&&(this.init&&this.init.apply(this,arguments),t=this.Mixins))for(e=t.length;e--;)(n=t[e]).init&&n.init.apply(this,arguments)},a=function(){return this},u=function(n,r){return function(){var e,t=this._super;return this._super=o[n],e=r.apply(this,arguments),this._super=t,e}};for(t in vN=!0,e=new this,vN=!1,n.Mixins&&(SN(n.Mixins,function(e){for(var t in e)"init"!==t&&(n[t]=e[t])}),o.Mixins&&(n.Mixins=o.Mixins.concat(n.Mixins))),n.Methods&&SN(n.Methods.split(","),function(e){n[e]=a}),n.Properties&&SN(n.Properties.split(","),function(e){var t="_"+e;n[e]=function(e){return e!==undefined?(this[t]=e,this):this[t]}}),n.Statics&&SN(n.Statics,function(e,t){i[t]=e}),n.Defaults&&o.Defaults&&(n.Defaults=kN({},o.Defaults,n.Defaults)),n)"function"==typeof(r=n[t])&&o[t]?e[t]=u(t,r):e[t]=r;return i.prototype=e,(i.constructor=i).extend=hN,i};var AN=Math.min,RN=Math.max,DN=Math.round,BN=function(e,n){var r,o,t,i;if(n=n||'"',null===e)return"null";if("string"==(t=typeof e))return o="\bb\tt\nn\ff\rr\"\"''\\\\",n+e.replace(/([\u0080-\uFFFF\x00-\x1f\"\'\\])/g,function(e,t){return'"'===n&&"'"===e?e:(r=o.indexOf(t))+1?"\\"+o.charAt(r+1):(e=t.charCodeAt().toString(16),"\\u"+"0000".substring(e.length)+e)})+n;if("object"!==t)return""+e;if(e.hasOwnProperty&&"[object Array]"===Object.prototype.toString.call(e)){for(r=0,o="[";r<e.length;r++)o+=(0<r?",":"")+BN(e[r],n);return o+"]"}for(i in o="{",e)e.hasOwnProperty(i)&&(o+="function"!=typeof e[i]?(1<o.length?","+n:n)+i+n+":"+BN(e[i],n):"");return o+"}"},ON={serialize:BN,parse:function(e){try{return JSON.parse(e)}catch(t){}}},_N={callbacks:{},count:0,send:function(t){var n=this,r=hi.DOM,o=t.count!==undefined?t.count:n.count,i="tinymce_jsonp_"+o;n.callbacks[o]=function(e){r.remove(i),delete n.callbacks[o],t.callback(e)},r.add(r.doc.body,"script",{id:i,src:t.url,type:"text/javascript"}),n.count++}},PN={send:function(e){var t,n=0,r=function(){!e.async||4===t.readyState||1e4<n++?(e.success&&n<1e4&&200===t.status?e.success.call(e.success_scope,""+t.responseText,t,e):e.error&&e.error.call(e.error_scope,1e4<n?"TIMED_OUT":"GENERAL",t,e),t=null):setTimeout(r,10)};if(e.scope=e.scope||this,e.success_scope=e.success_scope||e.scope,e.error_scope=e.error_scope||e.scope,e.async=!1!==e.async,e.data=e.data||"",PN.fire("beforeInitialize",{settings:e}),t=ud()){if(t.overrideMimeType&&t.overrideMimeType(e.content_type),t.open(e.type||(e.data?"POST":"GET"),e.url,e.async),e.crossDomain&&(t.withCredentials=!0),e.content_type&&t.setRequestHeader("Content-Type",e.content_type),e.requestheaders&&Yt.each(e.requestheaders,function(e){t.setRequestHeader(e.key,e.value)}),t.setRequestHeader("X-Requested-With","XMLHttpRequest"),(t=PN.fire("beforeSend",{xhr:t,settings:e}).xhr).send(e.data),!e.async)return r();setTimeout(r,10)}}};Yt.extend(PN,xw);var IN,LN,MN,FN,UN=Yt.extend,zN=function(e){this.settings=UN({},e),this.count=0};zN.sendRPC=function(e){return(new zN).send(e)},zN.prototype={send:function(n){var r=n.error,o=n.success;(n=UN(this.settings,n)).success=function(e,t){void 0===(e=ON.parse(e))&&(e={error:"JSON Parse error."}),e.error?r.call(n.error_scope||n.scope,e.error,t):o.call(n.success_scope||n.scope,e.result)},n.error=function(e,t){r&&r.call(n.error_scope||n.scope,e,t)},n.data=ON.serialize({id:n.id||"c"+this.count++,method:n.method,params:n.params}),n.content_type="application/json",PN.send(n)}};try{IN=window.localStorage}catch($N){LN={},MN=[],FN={getItem:function(e){var t=LN[e];return t||null},setItem:function(e,t){MN.push(e),LN[e]=String(t)},key:function(e){return MN[e]},removeItem:function(t){MN=MN.filter(function(e){return e===t}),delete LN[t]},clear:function(){MN=[],LN={}},length:0},Object.defineProperty(FN,"length",{get:function(){return MN.length},configurable:!1,enumerable:!1}),IN=FN}var VN,jN=gN,HN={geom:{Rect:EN},util:{Promise:me,Delay:ve,Tools:Yt,VK:bm,URI:Mw,Class:TN,EventDispatcher:bw,Observable:xw,I18n:ki,XHR:PN,JSON:ON,JSONRequest:zN,JSONP:_N,LocalStorage:IN,Color:function(e){var n={},u=0,s=0,c=0,t=function(e){var t;return"object"==typeof e?"r"in e?(u=e.r,s=e.g,c=e.b):"v"in e&&function(e,t,n){var r,o,i,a;if(e=(parseInt(e,10)||0)%360,t=parseInt(t,10)/100,n=parseInt(n,10)/100,t=RN(0,AN(t,1)),n=RN(0,AN(n,1)),0!==t){switch(r=e/60,i=(o=n*t)*(1-Math.abs(r%2-1)),a=n-o,Math.floor(r)){case 0:u=o,s=i,c=0;break;case 1:u=i,s=o,c=0;break;case 2:u=0,s=o,c=i;break;case 3:u=0,s=i,c=o;break;case 4:u=i,s=0,c=o;break;case 5:u=o,s=0,c=i;break;default:u=s=c=0}u=DN(255*(u+a)),s=DN(255*(s+a)),c=DN(255*(c+a))}else u=s=c=DN(255*n)}(e.h,e.s,e.v):(t=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)[^\)]*\)/gi.exec(e))?(u=parseInt(t[1],10),s=parseInt(t[2],10),c=parseInt(t[3],10)):(t=/#([0-F]{2})([0-F]{2})([0-F]{2})/gi.exec(e))?(u=parseInt(t[1],16),s=parseInt(t[2],16),c=parseInt(t[3],16)):(t=/#([0-F])([0-F])([0-F])/gi.exec(e))&&(u=parseInt(t[1]+t[1],16),s=parseInt(t[2]+t[2],16),c=parseInt(t[3]+t[3],16)),u=u<0?0:255<u?255:u,s=s<0?0:255<s?255:s,c=c<0?0:255<c?255:c,n};return e&&t(e),n.toRgb=function(){return{r:u,g:s,b:c}},n.toHsv=function(){return e=u,t=s,n=c,o=0,(i=AN(e/=255,AN(t/=255,n/=255)))===(a=RN(e,RN(t,n)))?{h:0,s:0,v:100*(o=i)}:(r=(a-i)/a,{h:DN(60*((e===i?3:n===i?1:5)-(e===i?t-n:n===i?e-t:n-e)/((o=a)-i))),s:DN(100*r),v:DN(100*o)});var e,t,n,r,o,i,a},n.toHex=function(){var e=function(e){return 1<(e=parseInt(e,10).toString(16)).length?e:"0"+e};return"#"+e(u)+e(s)+e(c)},n.parse=t,n}},dom:{EventUtils:ke,Sizzle:kt,DomQuery:pn,TreeWalker:oo,DOMUtils:hi,ScriptLoader:xi,RangeUtils:bN,Serializer:Xp,ControlSelection:th,BookmarkManager:Qp,Selection:Jh,Event:ke.Event},html:{Styles:ii,Entities:Wo,Node:Gc,Schema:ri,SaxParser:el,DomParser:Hp,Writer:Il,Serializer:Ll},Env:de,AddOnManager:Ai,Annotator:Hc,Formatter:Tp,UndoManager:Vm,EditorCommands:hw,WindowManager:Gf,NotificationManager:Yf,EditorObservable:Tw,Shortcuts:Ow,Editor:Hw,FocusManager:Kw,EditorManager:gN,DOM:hi.DOM,ScriptLoader:xi.ScriptLoader,PluginManager:Ai.PluginManager,ThemeManager:Ai.ThemeManager,IconManager:od,trim:Yt.trim,isArray:Yt.isArray,is:Yt.is,toArray:Yt.toArray,makeMap:Yt.makeMap,each:Yt.each,map:Yt.map,grep:Yt.grep,inArray:Yt.inArray,extend:Yt.extend,create:Yt.create,walk:Yt.walk,createNS:Yt.createNS,resolve:Yt.resolve,explode:Yt.explode,_addCacheSuffix:Yt._addCacheSuffix,isOpera:de.opera,isWebKit:de.webkit,isIE:de.ie,isGecko:de.gecko,isMac:de.mac},qN=jN=Yt.extend(jN,HN);VN=qN,window.tinymce=VN,window.tinyMCE=VN,function(e){if("object"==typeof module)try{module.exports=e}catch(t){}}(qN)}();