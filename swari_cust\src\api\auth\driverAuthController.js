import Driver from '../../models/Driver.js';
import jwt from 'jsonwebtoken';
import config from '../../config/config.js';
import logger from "../../utils/logger.js";
import mongoose from 'mongoose'; 
// import { generateOTP } from '../../utils/otpUtils.js';
import Vehicle from '../../models/Vehicle.js';
import VehicleMaker from '../../models/VehicleMaker.js';
import VehicleModel from '../../models/VehicleModel.js';
import Bid from '../../models/Bid.js';
import Trip from '../../models/Trip.js';
import mqttService from '../../services/mqttService.js';
import { validationResult } from 'express-validator';
// Add this with other imports at the top
import { getCurrentBalance } from '../../services/walletService.js';

export const registerDriver = async (req, res) => {
  try {
    const { name, phone, email } = req.body;
    logger.info('request received', {name, phone, email});

    // Check if driver already exists
    const existingDriver = await Driver.findOne({ 
      $or: [{ phone }, { email }] 
    });

    if (existingDriver) {
      return res.status(400).json({ 
        message: 'Driver already exists with this phone or email' 
      });
    }

    // Generate OTP
    const otp = '123456';
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    logger.info('OTP generated', {otp, otpExpiry});
    // Create new driver
    const driver = new Driver({
      name,
      phone,
      email,
      otp,
      otp_expiry: otpExpiry
    });

    logger.info('driver created', driver);

    await driver.save();

    // TODO: Send OTP via SMS/Email service

    res.status(201).json({
      message: 'Driver registered successfully. Please verify with OTP.',
      driverId: driver._id
    });

  } catch (error) {
    res.status(500).json({ 
      message: 'Error registering driver', 
      error: error.message 
    });
  }
};

export const loginDriver = async (req, res) => {
  try {
    const { phone, otp } = req.body;

    // Find driver
    const driver = await Driver.findOne({ phone });
    if (!driver) {
      return res.status(404).json({ message: 'Driver not found' });
    }

    // Verify OTP
    if (!driver.otp || driver.otp !== otp) {
      return res.status(401).json({ message: 'Invalid OTP' });
    }

    // Check OTP expiry
    if (driver.otp_expiry < Date.now()) {
      return res.status(401).json({ message: 'OTP expired' });
    }

    // Clear OTP after successful verification
    driver.otp = null;
    driver.otp_expiry = null;
    await driver.save();

    // Generate JWT token
    const token = jwt.sign(
      { id: driver._id, role: driver.role },
      config.jwtSwariNodeSecret,
      { expiresIn: '365d' }
    );

    res.json({
      message: 'Login successful',
      token,
      driver: {
        id: driver._id,
        name: driver.name,
        phone: driver.phone,
        email: driver.email,
        role: driver.role
      }
    });

  } catch (error) {
    res.status(500).json({ 
      message: 'Error during login', 
      error: error.message 
    });
  }
};

export const updateDriverProfile = async (req, res) => {
  try {
    logger.info('user', req.user);
    const driverId = req.user.id;
    
    const allowedDriverUpdates = ["name", "email"];
    const driverUpdateData = {};
    const vehicleData = req.body.vehicle;

    // Filter driver updates
    Object.keys(req.body).forEach((key) => {
      if (allowedDriverUpdates.includes(key)) {
        driverUpdateData[key] = req.body[key];
      }
    });

    // Update driver profile if there are changes
    let updatedDriver = null;
    if (Object.keys(driverUpdateData).length > 0) {
      updatedDriver = await Driver.findByIdAndUpdate(
        driverId,
        driverUpdateData,
        { new: true, runValidators: true }
      ).select('name email');
    }

    // Create vehicle if vehicle data is provided
    let vehicle = null;
    if (vehicleData) {
      vehicle = new Vehicle({
        user_id: driverId,
        reg_no: vehicleData.reg_no,
        vehical_image_url: vehicleData.vehical_image_url,
        category: vehicleData.category,
        vehical_make: vehicleData.vehical_make,
        vehical_model: vehicleData.vehical_model,
        no_of_seats: vehicleData.no_of_seats,
        ac: vehicleData.ac,
        carrier: vehicleData.carrier,
        created_at: new Date(),
        year: vehicleData.year ? new Date(vehicleData.year) : null
      });

      await vehicle.save();

      // Add vehicle reference to driver
      await Driver.findByIdAndUpdate(
        driverId,
        { $push: { vehicles: vehicle._id } }
      );
    }

    res.status(200).json({
      message: "Profile updated successfully!",
      data: {
        driver: updatedDriver,
        vehicle: vehicle
      }
    });
    
  } catch (error) {
    logger.error("Error updating driver profile:", error);
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

export const getDriverProfile = async (req, res) => {
  try {
    const driverId = req.user.id;
    
    const driver = await Driver.findById(driverId)
      .select('name phone email vehicle_type vehicle_make vehicle_model reg_no no_of_seats ac carrier')
      .lean();

    if (!driver) {
      logger.warn("Driver not found");
      return res.status(404).json({ message: "Driver not found" });
    }

    // Get current balance from wallet service
    const balance = await getCurrentBalance(driverId);

    res.status(200).json({
      message: "Driver profile retrieved successfully",
      data: {
        ...driver,
        wallet_balance: balance
      }
    });
    
  } catch (error) {
    logger.error("Error fetching driver profile:", error);
    res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

export const getVehicleData = async (req, res) => {
    try {
        const vehicleData = {
            "Maruti Suzuki": {
                "SUV": ["Brezza", "Grand Vitara", "Jimny", "Fronx"],
                "Sedan": ["Dzire", "Ciaz"],
                "Hatchback": ["Alto", "Swift", "Baleno", "WagonR", "Celerio"]
            },
            "Hyundai": {
                "SUV": ["Creta", "Venue", "Tucson", "Alcazar"],
                "Sedan": ["Verna", "Aura"],
                "Hatchback": ["i20", "Grand i10 NIOS", "i10"]
            },
            "Tata Motors": {
                "SUV": ["Nexon", "Harrier", "Safari", "Punch"],
                "Sedan": ["Tigor"],
                "Hatchback": ["Tiago", "Altroz"]
            },
            "Mahindra": {
                "SUV": ["Scorpio", "XUV700", "XUV300", "Thar", "Bolero"],
                "Van": ["Marazzo"]
            },
            "Honda": {
                "SUV": ["Elevate"],
                "Sedan": ["City", "Amaze"],
                "Hatchback": ["Jazz"]
            },
            "Toyota": {
                "SUV": ["Fortuner", "Urban Cruiser Hyryder"],
                "Sedan": ["Camry"],
                "Hatchback": ["Glanza"]
            },
            "Kia": {
                "SUV": ["Seltos", "Sonet", "Carens"],
                "Van": ["Carnival"]
            },
            "MG": {
                "SUV": ["Hector", "Astor", "Gloster", "ZS EV"]
            },
            "Volkswagen": {
                "SUV": ["Taigun"],
                "Sedan": ["Virtus"]
            },
            "Skoda": {
                "SUV": ["Kushaq"],
                "Sedan": ["Slavia", "Superb"]
            }
        };

        // Store makers and models in database
        for (const [maker, types] of Object.entries(vehicleData)) {
            // Create or find maker
            const makerDoc = await VehicleMaker.findOneAndUpdate(
                { maker: maker },
                { maker: maker },
                { upsert: true, new: true }
            );

            // Process each vehicle type and its models
            for (const [type, models] of Object.entries(types)) {
                let typeId;
                switch(type.toLowerCase()) {
                    case 'suv': typeId = '682f0b083066656c731b46eb'; break;
                    case 'sedan': typeId = '682f0b083066656c731b46ec'; break;
                    case 'hatchback': typeId = '682f0b083066656c731b46ed'; break;
                    case 'van': typeId = '682f0b083066656c731b46ee'; break;
                }

                // Update maker with type_id
                await VehicleMaker.findByIdAndUpdate(
                    makerDoc._id,
                    { type_id: typeId }
                );

                // Create models for this maker and type
                for (const model of models) {
                    await VehicleModel.findOneAndUpdate(
                        { 
                            maker_id: makerDoc._id,
                            model: model,
                            type_id: typeId
                        },
                        {
                            maker_id: makerDoc._id,
                            model: model,
                            type_id: typeId
                        },
                        { upsert: true, new: true }
                    );
                }
            }
        }

        // Retrieve all data with relationships
        const allMakers = await VehicleMaker.find();
        const allModels = await VehicleModel.find();

        // Format response
        const response = await Promise.all(allMakers.map(async (maker) => {
            const models = allModels.filter(model => 
                model.maker_id.toString() === maker._id.toString()
            );

            return {
                maker: maker.maker,
                type_id: maker.type_id,
                models: models.map(m => ({
                    model: m.model,
                    type_id: m.type_id
                }))
            };
        }));

        res.status(200).json({
            success: true,
            data: response
        });

    } catch (error) {
        console.error('Error in getVehicleData:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing vehicle data',
            error: error.message
        });
    }
};

export const createBid = async (req, res) => {
  
  try {
    const { trip_id, vehicle_id, amount, notes } = req.body;
    const driver_id = req.user.id;

    // Validate required fields
    if (!trip_id || !amount || !vehicle_id) {
      return res.status(400).json({ 
        message: 'Trip ID, vehicle ID, and amount are required' 
      });
    }

    // Check if trip exists
    const trip = await Trip.findById(trip_id);
    if (!trip) {
      logger.warn('Trip not found for bid creation', { trip_id });
      return res.status(404).json({
        message: 'Trip not found with the provided ID'
      });
    }

    // Verify vehicle belongs to driver
    const vehicle = await Vehicle.findById(vehicle_id);
    if (!vehicle) {
      logger.warn('Vehicle not found', { vehicle_id });
      return res.status(404).json({
        message: 'Vehicle not found with the provided ID'
      });
    }

    if (vehicle.user_id.toString() !== driver_id) {
      logger.warn('Vehicle does not belong to driver', { 
        vehicle_id, 
        driver_id,
        vehicle_user_id: vehicle.user_id 
      });
      return res.status(403).json({
        message: 'Driver not owner of this vehicle'
      });
    }

    // Check if driver already has 2 bids for this trip
    const existingBids = await Bid.countDocuments({ 
      trip_id, 
      driver_id 
    });

    if (existingBids >= 2) {
      logger.warn('Driver exceeded bid limit for trip', { 
        driver_id, 
        trip_id 
      });
      return res.status(400).json({ 
        message: 'You have reached the bid limit for this trip' 
      });
    }

    // Create new bid
    const bid = new Bid({
      trip_id,
      driver_id,
      vehicle_id,
      amount,
      notes,
      status: 'pending',
      created_at: new Date()
    });

    await bid.save();

    // Publish bid update to MQTT
    const bidData = {
      type: 'new_bid',
      bid: {
        _id: bid._id,
        trip_id: bid.trip_id,
        driver_id: bid.driver_id,
        vehicle_id: bid.vehicle_id,
        amount: bid.amount,
        notes: bid.notes,
        status: bid.status,
        created_at: bid.created_at
      }
    };

    // Publish to MQTT with 30 minutes expiry
    mqttService.publishBidUpdate(trip_id, bidData, {
      expiryInterval: 1800, // 30 minutes
      userProperties: {
        bidType: 'new_bid',
        driverId: driver_id
      }
    });

    logger.info('Bid created and published successfully', { 
      bid_id: bid._id, 
      trip_id, 
      driver_id,
      vehicle_id 
    });

    res.status(201).json({
      message: 'Bid submitted successfully',
      bid: {
        _id: bid._id,
        trip_id: bid.trip_id,
        driver_id: bid.driver_id,
        vehicle_id: bid.vehicle_id,
        amount: bid.amount,
        notes: bid.notes,
        status: bid.status,
        created_at: bid.created_at
      }
    });

  } catch (error) {
    logger.error('Error creating bid:', error);
    res.status(500).json({ 
      message: 'Error creating bid', 
      error: error.message 
    });
  }
};