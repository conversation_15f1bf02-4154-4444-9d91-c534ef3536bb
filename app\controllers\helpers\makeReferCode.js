
// exports.generateTransactionId = function(args){
// 	var result=Number(String(Math.random()).slice(2)) + (Date.now() + Math.round( Date.now())).toString(36);
// 	return(result)

// }
import User from '../../models/user';

exports.makeid = function(){

  // makeid(length) {
     var result           = '';
     var characters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
     var charactersLength = characters.length;
     for ( var i = 0; i < 6; i++ ) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
     }

    	// if (result != undefined) {
    	// 	console.log('1231111111111111111111111111111111111111',result);  

    	// }else{
    	// 	console.log('1231111111111111111122222222222222222222',result);  

    	// }

    	  // User.aggregate([ 
    	  // 			{
                  
       //              $match: {
       //                        'referal_code':result
       //                      }
       //             },
       //            {
       //                 $group: {
       //                      _id: {
                              
       //                      },
       //                      myCount: { $sum: 1 } 
       //                    }
       //            }
       //          }

       //                  ])
       //  .then((trc)=>{
       //      // return(result);

       //  });

     // User.count({'referal_code':result}, function (err, res) {
  

     //  }).then((count) => {
    	// 	// console.log('12311111111111111111113333333333333333333',resulttt);  
     //  		if(count>0){
     //        makeid();
     //       }else{
    	// 	console.log('12311111111111111111113333333333333333333',result);  

     //        return(result);

     //       }
     //        // return resulttt;

     //  });


            // return result;

  }
