import express from 'express';
import CommonSettingsController from '../controllers/commonSettingsController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initcommonSettingsRoutes = () => {
  const commonSettingsRoutes = express.Router();


  commonSettingsRoutes.put('/updateDataForRegister',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForRegister);
  commonSettingsRoutes.put('/updateDataForForgotPassword',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForForgotPassword);
  commonSettingsRoutes.put('/updateDataForTransactionCR',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  CommonSettingsController.updateDataForTransactionCR);
  commonSettingsRoutes.put('/updateDataForTransactionDR',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForTransactionDR);
  commonSettingsRoutes.put('/updateDataForTransactionWithdraw',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForTransactionWithdraw);
  commonSettingsRoutes.put('/updateDataForTransactionRazorpay',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForTransactionRazorpay);
  commonSettingsRoutes.put('/updateDataForTransactionBoostTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForTransactionBoostTrip);
  commonSettingsRoutes.put('/updateDataForTransactionSubscriptionFees',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.updateDataForTransactionSubscriptionFees);
  commonSettingsRoutes.post('/createType',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.createType);
  commonSettingsRoutes.get('/getRegisterationTemplate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getRegisterationTemplate);
  commonSettingsRoutes.get('/getForgotPasswordTemplate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getForgotPasswordTemplate);
  commonSettingsRoutes.get('/getTransactionCRTemplate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionCRTemplate);
  commonSettingsRoutes.get('/getTransactionDRTemplate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionDRTemplate);
  commonSettingsRoutes.get('/getTransactionTemplateWithdraw',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionTemplateWithdraw);
  commonSettingsRoutes.get('/getTransactionTemplateRazorpay',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionTemplateRazorpay);
  commonSettingsRoutes.get('/getTransactionTemplateBoostTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionTemplateBoostTrip);
  commonSettingsRoutes.get('/getTransactionSubscriptionFees',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  CommonSettingsController.getTransactionSubscriptionFees);
  
  // commonSettingsRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleMakerController.update);
  // commonSettingsRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleMakerController.remove);
 // updateTransactionWithdrawTemplate

  return commonSettingsRoutes;
};

export default initcommonSettingsRoutes;
