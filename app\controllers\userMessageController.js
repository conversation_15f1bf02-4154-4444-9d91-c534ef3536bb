import Responder from '../../lib/expressResponder';
import UserMessage from '../models/userMessage';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
var moment = require('moment-timezone');

export default class UserMessageController {
  
  static getAllUserpage(req, res) {
      UserMessage.aggregate([ 
  {
    $match: {
      $or: [
        {
          myId: 'SWARI'
        },
        {
          "senderId": ObjectId(req.params.id)
        },
        {
          "recieverId": ObjectId(req.params.id)
        }
      ]
    }
  },
  {
    "$project": {
      senderId: 1,
      recieverId: 1,
      message: 1,
      createdAt: 1,
      seen:1,
      myId:1,
      count:1,
      fromsenderId: [
        "$recieverId",
        "$senderId"
      ]
    }
  },
  {
    $unwind: "$fromsenderId"
  },
  {
    $sort: {
      "fromsenderId": 1
    }
  },
  {
    $group: {
      _id: "$_id",
      "fromsenderId": {
        $push: "$fromsenderId"
      },
      "recieverId": {
        "$first": "$recieverId"
      },
      "senderId": {
        "$first": "$senderId"
      },
      "message": {
        "$first": "$message"
      },
      "myId": {
        "$first": "$myId"
      },

      "createdAt": {
        "$first": "$createdAt"
      },

      "seen": {
        "$first": "$seen"
      }
    }
  },
  {
    "$sort": {
      "createdAt": -1
    }
  },

  {
    "$group": {
      "_id": "$fromsenderId",
      "recieverId": {
        "$first": "$recieverId"
      },
      "senderId": {
        "$first": "$senderId"
      },
      "message": {
        "$first": "$message"
      },
      "myId": {
        "$first": "$myId"
      },
      "createdAt": {
        "$first": "$createdAt"
      },
       "seen": {
        "$first": "$seen"
      },
      "count": { $sum: 1 } ,

    }
  },
  // {
  //   "$group": 
  //     {
        
  //     // myId: {$ne:req.params.id},
  //     // seen:false,
  //    count: { $sum: 1 } ,
      
  //   }
  // },
   { $lookup:
     {
       from: 'users',
       localField: 'senderId',
       foreignField: '_id',
       as: 'senderDetials'
     }
   },
     { $lookup:
     {
       from: 'users',
       localField: 'recieverId',
       foreignField: '_id',
       as: 'recieveDetials'
     }
   }
])

                  
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    }
    // var usrData =[];
    // var sender ={};
    // UserMessage.find({recieverId:req.params.id})
    // .then((data)=> {usrData=data; return User.findOne({_id:{$in:_.map(data,'senderId')}})})
    // .then((user)=>{ sender = user;var response=[];_.each(usrData,usdata =>{
    //   var trp={_id:usdata._id,
    //     message:usdata.message,
    //     senderId:usdata.senderId,
    //     recieverId:usdata.recieverId,
    //     createdAt:usdata.time,
    //   };
      
    
    //   trp.senderDetials=sender;
         
    //   response.push(trp);
    // });Responder.success(res,response) })
    // .catch((err)=>Responder.operationFailed(res,err))
    
  // }


    static getBroadcastMessages(req, res) {
        UserMessage
        .find({ myId:'SWARI' })
        .sort({ createdAt: 1 })
        .then((messag)=>{         
          Responder.success(res,messag);      
        })
    }

  static count(req, res) {
  }

  

  static show(req, res) {
    console.log("11111111",req.body)
    let senderId=req.body.senderId;
    let recieverId=req.body.recieverId;
    console.log(senderId,recieverId)


    // UserMessage.find({ senderId: { $in: [ senderId, recieverId ] } }, { recieverId: { $in: [ senderId, recieverId ]}} )
    // .sort({ createdAt: 1 })
    // .then((messag)=>Responder.success(res,messag))
    // .catch((err)=>Responder.operationFailed(res,err))

    //let senderId=req.params.senderId;
   // let recieverId=req.params.reciverId;
  if(!( senderId && recieverId ))
  throw new Error('senderId and recieverId are required');
  UserMessage
  .find({ $and: [
    { senderId: { $in: [ senderId, recieverId ] } },
    { recieverId: { $in: [ senderId, recieverId ] } }
  ] })
  .sort({ createdAt: 1 })
  .then((messag)=>{

    // messag.forEach(function(messag,k){
    // // mark all found messages as seen
    // console.log('Beforeupdateseen --- ',messag);
    //   UserMessage.update({_id:messag._id }, {$set: { seen: true }},{multi: true})
    //   .then((messag111)=>{

    //         console.log('updateseen --- ',messag111);
    //         // UserMessage.update({
    //         //   senderId:ObjectId("5cee15726a58861653fe04d8")
    //         //   // $and: [
    //         //   // { senderId: { $in: [ senderId, recieverId ] } },
    //         //   // { recieverId: { $in: [ senderId, recieverId ] } }
    //         //   // ] 
    //         // }, { $set: { seen: true }},{multi: true});  
            Responder.success(res,messag);
    // })
    // })  
  })
  
  }


  
 // static userMessageSeen(req, res) {
 //     let senderId=req.body.senderId;
 //     let recieverId=req.body.recieverId;
 //     console.log('Sender ID:', senderId);
 //   console.log('Receiver ID:', recieverId);
 //  // console.log('MyID:', myId);
//
 //   UserMessage
 //   .update({ $and: [
 //     { senderId: { $in: [ senderId, recieverId ] } },
 //     { recieverId: { $in: [ senderId, recieverId ] } },
 //     { myId: req.body.myId},      
 //     { seen:false}
 //     ] },{$set:{seen:true}},{multi:true})
 //   .then((updatedSeen)=>{
 //     console.log('updatedSeen--- ',updatedSeen);
 //     Responder.success(res,updatedSeen);
 //   })
 //   .catch((err) => {
 //       console.error('Error updating seen status:', err);
 //       Responder.operationFailed(res, err);
 //   });
//
 // }
//


static userMessageSeen(req, res) {
  let senderId = req.body.senderId;
  let recieverId = req.body.recieverId;
  let myId = req.body.myId; // Assuming myId is passed to identify the current user

  console.log('Sender ID:', senderId);
  console.log('Receiver ID:', recieverId);
  console.log('MyID:', myId);

  // Update the seen status only if the current user is the receiver
  if (myId === recieverId) {
      UserMessage.update(
          {
              senderId: senderId,
              recieverId: recieverId,
              seen: false // Only update messages that haven't been seen
          },
          { $set: { seen: true } }, // Set seen to true
          { multi: true } // Update all matching documents
      )
      .then((updatedSeen) => {
          console.log('updatedSeen--- ', updatedSeen);
          Responder.success(res, updatedSeen);
      })
      .catch((err) => {
          console.error('Error updating seen status:', err);
          Responder.operationFailed(res, err);
      });
  } else {
      console.log('Seen status not updated because the current user is not the receiver.');
      Responder.success(res, { message: 'No update needed' });
  }
}

  static create(req, res) {
    
    console.log(req.body);

    UserMessage.create(req.body)
    .then((userMessage)=>Responder.success(res,userMessage))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  static broadcastMessage(req, res) {
    console.log(req.body);
      req.body.createdAt= moment(new Date()).add(5.5, 'hours').toDate();

      // req.body.senderId= '';
      // req.body.recieverId;
    
    UserMessage.create(req.body)
    .then((userMessage)=>
      {
        Responder.success(res,userMessage)

      }
      )
    .catch((err)=>Responder.operationFailed(res,err))
  }


  static getSenderDetail(req, res) {
    console.log(req.body);
    User.findOne({ _id: req.body.senderId })
      .then((user) => Responder.success(res, user.name))
      .catch((err) => Responder.operationFailed(res, err))
  }


  static getRecieverDetail(req, res) {
    console.log(req.body);
    User.findOne({ _id: req.body.recieverId })
      .then((user) => Responder.success(res, user.name))
      .catch((err) => Responder.operationFailed(res, err))
  }


  

  static update(req, res) {
  }

  static remove(req, res) {
  }
}
