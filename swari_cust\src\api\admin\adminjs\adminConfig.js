/**
 * AdminJS Configuration
 * Sets up AdminJS with authentication, resources, and components
 * PRD Reference: Sections 4.11, 10.2
 */

// const AdminJS = require('adminjs');
import AdminJS, {ComponentLoader} from 'adminjs';
// const AdminJSExpress = require('@adminjs/express');
import AdminJSExpress from "@adminjs/express";
// const AdminJSMongoose = require('@adminjs/mongoose');
// console.trace("trace 1");


// const bcrypt = require('bcrypt');
import bcrypt from 'bcrypt';
// const session = require('express-session');
import session from 'express-session'
// const mongoose = require('mongoose');
import mongoose from 'mongoose';

// Import models
import Admin from '../../../models/Admin.js';
import Log from '../../../models/Log.js';

// Import resources
import resources from './resources/index.js';

// Import utilities
import logger from '../../../utils/logger.js';
import config from '../../../config/config.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);



const registerAdminJsAdapterHandler = async() => {
  const AdminJSMongoose  = await import('@adminjs/mongoose') ;
  // Register the mongoose adapter
AdminJS.registerAdapter({
  Database: AdminJSMongoose.Database,
  Resource: AdminJSMongoose.Resource,
});
}
registerAdminJsAdapterHandler()

/**
 * Configure AdminJS
 * @param {Object} app - Express app
 */
const configureAdminJS = (app) => {
  const componentLoader = new ComponentLoader();
  const enhancedDashboardComponent = componentLoader.add(
    'enhancedDashboard', 
    join(__dirname, './components/enhanced-dashboard') // Path to your React component
  );
 
  const logViewerComponent = componentLoader.add(
    'logViewer', 
    join(__dirname, './components/log-viewer') // Path to your React component
  );
  const logAnalyticsComponent = componentLoader.add(
    'logAnalytics', 
    join(__dirname, './components/log-analytics') // Path to your React component
  );
  // AdminJS.bundle('./components/log-analytics')
  
  // Define custom dashboard
  const dashboard = {
    //component: AdminJS.bundle('./components/dashboard')
    component:  enhancedDashboardComponent
  };

  // Define custom branding
  const branding = {
    companyName: 'Swari Taxi Admin',
    logo: false,
    softwareBrothers: false,
    favicon: '/favicon.ico'
  };

  // Define custom theme
  const theme = {
    colors: {
      primary100: '#4268F6',
      primary80: '#5C7DF7',
      primary60: '#7693F8',
      primary40: '#90A8F9',
      primary20: '#ABBEFA',
      grey100: '#151C38',
      grey80: '#424D6A',
      grey60: '#68718D',
      grey40: '#ACAEBF',
      grey20: '#F0F0F7',
      filterBg: '#4268F6',
      accent: '#38CAF1',
      hoverBg: '#4268F6',
    },
  };
  
  // Define custom pages
  const pages = {
    logViewer: {
      component: logViewerComponent,
      handler: async (request, response, context) => {
        return { message: 'Log Viewer Page' };
      },
    },
    logAnalytics: {
      component: logAnalyticsComponent,
      handler: async (request, response, context) => {
        return { message: 'Log Analytics Page' };
      },
    },
  };

  // Create AdminJS instance
  const adminJs = new AdminJS({
    resources,
    dashboard,
    branding,
    theme,
    pages,
    rootPath: '/admin',
  });

  // Setup authentication
  const router = AdminJSExpress.buildAuthenticatedRouter(
    adminJs,
    {
      authenticate: async (email, password) => {
        try {
          const admin = await Admin.findOne({ email });
          
          if (!admin) {
            return false;
          }
          
          if (admin.account_locked) {
            return false;
          }
          
          const matched = await bcrypt.compare(password, admin.password);
          
          if (matched) {
            // Reset login attempts on successful login
            admin.login_attempts = 0;
            admin.last_login = new Date();
            await admin.save();
            
            // Log successful login
            const log = new Log({
              event_type: 'admin_action',
              user_id: admin._id,
              user_type: 'Admin',
              details: { action: 'login', success: true },
              ip_address: '127.0.0.1' // In a real app, get the actual IP
            });
            await log.save();
            
            return admin;
          }
          
          // Increment login attempts on failed login
          admin.login_attempts += 1;
          
          // Lock account after 5 failed attempts
          if (admin.login_attempts >= 5) {
            admin.account_locked = true;
            logger.warn('Admin account locked due to too many failed login attempts', { adminId: admin._id });
          }
          
          await admin.save();
          
          // Log failed login
          const log = new Log({
            event_type: 'admin_action',
            user_id: admin._id,
            user_type: 'Admin',
            details: { action: 'login', success: false },
            ip_address: '127.0.0.1' // In a real app, get the actual IP
          });
          await log.save();
          
          return false;
        } catch (error) {
          logger.error('Error during admin authentication', { error: error.message });
          return false;
        }
      },
      cookiePassword: config.adminJSCookiesSecret,
    },
    null,
    {
      resave: false,
      saveUninitialized: true,
      secret: config.adminJSSessionSecret,
      cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60 * 1000, // 1 hour
      },
      name: 'adminjs',
    }
  );

  // Mount AdminJS router
  app.use(adminJs.options.rootPath, router);

  return adminJs;
};

export default configureAdminJS;

  