angular.module('referralEarningByUser.controllers', [])

    .controller('ReferralIncomeByUserCtrl', function ($scope,APIService, $state,$stateParams) {
        $scope.page = 'main';

      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getAllReferralTransactionsByUser'  
      }).then(function(resp) {
        console.log('getAllReferralTransactions')
        console.log(resp)
        $scope.referralEarningList= resp.data;
      });


    $scope.findUserBusiness = function(user) {
     
     console.log('ddddddddddddddddddddddddddddddddddd')
      console.log(user)
      $state.go('app.businessProfile',{data:JSON.stringify(user)});
    };


});
