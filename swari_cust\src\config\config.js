/**
 * Configuration
 * Defines environment variables and configuration settings
 * PRD Reference: Sections 10.1, 10.2
 */

export default {
  port: process.env.PORT || 3000,
  mongoURI: process.env.MONGO_URI || 'mongodb://localhost:27017/triva',
  jwtSecret: process.env.JWT_SECRET || 'swari_secret_key',
  jwtSwariNodeSecret: process.env.JWT_SWARINODE_SECRET || 'long-live-the-ionic-academy',
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || 'swari_re_key_secret',
  environment: process.env.NODE_ENV || 'development',
  mqttBrokerUrl: process.env.MQTT_BROKER_URL || 'ws://*************:8083',
  mqttPort: process.env.MQTT_PORT || 8083,
  adminJSCookiesSecret: 'swari_adminjs_secret',
  adminJSSessionSecret: 'swari_adminjs_session_secret'
};
