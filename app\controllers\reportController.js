import Responder from '../../lib/expressResponder';
import Report from '../models/report';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class ReportController {

  static pageNew(req, res) {

     Report.aggregate([ 
              {
                $match: {
                          processed: false,
                          delete_status: false
                        }
                    
              },
               { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'reported'
                 }
               },
               { $lookup:
                 {
                   from: 'users',
                   localField: 'reported_by',
                   foreignField: '_id',
                   as: 'reportedBy'
                 }
               }
               ,
               { 
                "$sort": { 
                    "time": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))



   
  }

  
  static pageProcessed(req, res) {

     Report.aggregate([ 
              {
                $match: {
                          processed: true,
                          delete_status: false
                        }
                    
              },
               { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'reported'
                 }
               },
               { $lookup:
                 {
                   from: 'users',
                   localField: 'reported_by',
                   foreignField: '_id',
                   as: 'reportedBy'
                 }
               }
               ,
               { 
                "$sort": { 
                    "time": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))



   
  }


  static count(req, res) {
  }

  static show(req, res) {

    console.log(req.params.user_id)
    Vehicle.find({user_id: req.params.user_id})
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }
 
  static create(req, res) {
   
 
    Report.create(req.body)
       .then((rep)=>Responder.success(res,rep))
       .catch((err)=>Responder.operationFailed(res,err))
      
  }

  static update(req, res) {

  }

  static removeReport(req, res) {
     Report.deleteOne({ _id: req.params.id })
    .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static filterReports(req, res) {
     User.aggregate([ 
                   {
                    $match: {
                              $or: [ 
                               {name: {$regex : "^" + req.body.name,$options: 'i'}},
                               {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                               {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                               
                              ]
                              
                            },
                        
                  }

                        ])
        
        .then((makers)=>{
            var object= [];
                makers.forEach(function (maker, k) {                
                  console.log('ffffffffffffffff',maker.name)
                  object.push(ObjectId(maker._id))
                });

                Report.aggregate([ 
                   {
                    $match: {
                              // $and: [ {'role': {$ne: 'admin'}} ,
                               // user_id: ObjectId(maker._id),
                               user_id: { $in: object }

                              // ]
                              
                            }
                    },   
                             { $lookup:
                               {
                                 from: 'users',
                                 localField: 'user_id',
                                 foreignField: '_id',
                                 as: 'reported'
                               }
                             },
                             { $lookup:
                               {
                                 from: 'users',
                                 localField: 'reported_by',
                                 foreignField: '_id',
                                 as: 'reportedBy'
                               }
                             }
                             ,
                             { 
                              "$sort": { 
                                  "created_at": -1,
                              }   
                            }
                  ])
              .then((product)=> {
                console.log('lengthhhhhh'+product.length)
                // if (product.length > 0) {
                  Responder.success(res,product)
                // }
              }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
  }


  
  static updateReport(req, res) {
    console.log(req.params.id);
    Report.update({ _id: ObjectId(req.params.id) }, { $set: {processed:true,processedComment:req.body.processedComment} })
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
  }


  
  static reportedBy(req, res) {

    Report.aggregate([ 
          {
                $match: {
                    'user_id':ObjectId(req.params.id)
                  }
                
          },
          
             {
                $group: {
                      _id: {
                       
                      },
                        myCount: { $sum: 1 } ,
                    }
              }


                ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   

  }



  
  static hideReport(req, res) {

    Report.update({ _id: ObjectId(req.params.id) }, { $set: {delete_status:true} })
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
   

  }


}
