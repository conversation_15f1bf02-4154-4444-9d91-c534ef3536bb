/**
 * Log Analytics Routes
 * Defines API routes for log analytics features
 * PRD Reference: Sections 10.10
 */

import express from 'express';
const router = express.Router();
import logAnalyticsController from '../controllers/logAnalyticsController.js';
import { verifyToken } from '../../../auth/authMiddleware.js';

// Middleware to verify admin authentication
// const verifyAdmin = authMiddleware.verifyToken(['admin', 'super_admin']);

/**
 * @route GET /api/admin/logs/stats
 * @desc Get log statistics for charts
 * @access Admin only
 */
router.get('/stats', verifyToken, logAnalyticsController.getLogStats);
// console.trace("Trace:");

/**
 * @route GET /api/admin/logs/trends
 * @desc Get time-based log trends for charts
 * @access Admin only
 */
router.get('/trends', verifyToken, logAnalyticsController.getLogTrends);

export default router;