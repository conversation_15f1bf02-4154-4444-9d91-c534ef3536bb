import Responder from '../../lib/expressResponder';
import Payout from '../models/payout';
import Transaction from '../models/transaction';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
import http from 'http';
var dateFormat = require('dateformat');
import async from 'async';


export default class PayoutController {


	static getReferalUsers(req, res) {
       // User.find({"role":"referalUser","wallet_balance":{$gt:0},suspend:false})
           User.aggregate([ 
              {
                    $match: {
                      "role":"referalUser",
                      "wallet_balance":{$gte:200},
                      "suspend":false,
                      "bankDetails.accountName":{$ne:null},
                      "bankDetails.hiddenAccountNo":{$ne:null},
                      "bankDetails.ifsceCode":{$ne:null},
                      "bankDetails.bankName":{$ne:null},
                      }
                    
              },
                { 
                "$sort": { 
                    "created_at": -1,
                } 
              }, 
              // ,
              // { $lookup:
              //    {
              //      from: 'withdDrawWallets',
              //      localField: '_id',
              //      foreignField: 'user_id',
              //      as: 'bankDetails'
              //    }
              //  }



                    ])
     .then((users)=>Responder.success(res,users))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    


    static getLatestRecord(req, res) {
           Payout.find({}).sort({"payout_id": -1}).limit(1)
     .then((users)=>Responder.success(res,users))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static createPayout(){

          User.aggregate([ 
              {
                    $match: {
                      "role":"referalUser",
                      "wallet_balance":{$gt:0},
                      suspend:false

                      }
                    
              }

                    ])
     .then((users)=>
     {
      console.log('users')
      console.log(users)
     }
     )
     .catch((err)=>Responder.operationFailed(res,err))

    }

    static create(req, res) {
      Payout.create(req.body)
      .then((users)=>Responder.success(res,users))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static updateForUser(req, res) {
      // db.payouts.update({"user_id":ObjectId("5cdd239209e48a143bf6f178")},{$set:{"status":false}},{multi: true})
      Payout.update({user_id:req.params.id}, {$set: req.body},{multi: true})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }

      static getPayoutTotal(req, res) {
      
      Payout.aggregate([ 
            {
                $match: {
                  // 'created_at': {$ne: null},

                }
            },
            {
                $group: {
                      _id: {
                      // /  payout_id:  '$payout_id'
                        

                      },
                        total: { $sum: "$amount"  },
                        // obj: { $push : "$$ROOT" }
                      
                    

                    }
                   
            }
            // ,
            //   { 
            //     "$sort": { 
            //         // "_id.payout_id": -1,
            //         "obj.created_at": 1,
                    
            //     } 
            // }, 
           

          ])
      .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }



    static getPayoutCreatedReport(req, res) {
      
      Payout.aggregate([ 
            {
                $match: {
                  // 'created_at': {$ne: null},

                }
            },
            {
                $group: {
                      _id: {
                        payout_id:  '$payout_id'
                        

                      },
                        total: { $sum: "$amount"  },
                        obj: { $push : "$$ROOT" }
                      
                    

                    }
                   
            }
            ,
              { 
                "$sort": { 
                    // "_id.payout_id": -1,
                    "obj.created_at": 1,
                    
                } 
            }, 
           

          ])
      .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static payoutCreatedReportForMonth(req, res) {

    Payout.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'created_at': { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.enddate+'T23:59:59Z') },
                }
            },
            {
                $group: {
                      _id: {
                        //payout_id:  '$payout_id'
                        

                      },
                        total: { $sum: "$amount"  },
                       // obj: { $push : "$$ROOT" }
                      
                    

                    }
                   
            }
            
              // { 
              //   "$sort": { 
              //       // "_id.payout_id": -1,
              //       "obj.created_at": 1,
                    
              //   } 
            // }, 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



    static viewPayout(req, res) {
      console.log('req.params.id'+req.params.id)
      

      Payout.aggregate([ 
            {
                $match: {
                  "payout_id": JSON.parse(req.params.id)

                }
            }
            ,
            { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               }
            // {
            //     $group: {
            //           _id: {
            //             payout_id:  '$payout_id'
                        

            //           },
            //             total: { $sum: "$amount"  },
            //             obj: { $push : "$$ROOT" }
                      
                    

            //         }
                   
            // }
            // ,
            //   { 
            //     "$sort": { 
            //         "_id.payout_id": -1,
            //     } 
            // }, 
           

          ])
      .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static submitForPay(req, res) {
      console.log('submitForPay---------')
      console.log(req.body)


      Payout.find({ _id: { $in: req.body.list } })
      .then((result)=>{
          result.forEach(function (obj, k) {
              User.find({ _id: obj.user_id  })
              .then((user)=>{
                  console.log('aaaaaaaaaaaaa ' ,user[0].phone_number );
                  // send message start
                     var extServerOptionsPost = {
                          hostname: "***************",
                          path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                          method: 'POST',
                          port: '80',
                          headers: {
                            'Content-Type': 'application/json'
                          }
                        }
                    console.log('Email sent: 111111111111');

                        // const token = otplib.authenticator.generate(secret);
                        // console.log(token);

                        var reqPost = http.request(extServerOptionsPost, function (response) {

                          response.on('data', function (data) {
                            console.log("line no: 87 ", data);
                          });
                        });
                        var body =
                          {
                            "smsContent": 'Referral commission of RS ' + obj.amount + ' has been successfully transferred to your bank acccount on or before '+ dateFormat(new Date(), "dd-mm-yyyy h:MM:ss TT") +'.',
                            "routeId": "1",
                            "mobileNumbers": user[0].phone_number,
                            // "mobileNumbers": '**********,**********',
                            "senderId": "SWARII",
                            "signature": "signature",
                            "smsContentType": "english"
                          }
                    console.log('Email sent: **************');

                        reqPost.write(JSON.stringify(body));
                        reqPost.end();
                        reqPost.on('error', function (e) {
                          console.error("line: 102 " + e);
                        });
                    // send message end
              });                
          });                
      })

       Payout.update({ _id: { $in: req.body.list } }, {$set: {"status":true,"updated_at":new Date()}},{multi: true})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static submitForRemarksForMultiple(req, res) {
        async.forEachLimit(req.body.list, 1, function(objId, userCallback){
            async.waterfall([
              function(callback){
                    console.log('objId??',objId)

                Payout.find({_id: ObjectId(objId) })
                  .then((payout) =>
                  {
                    console.log('payout??',payout)
                    
                      callback(null,payout);

                  })
                .catch((err)=>Responder.operationFailed(res,err))

                
                 
              },
              function(payout,callback){
                  Transaction.find({payout_id: payout[0].payout_id ,
                      user_id:ObjectId(payout[0].user_id),
                      due_to_user_name:"Swari"
                     })
                  .then((transaction) =>
                  {
                    console.log('transaction??',transaction)
                      callback(null,transaction,payout);

                  })
                  .catch((err)=>Responder.operationFailed(res,err))

                
              },  
              function(transaction,payout,callback){
                var str1= transaction[0].transaction_reason;
                var str2= ' '+req.body.remarks;
                var str= str1.concat(str2);
                console.log('transaction  ?? ',str)

                Transaction.findOneAndUpdate({payout_id: payout[0].payout_id ,
                  user_id:ObjectId(payout[0].user_id),
                  due_to_user_name:"Swari"
                 },{$set:{transaction_reason:str}}
                 )
                .then((data) =>{
                    console.log('transactiondata??',data)

                  callback(null,payout);

                })
                  .catch((err)=>Responder.operationFailed(res,err))

              },        

              function(payout,callback){  
              console.log('chek payout?? ',payout) 
                 Payout.findOneAndUpdate({ _id: ObjectId(payout[0]._id) } , {$set: {"remarks":req.body.remarks}})
               .then((val)=>{
                  console.log('payout new ?? ',val)

                 callback(null);

               })
                .catch((err)=>Responder.operationFailed(res,err))
               
              },        
              
            ], function (err, result) {
              // result now equals 'done'
              console.log('done')
              Responder.success(res, 'success')

              userCallback();
            });
          });

    }


    

    static viewPayoutForReferralUser(req, res) {
      console.log('req.params.id'+req.params.id)
      

      Payout.aggregate([ 
            {
                $match: {
                  "user_id": ObjectId(req.params.id)

                }
            }
            ,
            { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               }

           

          ])
      .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static addRemarks(req, res) {
      Payout.update({_id:req.body._id}, {$set: {remarks: req.body.remarks}})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
    }


}
