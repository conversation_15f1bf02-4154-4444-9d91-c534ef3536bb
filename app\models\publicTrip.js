import mongoose from 'mongoose';
const Schema = mongoose.Schema;
const moment = require('moment-timezone');
const timeZone = require('mongoose-timezone');

const PublicTripSchema = new Schema({
    // trip_type: String,   
    origin: String,    
    destination: String,   
    address: [], // array of cities
    trip_date: String,
    date_requested: String,
    status:{
       type: Number,
       default: 0
    },
    passengers:{
       type: Number,
       default: 0
    },
    location: {
        coordinates : [],     
        type : {type:String, default:'Point'}, 
    },  
    destination_location: {
        coordinates : [],     
        type : {type:String, default:'Point'}, 
    },   
    contact_no: { type: String, default: null },
    amount: { type: String, default: null },
    exchange: { type: Boolean, default: 0},
    exchange_text: { type: String, default: null},    
    wc:  { type: Boolean, default: 0},    
    car_model: { type: String, default: null},
    created_at:{ type: Date, default: Date.now }, // 1800 seconds = 30 minutes },
    // delete_in:{ type: Date, default: Date.now,  expires: 60 } // 1800 seconds = 30 minutes },
});

// PublicTripSchema.createIndex({ delete_in: 1 }, { expireAfterSeconds: 20 })

PublicTripSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('PublicTrip', PublicTripSchema);
