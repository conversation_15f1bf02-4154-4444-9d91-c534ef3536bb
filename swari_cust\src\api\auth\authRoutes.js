/**
 * Authentication Routes
 * Defines API endpoints for user registration and login
 * PRD Reference: Sections 4.1, 10.2
 */

import {Router} from 'express' ;

import { check } from 'express-validator';
import {registerCustomer,authCustomer,loginCustomer, refreshToken,registerDriver} from './authController.js';

import { validateRequest, verifyToken, isCustomer} from './authMiddleware.js';

const router = Router();


/**
 * @route POST /api/customer/register
 * @desc Customer registration endpoint
 * @access all
 */
router.post(
  '/customer/register',
  [
    check('name', 'Name is required')
    .notEmpty()
    .withMessage('Name is required'),
    check('phone', 'Please enter a valid phone number')
    .notEmpty() // Ensures the field is not empty
    .withMessage('Phone number is required')
    .isMobilePhone()
    .withMessage('Please enter a valid phone number'),
    check('email', 'Please enter a valid email').isEmail(),
    check('appSignature')
    .matches(/^[A-Za-z0-9]{11}$/)
    .withMessage('Must be an 11-character alphanumeric string')
  ],
  validateRequest,
  registerCustomer
);

/**
 * @route POST /api/customer/login
 * @desc Customer login endpoint
 * @access all
 */

router.post(
  '/customer/login',
  [
    check('phone', 'Please enter a valid phone number')
      .notEmpty() // Ensures the field is not empty
      .withMessage('Phone number is required')
      .isMobilePhone()
      .withMessage('Please enter a valid phone number'),
      check('appSignature')
      .matches(/^[A-Za-z0-9]{11}$/)
      .withMessage('Must be an 11-character alphanumeric string')
    
  ],
  validateRequest,
  authCustomer
);

/**
 * @route POST /api/customer/verify-otp
 * @desc Verify otp 
 * @access all
 */

router.post(
    '/customer/verify-otp',
    [ 
      check('phone', 'Please enter a valid phone number')
      .notEmpty() // Ensures the field is not empty
      .withMessage('Phone number is required')
      .isMobilePhone()
      .withMessage('Please enter a valid phone number'),

      check('otp', 'OTP must be a 6-digit number')
      .notEmpty() // Ensures the field is not empty
      .withMessage('OTP is required') // Error message for empty field
      .isLength({ min: 6, max: 6 }) // Ensures exactly 6 characters
      .withMessage('OTP must be exactly 6 digits') // Error for incorrect length
      .isNumeric() // Ensures only numeric values
      .withMessage('OTP must only contain numbers'), // Error for non-numeric values
    ],
    validateRequest,
    loginCustomer

  );

  /**
 * @route POST /api/customer/refresh-token
 * @desc refresh token for customer
 * @access customer only
 */

  router.post('/customer/refresh-token', [
    check('refreshToken', 'Refresh token is required')
        .notEmpty()
        .withMessage('Refresh token cannot be empty')
        .isJWT()
        .withMessage('Invalid refresh token format'),

  ],
  verifyToken, 
  isCustomer,
  refreshToken
 )

// Driver registration endpoint
// router.post(
//   '/driver/register',
//   [
//     check('name', 'Name is required').not().isEmpty(),
//     check('phone', 'Please enter a valid phone number').isMobilePhone(),
//     check('email', 'Please enter a valid email').isEmail()
//   ],
//   registerDriver
// );

export default router;