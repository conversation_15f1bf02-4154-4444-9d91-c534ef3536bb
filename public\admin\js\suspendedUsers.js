angular.module('suspendedUsers.controllers', [])

    .controller('suspendedUsersCtrl', function ($scope, APIService,$state, $stateParams) {
        $scope.page = 'main';




        $scope.getSuspendedUsers = function(prod){
          APIService.setData({
              req_url: PrefixUrl + '/user/getSuspendedUsers/'  
          }).then(function(resp) {
          	$scope.userDetails= resp.data; 
          },function(resp) {
           
          });
        }


    $scope.filterUsers = function() {

      if ($scope.phone_number) {
        var phone_number= $scope.phone_number;
      }else{
        var phone_number= null;
      }
      if($scope.role){
        var role= $scope.role;
      }else{
        var role= null;
      }
      // console.log("role---",role);


       APIService.setData({
          req_url: PrefixUrl + '/user/filterSuspendedUsers' ,data:{phone_number:phone_number,role:role} 
      }).then(function(resp) {
        $scope.userDetails= resp.data;
      },function(resp) {
       
      });
    }


    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }

    $scope.getSuspendedUsers();
     
})