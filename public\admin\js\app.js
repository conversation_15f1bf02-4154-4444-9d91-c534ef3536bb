
// var PrefixUrl="http://localhost:3000";

// var PrefixUrl="http://panel.triva.in";
//var PrefixUrl="http://panel.triva.in";

//var PrefixUrl="http://**************:3000";
var PrefixUrl="https://"+window.location.hostname; //  https://panel.swari.in";
var app= angular.module('triva', ['ui.router','ngDialog', 'app.controllers', 'subscriptionFees.controllers',
  'login.controllers','vehicleList.controllers','users.controllers','transactions.controllers',
   'trips.controllers', 'filter.controllers','tripDetails.controllers',
   'reports.controllers','vehicleDetails.controllers','categoryAdd.controllers','720kb.datepicker','angularSimplePagination',
   'referralEarning.controllers','referralEarningByUser.controllers','userBusinessProfile.controllers',
   'withdrawWallet.controllers','userTrips.controllers','payToBankRemarks.controllers',
   'rateReview.controllers','addUser.controllers','chart.js','notificationlogEntries.controllers',
   'userSuspendRemarks.controllers','addUserByAdmin.controllers','otp.controllers',
   'forgotPassword.controllers','addVehicleMaker.controllers','newPayouts.controllers',
   'referralUsers.controllers','updateProfile.controllers','payoutCreatedReport.controllers',
   'viewPayout.controllers','viewPayoutTransactions.controllers','emailSettings.controllers',
   'reportedComments.controllers','textAngular','suspendedUsers.controllers',
   'subAdminList.controllers','userContacts.controllers','viewUserContacts.controllers',
   'passengerTrips.controllers','gatewaytransactions.controllers','tripDetailsPassenger.controllers','supportTicket.controllers',
   'supportTicketDetail.controllers','states.controllers','updateDateState.controllers',
   'supportTicketClose.controllers','angularMoment','resetPassword.controllers',
   'broadcastMessage.controllers','offers.controllers','offerCategory.controllers',
   'viewOffers.controllers','offerRedeem.controllers','userTracking.controllers','base64'])




.config(function($httpProvider) {
    // $httpProvider.interceptors.push('genericInterceptor');
    // var config = {
    //     apiKey: "AIzaSyDOFWbrz5krxm04rU3Ru-2gamc1z_fSuXI",               // Your Firebase API key
    //     authDomain: "take-me-15b48.firebaseio.com",       // Your Firebase Auth domain ("*.firebaseapp.com")
    //     databaseURL: "https://take-me-15b48.firebaseio.com/"     // Your Firebase Database URL ("https://*.firebaseio.com")
    //  //   storageBucket: "<STORAGE_BUCKET>"  // Your Cloud Storage for Firebase bucket ("*.appspot.com")
    // };
    // firebase.initializeApp(config);
    })

// .factory('genericInterceptor', function($q, $rootScope) {
//     var interceptor = {
//         'request': function(config) {
//             // Successful request method
//             $rootScope.loadCompetition = true;
//             return config; // or $q.when(config);
//         },
//         'response': function(response) {
//             // Successful response
//             $rootScope.loadCompetition = false;
//             return response; // or $q.when(config);
//         },
//         'requestError': function(rejection) {
//             // An error happened on the request
//             // if we can recover from the error
//             // we can return a new request
//             // or promise
//             $rootScope.loadCompetition = false;
//             return response;
//             // Otherwise, we can reject the next
//             // by returning a rejection
//             // return $q.reject(rejection);
//         },
//         'responseError': function(rejection) {
            
//             // Returning a rejection
//             $rootScope.loadCompetition = false;
//             return rejection;
//         }
//     };
//     return interceptor;
// })


.config(function($stateProvider, $urlRouterProvider) {
  $stateProvider

  .state('app', {
    url: '/app',
    abstract: true,
    templateUrl: 'partials/main.html',
    controller: 'AppCtrl'

  })



  .state('login', {
    url: '/login',
    templateUrl: 'partials/login.html',
    controller: 'LoginController'
  })

  .state('app.subscriptionFees', {
    url: '/subscriptionFees',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/subscriptionFees.html',
        controller: 'subscriptionFeesCtrl'
      }
    }
  })

   .state('app.UserDetails', {
    url: '/UserDetails',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/usersDetails.html',
        controller: 'usersCtrl'
      }
    }
  })


  .state('app.SendNotification', {
    url: '/SendNotification',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/sendNotification.html',
        controller: 'usersCtrl'
      }
    }
  })


  .state('app.suspendedUsers', {
    url: '/suspendedUsers',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/suspendedUsers.html',
        controller: 'suspendedUsersCtrl'
      }
    }
  })


  .state('app.subAdminList', {
    url: '/subAdminList',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/subAdminList.html',
        controller: 'subAdminListCtrl'
      }
    }
  })

  
  // .state('app.resetPassword', {
  //   url: '/resetPassword/:data',
  //   // authenticate: true,
  //   // views: {
  //     // 'container': {
  //       templateUrl: 'partials/resetPassword.html',
  //       controller: 'resetPasswordCtrl'
  //     // }
  //   // }
  // })

  .state('resetPassword', {
    url: '/resetPassword/:data',
        templateUrl: 'partials/resetPassword.html',
        controller: 'resetPasswordCtrl'
  })



  .state('app.userContacts', {
    url: '/userContacts',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/userContacts.html',
        controller: 'userContactsCtrl'
      }
    }
  })


  .state('app.states', {
    url: '/states',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/states.html',
        controller: 'statesCtrl'
      }
    }
  })

  .state('app.updateDateState', {
    url: '/updateDateState',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/updateDateState.html',
        controller: 'updateDateStateCtrl'
      }
    }
  })


  .state('app.viewUserContacts', {
    url: '/viewUserContacts/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/viewUserContacts.html',
        controller: 'viewUserContactsCtrl'
      }
    }
  })

  .state('app.referralEarning', {
    url: '/referralEarning',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/referralEarning.html',
        controller: 'ReferralIncomeCtrl'
      }
    }
  })


  .state('app.referralEarningByUser', {
    url: '/referralEarningByUser',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/referralEarningByUser.html',
        controller: 'ReferralIncomeByUserCtrl'
      }
    }
  })

  .state('app.userBusinessProfile', {
    url: '/userBusinessProfile/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/userBusinessProfile.html',
        controller: 'userBusinessProfileCtrl'
      }
    }
  })

  .state('app.withdrawWallet', {
    url: '/withdrawWallet/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/withdrawWallet.html',
        controller: 'withdrawWalletCtrl'
      }
    }
  })

  .state('app.payToBankRemarks', {
    url: '/payToBankRemarks/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/payToBankRemarks.html',
        controller: 'payToBankRemarksCtrl'
      }
    }
  })


  .state('app.rateReview', {
    url: '/rateReview/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/rateReview.html',
        controller: 'rateReviewCtrl'
      }
    }
  })

  .state('app.addUser', {
    url: '/addUser/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/addUser.html',
        controller: 'addUserCtrl'
      }
    }
  })


.state('app.notificationlogEntries', {
    url: '/notificationlogEntries/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/notificationlogEntries.html',
        controller: 'notificationlogEntriesCtrl'
      }
    }
  })


.state('app.broadcastMessage', {
    url: '/broadcastMessage/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/broadcastMessage.html',
        controller: 'broadcastMessageCtrl'
      }
    }
  })


  


.state('app.offers', {
    url: '/offers/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/offers.html',
        controller: 'offersCtrl'
      }
    }
  })

  .state('app.offerRedeem', {
    url: '/offerRedeem/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/offerRedeem.html',
        controller: 'offerRedeemCtrl'
      }
    }
  })




.state('app.offerCategory', {
    url: '/offerCategory/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/offerCategory.html',
        controller: 'offerCategoryCtrl'
      }
    }
  })




.state('app.viewOffers', {
    url: '/viewOffers/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/viewOffers.html',
        controller: 'viewOffersCtrl'
      }
    }
  })

.state('app.userSuspendRemarks', {
    url: '/userSuspendRemarks/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/userSuspendRemarks.html',
        controller: 'userSuspendRemarksCtrl'
      }
    }
  })

.state('app.addUserByAdmin', {
    url: '/addUserByAdmin/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/addUserByAdmin.html',
        controller: 'addUserByAdminCtrl'
      }
    }
  })

.state('otp', {
    url: '/otp/:data',
    // authenticate: true,
    // views: {
      // 'container': {
        templateUrl: 'partials/otp.html',
        controller: 'otpCtrl'
      // }
    // }
  })



.state('forgotPassword', {
    url: '/forgotPassword/:data',
    // authenticate: true,
    // views: {
      // 'container': {
        templateUrl: 'partials/forgotPassword.html',
        controller: 'forgotPasswordCtrl'
      // }
    // }
  })



.state('app.addVehicleMaker', {
    url: '/addVehicleMaker/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/addVehicleMaker.html',
        controller: 'addVehicleMakerCtrl'
      }
    }
  })


  
.state('app.newPayouts', {
    url: '/newPayouts/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/newPayouts.html',
        controller: 'newPayoutsCtrl'
      }
    }
  })




.state('app.referralUsers', {
    url: '/referralUsers/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/referralUsers.html',
        controller: 'referralUsersCtrl'
      }
    }
  })


.state('app.updateProfile', {
    url: '/updateProfile/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/updateProfile.html',
        controller: 'updateProfileCtrl'
      }
    }
  })


.state('app.payoutCreatedReport', {
    url: '/payoutCreatedReport/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/payoutCreatedReport.html',
        controller: 'payoutCreatedReportCtrl'
      }
    }
  })




.state('app.viewPayout', {
    url: '/viewPayout/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/viewPayout.html',
        controller: 'viewPayoutCtrl'
      }
    }
  })


.state('app.viewPayoutTransactions', {
    url: '/viewPayoutTransactions/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/viewPayoutTransactions.html',
        controller: 'viewPayoutTransactionsCtrl'
      }
    }
  })



.state('app.emailSettings', {
    url: '/emailSettings/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/emailSettings.html',
        controller: 'emailSettingsCtrl'
      }
    }
  })



.state('app.supportTicket', {
    url: '/supportTicket/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/supportTicket.html',
        controller: 'supportTicketCtrl'
      }
    }
  })




  .state('app.supportTicketClose', {
    url: '/supportTicketClose/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/supportTicketClose.html',
        controller: 'supportTicketCloseCtrl'
      }
    }
})




.state('app.supportTicketDetail', {
    url: '/supportTicketDetail/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/supportTicketDetail.html',
        controller: 'supportTicketDetailCtrl'
      }
    }
  })


.state('app.reportedComments', {
    url: '/reportedComments/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/reportedComments.html',
        controller: 'reportedCommentsCtrl'
      }
    }
  })

  


  .state('app.userTrips', {
    url: '/userTrips/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/userTrips.html',
        controller: 'userTripsCtrl'
      }
    }
  })

  .state('app.filter', {
    url: '/filter',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/filter.html',
        controller: 'FilterCtrl'
      }
    }
  })

  .state('app.trips', {
    url: '/trips',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/trips.html',
        controller: 'TripsCtrl'
      }
    }
  })



  .state('app.passengerTrips', {
    url: '/passengerTrips',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/passengerTrips.html',
        controller: 'passengerTripsCtrl'
      }
    }
  })
  .state('app.categoryAdd', {
    url: '/categoryAdd',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/categoryAdd.html',
        controller: 'categoryAddCtrl'
      }
    }
  })

   .state('app.transactions', {
    url: '/transactions',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/transactions.html',
        controller: 'transactionsCtrl'
      }
    }
  })
  .state('app.gatewaytransactions', {
    url: '/gatewaytransactions',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/gateway-transactions.html',
        controller: 'gatewaytransactionsCtrl'
      }
    }
  })

   .state('app.vehicleList', {
    url: '/vehicleList/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/vehicleList.html',
        controller: 'VehicleListCtrl'
      }
    }
  })

  .state('app.reports', {
    url: '/reports',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/reports.html',
        controller: 'reportsCtrl'
      }
    }
  })

    .state('app.refer', {
    url: '/refer',
    authenticate: true,
    views: {
      'container': {
        templateUrl: '/refer.html',
       
      }
    }
  })
    


  //   .state('app.subscription', {
  //     url: '/subscription',
  //     authenticate: true,
  //     views: {
  //       'container': {
  //         templateUrl: 'partials/subscription.html',
  //         controller: 'SubscriptionCtrl'
  //       }
  //     }
  // })

  .state('app.tripDetails', {
    url: '/tripDetails/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/tripDetails.html',
        controller: 'tripDetailsCtrl'
      }
    }
})

  
  .state('app.tripDetailsPassenger', {
    url: '/tripDetailsPassenger/:data',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/tripDetailsPassenger.html',
        controller: 'tripDetailsPassengerCtrl'
      }
    }
})  

.state('app.vehicleDetails', {
  url: '/vehicleDetails/:data',
  authenticate: true,
  views: {
    'container': {
      templateUrl: 'partials/vehicleDetails.html',
      controller: 'vehicleDetailsCtrl'
    }
  }
})

  // .state('app.notification', {
  //   url: '/notification',
  //   authenticate: true,
  //   views: {
  //     'container': {
  //       templateUrl: 'partials/Notification.html',
  //       controller: 'NotificationCtrl'
  //     }
  //   }
  // })



  // .state('app.homeSlider', {
  //   url: '/homeSlider',
  //   authenticate: true,
  //   views: {
  //     'container': {
  //       templateUrl: 'partials/homeSlider.html',
  //       controller: 'HomeSliderCtrl'
  //     }
  //   }
  // })

 
  .state('app.services', {
    url: '/services',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/services.html',
        controller: 'ServicesCtrl'
      }
    }
  })

  .state('app.userTracking', {
    url: '/userTracking',
    authenticate: true,
    views: {
      'container': {
        templateUrl: 'partials/userTracking.html',
        controller: 'userTrackingCtrl'
      }
    }
});
  
  if (typeof localStorage === "undefined" || localStorage === null) {
    var LocalStorage = require('node-localstorage').LocalStorage;
    localStorage = new LocalStorage('./scratch');
  }
  if (localStorage.getItem('UserDetails')) {
    var data = JSON.parse(localStorage.getItem('UserDetails'));
   
      $urlRouterProvider.otherwise('/app/UserDetails');
     

  } else {
    $urlRouterProvider.otherwise('/login');
    
 
  }



});


    var userData = JSON.parse(localStorage.getItem('UserDeatails'));
    var withdrawWalletTotal = JSON.parse(localStorage.getItem('withdrawWalletTotal'));
    console.log('withdrawWalletTotal  ---- ',withdrawWalletTotal)
                         



    
    var userData = JSON.parse(localStorage.getItem('UserDeatails'));
    var withdrawWalletTotal = JSON.parse(localStorage.getItem('withdrawWalletTotal'));
    console.log('withdrawWalletTotal  ---- ',withdrawWalletTotal)
      


       app.controller('AppCtrl', ['$http','$scope','$state', function($http,$scope,$state){


          $scope.logout = function() {

            console.log('llllllllllllll');
            localStorage.removeItem("UserDetails");
            localStorage.removeItem("token");
            $state.go('login');
                 
            // $urlRouterProvider.otherwise('/login');

          }
            


          
          // $scope.user = function()

          // {
          //   //$state.go("main.products",{})
          //   $state.go("app.UserDetails",{},{reload:true});
          //   location.reload();
          // }
          //  $scope.add = function()
          // {
          //   //$state.go("main.products",{})
          //   $state.go("app.addUser",{},{reload:true})
          //   location.reload();
          // }
          // $scope.referral = function()
          // {
          //   //$state.go("main.products",{})
          //   $state.go("app.referralUsers",{},{reload:true})
          //   location.reload();
          // }
        // $http.defaults.headers.common['Authorization'] = 'bearer '+userData.token;
          
        $http.get(PrefixUrl+"/trip/getSupportCounts/"+userData.user_details._id).then(function (counts, status, headers, config) { 
          console.log('get_counts???? ',counts.data[0].myCount)
          $scope.totalUnreadTicket= counts.data[0].myCount;
        })

        $http.post(PrefixUrl+"/withdrawWallet/withdrawWalletGetDataForApproveCount").then(function (data, status, headers, config) { 
            if (data.data['0']) {
              console.log('data' ,data.data['0'] )
              $scope.withdrawWalletCount= data.data['0'].myCount;
              localStorage.setItem('withdrawWalletCount', data.data['0'].myCount);
            }else{
              localStorage.setItem('withdrawWalletCount', 0);

            }

            // alert("success",status); 
        },function (data, status, headers, config) { 
            // alert("error"); 
        });

        $scope.withdrawWalletCount= JSON.parse(localStorage.getItem('withdrawWalletCount'));

          // setTimeout(() => {
            setInterval(function(){
              $scope.withdrawWalletCount= JSON.parse(localStorage.getItem('withdrawWalletCount'));

              // app.controller('usersCtrl', function($scope, $http) {
              // console.log('aaaaaaaaaaaaaaaaaaaaaa ',$scope)
              // $http.defaults.headers.common['Authorization'] = 'bearer '+userData.token;
              $http.get(PrefixUrl+"/trip/getSupportCounts/"+userData.user_details._id).then(function (counts, status, headers, config) { 
                console.log('get_counts???? ',counts.data[0].myCount)
                $scope.totalUnreadTicket= counts.data[0].myCount;
              })
                     $http.post(PrefixUrl+"/withdrawWallet/withdrawWalletGetDataForApproveCount").then(function (data, status, headers, config) { 
                        if (data.data['0']) {
                          console.log('data' ,data.data['0'] )
                          $scope.withdrawWalletCount= data.data['0'].myCount;
                          localStorage.setItem('withdrawWalletCount', data.data['0'].myCount);
                        }else{
                          localStorage.setItem('withdrawWalletCount', 0);

                        }

                        // alert("success",status); 
                    },function (data, status, headers, config) { 
                        // alert("error"); 
                    });

                    // $http.post(PrefixUrl+"/SupportTicketComment/totalUnread").then(function (data, status, headers, config) { 
                    //     if (data.data['0']) {
                    //       console.log('data' ,data.data['0'] )
                    //       $scope.totalUnreadSupport= data.data['0'].myCount;
                    //       localStorage.setItem('totalUnreadSupport', data.data['0'].myCount);
                    //     }else{
                    //       localStorage.setItem('totalUnreadSupport', 0);

                    //     }

                    //     // alert("success",status); 
                    // },function (data, status, headers, config) { 
                    //     // alert("error"); 
                    // });
                }, 900000)

                  $scope.user = function()

          {
            //$state.go("main.products",{})
            console.log('uuuuuu', $scope.user);
            $state.go('app.UserDetails',{},{reload:true})
            // .then(function() {
            //   console.log('reload1111111111 ')
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });

            
          }
           $scope.add = function()
          {
            //$state.go("main.products",{})
            $state.go('app.addUser',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             

            
          }
           $scope.referral = function()
          {
            //$state.go("main.products",{})
            $state.go('app.referralUsers',{},{reload:true})
            // .then(function() {
            //   console.log('reload2222222 ')

            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
            
          }
           $scope.transactions = function()
          {
            //$state.go("main.products",{})
            $state.go('app.transactions',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
          }

           $scope.suspendedUsers = function()
          {
            //$state.go("main.products",{})
            $state.go('app.suspendedUsers',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
          }
           $scope.referralEarning = function()
          {
            //$state.go("main.products",{})
            $state.go('app.referralEarning',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
          }
              $scope.addUserByAdmin = function(){
            //$state.go("main.products",{})
            $state.go('app.addUserByAdmin',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
          }
             $scope.subAdminList = function(){
            //$state.go("main.products",{})
            $state.go('app.subAdminList',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });

          }

          $scope.newPayouts = function(){
            //$state.go("main.products",{})
            $state.go('app.newPayouts',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
             $scope.payoutCreatedReport = function(){
            //$state.go("main.products",{})
            $state.go('app.payoutCreatedReport',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
            $scope.withdrawWallet = function(){
            //$state.go("main.products",{})
            $state.go('app.withdrawWallet',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
             $scope.SendNotification = function(){
            //$state.go("main.products",{})
            $state.go('app.SendNotification',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
             
             $scope.notificationlogEntries = function(){
            //$state.go("main.products",{})
            $state.go('app.notificationlogEntries',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.trips = function(){
            //$state.go("main.products",{})
            $state.go('app.trips',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
          $scope.passengerTrips = function(){
            //$state.go("main.products",{})
            $state.go('app.passengerTrips',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
             $scope.vehicleList = function(){
            //$state.go("main.products",{})
            $state.go('app.vehicleList',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
             $scope.categoryAdd = function(){
            //$state.go("main.products",{})
            $state.go('app.categoryAdd',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.addVehicleMaker = function(){
            //$state.go("main.products",{})
            $state.go('app.addVehicleMaker',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.supportTicket = function(){
            //$state.go("main.products",{})
            $state.go('app.supportTicket',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.supportTicketClose = function(){
            //$state.go("main.products",{})
            $state.go('app.supportTicketClose',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.reports = function(){
            //$state.go("main.products",{})
            $state.go('app.reports',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.reportedComments = function(){
            //$state.go("main.products",{})
            $state.go('app.reportedComments',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
          $scope.rateReview = function(){
            //$state.go("main.products",{})
            $state.go('app.rateReview',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
          $scope.updateDateState = function(){
            //$state.go("main.products",{})
            $state.go('app.updateDateState',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.states = function(){
            //$state.go("main.products",{})
            $state.go('app.states',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
              $scope.userContacts = function(){
            //$state.go("main.products",{})
            $state.go('app.userContacts',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
            $scope.viewUserContacts = function(){
            //$state.go("main.products",{})
            $state.go('app.viewUserContacts',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
           $scope.emailSettings = function(){
            //$state.go("main.products",{})
            $state.go('app.emailSettings',{},{reload:true})
            // .then(function() {
            //   setTimeout(function () {
            //     location.reload()
            //   }, 100);            
            // });
             
          }
          $scope.subscriptionFees = function(){
            //$state.go("main.products",{})
            $state.go('app.subscriptionFees',{},{reload:true})
          //   .then(function() {
          //     setTimeout(function () {
          //       location.reload()
          //     }, 100);            
          //   });
             
          }
          $scope.createOffer = function(){
            //$state.go("main.products",{})
            $state.go('app.createOffer',{},{reload:true})
          //   .then(function() {
          //     setTimeout(function () {
          //       location.reload()
          //     }, 100);            
          //   });
             
          }

          $scope.offerCategory = function(){
            //$state.go("main.products",{})
            $state.go('app.offerCategory',{},{reload:true})
          //   .then(function() {
          //     setTimeout(function () {
          //       location.reload()
          //     }, 100);            
          //   });
             
          }
          $scope.viewOffers = function(){
            //$state.go("main.products",{})
            $state.go('app.viewOffers',{},{reload:true})
          //   .then(function() {
          //     setTimeout(function () {
          //       location.reload()
          //     }, 100);            
          //   });
             
          }
          $scope.offerRedeem = function(){
            //$state.go("main.products",{})
            $state.go('app.offerRedeem',{},{reload:true})
          //   .then(function() {
          //     setTimeout(function () {
          //       location.reload()
          //     }, 100);            
          //   });
             
          }

             
          

        }]);




      // setTimeout(() => {

      //   app.controller('usersCtrl', function($scope, $http) {

      //     setInterval(function(){
      //         $http.defaults.headers.common['Authorization'] = 'bearer '+userData.token;

      //         console.log('aaaaaaaaaaaaaaaaaaaaaa ',userData.token)
      //          $http.post("http://localhost:3000/withdrawWallet/withdrawWalletGetDataForApproveCount").then(function (data, status, headers, config) { 
      //             console.log('data' ,data.data['0'].myCount )
      //             // alert("success",status); 
      //         },function (data, status, headers, config) { 
      //             alert("error"); 
      //         });
      //     }, 8000)


      //   });
      // }, 2000)


// app.controller('suspendedUsersCtrl', function($scope, $http) {
//   console.log('aaaaaaaaaaaaa')
//    $http.post("http://localhost:53263/api/Products/").then(function (data, status, headers, config) { 
//       alert("success"); 
//   },function (data, status, headers, config) { 
//       alert("error"); 
//   });
// });
