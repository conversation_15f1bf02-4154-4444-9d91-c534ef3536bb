import express from 'express';
// import ReportCommentController from '../controllers/reportCommentController';
import SupportTicketController from '../controllers/supportTicketController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initsupportTicketRoutes = () => {
  const supportTicketRoutes = express.Router();

  supportTicketRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.page);
  // supportTicketRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.page);
  supportTicketRoutes.get('/:user_id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> , SupportTicketController.getTickets);
  supportTicketRoutes.post('/getTicketsBySubAdmin/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler , SupportTicketController.getTicketsBySubAdmin);
  supportTicketRoutes.post('/getTicketsByAdmin/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler , SupportTicketController.getTicketsByAdmin);

  // supportTicketRoutes.get('/title/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.title);
  //supportTicketRoutes.post('/getSupport/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.getSupport);
  supportTicketRoutes.post('/SupportTicket/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.create);
  supportTicketRoutes.post('/filterSupportTicket/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.filterSupportTicket);
  supportTicketRoutes.put('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.updateTicket);
  supportTicketRoutes.put('/updateForReplyStatus/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.updateForReplyStatus);
  supportTicketRoutes.put('/updateForReplyDate/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.updateForReplyDate);
  supportTicketRoutes.get('/getTicketById/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.getTicketById);
  supportTicketRoutes.post('/getSupportTicketCount/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.getSupportTicketCount);
  supportTicketRoutes.post('/getSupportTicketCountAll/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.getSupportTicketCountAll);

  return supportTicketRoutes;
};

export default initsupportTicketRoutes;
