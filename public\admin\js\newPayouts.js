angular.module('newPayouts.controllers', [])

    .controller('newPayoutsCtrl', function ($scope, APIService, $state,$rootScope) {

      $scope.processing= false;

      $scope.createPayout= function(res){
         APIService.getData({ req_url: PrefixUrl + "/payout/createPayout"}).then(function (res) {
                console.log(res)
           
                   
            
            },function(er){

            })
      }


        $scope.pay = function (users) {
             if (confirm("Are you sure?")) {
                $scope.processing= true;

                  APIService.getData({ req_url: PrefixUrl + "/payout/getLatestRecord"}).then(function (res) {
                    console.log('res.data')
                    console.log(res.data)
                    console.log('res.data')
                    if (res.data.length == 0) {
                        $scope.payout_id= 1; 
                    }
                    else  {
                        $scope.payout_id= res.data[0].payout_id+1; 
                    }

                    console.log('payout_id  '+$scope.payout_id)
                        // $scope.userDetails
                        $scope.userDetails.forEach(function (user, k) {    
                                        
                            console.log('user')
                            console.log(user)
                            APIService.setData({ req_url: PrefixUrl + "/payout/" ,data:{user_id:user._id,amount:user.wallet_balance,payout_id:$scope.payout_id,created_at:new Date()}}).then(function (res) {
                                console.log(res)
                                // $scope.userDetails= res.data;
                                $scope.setTransactions(user.wallet_balance,$scope.payout_id,user._id,k);
                                
                            },function(er){

                            })

                        });



                        // $scope.userDetails.forEach(function (user, k) {                
                        //   $scope.updateTransactions();

                        // });




                
                },function(er){
                 
                })
            }
        }


        $scope.getReferalUsers = function(){
            APIService.getData({ req_url: PrefixUrl + "/payout/getReferalUsers"}).then(function (res) {
                console.log(res)
            $scope.totalPayout= 0;
    			   $scope.userDetails= res.data;
              $scope.userDetails.forEach(function (user, k) {                
                $scope.totalPayout= $scope.totalPayout + user.wallet_balance;
              });

			             
            
            },function(er){

            })

    
        }


        $scope.setTransactions = function(amount,payout_id,user_id,k){

            APIService.setData({
                req_url: PrefixUrl + '/trancsaction',data:{amount:parseFloat(amount).toFixed(2),payout_id:payout_id,transaction_type:"DR",user_id:user_id,transaction_reason:"payout created",transaction_date:new Date(),transaction_id :Number(String(Math.random()).slice(2)) + (Date.now() + Math.round( Date.now())).toString(36),due_to_user_name: 'Swari' }
           },function(resp) {
              // This block execute in case of error.
              console.log('errrrrrrr');
            }).then(function(resp) {
              console.log("====resp======",user_id);
                APIService.setData({
                    req_url: PrefixUrl + '/trancsaction/getTransactionBalance/',
                    data: {user_id: user_id,transaction_id:resp.data.transaction_id}
                }).then(function(resp2) {
                  console.log('resp2')
                  console.log(resp2.data[0].balance)
                    APIService.setData({
                      req_url: PrefixUrl + '/trancsaction/updateTransaction/',
                      data: {balance: resp2.data[0].balance,transaction_id:resp.data.transaction_id}
                    }).then(function(resp3) {
                      console.log('response------ ')
                      console.log(resp3)
                      console.log(resp2)
                        APIService.updateData({
                        req_url: PrefixUrl + '/user/update/'+user_id ,
                         data:{wallet_balance: parseFloat(resp2.data[0].balance).toFixed(2)}
                        }).then(function(resp4) {


                          $scope.getTransactionsForuser(user_id,k);


                          
                       },function(resp) {

                        // This block execute in case of error.

                        });


                   },function(resp) {

                    // This block execute in case of error.

                    });

                    

                   },function(resp) {
                    // This block execute in case of error.
                });
              
              // $scope.add={};
                  // console.log(resp);
            });

        }


        
        $scope.getTransactionsForuser = function(user_id,k2){
            APIService.getData({
            req_url: PrefixUrl + '/trancsaction/'+user_id
          }).then(function(resp) {
            
              resp.data.forEach(function (trc, k) {                
                console.log('tttttt  '+trc.amount)
                if (!trc.status) {
                  $scope.updateTransactions(trc);
                }
                else{
                  console.log('true status----------')
                }
                // trc.amount
              });
              console.log('kkkkkkkkkkkkk22222222222 ',k2)
              console.log('kkkkkkkkkkkkk22222222222 ',$scope.userDetails.length)

                if ((k2+1) == $scope.userDetails.length) {
                  alert('payout created successfully');
                  // location.reload(); 
                }
              // alert('payout created successfully');
              // location.reload(); 

                // $scope.userDetails.forEach(function (user, k) {                
                //     console.log('user')
                //     console.log(user)
                //     APIService.updateData({ req_url: PrefixUrl + "/payout/updateForUser/"+user_id ,data:{status:true}}).then(function (res) {
                //         // console.log(res)
                //         // $scope.userDetails= res.data;
                //         // $scope.setTransactions(user.wallet_balance,$scope.payout_id,user._id);
                    
                //     },function(er){

                //     })

                // });
              
           }).then(function(resp) {
              location.reload(); 

           });
        }





        $scope.updateTransactions = function(trc){

            APIService.setData({
              req_url: PrefixUrl + '/trancsaction/updateTransactionForPayout/',
              data: {transaction_id:trc.transaction_id,status: true,payout_id:$scope.payout_id,user_id:trc.user_id}
            }).then(function(resp3) {
              console.log('response------ ')
              console.log(resp3)
             

                // APIService.updateData({
                // req_url: PrefixUrl + '/user/update/'+user_id ,
                //  data:{wallet_balance: resp2.data[0].balance}
                // }).then(function(resp4) {

                    
                  
               // },function(resp) {

               //  // This block execute in case of error.

               //  });


           },function(resp) {

            // This block execute in case of error.

            });
        }




$scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }






        $scope.getReferalUsers();




})