import express from 'express';
import { registerDriver, loginDriver, updateDriverProfile, getDriverProfile, getVehicleData, createBid } from './driverAuthController.js';
import { validateDriverRegistration, validateDriverLogin, validateDriverBid } from './driverAuthValidation.js';
import { verifySwariNodeToken } from '../auth/authMiddleware.js';

const router = express.Router();

// router.post('/driver/register', validateDriverRegistration, registerDriver);
// router.post('/driver/login', validateDriverLogin, loginDriver);
// router.put('/driver/profile', 
//     verifySwariNodeToken,
//   updateDriverProfile
// );
router.get('/driver/profile', 
    verifySwariNodeToken,
    getDriverProfile
);
// router.get('/vehicle-data', getVehicleData);
router.post('/driver/bid', 
    verifySwariNodeToken,
    validateDriverBid,
    createBid
);

export default router;