import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');



const GatewayTransactionSchema = new Schema({  
  user_id: ObjectId,
  transaction_date: Date,
  transaction_id: String,
  amount: Number,
  gateway_response: {},  
  state: String, 
  status: Number,    
  mobile: String,
  created_at: Date,
  updated_at: Date,
});

GatewayTransactionSchema.plugin(timeZone, { paths: ['created_at','updated_at'] });

export default mongoose.model('GatewayTransaction', GatewayTransactionSchema);
