/**
 * Transaction Resource for AdminJS
 * Defines the AdminJS resource for Transaction model
 * PRD Reference: Sections 4.8, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Transaction from '../../../../models/Transaction.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register currency Value component
const currencyValueComponent = componentLoader.add(
  'currencyValue', 
  join(__dirname, '../components/currency-value') // Path to your React component
);

// Register date Range Filter component
const dateRangeFilterComponent = componentLoader.add(
  'dateRangeFilter', 
  join(__dirname, '../components/date-range-filter') // Path to your component
);
// Register transaction Analytics component
const transactionAnalyticsComponent = componentLoader.add(
  'transactionAnalytics', 
  join(__dirname, '../components/transaction-analytics') // Path to your React component
);






/**
 * Transaction resource configuration for AdminJS
 */
const transactionResource = {
  resource: Transaction,
  options: {
    id: 'transactions',
    navigation: {
      name: 'Wallet',
      icon: 'Money',
    },
    actions: {
      // Customize list action to add filters and pagination
      list: {
        isAccessible: ({ currentAdmin }) => currentAdmin && currentAdmin.role === 'admin',
        before: async (request, context) => {
          // Default sorting by created_at in descending order
          if (!request.query.sort) {
            request.query.sort = { field: 'created_at', direction: 'desc' };
          }
          return request;
        },
      },
      // Disable edit action for transactions (they should be immutable)
      edit: { isAccessible: false },
      // Disable delete action for transactions (they should be immutable)
      delete: { isAccessible: false },
      // Customize show action
      show: {
        isAccessible: ({ currentAdmin }) => currentAdmin && currentAdmin.role === 'admin',
      },
      // Disable new action (transactions are created through the system)
      new: { isAccessible: false },
    },
    properties: {
      // Format ObjectId fields for better display
      _id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
      },
      user_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        reference: 'users',
        isRequired: true,
      },
      user_type: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'customer', label: 'Customer' },
          { value: 'driver', label: 'Driver' },
        ],
      },
      transaction_type: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'bid_hold', label: 'Bid Hold' },
          { value: 'bid_release', label: 'Bid Release' },
          { value: 'bid_deduction', label: 'Bid Deduction' },
          { value: 'refund', label: 'Refund' },
          { value: 'admin_adjustment', label: 'Admin Adjustment' },
        ],
      },
      amount: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number',
        // Format amount as currency
        components: {
          list: currencyValueComponent,
          show: currencyValueComponent,
        },
      },
      reference_id: {
        isVisible: { list: false, filter: true, show: true, edit: false },
      },
      reference_type: {
        isVisible: { list: false, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'Bid', label: 'Bid' },
          { value: 'Trip', label: 'Trip' },
          { value: null, label: 'None' },
        ],
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'textarea',
      },
      balance_before: {
        isVisible: { list: true, filter: false, show: true, edit: false },
        type: 'number',
        components: {
          list: currencyValueComponent,
          show: currencyValueComponent,
        },
      },
      balance_after: {
        isVisible: { list: true, filter: false, show: true, edit: false },
        type: 'number',
        components: {
          list: currencyValueComponent,
          show: currencyValueComponent,
        },
      },
      created_at: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
        components: {
          filter: dateRangeFilterComponent,
        },
      },
      updated_at: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime',
      },
    },
    sort: {
      sortBy: 'created_at',
      direction: 'desc',
    },
    filterProperties: [
      'user_id',
      'user_type',
      'transaction_type',
      'amount',
      'created_at',
      'reference_type',
    ],
    listProperties: [
      '_id',
      'user_id',
      'user_type',
      'transaction_type',
      'amount',
      'balance_before',
      'balance_after',
      'created_at',
    ],
    showProperties: [
      '_id',
      'user_id',
      'user_type',
      'transaction_type',
      'amount',
      'reference_id',
      'reference_type',
      'notes',
      'balance_before',
      'balance_after',
      'created_at',
      'updated_at',
    ],
    // Add custom dashboard component for transaction analytics
    actions: {
      // Add transaction analytics dashboard
      transactionAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        label: 'Transaction Analytics',
        component: transactionAnalyticsComponent,
        handler: async (request, response, context) => {
          return { message: 'Transaction Analytics' };
        },
      },
    },
  },
};

export default transactionResource;