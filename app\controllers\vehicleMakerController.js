import Responder from '../../lib/expressResponder';
import VehicleMaker from '../models/vehicleMaker';
import User from '../models/user';
import _ from "lodash";

export default class VehicleMakerController {


	static create(req, res) {
     VehicleMaker.count({maker:req.body.maker})
     .then((val)=>{
        if (val > 0) {
          Responder.success(res,true)
        }else{
           VehicleMaker.create(req.body)
         .then((trip)=>Responder.success(res,trip))
         .catch((err)=>Responder.operationFailed(res,err))
       }
     })
    }


  static update(req, res) {

    VehicleMaker.count({maker:req.body.maker})
     .then((val)=>{
        if (val > 0) {
          Responder.success(res,true)
        }else{
          VehicleMaker.findOneAndUpdate({_id:req.params.id},{$set:req.body})
           .then((val)=>Responder.success(res,val))
           .catch((err)=>Responder.operationFailed(res,err))
         }
       })
  }

   static remove(req, res) {
    VehicleMaker.remove({_id:req.params.id})
    .then((product)=>Responder.success(res,product))
    .catch((err)=>Responder.operationFailed(res,err))
  }
  
}
