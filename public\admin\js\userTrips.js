angular.module('userTrips.controllers', [])

    .controller('userTripsCtrl', function ($scope,$state, APIService) {

    	
        $scope.page = 'main';
        $scope.upcomingTrip=true;
        $scope.cancelTrip=false;
        $scope.pendingTrip=false;
        $scope.boostedTrip=false;
        $scope.tripHistory=false;
        $scope.bookingRequest=false;
        $scope.count=0;
        $scope.upcomingTripsData;
        $scope.pendingTripsData;
        $scope.bookingRequestsData;
        $scope.cancelTripsData;
        $scope.boostedTripsData;
        $scope.tripsHistoryData;
        
        $scope.settings = {
          currentPage: 0,
          offset: 0,
          pageLimit: 2,
          pageLimits: [2, 5, 10,20,100]
        };
        

      

        // $scope.currentDate= new Date();
        $scope.currentDate= Date.parse(new Date());

        // $scope.getProductDetails = function(){
        //     APIService.getData({ req_url: PrefixUrl + "/trip"}).then(function (res) {
        //         console.log(res)
        //         $scope.tripDetails=res.data;
        //            $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.accepted_user_id && trp.status =='ACCEPTED') {
        //                     // $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
        //                     trp.trp_time= Date.parse(trp.time);
        //                 }
        //             });

        //         console.log($scope.tripDetails)
        //             $scope.upcomingTripsCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.accepted_user_id && trp.status =='ACCEPTED' && trp.trp_time >= $scope.currentDate) {
        //                     $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
        //                 }
        //             });

        //             $scope.pendingTripsCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.status =='ACTIVE'  && trp.trp_time >= $scope.currentDate) {
        //                     $scope.pendingTripsCount = $scope.pendingTripsCount + 1;                    
        //                 }
        //             });

        //             $scope.bookingRequestCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
        //                     $scope.bookingRequestCount = $scope.bookingRequestCount + 1;                    
        //                 }
        //             });

        //             $scope.tripHistoryCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 // if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
        //                     $scope.tripHistoryCount = $scope.tripHistoryCount + 1;                    
        //                 // }
        //             });

        //             $scope.boostedTripCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.is_trip_boost == true) {
        //                     $scope.boostedTripCount = $scope.boostedTripCount + 1;                    
        //                 }
        //             });

        //             $scope.cancelTripCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.status == 'CANCELLED') {
        //                     $scope.cancelTripCount = $scope.cancelTripCount + 1;                    
        //                 }
        //             });
        //             // alert($scope.upcomingTrips);
        //     },function(er){
        //         localStorage.removeItem("UserDeatails");
        //         localStorage.removeItem("token");
        //         $state.go('login');
        //     })
    
        // }
        // $scope.getProductDetails();

        

        $scope.getTripsForSelectedDate=function(startDate,endDate){
          if ($scope.upcomingTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTripsForDate" ,data:{startDate:startDate,endDate:endDate}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.upcomingTripsData= res.data;
            },function(er){

            })
          }

          if ($scope.pendingTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/pendingTripForDate" ,data:{startDate:startDate,endDate:endDate}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.upcomingTripsData= res.data;
            },function(er){

            })
          }
        }


        $scope.cancelTrips=function(){

            $scope.cancelTrip=true;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;

            APIService.setData({ req_url: PrefixUrl + "/trip/cancelTrips"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.cancelTripsData= res.data;
            },function(er){

            })


            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/cancelTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.cancelTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/cancelTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.cancelTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

        }
        
        $scope.bookingTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;

        }

        $scope.upcomingTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=true;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;

            APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTrips"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.upcomingTripsData= res.data;
            },function(er){

            })

            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/upcomingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.upcomingTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/upcomingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.upcomingTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );


        }

        $scope.pendingTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=true;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;
            
            APIService.setData({ req_url: PrefixUrl + "/trip/pendingTrips"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.pendingTripsData= res.data;
            },function(er){

            })



            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/pendingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.pendingTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/pendingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.pendingTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

        }

        $scope.boostedTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=true;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;


            APIService.setData({ req_url: PrefixUrl + "/trip/boostedTrips"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.boostedTripsData= res.data;
            },function(er){

            })

            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/boostedTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.boostedTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/boostedTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.boostedTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );
            
        }


        $scope.tripsHistory=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=true;
            $scope.bookingRequest=false;

            APIService.setData({ req_url: PrefixUrl + "/trip/tripsHistory"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.tripsHistoryData= res.data;
            },function(er){

            })


            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/tripsHistory' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.tripsHistoryData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/tripsHistory' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );
            
        }


        $scope.bookingRequests=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=true;

            APIService.setData({ req_url: PrefixUrl + "/trip/bookingRequests"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                // $scope.bookingRequestsData= res.data;
            },function(er){

            })

            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/bookingRequests' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.bookingRequestsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/bookingRequests' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.bookingRequestsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );
            
        }

        $scope.alltrips = function(prod){
            console.log(prod)
            $state.go("app.tripDetails",{data:JSON.stringify(prod)})
        console.log(prod._id);
        }


});
