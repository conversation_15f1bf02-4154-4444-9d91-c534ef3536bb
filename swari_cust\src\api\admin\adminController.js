/**
 * Admin Controller
 * Handles admin authentication, dashboard data, and system management
 * PRD Reference: Sections 4.11, 10.2
 */

import Admin from '../../models/Admin.js';
import Customer from '../../models/Customer.js';
import Driver from '../../models/Driver.js';
import Transaction from '../../models/Transaction.js';
import CancellationLog from '../../models/CancellationLog.js';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import config from '../../config/config.js';
import logger from '../../utils/logger.js';
import speakeasy from 'speakeasy';
import qrcode from 'qrcode';
import {createTransaction} from '../../services/walletService.js';

/**
 * Admin get dashboard data 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const getDashboardData = async (req, res) => {
  return res.status(200).json({ message: 'function not complete' });
}

/**
 * Admin get All Users 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const getAllUsers = async (req, res) => {
  return res.status(200).json({ message: 'function not complete' });
}




/**
 * Admin update User Status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const updateUserStatus = async (req, res) => {
  return res.status(200).json({ message: 'function not complete' });
}

/**
 * Admin get All Trips
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const getAllTrips = async (req, res) => {
  return res.status(200).json({ message: 'function not complete' });
}

/**
 * Admin get All Transactions 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const getAllTransactions = async (req, res) => {
  return res.status(200).json({ message: 'function not complete' });
}

/**
 * Admin login with MFA
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const login = async (req, res) => {
  try {
    const { username, password, mfa_token } = req.body;

    // Find admin by username
    const admin = await Admin.findOne({ username });
    if (!admin) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check if account is locked
    if (admin.account_locked && admin.account_locked_until > new Date()) {
      return res.status(401).json({
        message: 'Account locked. Try again later',
        locked_until: admin.account_locked_until
      });
    }

    // Verify password
    const isMatch = await admin.comparePassword(password);
    if (!isMatch) {
      // Increment login attempts
      admin.login_attempts += 1;
      
      // Lock account after 5 failed attempts
      if (admin.login_attempts >= 5) {
        admin.account_locked = true;
        admin.account_locked_until = new Date(Date.now() + 30 * 60 * 1000); // Lock for 30 minutes
        await admin.save();
        
        return res.status(401).json({
          message: 'Account locked due to too many failed attempts',
          locked_until: admin.account_locked_until
        });
      }
      
      await admin.save();
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Verify MFA token if MFA is enabled
    if (admin.mfa_enabled) {
      if (!mfa_token) {
        return res.status(400).json({ message: 'MFA token required' });
      }

      const verified = speakeasy.totp.verify({
        secret: admin.mfa_secret,
        encoding: 'base32',
        token: mfa_token
      });

      if (!verified) {
        // Increment login attempts for failed MFA
        admin.login_attempts += 1;
        await admin.save();
        return res.status(401).json({ message: 'Invalid MFA token' });
      }
    }

    // Reset login attempts and update last login
    admin.login_attempts = 0;
    admin.last_login = new Date();
    await admin.save();

    // Generate JWT token
    const token = jwt.sign(
      { id: admin._id, role: admin.role },
      config.jwtSecret,
      { expiresIn: '8h' }
    );

    logger.info('Admin login successful', { adminId: admin._id });
    res.json({
      token,
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        mfa_enabled: admin.mfa_enabled
      }
    });
  } catch (error) {
    logger.error('Admin login error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Setup MFA for admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const setupMFA = async (req, res) => {
  try {
    const adminId = req.user.id;

    // Find admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    // Generate new secret
    const secret = speakeasy.generateSecret({
      name: `Swari Admin:${admin.username}`
    });

    // Save secret to admin
    admin.mfa_secret = secret.base32;
    await admin.save();

    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);

    res.json({
      message: 'MFA setup initiated',
      secret: secret.base32,
      qrCode: qrCodeUrl
    });
  } catch (error) {
    logger.error('MFA setup error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};



/**
 * Verify and enable MFA
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const verifyMFA = async (req, res) => {
  try {
    const { token } = req.body;
    const adminId = req.user.id;

    // Find admin
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return res.status(404).json({ message: 'Admin not found' });
    }

    // Verify token
    const verified = speakeasy.totp.verify({
      secret: admin.mfa_secret,
      encoding: 'base32',
      token
    });

    if (!verified) {
      return res.status(400).json({ message: 'Invalid token' });
    }

    // Enable MFA
    admin.mfa_enabled = true;
    await admin.save();

    res.json({ message: 'MFA enabled successfully' });
  } catch (error) {
    logger.error('MFA verification error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get system logs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getSystemLogs = async (req, res) => {
  try {
    // This would typically integrate with a log aggregation system
    // For demonstration, we'll return a message
    res.json({
      message: 'Log integration required',
      info: 'In a production environment, this would connect to a log aggregation system like ELK or Loki'
    });
  } catch (error) {
    logger.error('Get system logs error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get user statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getUserStats = async (req, res) => {
  try {
    // Count customers and drivers
    const customerCount = await Customer.countDocuments();
    const driverCount = await Driver.countDocuments();

    // Get recent registrations
    const recentCustomers = await Customer.find()
      .sort({ created_at: -1 })
      .limit(5)
      .select('name email phone created_at');

    const recentDrivers = await Driver.find()
      .sort({ created_at: -1 })
      .limit(5)
      .select('name email phone created_at vehicle_details');

    res.json({
      stats: {
        total_customers: customerCount,
        total_drivers: driverCount
      },
      recent_registrations: {
        customers: recentCustomers,
        drivers: recentDrivers
      }
    });
  } catch (error) {
    logger.error('Get user stats error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get transaction statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getTransactionStats = async (req, res) => {
  try {
    // Get transaction counts by type
    const transactionCounts = await Transaction.aggregate([
      {
        $group: {
          _id: '$transaction_type',
          count: { $sum: 1 },
          total_amount: { $sum: '$amount' }
        }
      }
    ]);

    // Get recent transactions
    const recentTransactions = await Transaction.find()
      .sort({ created_at: -1 })
      .limit(10)
      .populate({
        path: 'user_id',
        select: 'name email phone'
      });

    res.json({
      transaction_stats: transactionCounts,
      recent_transactions: recentTransactions
    });
  } catch (error) {
    logger.error('Get transaction stats error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Get cancellation statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getCancellationStats = async (req, res) => {
  try {
    // Get cancellation counts by reason
    const cancellationCounts = await CancellationLog.aggregate([
      {
        $group: {
          _id: '$reason',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get users with high cancellation rates
    const highCancellationUsers = await CancellationLog.aggregate([
      {
        $group: {
          _id: {
            user_id: '$initiator_id',
            user_type: '$initiator_type'
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gt: 3 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);

    // Get recent cancellations
    const recentCancellations = await CancellationLog.find()
      .sort({ created_at: -1 })
      .limit(10)
      .populate({
        path: 'initiator_id',
        select: 'name email phone'
      })
      .populate({
        path: 'trip_id',
        select: 'pickup_location destination trip_type status'
      });

    res.json({
      cancellation_stats: cancellationCounts,
      high_cancellation_users: highCancellationUsers,
      recent_cancellations: recentCancellations
    });
  } catch (error) {
    logger.error('Get cancellation stats error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Update wallet threshold configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateWalletThresholds = async (req, res) => {
  try {
    const { min_balance_for_bidding, min_balance_for_high_value_trips } = req.body;

    // In a real implementation, this would update a configuration collection
    // For demonstration, we'll return a success message
    res.json({
      message: 'Wallet thresholds updated successfully',
      thresholds: {
        min_balance_for_bidding,
        min_balance_for_high_value_trips
      }
    });
  } catch (error) {
    logger.error('Update wallet thresholds error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Adjust user wallet balance (admin action)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const adjustWalletBalance = async (req, res) => {
  try {
    // Support both user_role and user_type parameters for backward compatibility
    const { user_id, user_role, user_type, amount, notes } = req.body;
    
    // Use user_role if provided, otherwise use user_type
    const userRole = user_role || user_type;

    // Validate inputs
    if (!user_id || !userRole || !amount) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    if (!['customer', 'driver'].includes(userRole)) {
      return res.status(400).json({ message: 'Invalid user role' });
    }

    // Create transaction using wallet service
    const transaction = await createTransaction(
      user_id,
      userRole,
      'admin_adjustment',
      amount,
      { notes: notes || 'Admin adjustment' }
    );

    logger.info('Admin wallet adjustment', {
      adminId: req.user.id,
      userId: user_id,
      userRole: user_role,
      amount,
      transactionId: transaction._id
    });

    res.json({
      message: 'Wallet balance adjusted successfully',
      transaction
    });
  } catch (error) {
    logger.error('Adjust wallet balance error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Flag abusive user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const flagAbusiveUser = async (req, res) => {
  try {
    const { user_id, user_role, reason } = req.body;

    // Validate inputs
    if (!user_id || !user_role || !reason) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    if (!['customer', 'driver'].includes(user_role)) {
      return res.status(400).json({ message: 'Invalid user role' });
    }

    // Get user model based on role
    const UserModel = user_role === 'customer' ? Customer : Driver;
    
    // Find user
    const user = await UserModel.findById(user_id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Flag user as abusive
    user.is_flagged = true;
    user.flag_reason = reason;
    user.flagged_at = new Date();
    user.flagged_by = req.user.id;
    await user.save();

    logger.info('User flagged as abusive', {
      adminId: req.user.id,
      userId: user_id,
      userRole: user_role,
      reason
    });

    res.json({
      message: 'User flagged successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        is_flagged: user.is_flagged,
        flag_reason: user.flag_reason,
        flagged_at: user.flagged_at
      }
    });
  } catch (error) {
    logger.error('Flag abusive user error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

/**
 * Create admin user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createAdmin = async (req, res) => {
  try {
    const { username, email, password, role } = req.body;

    // Check if admin exists
    const existingAdmin = await Admin.findOne({ $or: [{ username }, { email }] });
    if (existingAdmin) {
      return res.status(400).json({ message: 'Admin already exists' });
    }

    // Create new admin
    const admin = new Admin({
      username,
      email,
      password,
      role: role || 'admin'
    });

    await admin.save();

    logger.info('Admin created successfully', { adminId: admin._id });
    res.status(201).json({
      message: 'Admin created successfully',
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role
      }
    });
  } catch (error) {
    logger.error('Create admin error', { error: error.message });
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

