/**
 * Log Resource Configuration
 * Defines the AdminJS resource for Log model
 * PRD Reference: Sections 4.11, 10.2
 */

import Log from '../../../../models/Log.js';
import logger from '../../../../utils/logger.js';

/**
 * Log resource configuration
 */
export default {
  resource: Log,
  options: {
    navigation: {
      name: 'System',
      icon: 'Documentation',
    },
    listProperties: ['event_type', 'user_id', 'user_type', 'ip_address', 'timestamp'],
    filterProperties: ['event_type', 'user_id', 'user_type', 'ip_address', 'timestamp'],
    showProperties: ['event_type', 'user_id', 'user_type', 'ip_address', 'timestamp', 'details'],
    actions: {
      list: {
        isAccessible: ({ currentAdmin }) => currentAdmin && currentAdmin.role === 'super_admin',
        handler: async (request, response, context) => {
          const { query = {}, page = 1, perPage = 10 } = request.query;
          const { filters } = request.payload || {};
          
          try {
            const filter = {};
            
            if (filters) {
              if (filters.eventType) {
                filter.event_type = filters.eventType;
              }
              
              if (filters.userId) {
                filter.user_id = filters.userId;
              }
              
              if (filters.ipAddress) {
                filter.ip_address = filters.ipAddress;
              }
              
              if (filters.startDate && filters.endDate) {
                filter.timestamp = {
                  $gte: new Date(filters.startDate),
                  $lte: new Date(filters.endDate)
                };
              } else if (filters.startDate) {
                filter.timestamp = { $gte: new Date(filters.startDate) };
              } else if (filters.endDate) {
                filter.timestamp = { $lte: new Date(filters.endDate) };
              }
            }
            
            const total = await Log.countDocuments(filter);
            const logs = await Log.find(filter)
              .sort({ timestamp: -1 })
              .skip((page - 1) * perPage)
              .limit(perPage);
            
            // Log this access
            const log = new Log({
              event_type: 'admin_action',
              user_id: context.currentAdmin._id,
              user_type: 'Admin',
              details: { action: 'view_logs', filters },
              ip_address: request.ip
            });
            
            await log.save();
            
            return {
              logs,
              total,
              page,
              perPage
            };
          } catch (error) {
            throw new Error(`Error fetching logs: ${error.message}`);
          }
        },
      },
      new: { isAccessible: false },
      edit: { isAccessible: false },
      delete: { isAccessible: false },
    },
  },
};