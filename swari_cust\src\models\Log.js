/**
 * Log Model
 * Defines the schema for system logs
 * PRD Reference: Sections 10.10
 */

import mongoose from 'mongoose';

const LogSchema = new mongoose.Schema({
  event_type: {
    type: String,
    required: true,
    enum: ['transaction', 'cancellation', 'admin_action', 'api_error']
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'user_type'
  },
  user_type: {
    type: String,
    enum: ['Customer', 'Driver', 'Admin', null]
  },
  details: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  ip_address: {
    type: String
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Create indexes for efficient querying
LogSchema.index({ event_type: 1, timestamp: 1 });
LogSchema.index({ user_id: 1, timestamp: 1 });
LogSchema.index({ ip_address: 1, timestamp: 1 });

// module.exports = mongoose.model('Log', LogSchema);
const Log = mongoose.model('Log', LogSchema);
export default Log;