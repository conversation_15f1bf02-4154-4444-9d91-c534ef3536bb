import Responder from '../../lib/expressResponder';
import CommonSettings from '../models/commonSettings';
import User from '../models/user';
import _ from "lodash";

export default class CommonSettingsController {


	static createType(req, res) {
		// CommonSettings.find({type:'user_register'}).
		//  .then((count)=> {
		//  	console.log('count-------'+count)
		//  }).then((trip)=>Responder.success(res,trip))
  //   .catch((err)=>Responder.operationFailed(res,err))


  CommonSettings.count({type:'user_register'})
    .then((count)=>
    {
    	if (count > 0) {
    		Responder.success(res,true);

    	}else{
    		 CommonSettings.create(
                                    {type:'user_register'},{type:'forgot_password'},
                                    {type:'transactionCR'},{type:'transactionDR'},{type:'transactionRazorpay'},
                                    {type:'transactionWithdraw'},{type:'transactionBoostTrip'},
                                    {type:'transactionSubscriptionFees'}
                                    )
     		.then((trip)=>Responder.success(res,trip))
     		.catch((err)=>Responder.operationFailed(res,err))		
    	}
    })
    .catch((err)=>Responder.operationFailed(res,err))
     
    }

	static updateDataForRegister(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'user_register'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }



    static updateDataForForgotPassword(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'forgot_password'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static updateDataForTransactionCR(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionCR'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static updateDataForTransactionDR(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionDR'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static updateDataForTransactionWithdraw(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionWithdraw'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static updateDataForTransactionRazorpay(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionRazorpay'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static updateDataForTransactionBoostTrip(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionBoostTrip'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static getRegisterationTemplate(req, res) {
       CommonSettings.find({type:'user_register'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static getForgotPasswordTemplate(req, res) {
       CommonSettings.find({type:'forgot_password'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static getTransactionCRTemplate(req, res) {
       CommonSettings.find({type:'transactionCR'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static getTransactionDRTemplate(req, res) {
       CommonSettings.find({type:'transactionDR'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    static getTransactionTemplateWithdraw(req, res) {
       CommonSettings.find({type:'transactionWithdraw'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static getTransactionTemplateRazorpay(req, res) {
       CommonSettings.find({type:'transactionRazorpay'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }


    static getTransactionTemplateBoostTrip(req, res) {
       CommonSettings.find({type:'transactionBoostTrip'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    
  static getTransactionSubscriptionFees(req, res) {
       CommonSettings.find({type:'transactionSubscriptionFees'})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    

    static updateDataForTransactionSubscriptionFees(req, res) {
        console.log('req.body.html_content')
        console.log(req.body.html_content)

       CommonSettings.update({type:'transactionSubscriptionFees'},{$set:req.body.html_content})
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }



}
