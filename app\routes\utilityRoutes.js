import express from "express";
import UtilityController from "../controllers/utilityController";
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');
import moment from "moment";

const initUtilityRoutes = () => {
  const utilityRoutes = express.Router();
  // utilityRoutes.post('/images', UtilityController.uploadImage);
  utilityRoutes.post("/images", UtilityController.uploadImageSpaces);

  utilityRoutes.post(
    "/profile",
    (req, res, next) => {
      req.folder = "user";
      return next();
    },
    UtilityController.uploadImageSpaces
  );
  
  utilityRoutes.get("/", (req, res) => {
    return res.send("Hello, this is the API!");
  });

  utilityRoutes.post(
    "/ticket",
    (req, res, next) => {
      let mmt = moment().format("YYYY/M");
      req.folder = `tickets/${mmt}`;

      return next();
    },
    UtilityController.uploadImageSpaces
  );


  utilityRoutes.post(
    "/files",
    passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, 
    (req, res, next) => {
      req.folder = "user-docs";
      return next();
    },
    UtilityController.useUploadFiles
  );

  utilityRoutes.delete(
    "/files/:id",
    passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, 
    (req, res, next) => {
      req.folder = "user-docs";
      return next();
    },
    UtilityController.deleteDoc
  );

  return utilityRoutes;
};

export default initUtilityRoutes;
