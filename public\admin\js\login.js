angular.module('login.controllers', [])

.controller('LoginController', function ($scope, $state, APIService) {
   
        $scope.user = {};
        $scope.loading = false;
        $scope.loginAdmin = function (user) {
            $scope.loading = true;
            $scope.user =user;
            $scope.user.role ="admin"
            APIService.setData({ req_url: PrefixUrl + '/user/login', data:$scope.user }).then(function (res) {
                console.log(res);
              
                     if (res.data.user_details.suspend == true) {
                          // this.alertSuspended();
                          alert("Your account is suspended please contact admin")  ;
                          $scope.loading = false;

                      }else{
                          console.log(res);
                          console.log("---------",res);
                          // if (res.data.user_details.affiliated_user == true) {
                          if (res.data.user_details.role == "admin") {
                              if (!res.data.user_details.active_status) {
                                  alert('User is not active');
                              }else{
                                  if (!res.data.message) {
                                    // localStorage.setItem('UserDeatails', JSON.stringify(res.data.user_details));
                                    // localStorage.setItem('token', res.data.token);
                                          localStorage.setItem('UserDeatails', JSON.stringify(res.data));
                                          localStorage.setItem('token', res.data.token);
                                      console.log("---------1",localStorage.getItem('UserDeatails'));
                                    $scope.user = {};
                                    $state.go("app.addUser");
                                     
                                    // location.reload()
                                   
                                  } else {
                                      console.log("---------2");
                                      alert('Enter valid username or password.');
                                      $scope.user = {};
                                      $scope.loading = false;

                                  }
                              }
                          }else{
                              alert('not valid user');
                              $scope.loading = false;


                          }
                        }
                        

            }, function (er) {
              alert(er.data.msg);
              $scope.loading = false;

            })


        }

        $scope.forgotPassword = function(){
    
            $state.go("forgotPassword");
        }
       


});