/**
 * Authentication Controller
 * Handles user registration and login functionality
 * PRD Reference: Sections 4.1, 10.2
 */

import jwt from "jsonwebtoken";
import bcrypt from "bcrypt";
import { validationResult } from "express-validator";
import Customer from "../../models/Customer.js";
import Driver from "../../models/Driver.js";
import logger from "../../utils/logger.js";
import { generateOTP, verifyOTP } from "../../services/otpService.js";
import config from "../../config/config.js";
import dotenv from "dotenv";
import {createOtpSmsBody} from "../../helpers/sms_helper.js";
import sendTXTGuruSMS  from "../../services/smsService.js";
dotenv.config();

/**
 * Register a new customer
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const registerCustomer = async (req, res) => {
  try {
    const { name, phone, email, appSignature } = req.body;

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({ phone });
    if (existingCustomer) {
      logger.warn("Customer registration attempt with existing phone number", {
        phone,
      });
      return res
        .status(400)
        .json({ message: "Phone number already registered" });
    }

    // Generate OTP
    const { otp, secret, expiresAt } = generateOTP();

    // Create new customer
    const newCustomer = new Customer({
      name,
      phone,
      email,
      role: "customer",
      wallet_balance: 0,
      otp_secret: secret,
      otp_expiry: expiresAt,
      is_profile_complete: true,
    });

    await newCustomer.save();

    // In a real application, send OTP via SMS
    // For development, we'll return it in the response
    logger.info("Customer registered successfully", {
      customerId: newCustomer._id,
    });



   let otpBody = createOtpSmsBody(phone, otp, appSignature);
    sendTXTGuruSMS(process.env.SMS_GURU_TEMPLATEID_REGISTER, otpBody,(error, result) => {
      if (error) {
        console.error("Error sending SMS:", error.message);
        return res.status(500).json({ success: false, error: error.message });
      }
    
      return res.status(201).json({
        message: "Customer registered successfully. Please verify with OTP.",
        otp_method: "sms", // Remove this in production
        customerId: newCustomer._id,
      });
    });

    
  } catch (error) {
    logger.error("Error in customer registration", { error: error.message });
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};

/**
 * generate Otp for login
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const authCustomer = async (req, res) => {
  try {
    // phone number
    const { phone, appSignature } = req.body;
    // check if number exists in customers
    const existingCustomer = await Customer.findOne({ phone });

    logger.info('existing customer', {existingCustomer});
    // generate otp
    const { otp, secret, expiresAt } = generateOTP();
    let otpBody = createOtpSmsBody(phone, otp, appSignature);

    logger.info('otpbody' ,{otpBody});
    // if exits send otp to the number
    if (existingCustomer) {
      
      sendTXTGuruSMS(process.env.SMS_GURU_TEMPLATEID_REGISTER, otpBody, (error, result) => {
        if (error) {
          console.error("Error sending SMS:", error.message);
          return res.status(500).json({ success: false, error: error.message });
        }
      });
      existingCustomer.otp_secret = secret;
      existingCustomer.otp_expiry = expiresAt;
      logger.info('otp secret', {secret})
      await existingCustomer.save();
      
      // and *return otp expiry time
      return res.status(201).json({
        message: "Otp send to " + phone + ". Please verify with OTP.",
        otp_method: "sms", // Remove this in production
        otp_expiry: expiresAt,
      });
    } else {
      // else add customer with number
      const newCustomer = new Customer({
        phone,
        role: "customer",
        wallet_balance: 0,
        otp_secret: secret,
        otp_expiry: expiresAt,
        is_profile_complete: false,
      });
      await newCustomer.save();
      // send otp and return expity time
      sendTXTGuruSMS(process.env.SMS_GURU_TEMPLATEID_REGISTER, otpBody, (error, result) => {
        if (error) {
          console.error("Error sending SMS:", error.message);
          return res.status(500).json({ success: false, error: error.message });
        }
      
        return res.status(201).json({
          message: "Otp send to " + phone + ". Please verify with OTP.",
          otp_method: "sms", // Remove this in production
          otp_expiry: expiresAt,
        }); 
      });  
    }
    
  } catch (error) {
    logger.error("Error in customer registration", { error: error.message });
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};
/**
 * regenerate token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const refreshToken = (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: "Refresh token is required" });
    }

    jwt.verify(
      refreshToken,
      config.jwtRefreshSecret,
      (err, decoded) => {
        if (err) {
          return res
            .status(401)
            .json({ message: "Invalid or expired refresh token" });
        }

        // Generate new JWT token with expiration
        
        const newAccessToken = jwt.sign(
          {  id: decoded.id, role: decoded.role  },
          config.jwtSecret,
          { expiresIn : "24h" }
        );

        const newRefreshToken = jwt.sign(
          {  id: decoded.id, role: decoded.role  },
          config.jwtRefreshSecret,
          { expiresIn : "7d" }
        );

        res.status(200).json({
          accessToken: { token: newAccessToken, expiry: "24h"},
          refreshToken: { token: newRefreshToken, expiry: "7d"},
        });
      }
    );
  } catch (error) {
    console.error("Error refreshing token:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

/**
 * Login a customer
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const loginCustomer = async (req, res) => {
  try {
    const { phone, otp } = req.body;
    // Find customer by phone
    const customer = await Customer.findOne({ phone });
    if (!customer) {
      logger.warn("Customer login attempt with non-existent phone number", {
        phone,
      });
      return res.status(400).json({ message: "Phone number not registered" });
    }
    // Verify OTP
    const isValidOTP = verifyOTP(customer, otp);
    logger.info(isValidOTP);
    if (!isValidOTP) {
      logger.warn("Customer login attempt with invalid OTP", { phone });
      return res.status(400).json({ message: "Invalid OTP" });
    }

    // Clear OTP after successful verification
    customer.otp_secret = undefined;
    customer.otp_expiry = undefined;
    await customer.save();

    // Generate JWT token
    const accessToken = jwt.sign(
      { id: customer._id, role: customer.role },
      config.jwtSecret,
      { expiresIn: "24h" }
    );

    const refreshToken = jwt.sign(
      { id: customer._id, role: customer.role },
      config.jwtRefreshSecret,
      { expiresIn: "7d" }
    )

    logger.info("Customer logged in successfully", {
      customerId: customer._id,
    });

    return res.status(200).json({
      message: "Login successful",
      access_token: {
        token : accessToken,
        expiry : "24h"
      },
      refresh_token: {
        token : refreshToken,
        expiry: "7d"
      },
      user: {
        id: customer._id,
        role: customer.role,
        phone: customer.phone,
        complete: customer.is_profile_complete,
        name: customer.name,
        email: customer.email,
      },
    });
  } catch (error) {
    logger.error("Error in customer login", { error: error.message });
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};

/**
 * Register a new driver
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const registerDriver = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error("Driver registration validation failed", {
        errors: errors.array(),
      });
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, phone, email } = req.body;

    // Check if driver already exists
    const existingDriver = await Driver.findOne({ phone });
    if (existingDriver) {
      logger.warn("Driver registration attempt with existing phone number", {
        phone,
      });
      return res
        .status(400)
        .json({ message: "Phone number already registered" });
    }

    // Generate OTP
    const otp = generateOTP();

    // Create new driver
    const newDriver = new Driver({
      name,
      phone,
      email,
      role: "driver",
      wallet_balance: 0,
      vehicles: [],
      otp: otp,
      otp_expiry: Date.now() + 10 * 60 * 1000, // OTP valid for 10 minutes
    });

    await newDriver.save();

    // In a real application, send OTP via SMS
    // For development, we'll return it in the response
    logger.info("Driver registered successfully", { driverId: newDriver._id });

    return res.status(201).json({
      message: "Driver registered successfully. Please verify with OTP.",
      otp: otp, // Remove this in production
      driverId: newDriver._id,
    });
  } catch (error) {
    logger.error("Error in driver registration", { error: error.message });
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};


