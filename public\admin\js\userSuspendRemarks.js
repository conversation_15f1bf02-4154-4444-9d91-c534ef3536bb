angular.module('userSuspendRemarks.controllers', [])

    .controller('userSuspendRemarksCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.page = 'main';
    	$scope.adminRemarks;
    	var dataJson;
    	$scope.updatedata ={};



    	APIService.setData({
	        req_url: PrefixUrl + '/user/getCityList'  
	    }).then(function(resp) {
	      $scope.cityList= resp.data;
	    },function(resp) {
	      // This block execute in case of error.
	       // $scope.logout = function() {
	      localStorage.removeItem("UserDeatails");
	      localStorage.removeItem("token");
	      $state.go('login');
	      console.log('error')
	    });
    


        if($stateParams.data){

            console.log(JSON.parse($stateParams.data))

            dataJson=JSON.parse($stateParams.data);

		  

        }


    	$scope.submit = function(id) {

      		if (confirm("Are you sure?")) {

				if (dataJson.suspend_remarks) {

					APIService.updateData({
			            req_url: PrefixUrl + '/user/update/'+ dataJson.id,data:{suspend:true}
			        }).then(function(user) {
			        	
			    		APIService.setData({
				            req_url: PrefixUrl + '/suspenduserlog/create/',data:{user_id:dataJson.id,suspend:true,suspend_remarks:$scope.adminRemarks,suspend_remarks_date:new Date(),created_at:new Date()}
				        }).then(function(user) {
				        	console.log('uuuuuu')
				        	console.log(user)
							alert('suspendded')
				          	// $state.go('app.userBusinessProfile');
			  				$state.go('app.userBusinessProfile',{data:JSON.stringify(dataJson.id)});

				          
				           },function(resp) {
				        });      
			           },function(resp) {
			        });

				



					 //  console.log('remarks')
			   //    console.log(dataJson)
			   //      APIService.updateData({
			   //          req_url: PrefixUrl + '/user/update/'+ dataJson.id,data:{suspend:true,suspend_remarks:$scope.adminRemarks,suspend_remarks_date:new Date()}
			   //      }).then(function(user) {
			   //      	console.log('uuuuuu')
			   //      	console.log(user)
						// alert('suspendded')
			   //        	// $state.go('app.userBusinessProfile');
		  		// 		$state.go('app.userBusinessProfile',{data:JSON.stringify(user.data._id)});

			          
			   //         },function(resp) {
			   //      });
				}

				if (dataJson.activate_remarks) {


					APIService.updateData({
			            req_url: PrefixUrl + '/user/update/'+ dataJson.id,data:{suspend:false}
			        }).then(function(user) {
			        	
			    		APIService.setData({
				            req_url: PrefixUrl + '/suspenduserlog/create/',data:{user_id:dataJson.id,suspend:false,activate_remarks:$scope.adminRemarks,activate_remarks_date:new Date(),created_at: new Date()}
				        }).then(function(user) {
				        	console.log('uuuuuu')
				        	console.log(user)
							alert('activated')
				          	// $state.go('app.userBusinessProfile');
			  				$state.go('app.userBusinessProfile',{data:JSON.stringify(dataJson.id)});

				          
				           },function(resp) {
				        });      
			           },function(resp) {
			        });
					 //   console.log('remarks')
			   //    console.log(dataJson)
			   //      APIService.updateData({
			   //          req_url: PrefixUrl + '/user/update/'+ dataJson.id,data:{suspend:false,activate_remarks:$scope.adminRemarks,activate_remarks_date:new Date()}
			   //      }).then(function(user) {
			   //      	console.log('uuuuuu')
			   //      	console.log(user)
						// alert('activated')
			   //        	// $state.go('app.userBusinessProfile');
		  		// 		$state.go('app.userBusinessProfile',{data:JSON.stringify(user.data._id)});

			          
			   //         },function(resp) {
			   //      });
				}
			}

    		  
    	};


    	$scope.getHistory = function(id) {

    		APIService.getData({
	            req_url: PrefixUrl + '/suspendUserLog/'+dataJson.id
	        }).then(function(suspendedLog) {
	        	console.log('suspendedLog')
	        	console.log(suspendedLog.data)
	        	$scope.suspendHistory= suspendedLog.data;
	          
	           },function(resp) {
	        });      

    	}



    	$scope.getHistory(); 

    	$scope.activateUser = function(id) {
    		 
    	};



})
