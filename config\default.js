import packageJSON from '../package.json';
var DBHost=process.env["DBHOST"] || "localhost";

console.log('default-------------------');
module.exports = {
  app: {
    version: packageJSON.version,
    title: 'Triva',
    description: packageJSON.description
  },

  dir_structure: {
    models: 'app/models/**/*.js',
    routes: 'app/routes/**/*Routes.js',
    controllers: 'app/conrollers/**/*Controller.js'
  },

  otpService:{
  
      host: 'http://bulksms.genx-infotech.com',
      AUTH_KEY:'7f1547ae1f47dc1bb0eb7478e2745b71'

  },

  jwtSecret: 'long-live-the-ionic-academy',
  db: {
    // uri: 'mongodb://127.0.0.1:27017/triva',
    uri: DBHost+'/triva',
    options: {
      // user: 'root',
      // pass: 'p@ssw0rd',
      server: { poolSize: 40 },
      replset: { poolSize: 40 }
    },
    debug: false
  },
};
