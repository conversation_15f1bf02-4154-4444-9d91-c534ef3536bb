import express from 'express';
import OfferRedeemedController from '../controllers/offerRedeemedController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initofferRedeemedRoutes = () => {
  const offerRedeemedRoutes = express.Router();

  offerRedeemedRoutes.post('/redeemOffer',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON><PERSON><PERSON> ,  OfferRedeemedController.redeemOffer);
  offerRedeemedRoutes.post('/getRedeemOffer',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  OfferRedeemedController.getRedeemOffer);
  offerRedeemedRoutes.post('/checkRedeemOffer',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  OfferRedeemedController.checkRedeemOffer);
  offerRedeemedRoutes.post('/getViewRedeemOffer',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON>and<PERSON> ,  OfferRedeemedController.getViewRedeemOffer);
  // offerRedeemedRoutes.post('/filterOffer',  OfferRedeemedController.filterOffer);
  // offerRedeemedRoutes.post('/filterOfferCount',  OfferRedeemedController.filterOfferCount);
  
  // offerRedeemedRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  OfferRedeemedController.create);
  
  // rateReviewRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.show);
  // rateReviewRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.page);
  // rateReviewRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.create);
  // rateReviewRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.update);
  // rateReviewRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.remove);
  // rateReviewRoutes.post('/reviewBy',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.reviewBy);
  // rateReviewRoutes.delete('/removeReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.removeReview);
  // rateReviewRoutes.post('/filterReview',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.filterReview);
  // rateReviewRoutes.post('/getByPostMethod',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.getByPostMethod);
  // rateReviewRoutes.post('/reviewCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.reviewCount);



  // rateReviewRoutes.get('/', RateAndReviewController.show);
  // rateReviewRoutes.get('/review/:id', RateAndReviewController.page);
  // rateReviewRoutes.post('/', RateAndReviewController.create);
  // rateReviewRoutes.put('/update/:vehicle_id', RateAndReviewController.update);
  // rateReviewRoutes.delete('/:vehicle_id', RateAndReviewController.remove);

  return offerRedeemedRoutes;
};

export default initofferRedeemedRoutes;
