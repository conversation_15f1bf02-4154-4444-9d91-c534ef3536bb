/**
 * Admin Routes
 * Defines routes for admin authentication and dashboard endpoints
 * PRD Reference: Sections 4.11, 10.2
 */

import express from 'express';
const router = express.Router();
import { getDashboardData, getAllUsers, updateUserStatus, getAllTrips, getAllTransactions} from './adminController.js';

import { verifyToken } from '../auth/authMiddleware.js';

// Middleware to verify admin authentication
// const verifyAdmin = authMiddleware.verifyToken(['admin', 'super_admin']);

/**
 * @route GET /api/admin/dashboard
 * @desc Get admin dashboard data
 * @access Admin only
 */

router.get('/dashboard', verifyToken, getDashboardData);

/**
 * @route GET /api/admin/users
 * @desc Get all users (customers and drivers)
 * @access Admin only
 */
router.get('/users', verifyToken, getAllUsers);

/**
 * @route PUT /api/admin/users/:id/status
 * @desc Update user status (suspend/activate)
 * @access Admin only
 */
router.put('/users/:id/status', verifyToken, updateUserStatus);

/**
 * @route GET /api/admin/trips
 * @desc Get all trips with filters
 * @access Admin only
 */
router.get('/trips', verifyToken, getAllTrips);

/**
 * @route GET /api/admin/transactions
 * @desc Get all transactions with filters
 * @access Admin only
 */
router.get('/transactions', verifyToken, getAllTransactions);

// Mount log analytics routes
// router.use('/logs', logAnalyticsRoutes);

export default router;