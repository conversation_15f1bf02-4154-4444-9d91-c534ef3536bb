import Responder from '../../lib/expressResponder';
import UserContacts from '../models/userContacts';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class UserContactsController {

    static create(req, res) {
      //  console.log('contactsdata --  ', req.user)
     			
     			var obj= {};
      			obj.user_id= req.body.user_id;// req.body.contacts.user_id;
      // console.log('contactsdata --1  ', obj)
      // console.log('contactsdata --2  ', req.body.contacts[0].user_id)


      if(req.body.contacts && req.body.contacts.length) {
          req.body.contacts.forEach(function (cont, k) {
          // console.log(cont)
          console.log(cont.displayName, cont.phones)
          obj.contact_name= cont.displayName;

          if (cont.phones && cont.phones.length) {
              console.log('contact--  '+cont.phones[0].number);

              obj.contact_number= cont.phones[0].normalizedNumber || cont.phones[0].number;
        }

            UserContacts.create(obj)
            .then((resp)=>{
              // console.log('resp- ',resp);
            })
            .catch((err)=>Responder.operationFailed(res,err))
        });  
      }              

     	console.log('user_id -- ',req.body.contacts.user_id);
     	Responder.success(res,null)


    }


    
    static show(req, res) {


      UserContacts.aggregate([ 
            {
                $match: {

                }
            },
             // { 
             //    "$sort": {  
             //        "transaction_date": 1,
             //    } 
             //  },
            {
                 $group: {
                      _id: {
                        // user_id: {
                          user_id: '$user_id'
                        // }
                      },
                      user_id: {
                          $first: '$user_id'
                      },
                        // total: { $sum: { $add: ["$amount"] } },
                        myCount: { $sum: 1 } ,
                        // obj: { $push : "$$ROOT" }
                    }
            },
             { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'userDetails'
               }
              },

      ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
       // UserContacts.find()
       // .then((resp)=>{
       //    Responder.success(res,resp)
       //  // console.log('resp- ',resp);
       // })
    }



    

    static showAllUserContacts(req, res) {

        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

      UserContacts.aggregate([ 
            {
                $match: {
                  user_id: ObjectId(req.body.user_id)
                }
            },
             // { 
             //    "$sort": {  
             //        "transaction_date": 1,
             //    } 
             //  },
            
             { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'userDetails'
               }
              },

      ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    
    }


    static showAllUserContactsAll(req, res) {

        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

      UserContacts.aggregate([ 
            {
                $match: {
                }
            },
             // { 
             //    "$sort": {  
             //        "transaction_date": 1,
             //    } 
             //  },
            
             { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'userDetails'
               }
              },

      ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    
    }



  static userContactsCount(req, res) {

    UserContacts.aggregate([ 
            {
                  $match: {
                    }
                  
            },
             {
               $group: {
                    _id: {
                      
                    },
                    myCount: { $sum: 1 } ,
                  }
            },

              ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))

  }




  



    static filterUserContacts(req, res) {

        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

          UserContacts.aggregate([ 
                {
                    $match: {
                              $or: [ 
                                {contact_name: {$regex : "^" + req.body.name,$options: 'i'}},
                                {contact_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},

                              ],

                      user_id: ObjectId(req.body.user_id)
                    }
                },
                 // { 
                 //    "$sort": {  
                 //        "transaction_date": 1,
                 //    } 
                 //  },
                
                 { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                  },

          ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }


    
    static showAllFiltered(req, res) {

        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

      UserContacts.aggregate([ 
            {
                $match: {
                  contact_name: {$regex : "^" + req.body.name,$options: 'i'}

                }
            },
             // { 
             //    "$sort": {  
             //        "transaction_date": 1,
             //    } 
             //  },
            
             { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'userDetails'
               }
              },

      ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    
    }


    
    static filterUserContactsAll(req, res) {

        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

          UserContacts.aggregate([ 
                {
                    $match: {
                            $or: [ 
                              {contact_name: {$regex : "^" + req.body.name,$options: 'i'}},
                              {contact_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},

                            ]
                    }
                },
                 // { 
                 //    "$sort": {  
                 //        "transaction_date": 1,
                 //    } 
                 //  },
                
                 { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                  },

          ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }



    static filterUserContactsAllCount(req, res) {

       

          UserContacts.aggregate([ 
                {
                    $match: {
                            $or: [ 
                              {contact_name: {$regex : "^" + req.body.name,$options: 'i'}},
                              {contact_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},

                            ]
                    }
                },
                {
                     $group: {
                          _id: {
                              user_id: '$user_id'
                          },
                          user_id: {
                              $first: '$user_id'
                          },
                            myCount: { $sum: 1 } ,
                        }
                }
          ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }

    
    static showForUserCount(req, res) {

      UserContacts.aggregate([ 
            {
                $match: {
                  user_id: ObjectId(req.body.user_id)
                }
            },
            {
                 $group: {
                      _id: {
                          user_id: '$user_id'
                      },
                      user_id: {
                          $first: '$user_id'
                      },
                        myCount: { $sum: 1 } ,
                    }
            }

      ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    
    }


    
     static filterUserContactsCount(req, res) {
          UserContacts.aggregate([ 
                {
                    $match: {
                              $or: [ 
                                {contact_name: {$regex : "^" + req.body.name,$options: 'i'}},
                                {contact_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},

                              ],

                      user_id: ObjectId(req.body.user_id)
                    }
                },
                
               {
                 $group: {
                          _id: {
                              user_id: '$user_id'
                          },
                          user_id: {
                              $first: '$user_id'
                          },
                            myCount: { $sum: 1 } ,
                        }
                }


          ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }





}
