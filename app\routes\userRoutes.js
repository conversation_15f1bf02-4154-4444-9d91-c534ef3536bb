// import express from 'express';
import UserController from '../controllers/userController';
// var Parent = require('../controllers/userController');


var express         = require('express'),
    routes          = express.Router();
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');



const initUserRoutes = () => {
  const userRoutes = express.Router();




// router.get('/userProfile',jwtHelper.verifyJwtToken, ctrlUser.userProfile);

  // userRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.show);
  //   console.log('res.params--------1');
  //   // console.log(res);
  //      UserController.show
  // });
  
  // userRoutes.get('/boostfees/:id',passport.authenticate('jwt', { session: false }), jwt<PERSON>uth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UserController.getSubscriptionFess);
  //   console.log('res.params--------2');
  //   // console.log(res);
  //   // userRoutes.get('/boostfees/:id', UserController.getSubscriptionFess);
  //     UserController.getSubscriptionFess;
  // });

  // userRoutes.get('/allusers/admin',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllUsers);
  //   console.log('res.params--------3');
  //   // console.log(res);
  //      UserController.showAllUsers
  // });
  userRoutes.post('/txn-create', UserController.initiateTxn);
  userRoutes.post('/txn-initiated', UserController.createdPayment);
  userRoutes.post('/txn-status', UserController.checkTxnStatus);
  userRoutes.post('/txn-webhook/:phone', UserController.checkPhonePayTransaction);
  
  userRoutes.get('/settings',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSettings);
  userRoutes.post('/settings',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.updateSettings);
  userRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.show);
  userRoutes.get('/allusers/admin',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllUsers);  
  userRoutes.post('/forgotPassword', UserController.forgotPassword);
  userRoutes.post('/resetPassword', UserController.resetPassword);
  userRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.create);
  userRoutes.post('/otp', UserController.sendOtp);
  userRoutes.post('/resendotp', UserController.resendOtp);
  userRoutes.put('/rate/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.updateForRate);
  userRoutes.post('/checkotp', UserController.checkOtp);
  userRoutes.post('/checkotpforgot', UserController.checkOtpForgot);
  userRoutes.get('/usersvehicle/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllVehicleByUsers);
  userRoutes.post('/appUsersCount/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getAppUsersCount);
  userRoutes.post('/getAppUsersCountForFirstTime/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getAppUsersCountForFirstTime);
  userRoutes.post('/referralUsersCount/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferralUsersCount);
  userRoutes.post('/getReferralUsersCountForFirstTime/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferralUsersCountForFirstTime);
  userRoutes.post('/subAdminUsersCount/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.subAdminUsersCount);
  userRoutes.post('/subAdminUsersCountForFirstTimeall/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.subAdminUsersCountForFirstTime);
  userRoutes.post('/usersvehiclePagination/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllVehicleByUsersPagination);
  userRoutes.post('/showAllVehicleByReferralUsersPagination/all',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllVehicleByReferralUsersPagination);
  userRoutes.post('/showAllSubAdminUsersPagination',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllSubAdminUsersPagination);
  userRoutes.get('/usersreport/user',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.showAllReportedUsers);
  userRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.update);

  userRoutes.put('/updateforwallet/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.updateForWallet);
  userRoutes.put('/subscribefees/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.updateForWalletWhenUserNotSubscribe);
  userRoutes.delete('/:userId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.remove);
  userRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReview);
  userRoutes.put('/refercode/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.checkReferCode);
  userRoutes.get('/refercode/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.checkAllUserReferCode);
  userRoutes.get('/referuser/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.checkAllUserReferCode);
  userRoutes.get('/boostfees/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSubscriptionFess);
  userRoutes.get('/checkMobile/:id', UserController.checkMobile);
  userRoutes.get('/checkEmail/:id', UserController.checkEmail);
  userRoutes.post('/checkEmailForUser/', UserController.checkEmailForUser);
  userRoutes.post('/saveFcmToken',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.saveFcmToken);
  userRoutes.post('/walletCheck',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.walletCheck);
  userRoutes.post('/getToken', UserController.getToken);
  userRoutes.post('/checkUserExist', UserController.checkUserExist);
  userRoutes.post('/getUsersByReferCode',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUsersByReferCode);
  userRoutes.get('/getUserById/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getUserById);
  userRoutes.post('/getUsersForSelectedDate/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getUsersForSelectedDate);
  userRoutes.post('/sendPushNotification',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendPushNotification);
  userRoutes.post('/getStateUsersFcmToken',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getStateUsersFcmToken);
  userRoutes.post('/sendPushNotificationToState',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendPushNotificationToState);
  userRoutes.post('/sendEmailToSelectedUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendEmailToSelectedUsers);
  userRoutes.post('/getUsersByStateForEmail',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getUsersByStateForEmail);
  userRoutes.post('/sendEmailToState',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendEmailToState);
  userRoutes.post('/sendMessageToSelectedUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendMessageToSelectedUsers);
  userRoutes.post('/getUsersByStateForMessage',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getUsersByStateForMessage);
  userRoutes.post('/sendMessageToState',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.sendMessageToState);
  userRoutes.post('/getAllUsersOnce',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getAllUsersOnce);
  userRoutes.post('/getCityList',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getCityList);
  userRoutes.post('/getUsersByCityForEmail',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUsersByCityForEmail);
  userRoutes.post('/getUsersByCityForNotification',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUsersByCityForNotification);
  userRoutes.post('/sendPushNotificationToCity',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendPushNotificationToCity);
  userRoutes.post('/getReferuser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferuser);
  userRoutes.post('/filterUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.filterUsers);
  userRoutes.post('/filterReferralUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.filterReferralUsers);
  userRoutes.post('/filterSubAdminUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.filterSubAdminUsers);
  userRoutes.post('/userCount/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.userCount);
  userRoutes.post('/getUsersByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUsersByMonth);
  userRoutes.post('/getAffiliateUsersByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getAffiliateUsersByMonth);
  userRoutes.post('/totalReferralIncome/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalReferralIncome);
  userRoutes.get('/totalReferralIncome/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUserTotalReferralIncome);
  userRoutes.post('/totalGst/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalGst);
  userRoutes.post('/totalBoostedIncome/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalBoostedIncome);
  userRoutes.post('/getReferralIncomeByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferralIncomeByMonth);
  userRoutes.get('/getReferralIncomeByMonth/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferralIncomeByMonthGetMethod);
  userRoutes.post('/getWithdrawTotal/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getWithdrawTotal);
  userRoutes.post('/getWithdrawAmountByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getWithdrawAmountByMonth);
  userRoutes.post('/getBoostTripGstTotal/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getBoostTripGstTotal);
  userRoutes.post('/getSubscriptionTotal/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSubscriptionTotal);
  userRoutes.post('/getSubscriptionByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSubscriptionByMonth);
  userRoutes.post('/getBoostIncomeByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getBoostIncomeByMonth);
  userRoutes.post('/getTotalGstByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getTotalGstByMonth);
  userRoutes.post('/getTotalTripBoostGstByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getTotalTripBoostGstByMonth);
  userRoutes.post('/checkValidUser/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.checkValidUser);
  userRoutes.get('/getreferuser/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getreferuserGetMethod);
  userRoutes.post('/getReferuserPagination/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferuserPagination);
  // userRoutes.post('/getDashboardUsersCountForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getDashboardUsersCountForSelectedMonth);
  userRoutes.post('/userCountForMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.userCountForMonth);
  userRoutes.post('/totalReferralIncomeForMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalReferralIncomeForMonth);
  userRoutes.post('/totalBoostedIncomeForMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalBoostedIncomeForMonth);
  userRoutes.post('/getWithdrawTotalForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getWithdrawTotalForSelectedMonth);
  userRoutes.post('/getBoostTripGstTotalForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getBoostTripGstTotalForSelectedMonth);
  userRoutes.post('/totalGstForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.totalGstForSelectedMonth);
  userRoutes.post('/getSubscriptionTotalForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSubscriptionTotalForSelectedMonth);
  userRoutes.post('/getUsersByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getUsersByMonthForSelectedMonth);
  userRoutes.post('/getReferalUsersByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferalUsersByMonthForSelectedMonth);
  userRoutes.post('/getReferralIncomeByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getReferralIncomeByMonthForSelectedMonth);
  userRoutes.post('/getSubscriptionByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getSubscriptionByMonthForSelectedMonth);
  userRoutes.post('/getBoostIncomeByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getBoostIncomeByMonthForSelectedMonth);
  userRoutes.post('/getTotalGstByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getTotalGstByMonthForSelectedMonth);
  userRoutes.post('/getTotalTripBoostGstByMonthForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getTotalTripBoostGstByMonthForSelectedMonth);
  userRoutes.post('/getIncomeForReferralUserByMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getIncomeForReferralUserByMonth);
  userRoutes.post('/getWithdrawRequestTotal/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getWithdrawRequestTotal);
  userRoutes.post('/sendEmailAddBalance/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendEmailAddBalance);
  userRoutes.post('/sendEmailAddBalanceDR/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendEmailAddBalanceDR);
  userRoutes.post('/sendEmailBoostTrip/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendEmailBoostTrip);
  userRoutes.post('/sendEmailSubscriptionFees/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendEmailSubscriptionFees);
  userRoutes.post('/sendEmailRazorpay/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.sendEmailRazorpay);
  userRoutes.post('/getSuspendedUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.getSuspendedUsers);
  userRoutes.post('/filterSuspendedUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserController.filterSuspendedUsers);

  userRoutes.post('/getWithdrawRequestTotalForSelectedMonth/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getWithdrawRequestTotalForSelectedMonth);
  userRoutes.post('/registerUserByAdmin', UserController.registerUserByAdmin);
  userRoutes.post('/filterSubscribedUsers', UserController.filterSubscribedUsers);
  userRoutes.get('/getUserTotalReferralUsers/:id', UserController.getUserTotalReferralUsers);
  userRoutes.post('/getTotalReferralCommission', UserController.getTotalReferralCommission);
  userRoutes.post('/checkAppVersion', UserController.checkAppVersion);
  // userRoutes.get('/cronJob/:id', UserController.cronJob);
  userRoutes.get('/cronJobUG/:id', UserController.cronJobUG);
  userRoutes.get('/cronJobCheckSubscription/:id', UserController.cronJobCheckSubscription);
  userRoutes.post('/checkRefferCodeExist', UserController.checkRefferCodeExist);
  userRoutes.post('/userCountAllCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.userCountAllCount);
  userRoutes.post('/userAllCountForMonth',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.userAllCountForMonth);
  userRoutes.get('/getUserForPhoneNumber/:phone_number', UserController.getUserForPhoneNumber);
  userRoutes.post('/generateQRCode/', UserController.generateQRCode);
  userRoutes.post('/getAdmin',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getAdmin);
  userRoutes.put('/stateUpdate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.stateUpdate);
  userRoutes.put('/updateUserExpiry/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.updateUserExpiry);
  userRoutes.get('/currentDate/:id', UserController.currentDate);
  userRoutes.get('/dashboardBackend/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.dashboardBackend);
  userRoutes.post('/backendRazorpay/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.backendRazorpay);
  userRoutes.post('/boostTripBackend/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.boostTripBackend);
  userRoutes.get('/cronTripArchieveUpdate/:id', UserController.cronTripArchieveUpdate);
  userRoutes.post('/checkRPTransaction',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.checkRPTransaction);
  userRoutes.post('/getTotalSMSBalance',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getTotalSMSBalance);
  userRoutes.post('/getreferuserCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.getreferuserCount);
  userRoutes.post('/contaboSearch',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, UserController.contaboSearch);

  console.log('eeeeer'+userRoutes)


  // userRoutes.get('/:id', UserController.show);
  // userRoutes.get('/allusers/admin', UserController.showAllUsers);
  // userRoutes.post('/login', UserController.userLogin);
  // userRoutes.post('/forgotPassword', UserController.forgotPassword);
  // userRoutes.post('/resetPassword', UserController.resetPassword);
  // userRoutes.post('/', UserController.create);
  // userRoutes.post('/otp', UserController.sendOtp);
  // userRoutes.put('/report/:id', UserController.updateForReport);
  // userRoutes.put('/rate/:id', UserController.updateForRate);
  // userRoutes.post('/checkotp', UserController.checkOtp);
  // userRoutes.post('/checkotpforgot', UserController.checkOtpForgot);
  // userRoutes.get('/usersvehicle/all', UserController.showAllVehicleByUsers);
  // userRoutes.get('/usersreport/user', UserController.showAllReportedUsers);
  // userRoutes.put('/update/:id', UserController.update);
  // userRoutes.put('/updateforwallet/:id', UserController.updateForWallet);
  // userRoutes.put('/subscribefees/:id', UserController.updateForWalletWhenUserNotSubscribe);
  // userRoutes.delete('/:userId', UserController.remove);
  // userRoutes.get('/review/:id', UserController.getReview);
  // userRoutes.put('/refercode/:id', UserController.checkReferCode);
  // userRoutes.get('/refercode/:id', UserController.checkAllUserReferCode);
  // userRoutes.get('/boostfees/:id', UserController.getSubscriptionFess);
  // userRoutes.get('/checkMobile/:id', UserController.checkMobile);
  // userRoutes.get('/checkEmail/:id', UserController.checkEmail);
  // userRoutes.post('/saveFcmToken', UserController.saveFcmToken);


  userRoutes.get('/swariconfig', (req, res) => {
    const appConfig = {
      contabo: 'https://contabo.swari.in',
    }

    return res.send({
      data: appConfig
    });
  });

  userRoutes.get('/', (req, res) => {
      return res.send('Hello, this is the API!');
  });

  userRoutes.get('/appconfig', (req, res) => {
    const appConfig = {
      contabo: 'https://contabo.swari.in',
    }

    return res.send({
      data: appConfig
    });
  });
   
  userRoutes.post('/register', UserController.registerUser);
  userRoutes.post('/login', UserController.loginUser);
   
  userRoutes.get('/special', passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
    console.log(res);
      return res.json({ msg: `Hey ${req.user.email}! I open at the close.` });
  });


  return userRoutes;
};

export default initUserRoutes;
