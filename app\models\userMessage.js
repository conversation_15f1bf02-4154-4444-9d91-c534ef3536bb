import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const UserMessageSchema = new Schema({
        message: {
          type: String,
          required: true,
        },
        senderId: {
          type: ObjectId,
          required: true,
        },
        recieverId: {
          type: ObjectId,
          required: true,
        },
        createdAt: { 
          type: Date,
          default: Date.now
          },
        seen: {
          type:Boolean,
          default:false
        },
        sender: {
          type: String,
          required: true,
        },
        receiver: {
          type: String,
          required: true,
        },
        myId: {
          type: String,
          required: true,
        }
});

UserMessageSchema.plugin(timeZone, { paths: ['createdAt'] });


export default mongoose.model('UserMessage', UserMessageSchema);
