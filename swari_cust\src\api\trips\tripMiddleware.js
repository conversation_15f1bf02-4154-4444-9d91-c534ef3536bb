/**
 * Trip Middleware
 * Handles validation and preprocessing for trip operations
 * PRD Reference: Sections 4.2, 10.2
 */


import { check, param, validationResult,  body } from 'express-validator';
import logger from '../../utils/logger.js';

/**
 * Middleware to validate trip creation request
 */
export const validateTripCreation = [
  // Validate required fields
  check("trip_type", "Trip type is required")
    .isIn(["one-way", "round-trip", "custom-days"])
    .withMessage("Trip type must be one-way, round-trip, or custom-days"),

  

  check("pickup_coords")
    .isArray()
    .withMessage(
      "Coordinates must be an array "
    )
    .custom((value) => {
      if (value.length !== 2) {
        throw new Error("Coordinates must be an array with exactly 2 elements [longitude, latitude]");
      }
      return true;
    }),
 

check("dropoff_coords")
    .isArray()
    .withMessage(
      "Coordinates must be an array "
    )
    .custom((value) => {
      if (value.length !== 2) {
        throw new Error("Coordinates must be an array with exactly 2 elements [longitude, latitude]");
      }
      return true;
    }),

  check("pickup_address", "Pickup address is required")
    .notEmpty()
    .withMessage("Pickup address cannot be empty"),

  check("dropoff_address", "Dropoff address is required")
    .notEmpty()
    .withMessage("Dropoff address cannot be empty"),

  check("date_time", "Date and time are required")
    .isISO8601()
    .withMessage("Date and time must be in ISO8601 format")
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error("Date and time must be in the future");
      }
      return true;
    }),

  check("car_type", "Car type is required")
    .isIn(["suv", "sedan", "hatchback", "van"])
    .withMessage("Car type must be suv, sedan, hatchback, or van"),

  // Conditional validation
  check("return_date_time")
    .if((body) => body.trip_type === "round-trip")
    .isISO8601()
    .withMessage(
      "Return date is required for round trips and must be in ISO8601 format"
    ).custom((value) => {
      if (new Date(value) <= new Date(body.date_time)) {
        throw new Error("Return Date must be more than the pickup date");
      }
      return true;
    }),

  check("custom_days")
    .if((body) => body.trip_type === "custom-days")
    .isArray()
    .withMessage(
      "Custom days are required for custom days trips and must be an array"
    )
    .custom((value, { req }) => {
      if (
        req.body.trip_type === "custom-days" &&
        (!value || value.length === 0)
      ) {
        throw new Error(
          "Custom days array cannot be empty for custom-days trip type"
        );
      }

      // Validate each date in the array
      if (Array.isArray(value)) {
        value.forEach((date, index) => {
          if (!date || !Date.parse(date)) {
            throw new Error(
              `Invalid date format at index ${index} in custom_days array`
            );
          }
        });
      }

      return true;
    }),
];

/**
 * Middleware to validate trip coordinates
 * Ensures coordinates are valid longitude and latitude values
 */
export const validateCoordinates = [
  check("pickup_coords.0")
    .isFloat({ min: -180, max: 180 })
    .withMessage("Pickup longitude must be between -180 and 180"),

  check("pickup_coords.1")
    .isFloat({ min: -90, max: 90 })
    .withMessage("Pickup latitude must be between -90 and 90"),

  check("dropoff_coords.0")
    .isFloat({ min: -180, max: 180 })
    .withMessage("Dropoff longitude must be between -180 and 180"),

  check("dropoff_coords.1")
    .isFloat({ min: -90, max: 90 })
    .withMessage("Dropoff latitude must be between -90 and 90"),
];

export const validateTripIdParam = [
  param('id').isMongoId().withMessage('Invalid trip ID format'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn('Trip ID validation failed', { errors: errors.array(), tripId: req.params.id });
      return res.status(400).json({ success: false, errors: errors.array() });
    }
    next();
  }
];
