/*!
 *  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */

@font-face {
    font-family: FontAwesome;
    src: url(../fonts/fontawesome-webfont.eot?v=4.5.0);
    src: url(../fonts/fontawesome-webfont.eot?#iefix&v=4.5.0) format('embedded-opentype'), url(../fonts/fontawesome-webfont.woff2?v=4.5.0) format('woff2'), url(../fonts/fontawesome-webfont.woff?v=4.5.0) format('woff'), url(../fonts/fontawesome-webfont.ttf?v=4.5.0) format('truetype'), url(../fonts/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular) format('svg');
    font-weight: 400;
    font-style: normal
}

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.close-icon-pos {
    position: absolute;
    right: 14px;
    top: 10px;
    color: white;
    font-size: 20px;
}
.fs-16{font-size: 16px;}
.btn-pos{position: absolute;top: 170px;}

.fa-lg {
    font-size: 1.33333333em;
    line-height: .75em;
    vertical-align: -15%
}

.border-shedow{
        border: 1px solid white;
    padding: 12px !important;
    box-shadow: 0px 1px 6px white;
}

.btn-select{
    background-color: #5bc0de !important;
    border-color: #5bc0de !important;
}

.notifi-number{

    background: yellowgreen;
    font-size: 14px;
    border-radius: 22px;
    width: 20px;
    height: 20px;
    position: absolute;
    right: 26px;
    top: 22px;
    margin: auto;
    align-items: center;
    display: flex;
    justify-content: center;

}

.fa-2x {
    font-size: 2em
}

.fa-3x {
    font-size: 3em
}

.fa-4x {
    font-size: 4em
}

.fa-5x {
    font-size: 5em
}

.fa-fw {
    width: 1.28571429em;
    text-align: center
}

.fa-ul {
    padding-left: 0;
    margin-left: 2.14285714em;
    list-style-type: none
}

.fa-ul>li {
    position: relative
}

.fa-li {
    position: absolute;
    left: -2.14285714em;
    width: 2.14285714em;
    top: .14285714em;
    text-align: center
}

.fa-li.fa-lg {
    left: -1.85714286em
}

.fa-border {
    padding: .2em .25em .15em;
    border: .08em solid #eee;
    border-radius: .1em
}

.fa-pull-left {
    float: left
}

.fa-pull-right {
    float: right
}

.fa.fa-pull-left {
    margin-right: .3em
}

.fa.fa-pull-right {
    margin-left: .3em
}

.pull-right {
    float: right
}

.pull-left {
    float: left
}

.fa.pull-left {
    margin-right: .3em
}

.fa.pull-right {
    margin-left: .3em
}

.fa-spin {
    -webkit-animation: a 2s infinite linear;
    animation: a 2s infinite linear
}

.fa-pulse {
    -webkit-animation: a 1s infinite steps(8);
    animation: a 1s infinite steps(8)
}

@-webkit-keyframes a {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

@keyframes a {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

.fa-rotate-90 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=1);
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.fa-rotate-180 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.fa-rotate-270 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=3);
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
}

.fa-flip-horizontal {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1)
}

.fa-flip-vertical {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
    -webkit-transform: scaleY(-1);
    transform: scaleY(-1)
}

:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270 {
    filter: none
}

.fa-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle
}

.fa-stack-1x,
.fa-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center
}

.fa-stack-1x {
    line-height: inherit
}

.fa-stack-2x {
    font-size: 2em
}

.fa-inverse {
    color: #fff
}

.fa-glass:before {
    content: "\f000"
}

.fa-music:before {
    content: "\f001"
}

.fa-search:before {
    content: "\f002"
}

.fa-envelope-o:before {
    content: "\f003"
}

.fa-heart:before {
    content: "\f004"
}

.fa-star:before {
    content: "\f005"
}

.fa-star-o:before {
    content: "\f006"
}

.fa-user:before {
    content: "\f007"
}

.fa-film:before {
    content: "\f008"
}

.fa-th-large:before {
    content: "\f009"
}

.fa-th:before {
    content: "\f00a"
}

.fa-th-list:before {
    content: "\f00b"
}

.fa-check:before {
    content: "\f00c"
}

.fa-close:before,
.fa-remove:before,
.fa-times:before {
    content: "\f00d"
}

.fa-search-plus:before {
    content: "\f00e"
}

.fa-search-minus:before {
    content: "\f010"
}

.fa-power-off:before {
    content: "\f011"
}

.fa-signal:before {
    content: "\f012"
}

.fa-cog:before,
.fa-gear:before {
    content: "\f013"
}

.fa-trash-o:before {
    content: "\f014"
}

.fa-home:before {
    content: "\f015"
}

.fa-file-o:before {
    content: "\f016"
}

.fa-clock-o:before {
    content: "\f017"
}

.fa-road:before {
    content: "\f018"
}

.fa-download:before {
    content: "\f019"
}

.fa-arrow-circle-o-down:before {
    content: "\f01a"
}

.fa-arrow-circle-o-up:before {
    content: "\f01b"
}

.fa-inbox:before {
    content: "\f01c"
}

.fa-play-circle-o:before {
    content: "\f01d"
}

.fa-repeat:before,
.fa-rotate-right:before {
    content: "\f01e"
}

.fa-refresh:before {
    content: "\f021"
}

.fa-list-alt:before {
    content: "\f022"
}

.fa-lock:before {
    content: "\f023"
}

.fa-flag:before {
    content: "\f024"
}

.fa-headphones:before {
    content: "\f025"
}

.fa-volume-off:before {
    content: "\f026"
}

.fa-volume-down:before {
    content: "\f027"
}

.fa-volume-up:before {
    content: "\f028"
}

.fa-qrcode:before {
    content: "\f029"
}

.fa-barcode:before {
    content: "\f02a"
}

.fa-tag:before {
    content: "\f02b"
}

.fa-tags:before {
    content: "\f02c"
}

.fa-book:before {
    content: "\f02d"
}

.fa-bookmark:before {
    content: "\f02e"
}

.fa-print:before {
    content: "\f02f"
}

.fa-camera:before {
    content: "\f030"
}

.fa-font:before {
    content: "\f031"
}

.fa-bold:before {
    content: "\f032"
}

.fa-italic:before {
    content: "\f033"
}

.fa-text-height:before {
    content: "\f034"
}

.fa-text-width:before {
    content: "\f035"
}

.fa-align-left:before {
    content: "\f036"
}

.fa-align-center:before {
    content: "\f037"
}

.fa-align-right:before {
    content: "\f038"
}

.fa-align-justify:before {
    content: "\f039"
}

.fa-list:before {
    content: "\f03a"
}

.fa-dedent:before,
.fa-outdent:before {
    content: "\f03b"
}

.fa-indent:before {
    content: "\f03c"
}

.fa-video-camera:before {
    content: "\f03d"
}

.fa-image:before,
.fa-photo:before,
.fa-picture-o:before {
    content: "\f03e"
}

.fa-pencil:before {
    content: "\f040"
}

.fa-map-marker:before {
    content: "\f041"
}

.fa-adjust:before {
    content: "\f042"
}

.fa-tint:before {
    content: "\f043"
}

.fa-edit:before,
.fa-pencil-square-o:before {
    content: "\f044"
}

.fa-share-square-o:before {
    content: "\f045"
}

.fa-check-square-o:before {
    content: "\f046"
}

.fa-arrows:before {
    content: "\f047"
}

.fa-step-backward:before {
    content: "\f048"
}

.fa-fast-backward:before {
    content: "\f049"
}

.fa-backward:before {
    content: "\f04a"
}

.fa-play:before {
    content: "\f04b"
}

.fa-pause:before {
    content: "\f04c"
}

.fa-stop:before {
    content: "\f04d"
}

.fa-forward:before {
    content: "\f04e"
}

.fa-fast-forward:before {
    content: "\f050"
}

.fa-step-forward:before {
    content: "\f051"
}

.fa-eject:before {
    content: "\f052"
}

.fa-chevron-left:before {
    content: "\f053"
}

.fa-chevron-right:before {
    content: "\f054"
}

.fa-plus-circle:before {
    content: "\f055"
}

.fa-minus-circle:before {
    content: "\f056"
}

.fa-times-circle:before {
    content: "\f057"
}

.fa-check-circle:before {
    content: "\f058"
}

.fa-question-circle:before {
    content: "\f059"
}

.fa-info-circle:before {
    content: "\f05a"
}

.fa-crosshairs:before {
    content: "\f05b"
}

.fa-times-circle-o:before {
    content: "\f05c"
}

.fa-check-circle-o:before {
    content: "\f05d"
}

.fa-ban:before {
    content: "\f05e"
}

.fa-arrow-left:before {
    content: "\f060"
}

.fa-arrow-right:before {
    content: "\f061"
}

.fa-arrow-up:before {
    content: "\f062"
}

.fa-arrow-down:before {
    content: "\f063"
}

.fa-mail-forward:before,
.fa-share:before {
    content: "\f064"
}

.fa-expand:before {
    content: "\f065"
}

.fa-compress:before {
    content: "\f066"
}

.fa-plus:before {
    content: "\f067"
}

.fa-minus:before {
    content: "\f068"
}

.fa-asterisk:before {
    content: "\f069"
}

.fa-exclamation-circle:before {
    content: "\f06a"
}

.fa-gift:before {
    content: "\f06b"
}

.fa-leaf:before {
    content: "\f06c"
}

.fa-fire:before {
    content: "\f06d"
}

.fa-eye:before {
    content: "\f06e"
}

.fa-eye-slash:before {
    content: "\f070"
}

.fa-exclamation-triangle:before,
.fa-warning:before {
    content: "\f071"
}

.fa-plane:before {
    content: "\f072"
}

.fa-calendar:before {
    content: "\f073"
}

.fa-random:before {
    content: "\f074"
}

.fa-comment:before {
    content: "\f075"
}

.fa-magnet:before {
    content: "\f076"
}

.fa-chevron-up:before {
    content: "\f077"
}

.fa-chevron-down:before {
    content: "\f078"
}

.fa-retweet:before {
    content: "\f079"
}

.fa-shopping-cart:before {
    content: "\f07a"
}

.fa-folder:before {
    content: "\f07b"
}

.fa-folder-open:before {
    content: "\f07c"
}

.fa-arrows-v:before {
    content: "\f07d"
}

.fa-arrows-h:before {
    content: "\f07e"
}

.fa-bar-chart-o:before,
.fa-bar-chart:before {
    content: "\f080"
}

.fa-twitter-square:before {
    content: "\f081"
}

.fa-facebook-square:before {
    content: "\f082"
}

.fa-camera-retro:before {
    content: "\f083"
}

.fa-key:before {
    content: "\f084"
}

.fa-cogs:before,
.fa-gears:before {
    content: "\f085"
}

.fa-comments:before {
    content: "\f086"
}

.fa-thumbs-o-up:before {
    content: "\f087"
}

.fa-thumbs-o-down:before {
    content: "\f088"
}

.fa-star-half:before {
    content: "\f089"
}

.fa-heart-o:before {
    content: "\f08a"
}

.fa-sign-out:before {
    content: "\f08b"
}

.fa-linkedin-square:before {
    content: "\f08c"
}

.fa-thumb-tack:before {
    content: "\f08d"
}

.fa-external-link:before {
    content: "\f08e"
}

.fa-sign-in:before {
    content: "\f090"
}

.fa-trophy:before {
    content: "\f091"
}

.fa-github-square:before {
    content: "\f092"
}

.fa-upload:before {
    content: "\f093"
}

.fa-lemon-o:before {
    content: "\f094"
}

.fa-phone:before {
    content: "\f095"
}

.fa-square-o:before {
    content: "\f096"
}

.fa-bookmark-o:before {
    content: "\f097"
}

.fa-phone-square:before {
    content: "\f098"
}

.fa-twitter:before {
    content: "\f099"
}

.fa-facebook-f:before,
.fa-facebook:before {
    content: "\f09a"
}

.fa-github:before {
    content: "\f09b"
}

.fa-unlock:before {
    content: "\f09c"
}

.fa-credit-card:before {
    content: "\f09d"
}

.fa-feed:before,
.fa-rss:before {
    content: "\f09e"
}

.fa-hdd-o:before {
    content: "\f0a0"
}

.fa-bullhorn:before {
    content: "\f0a1"
}

.fa-bell:before {
    content: "\f0f3"
}

.fa-certificate:before {
    content: "\f0a3"
}

.fa-hand-o-right:before {
    content: "\f0a4"
}

.fa-hand-o-left:before {
    content: "\f0a5"
}

.fa-hand-o-up:before {
    content: "\f0a6"
}

.fa-hand-o-down:before {
    content: "\f0a7"
}

.fa-arrow-circle-left:before {
    content: "\f0a8"
}

.fa-arrow-circle-right:before {
    content: "\f0a9"
}

.fa-arrow-circle-up:before {
    content: "\f0aa"
}

.fa-arrow-circle-down:before {
    content: "\f0ab"
}

.fa-globe:before {
    content: "\f0ac"
}

.fa-wrench:before {
    content: "\f0ad"
}

.fa-tasks:before {
    content: "\f0ae"
}

.fa-filter:before {
    content: "\f0b0"
}

.fa-briefcase:before {
    content: "\f0b1"
}

.fa-arrows-alt:before {
    content: "\f0b2"
}

.fa-group:before,
.fa-users:before {
    content: "\f0c0"
}

.fa-chain:before,
.fa-link:before {
    content: "\f0c1"
}

.fa-cloud:before {
    content: "\f0c2"
}

.fa-flask:before {
    content: "\f0c3"
}

.fa-cut:before,
.fa-scissors:before {
    content: "\f0c4"
}

.fa-copy:before,
.fa-files-o:before {
    content: "\f0c5"
}

.fa-paperclip:before {
    content: "\f0c6"
}

.fa-floppy-o:before,
.fa-save:before {
    content: "\f0c7"
}

.fa-square:before {
    content: "\f0c8"
}

.fa-bars:before,
.fa-navicon:before,
.fa-reorder:before {
    content: "\f0c9"
}

.fa-list-ul:before {
    content: "\f0ca"
}

.fa-list-ol:before {
    content: "\f0cb"
}

.fa-strikethrough:before {
    content: "\f0cc"
}

.fa-underline:before {
    content: "\f0cd"
}

.fa-table:before {
    content: "\f0ce"
}

.fa-magic:before {
    content: "\f0d0"
}

.fa-truck:before {
    content: "\f0d1"
}

.fa-pinterest:before {
    content: "\f0d2"
}

.fa-pinterest-square:before {
    content: "\f0d3"
}

.fa-google-plus-square:before {
    content: "\f0d4"
}

.fa-google-plus:before {
    content: "\f0d5"
}

.fa-money:before {
    content: "\f0d6"
}

.fa-caret-down:before {
    content: "\f0d7"
}

.fa-caret-up:before {
    content: "\f0d8"
}

.fa-caret-left:before {
    content: "\f0d9"
}

.fa-caret-right:before {
    content: "\f0da"
}

.fa-columns:before {
    content: "\f0db"
}

.fa-sort:before,
.fa-unsorted:before {
    content: "\f0dc"
}

.fa-sort-desc:before,
.fa-sort-down:before {
    content: "\f0dd"
}

.fa-sort-asc:before,
.fa-sort-up:before {
    content: "\f0de"
}

.fa-envelope:before {
    content: "\f0e0"
}

.fa-linkedin:before {
    content: "\f0e1"
}

.fa-rotate-left:before,
.fa-undo:before {
    content: "\f0e2"
}

.fa-gavel:before,
.fa-legal:before {
    content: "\f0e3"
}

.fa-dashboard:before,
.fa-tachometer:before {
    content: "\f0e4"
}

.fa-comment-o:before {
    content: "\f0e5"
}

.fa-comments-o:before {
    content: "\f0e6"
}

.fa-bolt:before,
.fa-flash:before {
    content: "\f0e7"
}

.fa-sitemap:before {
    content: "\f0e8"
}

.fa-umbrella:before {
    content: "\f0e9"
}

.fa-clipboard:before,
.fa-paste:before {
    content: "\f0ea"
}

.fa-lightbulb-o:before {
    content: "\f0eb"
}

.fa-exchange:before {
    content: "\f0ec"
}

.fa-cloud-download:before {
    content: "\f0ed"
}

.fa-cloud-upload:before {
    content: "\f0ee"
}

.fa-user-md:before {
    content: "\f0f0"
}

.fa-stethoscope:before {
    content: "\f0f1"
}

.fa-suitcase:before {
    content: "\f0f2"
}

.fa-bell-o:before {
    content: "\f0a2"
}

.fa-coffee:before {
    content: "\f0f4"
}

.fa-cutlery:before {
    content: "\f0f5"
}

.fa-file-text-o:before {
    content: "\f0f6"
}

.fa-building-o:before {
    content: "\f0f7"
}

.fa-hospital-o:before {
    content: "\f0f8"
}

.fa-ambulance:before {
    content: "\f0f9"
}

.fa-medkit:before {
    content: "\f0fa"
}

.fa-fighter-jet:before {
    content: "\f0fb"
}

.fa-beer:before {
    content: "\f0fc"
}

.fa-h-square:before {
    content: "\f0fd"
}

.fa-plus-square:before {
    content: "\f0fe"
}

.fa-angle-double-left:before {
    content: "\f100"
}

.fa-angle-double-right:before {
    content: "\f101"
}

.fa-angle-double-up:before {
    content: "\f102"
}

.fa-angle-double-down:before {
    content: "\f103"
}

.fa-angle-left:before {
    content: "\f104"
}

.fa-angle-right:before {
    content: "\f105"
}

.fa-angle-up:before {
    content: "\f106"
}

.fa-angle-down:before {
    content: "\f107"
}

.fa-desktop:before {
    content: "\f108"
}

.fa-laptop:before {
    content: "\f109"
}

.fa-tablet:before {
    content: "\f10a"
}

.fa-mobile-phone:before,
.fa-mobile:before {
    content: "\f10b"
}

.fa-circle-o:before {
    content: "\f10c"
}

.fa-quote-left:before {
    content: "\f10d"
}

.fa-quote-right:before {
    content: "\f10e"
}

.fa-spinner:before {
    content: "\f110"
}

.fa-circle:before {
    content: "\f111"
}

.fa-mail-reply:before,
.fa-reply:before {
    content: "\f112"
}

.fa-github-alt:before {
    content: "\f113"
}

.fa-folder-o:before {
    content: "\f114"
}

.fa-folder-open-o:before {
    content: "\f115"
}

.fa-smile-o:before {
    content: "\f118"
}

.fa-frown-o:before {
    content: "\f119"
}

.fa-meh-o:before {
    content: "\f11a"
}

.fa-gamepad:before {
    content: "\f11b"
}

.fa-keyboard-o:before {
    content: "\f11c"
}

.fa-flag-o:before {
    content: "\f11d"
}

.fa-flag-checkered:before {
    content: "\f11e"
}

.fa-terminal:before {
    content: "\f120"
}

.fa-code:before {
    content: "\f121"
}

.fa-mail-reply-all:before,
.fa-reply-all:before {
    content: "\f122"
}

.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
    content: "\f123"
}

.fa-location-arrow:before {
    content: "\f124"
}

.fa-crop:before {
    content: "\f125"
}

.fa-code-fork:before {
    content: "\f126"
}

.fa-chain-broken:before,
.fa-unlink:before {
    content: "\f127"
}

.fa-question:before {
    content: "\f128"
}

.fa-info:before {
    content: "\f129"
}

.fa-exclamation:before {
    content: "\f12a"
}

.fa-superscript:before {
    content: "\f12b"
}

.fa-subscript:before {
    content: "\f12c"
}

.fa-eraser:before {
    content: "\f12d"
}

.fa-puzzle-piece:before {
    content: "\f12e"
}

.fa-microphone:before {
    content: "\f130"
}

.fa-microphone-slash:before {
    content: "\f131"
}

.fa-shield:before {
    content: "\f132"
}

.fa-calendar-o:before {
    content: "\f133"
}

.fa-fire-extinguisher:before {
    content: "\f134"
}

.fa-rocket:before {
    content: "\f135"
}

.fa-maxcdn:before {
    content: "\f136"
}

.fa-chevron-circle-left:before {
    content: "\f137"
}

.fa-chevron-circle-right:before {
    content: "\f138"
}

.fa-chevron-circle-up:before {
    content: "\f139"
}

.fa-chevron-circle-down:before {
    content: "\f13a"
}

.fa-html5:before {
    content: "\f13b"
}

.fa-css3:before {
    content: "\f13c"
}

.fa-anchor:before {
    content: "\f13d"
}

.fa-unlock-alt:before {
    content: "\f13e"
}

.fa-bullseye:before {
    content: "\f140"
}

.fa-ellipsis-h:before {
    content: "\f141"
}

.fa-ellipsis-v:before {
    content: "\f142"
}

.fa-rss-square:before {
    content: "\f143"
}

.fa-play-circle:before {
    content: "\f144"
}

.fa-ticket:before {
    content: "\f145"
}

.fa-minus-square:before {
    content: "\f146"
}

.fa-minus-square-o:before {
    content: "\f147"
}

.fa-level-up:before {
    content: "\f148"
}

.fa-level-down:before {
    content: "\f149"
}

.fa-check-square:before {
    content: "\f14a"
}

.fa-pencil-square:before {
    content: "\f14b"
}

.fa-external-link-square:before {
    content: "\f14c"
}

.fa-share-square:before {
    content: "\f14d"
}

.fa-compass:before {
    content: "\f14e"
}

.fa-caret-square-o-down:before,
.fa-toggle-down:before {
    content: "\f150"
}

.fa-caret-square-o-up:before,
.fa-toggle-up:before {
    content: "\f151"
}

.fa-caret-square-o-right:before,
.fa-toggle-right:before {
    content: "\f152"
}

.fa-eur:before,
.fa-euro:before {
    content: "\f153"
}

.fa-gbp:before {
    content: "\f154"
}

.fa-dollar:before,
.fa-usd:before {
    content: "\f155"
}

.fa-inr:before,
.fa-rupee:before {
    content: "\f156"
}

.fa-cny:before,
.fa-jpy:before,
.fa-rmb:before,
.fa-yen:before {
    content: "\f157"
}

.fa-rouble:before,
.fa-rub:before,
.fa-ruble:before {
    content: "\f158"
}

.fa-krw:before,
.fa-won:before {
    content: "\f159"
}

.fa-bitcoin:before,
.fa-btc:before {
    content: "\f15a"
}

.fa-file:before {
    content: "\f15b"
}

.fa-file-text:before {
    content: "\f15c"
}

.fa-sort-alpha-asc:before {
    content: "\f15d"
}

.fa-sort-alpha-desc:before {
    content: "\f15e"
}

.fa-sort-amount-asc:before {
    content: "\f160"
}

.fa-sort-amount-desc:before {
    content: "\f161"
}

.fa-sort-numeric-asc:before {
    content: "\f162"
}

.fa-sort-numeric-desc:before {
    content: "\f163"
}

.fa-thumbs-up:before {
    content: "\f164"
}

.fa-thumbs-down:before {
    content: "\f165"
}

.fa-youtube-square:before {
    content: "\f166"
}

.fa-youtube:before {
    content: "\f167"
}

.fa-xing:before {
    content: "\f168"
}

.fa-xing-square:before {
    content: "\f169"
}

.fa-youtube-play:before {
    content: "\f16a"
}

.fa-dropbox:before {
    content: "\f16b"
}

.fa-stack-overflow:before {
    content: "\f16c"
}

.fa-instagram:before {
    content: "\f16d"
}

.fa-flickr:before {
    content: "\f16e"
}

.fa-adn:before {
    content: "\f170"
}

.fa-bitbucket:before {
    content: "\f171"
}

.fa-bitbucket-square:before {
    content: "\f172"
}

.fa-tumblr:before {
    content: "\f173"
}

.fa-tumblr-square:before {
    content: "\f174"
}

.fa-long-arrow-down:before {
    content: "\f175"
}

.fa-long-arrow-up:before {
    content: "\f176"
}

.fa-long-arrow-left:before {
    content: "\f177"
}

.fa-long-arrow-right:before {
    content: "\f178"
}

.fa-apple:before {
    content: "\f179"
}

.fa-windows:before {
    content: "\f17a"
}

.fa-android:before {
    content: "\f17b"
}

.fa-linux:before {
    content: "\f17c"
}

.fa-dribbble:before {
    content: "\f17d"
}

.fa-skype:before {
    content: "\f17e"
}

.fa-foursquare:before {
    content: "\f180"
}

.fa-trello:before {
    content: "\f181"
}

.fa-female:before {
    content: "\f182"
}

.fa-male:before {
    content: "\f183"
}

.fa-gittip:before,
.fa-gratipay:before {
    content: "\f184"
}

.fa-sun-o:before {
    content: "\f185"
}

.fa-moon-o:before {
    content: "\f186"
}

.fa-archive:before {
    content: "\f187"
}

.fa-bug:before {
    content: "\f188"
}

.fa-vk:before {
    content: "\f189"
}

.fa-weibo:before {
    content: "\f18a"
}

.fa-renren:before {
    content: "\f18b"
}

.fa-pagelines:before {
    content: "\f18c"
}

.fa-stack-exchange:before {
    content: "\f18d"
}

.fa-arrow-circle-o-right:before {
    content: "\f18e"
}

.fa-arrow-circle-o-left:before {
    content: "\f190"
}

.fa-caret-square-o-left:before,
.fa-toggle-left:before {
    content: "\f191"
}

.fa-dot-circle-o:before {
    content: "\f192"
}

.fa-wheelchair:before {
    content: "\f193"
}

.fa-vimeo-square:before {
    content: "\f194"
}

.fa-try:before,
.fa-turkish-lira:before {
    content: "\f195"
}

.fa-plus-square-o:before {
    content: "\f196"
}

.fa-space-shuttle:before {
    content: "\f197"
}

.fa-slack:before {
    content: "\f198"
}

.fa-envelope-square:before {
    content: "\f199"
}

.fa-wordpress:before {
    content: "\f19a"
}

.fa-openid:before {
    content: "\f19b"
}

.fa-bank:before,
.fa-institution:before,
.fa-university:before {
    content: "\f19c"
}

.fa-graduation-cap:before,
.fa-mortar-board:before {
    content: "\f19d"
}

.fa-yahoo:before {
    content: "\f19e"
}

.fa-google:before {
    content: "\f1a0"
}

.fa-reddit:before {
    content: "\f1a1"
}

.fa-reddit-square:before {
    content: "\f1a2"
}

.fa-stumbleupon-circle:before {
    content: "\f1a3"
}

.fa-stumbleupon:before {
    content: "\f1a4"
}

.fa-delicious:before {
    content: "\f1a5"
}

.fa-digg:before {
    content: "\f1a6"
}

.fa-pied-piper:before {
    content: "\f1a7"
}

.fa-pied-piper-alt:before {
    content: "\f1a8"
}

.fa-drupal:before {
    content: "\f1a9"
}

.fa-joomla:before {
    content: "\f1aa"
}

.fa-language:before {
    content: "\f1ab"
}

.fa-fax:before {
    content: "\f1ac"
}

.fa-building:before {
    content: "\f1ad"
}

.fa-child:before {
    content: "\f1ae"
}

.fa-paw:before {
    content: "\f1b0"
}

.fa-spoon:before {
    content: "\f1b1"
}

.fa-cube:before {
    content: "\f1b2"
}

.fa-cubes:before {
    content: "\f1b3"
}

.fa-behance:before {
    content: "\f1b4"
}

.fa-behance-square:before {
    content: "\f1b5"
}

.fa-steam:before {
    content: "\f1b6"
}

.fa-steam-square:before {
    content: "\f1b7"
}

.fa-recycle:before {
    content: "\f1b8"
}

.fa-automobile:before,
.fa-car:before {
    content: "\f1b9"
}

.fa-cab:before,
.fa-taxi:before {
    content: "\f1ba"
}

.fa-tree:before {
    content: "\f1bb"
}

.fa-spotify:before {
    content: "\f1bc"
}

.fa-deviantart:before {
    content: "\f1bd"
}

.fa-soundcloud:before {
    content: "\f1be"
}

.fa-database:before {
    content: "\f1c0"
}

.fa-file-pdf-o:before {
    content: "\f1c1"
}

.fa-file-word-o:before {
    content: "\f1c2"
}

.fa-file-excel-o:before {
    content: "\f1c3"
}

.fa-file-powerpoint-o:before {
    content: "\f1c4"
}

.fa-file-image-o:before,
.fa-file-photo-o:before,
.fa-file-picture-o:before {
    content: "\f1c5"
}

.fa-file-archive-o:before,
.fa-file-zip-o:before {
    content: "\f1c6"
}

.fa-file-audio-o:before,
.fa-file-sound-o:before {
    content: "\f1c7"
}

.fa-file-movie-o:before,
.fa-file-video-o:before {
    content: "\f1c8"
}

.fa-file-code-o:before {
    content: "\f1c9"
}

.fa-vine:before {
    content: "\f1ca"
}

.fa-codepen:before {
    content: "\f1cb"
}

.fa-jsfiddle:before {
    content: "\f1cc"
}

.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-ring:before,
.fa-life-saver:before,
.fa-support:before {
    content: "\f1cd"
}

.fa-circle-o-notch:before {
    content: "\f1ce"
}

.fa-ra:before,
.fa-rebel:before {
    content: "\f1d0"
}

.fa-empire:before,
.fa-ge:before {
    content: "\f1d1"
}

.fa-git-square:before {
    content: "\f1d2"
}

.fa-git:before {
    content: "\f1d3"
}

.fa-hacker-news:before,
.fa-y-combinator-square:before,
.fa-yc-square:before {
    content: "\f1d4"
}

.fa-tencent-weibo:before {
    content: "\f1d5"
}

.fa-qq:before {
    content: "\f1d6"
}

.fa-wechat:before,
.fa-weixin:before {
    content: "\f1d7"
}

.fa-paper-plane:before,
.fa-send:before {
    content: "\f1d8"
}

.fa-paper-plane-o:before,
.fa-send-o:before {
    content: "\f1d9"
}

.fa-history:before {
    content: "\f1da"
}

.fa-circle-thin:before {
    content: "\f1db"
}

.fa-header:before {
    content: "\f1dc"
}

.fa-paragraph:before {
    content: "\f1dd"
}

.fa-sliders:before {
    content: "\f1de"
}

.fa-share-alt:before {
    content: "\f1e0"
}

.fa-share-alt-square:before {
    content: "\f1e1"
}

.fa-bomb:before {
    content: "\f1e2"
}

.fa-futbol-o:before,
.fa-soccer-ball-o:before {
    content: "\f1e3"
}

.fa-tty:before {
    content: "\f1e4"
}

.fa-binoculars:before {
    content: "\f1e5"
}

.fa-plug:before {
    content: "\f1e6"
}

.fa-slideshare:before {
    content: "\f1e7"
}

.fa-twitch:before {
    content: "\f1e8"
}

.fa-yelp:before {
    content: "\f1e9"
}

.fa-newspaper-o:before {
    content: "\f1ea"
}

.fa-wifi:before {
    content: "\f1eb"
}

.fa-calculator:before {
    content: "\f1ec"
}

.fa-paypal:before {
    content: "\f1ed"
}

.fa-google-wallet:before {
    content: "\f1ee"
}

.fa-cc-visa:before {
    content: "\f1f0"
}

.fa-cc-mastercard:before {
    content: "\f1f1"
}

.fa-cc-discover:before {
    content: "\f1f2"
}

.fa-cc-amex:before {
    content: "\f1f3"
}

.fa-cc-paypal:before {
    content: "\f1f4"
}

.fa-cc-stripe:before {
    content: "\f1f5"
}

.fa-bell-slash:before {
    content: "\f1f6"
}

.fa-bell-slash-o:before {
    content: "\f1f7"
}

.fa-trash:before {
    content: "\f1f8"
}

.fa-copyright:before {
    content: "\f1f9"
}

.fa-at:before {
    content: "\f1fa"
}

.fa-eyedropper:before {
    content: "\f1fb"
}

.fa-paint-brush:before {
    content: "\f1fc"
}

.fa-birthday-cake:before {
    content: "\f1fd"
}

.fa-area-chart:before {
    content: "\f1fe"
}

.fa-pie-chart:before {
    content: "\f200"
}

.fa-line-chart:before {
    content: "\f201"
}

.fa-lastfm:before {
    content: "\f202"
}

.fa-lastfm-square:before {
    content: "\f203"
}

.fa-toggle-off:before {
    content: "\f204"
}

.fa-toggle-on:before {
    content: "\f205"
}

.fa-bicycle:before {
    content: "\f206"
}

.fa-bus:before {
    content: "\f207"
}

.fa-ioxhost:before {
    content: "\f208"
}

.fa-angellist:before {
    content: "\f209"
}

.fa-cc:before {
    content: "\f20a"
}

.fa-ils:before,
.fa-shekel:before,
.fa-sheqel:before {
    content: "\f20b"
}

.fa-meanpath:before {
    content: "\f20c"
}

.fa-buysellads:before {
    content: "\f20d"
}

.fa-connectdevelop:before {
    content: "\f20e"
}

.fa-dashcube:before {
    content: "\f210"
}

.fa-forumbee:before {
    content: "\f211"
}

.fa-leanpub:before {
    content: "\f212"
}

.fa-sellsy:before {
    content: "\f213"
}

.fa-shirtsinbulk:before {
    content: "\f214"
}

.fa-simplybuilt:before {
    content: "\f215"
}

.fa-skyatlas:before {
    content: "\f216"
}

.fa-cart-plus:before {
    content: "\f217"
}

.fa-cart-arrow-down:before {
    content: "\f218"
}

.fa-diamond:before {
    content: "\f219"
}

.fa-ship:before {
    content: "\f21a"
}

.fa-user-secret:before {
    content: "\f21b"
}

.fa-motorcycle:before {
    content: "\f21c"
}

.fa-street-view:before {
    content: "\f21d"
}

.fa-heartbeat:before {
    content: "\f21e"
}

.fa-venus:before {
    content: "\f221"
}

.fa-mars:before {
    content: "\f222"
}

.fa-mercury:before {
    content: "\f223"
}

.fa-intersex:before,
.fa-transgender:before {
    content: "\f224"
}

.fa-transgender-alt:before {
    content: "\f225"
}

.fa-venus-double:before {
    content: "\f226"
}

.fa-mars-double:before {
    content: "\f227"
}

.fa-venus-mars:before {
    content: "\f228"
}

.fa-mars-stroke:before {
    content: "\f229"
}

.fa-mars-stroke-v:before {
    content: "\f22a"
}

.fa-mars-stroke-h:before {
    content: "\f22b"
}

.fa-neuter:before {
    content: "\f22c"
}

.fa-genderless:before {
    content: "\f22d"
}

.fa-facebook-official:before {
    content: "\f230"
}

.fa-pinterest-p:before {
    content: "\f231"
}

.fa-whatsapp:before {
    content: "\f232"
}

.fa-server:before {
    content: "\f233"
}

.fa-user-plus:before {
    content: "\f234"
}

.fa-user-times:before {
    content: "\f235"
}

.fa-bed:before,
.fa-hotel:before {
    content: "\f236"
}

.fa-viacoin:before {
    content: "\f237"
}

.fa-train:before {
    content: "\f238"
}

.fa-subway:before {
    content: "\f239"
}

.fa-medium:before {
    content: "\f23a"
}

.fa-y-combinator:before,
.fa-yc:before {
    content: "\f23b"
}

.fa-optin-monster:before {
    content: "\f23c"
}

.fa-opencart:before {
    content: "\f23d"
}

.fa-expeditedssl:before {
    content: "\f23e"
}

.fa-battery-4:before,
.fa-battery-full:before {
    content: "\f240"
}

.fa-battery-3:before,
.fa-battery-three-quarters:before {
    content: "\f241"
}

.fa-battery-2:before,
.fa-battery-half:before {
    content: "\f242"
}

.fa-battery-1:before,
.fa-battery-quarter:before {
    content: "\f243"
}

.fa-battery-0:before,
.fa-battery-empty:before {
    content: "\f244"
}

.fa-mouse-pointer:before {
    content: "\f245"
}

.fa-i-cursor:before {
    content: "\f246"
}

.fa-object-group:before {
    content: "\f247"
}

.fa-object-ungroup:before {
    content: "\f248"
}

.fa-sticky-note:before {
    content: "\f249"
}

.fa-sticky-note-o:before {
    content: "\f24a"
}

.fa-cc-jcb:before {
    content: "\f24b"
}

.fa-cc-diners-club:before {
    content: "\f24c"
}

.fa-clone:before {
    content: "\f24d"
}

.fa-balance-scale:before {
    content: "\f24e"
}

.fa-hourglass-o:before {
    content: "\f250"
}

.fa-hourglass-1:before,
.fa-hourglass-start:before {
    content: "\f251"
}

.fa-hourglass-2:before,
.fa-hourglass-half:before {
    content: "\f252"
}

.fa-hourglass-3:before,
.fa-hourglass-end:before {
    content: "\f253"
}

.fa-hourglass:before {
    content: "\f254"
}

.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
    content: "\f255"
}

.fa-hand-paper-o:before,
.fa-hand-stop-o:before {
    content: "\f256"
}

.fa-hand-scissors-o:before {
    content: "\f257"
}

.fa-hand-lizard-o:before {
    content: "\f258"
}

.fa-hand-spock-o:before {
    content: "\f259"
}

.fa-hand-pointer-o:before {
    content: "\f25a"
}

.fa-hand-peace-o:before {
    content: "\f25b"
}

.fa-trademark:before {
    content: "\f25c"
}

.fa-registered:before {
    content: "\f25d"
}

.fa-creative-commons:before {
    content: "\f25e"
}

.fa-gg:before {
    content: "\f260"
}

.fa-gg-circle:before {
    content: "\f261"
}

.fa-tripadvisor:before {
    content: "\f262"
}

.fa-odnoklassniki:before {
    content: "\f263"
}

.fa-odnoklassniki-square:before {
    content: "\f264"
}

.fa-get-pocket:before {
    content: "\f265"
}

.fa-wikipedia-w:before {
    content: "\f266"
}

.fa-safari:before {
    content: "\f267"
}

.fa-chrome:before {
    content: "\f268"
}

.fa-firefox:before {
    content: "\f269"
}

.fa-opera:before {
    content: "\f26a"
}

.fa-internet-explorer:before {
    content: "\f26b"
}

.fa-television:before,
.fa-tv:before {
    content: "\f26c"
}

.fa-contao:before {
    content: "\f26d"
}

.fa-500px:before {
    content: "\f26e"
}

.fa-amazon:before {
    content: "\f270"
}

.fa-calendar-plus-o:before {
    content: "\f271"
}

.fa-calendar-minus-o:before {
    content: "\f272"
}

.fa-calendar-times-o:before {
    content: "\f273"
}

.fa-calendar-check-o:before {
    content: "\f274"
}

.fa-industry:before {
    content: "\f275"
}

.fa-map-pin:before {
    content: "\f276"
}

.fa-map-signs:before {
    content: "\f277"
}

.fa-map-o:before {
    content: "\f278"
}

.fa-map:before {
    content: "\f279"
}

.fa-commenting:before {
    content: "\f27a"
}

.fa-commenting-o:before {
    content: "\f27b"
}

.fa-houzz:before {
    content: "\f27c"
}

.fa-vimeo:before {
    content: "\f27d"
}

.fa-black-tie:before {
    content: "\f27e"
}

.fa-fonticons:before {
    content: "\f280"
}

.fa-reddit-alien:before {
    content: "\f281"
}

.fa-edge:before {
    content: "\f282"
}

.fa-credit-card-alt:before {
    content: "\f283"
}

.fa-codiepie:before {
    content: "\f284"
}

.fa-modx:before {
    content: "\f285"
}

.fa-fort-awesome:before {
    content: "\f286"
}

.fa-usb:before {
    content: "\f287"
}

.fa-product-hunt:before {
    content: "\f288"
}

.fa-mixcloud:before {
    content: "\f289"
}

.fa-scribd:before {
    content: "\f28a"
}

.fa-pause-circle:before {
    content: "\f28b"
}

.fa-pause-circle-o:before {
    content: "\f28c"
}

.fa-stop-circle:before {
    content: "\f28d"
}

.fa-stop-circle-o:before {
    content: "\f28e"
}

.fa-shopping-bag:before {
    content: "\f290"
}

.fa-shopping-basket:before {
    content: "\f291"
}

.fa-hashtag:before {
    content: "\f292"
}

.fa-bluetooth:before {
    content: "\f293"
}

.fa-bluetooth-b:before {
    content: "\f294"
}

.fa-percent:before {
    content: "\f295"
}


/*!
 * Bootstrap v4.0.0-alpha.2 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */

html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline
}

audio:not([controls]) {
    display: none;
    height: 0
}

[hidden],
template {
    display: none
}

a {
    background-color: transparent
}

a:active,
a:hover {
    outline: 0
}

abbr[title] {
    border-bottom: 1px dotted
}

b,
strong {
    font-weight: 700
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

mark {
    background: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    box-sizing: content-box;
    height: 0
}

pre {
    overflow: auto
}

code,
kbd,
pre,
samp {
    font-family: monospace;
    font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0
}

button {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],
html input[disabled] {
    cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0
}

input {
    line-height: normal
}

input[type=checkbox],
input[type=radio] {
    box-sizing: border-box;
    padding: 0
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    height: auto
}

input[type=search] {
    -webkit-appearance: textfield;
    box-sizing: content-box
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

fieldset {
    border: 1px solid silver;
    margin: 0 2px;
    padding: .35em .625em .75em
}

legend {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: 700
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

td,
th {
    padding: 0
}

@media print {
    *,
    :after,
    :before {
        text-shadow: none!important;
        box-shadow: none!important
    }
    a,
    a:visited {
        text-decoration: underline
    }
    abbr[title]:after {
        content: " (" attr(title) ")"
    }
    blockquote,
    pre {
        border: 1px solid #999;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    img,
    tr {
        page-break-inside: avoid
    }
    img {
        max-width: 100%!important
    }
    h2,
    h3,
    p {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    .navbar {
        display: none
    }
    .btn>.caret,
    .dropup>.btn>.caret {
        border-top-color: #000!important
    }
    .label {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse!important
    }
    .table td,
    .table th {
        background-color: #fff!important
    }
    .table-bordered td,
    .table-bordered th {
        border: 1px solid #ddd!important
    }
}

html {
    box-sizing: border-box
}

*,
:after,
:before {
    box-sizing: inherit
}

@-moz-viewport {
    width: device-width
}

@-ms-viewport {
    width: device-width
}

@-o-viewport {
    width: device-width
}

@-webkit-viewport {
    width: device-width
}

@viewport {
    width: device-width
}

html {
    font-size: 16px;
    -webkit-tap-highlight-color: transparent
}

body {
    font-family: Open Sans, Helvetica Neue, Helvetica, Arial, sans-serif;
    font-size: .875rem;
    line-height: 1.5;
    color: #373a3c;
    background-color: #fff
}

[tabindex="-1"]:focus {
    outline: none!important
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

abbr[data-original-title],
abbr[title] {
    cursor: help;
    border-bottom: 1px dotted #818a91
}

address {
    font-style: normal;
    line-height: inherit
}

address,
dl,
ol,
ul {
    margin-bottom: 12px;
}

dl,
ol,
ul {
    margin-top: 0
}

ol ol,
ol ul,
ul ol,
ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 700
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

a {
    color: #2c3e50;
    text-decoration: none
}

a:focus,
a:hover {
    color: #11181f;
    text-decoration: underline
}

a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

pre {
    margin-top: 0;
    margin-bottom: 1rem
}

figure {
    margin: 0 0 1rem
}

img {
    vertical-align: middle
}

[role=button] {
    cursor: pointer
}

[role=button],
a,
area,
button,
input,
label,
select,
summary,
textarea {
    touch-action: manipulation
}

table {
    background-color: transparent
}

caption {
    padding-top: .75rem;
    padding-bottom: .75rem;
    color: #818a91;
    caption-side: bottom
}

caption,
th {
    text-align: left
}

label {
    display: inline-block;
    margin-bottom: .5rem
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

button,
input,
select,
textarea {
    margin: 0;
    line-height: inherit;
    border-radius: 0
}

textarea {
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: 1.5rem;
    line-height: inherit
}

input[type=search] {
    box-sizing: inherit;
    -webkit-appearance: none
}

output {
    display: inline-block
}

[hidden] {
    display: none!important
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit
}

h1 {
    font-size: 2.5rem
}

h2 {
    font-size: 2rem
}

h3 {
    font-size: 1.75rem
}

h4 {
    font-size: 1.5rem
}

h5 {
    font-size: 1.25rem
}

h6 {
    font-size: 1rem
}

.h1 {
    font-size: 2.5rem
}

.h2 {
    font-size: 2rem
}

.h3 {
    font-size: 1.75rem
}

.h4 {
    font-size: 1.5rem
}

.h5 {
    font-size: 1.25rem
}

.h6 {
    font-size: 1rem
}

.lead {
    font-size: 1.25rem;
    font-weight: 300
}

.display-1 {
    font-size: 6rem;
    font-weight: 300
}

.display-2 {
    font-size: 5.5rem;
    font-weight: 300
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300
}

.display-4 {
    font-size: 3.5rem;
    font-weight: 300
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, .1)
}

.small,
small {
    font-size: 80%;
    font-weight: 400
}

.mark,
mark {
    padding: .2em;
    background-color: #f0ad4e
}

.list-inline,
.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: 5px
}

.dl-horizontal {
    margin-right: -.625rem;
    margin-left: -.625rem
}

.dl-horizontal:after {
    content: "";
    display: table;
    clear: both
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

.blockquote {
    padding: .5rem 1rem;
    margin-bottom: 1rem;
    font-size: 1.09375rem;
    border-left: .25rem solid #eceeef
}

.blockquote-footer {
    display: block;
    font-size: 80%;
    line-height: 1.5;
    color: #818a91
}

.blockquote-footer:before {
    content: "\2014 \00A0"
}

.blockquote-reverse {
    padding-right: 1rem;
    padding-left: 0;
    text-align: right;
    border-right: .25rem solid #eceeef;
    border-left: 0
}

.blockquote-reverse .blockquote-footer:before {
    content: ""
}

.blockquote-reverse .blockquote-footer:after {
    content: "\00A0 \2014"
}

.carousel-inner>.carousel-item>a>img,
.carousel-inner>.carousel-item>img,
.img-fluid {
    display: block;
    max-width: 100%;
    height: auto
}

.img-thumbnail {
    padding: .25rem;
    line-height: 1.5;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: .25rem;
    transition: all .2s ease-in-out;
    display: inline-block;
    max-width: 100%;
    height: auto
}

.img-circle {
    border-radius: 50%
}

.figure {
    display: inline-block
}

.figure-img {
    margin-bottom: .5rem;
    line-height: 1
}

.figure-caption {
    font-size: 90%;
    color: #818a91
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, Courier New, monospace
}

code {
    color: #bd4147;
    background-color: #f7f7f9
}

code,
kbd {
    padding: .2rem .4rem;
    font-size: 90%
}

kbd {
    color: #fff;
    background-color: #333
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 700
}

pre {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 90%;
    line-height: 1.5;
    color: #373a3c
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    margin-left: auto;
    margin-right: auto;
    padding-left: .3125rem;
    padding-right: .3125rem
}

.container:after {
    content: "";
    display: table;
    clear: both
}

@media (min-width:544px) {
    .container {
        max-width: 576px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 720px
    }
}

@media (min-width:992px) {
    .container {
        max-width: 940px
    }
}

@media (min-width:1200px) {
    .container {
        max-width: 1140px
    }
}

.container-fluid {
    margin-left: auto;
    margin-right: auto;
    padding-left: .3125rem;
    padding-right: .3125rem
}

.container-fluid:after {
    content: "";
    display: table;
    clear: both
}

.row {
    margin-left: -.3125rem;
    margin-right: -.3125rem
}

.row:after {
    content: "";
    display: table;
    clear: both
}

.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
    position: relative;
    min-height: 1px;
    padding-left: .3125rem;
    padding-right: .3125rem
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
    float: left
}

.col-xs-1 {
    width: 8.33333%
}

.col-xs-2 {
    width: 16.66667%
}

.col-xs-3 {
    width: 25%
}

.col-xs-4 {
    width: 33.33333%
}

.col-xs-5 {
    width: 41.66667%
}

.col-xs-6 {
    width: 50%
}

.col-xs-7 {
    width: 58.33333%
}

.col-xs-8 {
    width: 66.66667%
}

.col-xs-9 {
    width: 75%
}

.col-xs-10 {
    width: 83.33333%
}

.col-xs-11 {
    width: 91.66667%
}

.col-xs-12 {
    width: 100%
}

.col-xs-pull-0 {
    right: auto
}

.col-xs-pull-1 {
    right: 8.33333%
}

.col-xs-pull-2 {
    right: 16.66667%
}

.col-xs-pull-3 {
    right: 25%
}

.col-xs-pull-4 {
    right: 33.33333%
}

.col-xs-pull-5 {
    right: 41.66667%
}

.col-xs-pull-6 {
    right: 50%
}

.col-xs-pull-7 {
    right: 58.33333%
}

.col-xs-pull-8 {
    right: 66.66667%
}

.col-xs-pull-9 {
    right: 75%
}

.col-xs-pull-10 {
    right: 83.33333%
}

.col-xs-pull-11 {
    right: 91.66667%
}

.col-xs-pull-12 {
    right: 100%
}

.col-xs-push-0 {
    left: auto
}

.col-xs-push-1 {
    left: 8.33333%
}

.col-xs-push-2 {
    left: 16.66667%
}

.col-xs-push-3 {
    left: 25%
}

.col-xs-push-4 {
    left: 33.33333%
}

.col-xs-push-5 {
    left: 41.66667%
}

.col-xs-push-6 {
    left: 50%
}

.col-xs-push-7 {
    left: 58.33333%
}

.col-xs-push-8 {
    left: 66.66667%
}

.col-xs-push-9 {
    left: 75%
}

.col-xs-push-10 {
    left: 83.33333%
}

.col-xs-push-11 {
    left: 91.66667%
}

.col-xs-push-12 {
    left: 100%
}

.col-xs-offset-0 {
    margin-left: 0
}

.col-xs-offset-1 {
    margin-left: 8.33333%
}

.col-xs-offset-2 {
    margin-left: 16.66667%
}

.col-xs-offset-3 {
    margin-left: 25%
}

.col-xs-offset-4 {
    margin-left: 33.33333%
}

.col-xs-offset-5 {
    margin-left: 41.66667%
}

.col-xs-offset-6 {
    margin-left: 50%
}

.col-xs-offset-7 {
    margin-left: 58.33333%
}

.col-xs-offset-8 {
    margin-left: 66.66667%
}

.col-xs-offset-9 {
    margin-left: 75%
}

.col-xs-offset-10 {
    margin-left: 83.33333%
}

.col-xs-offset-11 {
    margin-left: 91.66667%
}

.col-xs-offset-12 {
    margin-left: 100%
}

@media (min-width:544px) {
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12 {
        float: left
    }
    .col-sm-1 {
        width: 8.33333%
    }
    .col-sm-2 {
        width: 16.66667%
    }
    .col-sm-3 {
        width: 25%
    }
    .col-sm-4 {
        width: 33.33333%
    }
    .col-sm-5 {
        width: 41.66667%
    }
    .col-sm-6 {
        width: 50%
    }
    .col-sm-7 {
        width: 58.33333%
    }
    .col-sm-8 {
        width: 66.66667%
    }
    .col-sm-9 {
        width: 75%
    }
    .col-sm-10 {
        width: 83.33333%
    }
    .col-sm-11 {
        width: 91.66667%
    }
    .col-sm-12 {
        width: 100%
    }
    .col-sm-pull-0 {
        right: auto
    }
    .col-sm-pull-1 {
        right: 8.33333%
    }
    .col-sm-pull-2 {
        right: 16.66667%
    }
    .col-sm-pull-3 {
        right: 25%
    }
    .col-sm-pull-4 {
        right: 33.33333%
    }
    .col-sm-pull-5 {
        right: 41.66667%
    }
    .col-sm-pull-6 {
        right: 50%
    }
    .col-sm-pull-7 {
        right: 58.33333%
    }
    .col-sm-pull-8 {
        right: 66.66667%
    }
    .col-sm-pull-9 {
        right: 75%
    }
    .col-sm-pull-10 {
        right: 83.33333%
    }
    .col-sm-pull-11 {
        right: 91.66667%
    }
    .col-sm-pull-12 {
        right: 100%
    }
    .col-sm-push-0 {
        left: auto
    }
    .col-sm-push-1 {
        left: 8.33333%
    }
    .col-sm-push-2 {
        left: 16.66667%
    }
    .col-sm-push-3 {
        left: 25%
    }
    .col-sm-push-4 {
        left: 33.33333%
    }
    .col-sm-push-5 {
        left: 41.66667%
    }
    .col-sm-push-6 {
        left: 50%
    }
    .col-sm-push-7 {
        left: 58.33333%
    }
    .col-sm-push-8 {
        left: 66.66667%
    }
    .col-sm-push-9 {
        left: 75%
    }
    .col-sm-push-10 {
        left: 83.33333%
    }
    .col-sm-push-11 {
        left: 91.66667%
    }
    .col-sm-push-12 {
        left: 100%
    }
    .col-sm-offset-0 {
        margin-left: 0
    }
    .col-sm-offset-1 {
        margin-left: 8.33333%
    }
    .col-sm-offset-2 {
        margin-left: 16.66667%
    }
    .col-sm-offset-3 {
        margin-left: 25%
    }
    .col-sm-offset-4 {
        margin-left: 33.33333%
    }
    .col-sm-offset-5 {
        margin-left: 41.66667%
    }
    .col-sm-offset-6 {
        margin-left: 50%
    }
    .col-sm-offset-7 {
        margin-left: 58.33333%
    }
    .col-sm-offset-8 {
        margin-left: 66.66667%
    }
    .col-sm-offset-9 {
        margin-left: 75%
    }
    .col-sm-offset-10 {
        margin-left: 83.33333%
    }
    .col-sm-offset-11 {
        margin-left: 91.66667%
    }
    .col-sm-offset-12 {
        margin-left: 100%
    }
}

@media (min-width:768px) {
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
        float: left
    }
    .col-md-1 {
        width: 8.33333%
    }
    .col-md-2 {
        width: 16.66667%
    }
    .col-md-3 {
        width: 25%
    }
    .col-md-4 {
        width: 33.33333%
    }
    .col-md-5 {
        width: 41.66667%
    }
    .col-md-6 {
        width: 50%
    }
    .col-md-7 {
        width: 58.33333%
    }
    .col-md-8 {
        width: 66.66667%
    }
    .col-md-9 {
        width: 75%
    }
    .col-md-10 {
        width: 83.33333%
    }
    .col-md-11 {
        width: 91.66667%
    }
    .col-md-12 {
        width: 100%
    }
    .col-md-pull-0 {
        right: auto
    }
    .col-md-pull-1 {
        right: 8.33333%
    }
    .col-md-pull-2 {
        right: 16.66667%
    }
    .col-md-pull-3 {
        right: 25%
    }
    .col-md-pull-4 {
        right: 33.33333%
    }
    .col-md-pull-5 {
        right: 41.66667%
    }
    .col-md-pull-6 {
        right: 50%
    }
    .col-md-pull-7 {
        right: 58.33333%
    }
    .col-md-pull-8 {
        right: 66.66667%
    }
    .col-md-pull-9 {
        right: 75%
    }
    .col-md-pull-10 {
        right: 83.33333%
    }
    .col-md-pull-11 {
        right: 91.66667%
    }
    .col-md-pull-12 {
        right: 100%
    }
    .col-md-push-0 {
        left: auto
    }
    .col-md-push-1 {
        left: 8.33333%
    }
    .col-md-push-2 {
        left: 16.66667%
    }
    .col-md-push-3 {
        left: 25%
    }
    .col-md-push-4 {
        left: 33.33333%
    }
    .col-md-push-5 {
        left: 41.66667%
    }
    .col-md-push-6 {
        left: 50%
    }
    .col-md-push-7 {
        left: 58.33333%
    }
    .col-md-push-8 {
        left: 66.66667%
    }
    .col-md-push-9 {
        left: 75%
    }
    .col-md-push-10 {
        left: 83.33333%
    }
    .col-md-push-11 {
        left: 91.66667%
    }
    .col-md-push-12 {
        left: 100%
    }
    .col-md-offset-0 {
        margin-left: 0
    }
    .col-md-offset-1 {
        margin-left: 8.33333%
    }
    .col-md-offset-2 {
        margin-left: 16.66667%
    }
    .col-md-offset-3 {
        margin-left: 25%
    }
    .col-md-offset-4 {
        margin-left: 33.33333%
    }
    .col-md-offset-5 {
        margin-left: 41.66667%
    }
    .col-md-offset-6 {
        margin-left: 50%
    }
    .col-md-offset-7 {
        margin-left: 58.33333%
    }
    .col-md-offset-8 {
        margin-left: 66.66667%
    }
    .col-md-offset-9 {
        margin-left: 75%
    }
    .col-md-offset-10 {
        margin-left: 83.33333%
    }
    .col-md-offset-11 {
        margin-left: 91.66667%
    }
    .col-md-offset-12 {
        margin-left: 100%
    }
}

@media (min-width:992px) {
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12 {
        float: left
    }
    .col-lg-1 {
        width: 8.33333%
    }
    .col-lg-2 {
        width: 16.66667%
    }
    .col-lg-3 {
        width: 25%
    }
    .col-lg-4 {
        width: 33.33333%
    }
    .col-lg-5 {
        width: 41.66667%
    }
    .col-lg-6 {
        width: 50%
    }
    .col-lg-7 {
        width: 58.33333%
    }
    .col-lg-8 {
        width: 66.66667%
    }
    .col-lg-9 {
        width: 75%
    }
    .col-lg-10 {
        width: 83.33333%
    }
    .col-lg-11 {
        width: 91.66667%
    }
    .col-lg-12 {
        width: 100%
    }
    .col-lg-pull-0 {
        right: auto
    }
    .col-lg-pull-1 {
        right: 8.33333%
    }
    .col-lg-pull-2 {
        right: 16.66667%
    }
    .col-lg-pull-3 {
        right: 25%
    }
    .col-lg-pull-4 {
        right: 33.33333%
    }
    .col-lg-pull-5 {
        right: 41.66667%
    }
    .col-lg-pull-6 {
        right: 50%
    }
    .col-lg-pull-7 {
        right: 58.33333%
    }
    .col-lg-pull-8 {
        right: 66.66667%
    }
    .col-lg-pull-9 {
        right: 75%
    }
    .col-lg-pull-10 {
        right: 83.33333%
    }
    .col-lg-pull-11 {
        right: 91.66667%
    }
    .col-lg-pull-12 {
        right: 100%
    }
    .col-lg-push-0 {
        left: auto
    }
    .col-lg-push-1 {
        left: 8.33333%
    }
    .col-lg-push-2 {
        left: 16.66667%
    }
    .col-lg-push-3 {
        left: 25%
    }
    .col-lg-push-4 {
        left: 33.33333%
    }
    .col-lg-push-5 {
        left: 41.66667%
    }
    .col-lg-push-6 {
        left: 50%
    }
    .col-lg-push-7 {
        left: 58.33333%
    }
    .col-lg-push-8 {
        left: 66.66667%
    }
    .col-lg-push-9 {
        left: 75%
    }
    .col-lg-push-10 {
        left: 83.33333%
    }
    .col-lg-push-11 {
        left: 91.66667%
    }
    .col-lg-push-12 {
        left: 100%
    }
    .col-lg-offset-0 {
        margin-left: 0
    }
    .col-lg-offset-1 {
        margin-left: 8.33333%
    }
    .col-lg-offset-2 {
        margin-left: 16.66667%
    }
    .col-lg-offset-3 {
        margin-left: 25%
    }
    .col-lg-offset-4 {
        margin-left: 33.33333%
    }
    .col-lg-offset-5 {
        margin-left: 41.66667%
    }
    .col-lg-offset-6 {
        margin-left: 50%
    }
    .col-lg-offset-7 {
        margin-left: 58.33333%
    }
    .col-lg-offset-8 {
        margin-left: 66.66667%
    }
    .col-lg-offset-9 {
        margin-left: 75%
    }
    .col-lg-offset-10 {
        margin-left: 83.33333%
    }
    .col-lg-offset-11 {
        margin-left: 91.66667%
    }
    .col-lg-offset-12 {
        margin-left: 100%
    }
}

@media (min-width:1200px) {
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12 {
        float: left
    }
    .col-xl-1 {
        width: 8.33333%
    }
    .col-xl-2 {
        width: 16.66667%
    }
    .col-xl-3 {
        width: 25%
    }
    .col-xl-4 {
        width: 33.33333%
    }
    .col-xl-5 {
        width: 41.66667%
    }
    .col-xl-6 {
        width: 50%
    }
    .col-xl-7 {
        width: 58.33333%
    }
    .col-xl-8 {
        width: 66.66667%
    }
    .col-xl-9 {
        width: 75%
    }
    .col-xl-10 {
        width: 83.33333%
    }
    .col-xl-11 {
        width: 91.66667%
    }
    .col-xl-12 {
        width: 100%
    }
    .col-xl-pull-0 {
        right: auto
    }
    .col-xl-pull-1 {
        right: 8.33333%
    }
    .col-xl-pull-2 {
        right: 16.66667%
    }
    .col-xl-pull-3 {
        right: 25%
    }
    .col-xl-pull-4 {
        right: 33.33333%
    }
    .col-xl-pull-5 {
        right: 41.66667%
    }
    .col-xl-pull-6 {
        right: 50%
    }
    .col-xl-pull-7 {
        right: 58.33333%
    }
    .col-xl-pull-8 {
        right: 66.66667%
    }
    .col-xl-pull-9 {
        right: 75%
    }
    .col-xl-pull-10 {
        right: 83.33333%
    }
    .col-xl-pull-11 {
        right: 91.66667%
    }
    .col-xl-pull-12 {
        right: 100%
    }
    .col-xl-push-0 {
        left: auto
    }
    .col-xl-push-1 {
        left: 8.33333%
    }
    .col-xl-push-2 {
        left: 16.66667%
    }
    .col-xl-push-3 {
        left: 25%
    }
    .col-xl-push-4 {
        left: 33.33333%
    }
    .col-xl-push-5 {
        left: 41.66667%
    }
    .col-xl-push-6 {
        left: 50%
    }
    .col-xl-push-7 {
        left: 58.33333%
    }
    .col-xl-push-8 {
        left: 66.66667%
    }
    .col-xl-push-9 {
        left: 75%
    }
    .col-xl-push-10 {
        left: 83.33333%
    }
    .col-xl-push-11 {
        left: 91.66667%
    }
    .col-xl-push-12 {
        left: 100%
    }
    .col-xl-offset-0 {
        margin-left: 0
    }
    .col-xl-offset-1 {
        margin-left: 8.33333%
    }
    .col-xl-offset-2 {
        margin-left: 16.66667%
    }
    .col-xl-offset-3 {
        margin-left: 25%
    }
    .col-xl-offset-4 {
        margin-left: 33.33333%
    }
    .col-xl-offset-5 {
        margin-left: 41.66667%
    }
    .col-xl-offset-6 {
        margin-left: 50%
    }
    .col-xl-offset-7 {
        margin-left: 58.33333%
    }
    .col-xl-offset-8 {
        margin-left: 66.66667%
    }
    .col-xl-offset-9 {
        margin-left: 75%
    }
    .col-xl-offset-10 {
        margin-left: 83.33333%
    }
    .col-xl-offset-11 {
        margin-left: 91.66667%
    }
    .col-xl-offset-12 {
        margin-left: 100%
    }
}

/* .table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem
} */

.table td,
.table th {
    padding: .75rem;
    line-height: 1.5;
    vertical-align: top;
    border-top: 1px solid #eceeef
}

.max-wid-100{
    max-width: 100% !important; 
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #eceeef
}

.table tbody+tbody {
    border-top: 2px solid #eceeef
}

.table .table {
    background-color: #fff
}

.table-sm td,
.table-sm th {
    padding: .3rem
}

.table-bordered,
.table-bordered td,
.table-bordered th {
    border: 1px solid #eceeef
}

.table-bordered thead td,
.table-bordered thead th {
    border-bottom-width: 2px
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #f9f9f9
}

.table-active,
.table-active>td,
.table-active>th,
.table-hover tbody tr:hover {
    background-color: #f5f5f5
}

.table-hover .table-active:hover,
.table-hover .table-active:hover>td,
.table-hover .table-active:hover>th {
    background-color: #e8e8e8
}

.table-success,
.table-success>td,
.table-success>th {
    background-color: #5cb85c
}

.table-hover .table-success:hover,
.table-hover .table-success:hover>td,
.table-hover .table-success:hover>th {
    background-color: #4cae4c
}

.table-info,
.table-info>td,
.table-info>th {
    background-color: #5bc0de
}

.table-hover .table-info:hover,
.table-hover .table-info:hover>td,
.table-hover .table-info:hover>th {
    background-color: #46b8da
}

.table-warning,
.table-warning>td,
.table-warning>th {
    background-color: #f0ad4e
}

.table-hover .table-warning:hover,
.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
    background-color: #eea236
}

.table-danger,
.table-danger>td,
.table-danger>th {
    background-color: #de6764
}

.table-hover .table-danger:hover,
.table-hover .table-danger:hover>td,
.table-hover .table-danger:hover>th {
    background-color: #da524f
}

.table-responsive {
    display: block;
    width: 100%;
    min-height: .01%;
    overflow-x: auto
}

.thead-inverse th {
    color: #fff;
    background-color: #373a3c
}

.thead-default th {
    color: #55595c;
    background-color: #eceeef
}

.table-inverse {
    color: #eceeef;
    background-color: #373a3c
}

.table-inverse.table-bordered {
    border: 0
}

.table-inverse td,
.table-inverse th,
.table-inverse thead th {
    border-color: #55595c
}

.table-reflow thead {
    float: left
}

.table-reflow tbody {
    display: block;
    white-space: nowrap
}

.table-reflow td,
.table-reflow th {
    border-top: 1px solid #eceeef;
    border-left: 1px solid #eceeef
}

.table-reflow td:last-child,
.table-reflow th:last-child {
    border-right: 1px solid #eceeef
}

.table-reflow tbody:last-child tr:last-child td,
.table-reflow tbody:last-child tr:last-child th,
.table-reflow tfoot:last-child tr:last-child td,
.table-reflow tfoot:last-child tr:last-child th,
.table-reflow thead:last-child tr:last-child td,
.table-reflow thead:last-child tr:last-child th {
    border-bottom: 1px solid #eceeef
}

.table-reflow tr {
    float: left
}

.table-reflow tr td,
.table-reflow tr th {
    display: block!important;
    border: 1px solid #eceeef
}

/*.form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-size: .875rem;
    line-height: 1.5;
    color: #55595c;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}

.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}

.form-control:focus {
    border-color: #66afe9;
    outline: none
}

.form-control::placeholder {
    color: #999;
    opacity: 1
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #eceeef;
    opacity: 1
}

.form-control:disabled {
    cursor: not-allowed
}

.form-control-file,
.form-control-range {
    display: block
}

.form-control-label {
    padding: .375rem .75rem;
    margin-bottom: 0
}*/

@media screen and (-webkit-min-device-pixel-ratio:0) {
    input[type=date].form-control,
    input[type=datetime-local].form-control,
    input[type=month].form-control,
    input[type=time].form-control {
        line-height: 2.0625rem
    }
    .input-group-sm input[type=date].form-control,
    .input-group-sm input[type=datetime-local].form-control,
    .input-group-sm input[type=month].form-control,
    .input-group-sm input[type=time].form-control,
    input[type=date].input-sm,
    input[type=datetime-local].input-sm,
    input[type=month].input-sm,
    input[type=time].input-sm {
        line-height: 1.8625rem
    }
    .input-group-lg input[type=date].form-control,
    .input-group-lg input[type=datetime-local].form-control,
    .input-group-lg input[type=month].form-control,
    .input-group-lg input[type=time].form-control,
    input[type=date].input-lg,
    input[type=datetime-local].input-lg,
    input[type=month].input-lg,
    input[type=time].input-lg {
        line-height: 3.16667rem
    }
}

.form-control-static {
    min-height: 2.0625rem;
    padding-top: .375rem;
    padding-bottom: .375rem;
    margin-bottom: 0
}

.form-control-static.form-control-lg,
.form-control-static.form-control-sm,
.input-group-lg>.form-control-static.form-control,
.input-group-lg>.form-control-static.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-static.btn,
.input-group-sm>.form-control-static.form-control,
.input-group-sm>.form-control-static.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-static.btn {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
    padding: .275rem .75rem;
    font-size: .875rem;
    line-height: 1.5
}

.form-control-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
    padding: .75rem 1.25rem;
    font-size: 1.25rem;
    line-height: 1.33333
}

.form-group {
    margin-bottom: 1rem
}

.checkbox,
.radio {
    position: relative;
    display: block;
    margin-bottom: .75rem
}

.checkbox label,
.radio label {
    padding-left: 1.25rem;
    margin-bottom: 0;
    font-weight: 400;
    cursor: pointer
}

.checkbox label input:only-child,
.radio label input:only-child {
    position: static
}

.checkbox-inline input[type=checkbox],
.checkbox input[type=checkbox],
.radio-inline input[type=radio],
.radio input[type=radio] {
    position: absolute;
    margin-top: .25rem;
    margin-left: -1.25rem
}

.checkbox+.checkbox,
.radio+.radio {
    margin-top: -.25rem
}

.checkbox-inline,
.radio-inline {
    position: relative;
    display: inline-block;
    padding-left: 1.25rem;
    margin-bottom: 0;
    font-weight: 400;
    vertical-align: middle;
    cursor: pointer
}

.checkbox-inline+.checkbox-inline,
.radio-inline+.radio-inline {
    margin-top: 0;
    margin-left: .75rem
}

.checkbox-inline.disabled,
.checkbox.disabled label,
.radio-inline.disabled,
.radio.disabled label,
input[type=checkbox].disabled,
input[type=checkbox]:disabled,
input[type=radio].disabled,
input[type=radio]:disabled {
    cursor: not-allowed
}

.form-control-danger,
.form-control-success,
.form-control-warning {
    padding-right: 2.25rem;
    background-repeat: no-repeat;
    background-position: center right .51562rem;
    background-size: 1.34062rem 1.34062rem
}

.has-success .checkbox,
.has-success .checkbox-inline,
.has-success.checkbox-inline label,
.has-success.checkbox label,
.has-success .form-control-label,
.has-success .radio,
.has-success .radio-inline,
.has-success.radio-inline label,
.has-success.radio label,
.has-success .text-help {
    color: #5cb85c
}

.has-success .form-control {
    border-color: #5cb85c
}

.has-success .input-group-addon {
    color: #5cb85c;
    border-color: #5cb85c;
    background-color: #eaf6ea
}

.has-success .form-control-feedback {
    color: #5cb85c
}

.has-success .form-control-success {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjNWNiODVjIiBkPSJNMjMzLjggNjEwYy0xMy4zIDAtMjYtNi0zNC0xNi44TDkwLjUgNDQ4LjhDNzYuMyA0MzAgODAgNDAzLjMgOTguOCAzODljMTguOC0xNC4yIDQ1LjUtMTAuNCA1OS44IDguNGw3MiA5NUw0NTEuMyAyNDJjMTIuNS0yMCAzOC44LTI2LjIgNTguOC0xMy43IDIwIDEyLjQgMjYgMzguNyAxMy43IDU4LjhMMjcwIDU5MGMtNy40IDEyLTIwLjIgMTkuNC0zNC4zIDIwaC0yeiIvPjwvc3ZnPg==")
}

.has-warning .checkbox,
.has-warning .checkbox-inline,
.has-warning.checkbox-inline label,
.has-warning.checkbox label,
.has-warning .form-control-label,
.has-warning .radio,
.has-warning .radio-inline,
.has-warning.radio-inline label,
.has-warning.radio label,
.has-warning .text-help {
    color: #f0ad4e
}

.has-warning .form-control {
    border-color: #f0ad4e
}

.has-warning .input-group-addon {
    color: #f0ad4e;
    border-color: #f0ad4e;
    background-color: #fff
}

.has-warning .form-control-feedback {
    color: #f0ad4e
}

.has-warning .form-control-warning {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjZjBhZDRlIiBkPSJNNjAzIDY0MC4ybC0yNzguNS01MDljLTMuOC02LjYtMTAuOC0xMC42LTE4LjUtMTAuNnMtMTQuNyA0LTE4LjUgMTAuNkw5IDY0MC4yYy0zLjcgNi41LTMuNiAxNC40LjIgMjAuOCAzLjggNi41IDEwLjggMTAuNCAxOC4zIDEwLjRoNTU3YzcuNiAwIDE0LjYtNCAxOC40LTEwLjQgMy41LTYuNCAzLjYtMTQuNCAwLTIwLjh6bS0yNjYuNC0zMGgtNjEuMlY1NDloNjEuMnY2MS4yem0wLTEwN2gtNjEuMlYzMDRoNjEuMnYxOTl6Ii8+PC9zdmc+")
}

.has-danger .checkbox,
.has-danger .checkbox-inline,
.has-danger.checkbox-inline label,
.has-danger.checkbox label,
.has-danger .form-control-label,
.has-danger .radio,
.has-danger .radio-inline,
.has-danger.radio-inline label,
.has-danger.radio label,
.has-danger .text-help {
    color: #de6764
}

.has-danger .form-control {
    border-color: #de6764
}

.has-danger .input-group-addon {
    color: #de6764;
    border-color: #de6764;
    background-color: #fff
}

.has-danger .form-control-feedback {
    color: #de6764
}

.has-danger .form-control-danger {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA2MTIgNzkyIj48cGF0aCBmaWxsPSIjZDk1MzRmIiBkPSJNNDQ3IDU0NC40Yy0xNC40IDE0LjQtMzcuNiAxNC40LTUyIDBsLTg5LTkyLjctODkgOTIuN2MtMTQuNSAxNC40LTM3LjcgMTQuNC01MiAwLTE0LjQtMTQuNC0xNC40LTM3LjYgMC01Mmw5Mi40LTk2LjMtOTIuNC05Ni4zYy0xNC40LTE0LjQtMTQuNC0zNy42IDAtNTJzMzcuNi0xNC4zIDUyIDBsODkgOTIuOCA4OS4yLTkyLjdjMTQuNC0xNC40IDM3LjYtMTQuNCA1MiAwIDE0LjMgMTQuNCAxNC4zIDM3LjYgMCA1MkwzNTQuNiAzOTZsOTIuNCA5Ni40YzE0LjQgMTQuNCAxNC40IDM3LjYgMCA1MnoiLz48L3N2Zz4=")
}

@media (min-width:544px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .form-control-static {
        display: inline-block
    }
    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle
    }
    .form-inline .input-group .form-control,
    .form-inline .input-group .input-group-addon,
    .form-inline .input-group .input-group-btn {
        width: auto
    }
    .form-inline .input-group>.form-control {
        width: 100%
    }
    .form-inline .form-control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .checkbox,
    .form-inline .radio {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .checkbox label,
    .form-inline .radio label {
        padding-left: 0
    }
    .form-inline .checkbox input[type=checkbox],
    .form-inline .radio input[type=radio] {
        position: relative;
        margin-left: 0
    }
    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem 1rem;
    font-size: .875rem;
    line-height: 1.5;
    transition: all .2s ease-in-out
}

.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.btn.focus,
.btn:focus,
.btn:hover {
    text-decoration: none
}

.btn.active,
.btn:active {
    background-image: none;
    outline: 0
}

.btn.disabled,
.btn:disabled {
    cursor: not-allowed;
    opacity: .65
}

a.btn.disabled,
fieldset[disabled] a.btn {
    pointer-events: none
}

.btn-primary {
    color: #fff;
    background-color: #2c3e50;
    border-color: #2c3e50
}

.btn-primary.focus,
.btn-primary:focus,
.btn-primary:hover {
    color: #fff;
    background-color: #1a252f;
    border-color: #161f29
}

.btn-primary.active,
.btn-primary:active,
.open>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #1a252f;
    border-color: #161f29;
    background-image: none
}

.btn-primary.active.focus,
.btn-primary.active:focus,
.btn-primary.active:hover,
.btn-primary:active.focus,
.btn-primary:active:focus,
.btn-primary:active:hover,
.open>.btn-primary.dropdown-toggle.focus,
.open>.btn-primary.dropdown-toggle:focus,
.open>.btn-primary.dropdown-toggle:hover {
    color: #fff;
    background-color: #0d1318;
    border-color: #000
}

.btn-primary.disabled.focus,
.btn-primary.disabled:focus,
.btn-primary.disabled:hover,
.btn-primary:disabled.focus,
.btn-primary:disabled:focus,
.btn-primary:disabled:hover {
    background-color: #2c3e50;
    border-color: #2c3e50
}

.btn-secondary {
    color: #373a3c;
    background-color: #fff;
    border-color: #ccc
}

.btn-secondary.focus,
.btn-secondary:focus,
.btn-secondary:hover {
    color: #373a3c;
    background-color: #e6e6e6;
    border-color: #adadad
}

.btn-secondary.active,
.btn-secondary:active,
.open>.btn-secondary.dropdown-toggle {
    color: #373a3c;
    background-color: #e6e6e6;
    border-color: #adadad;
    background-image: none
}

.btn-secondary.active.focus,
.btn-secondary.active:focus,
.btn-secondary.active:hover,
.btn-secondary:active.focus,
.btn-secondary:active:focus,
.btn-secondary:active:hover,
.open>.btn-secondary.dropdown-toggle.focus,
.open>.btn-secondary.dropdown-toggle:focus,
.open>.btn-secondary.dropdown-toggle:hover {
    color: #373a3c;
    background-color: #d4d4d4;
    border-color: #8c8c8c
}

.btn-secondary.disabled.focus,
.btn-secondary.disabled:focus,
.btn-secondary.disabled:hover,
.btn-secondary:disabled.focus,
.btn-secondary:disabled:focus,
.btn-secondary:disabled:hover {
    background-color: #fff;
    border-color: #ccc
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info.focus,
.btn-info:focus,
.btn-info:hover {
    color: #fff;
    background-color: #31b0d5;
    border-color: #2aabd2
}

.btn-info.active,
.btn-info:active,
.open>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    border-color: #2aabd2;
    background-image: none
}

.btn-info.active.focus,
.btn-info.active:focus,
.btn-info.active:hover,
.btn-info:active.focus,
.btn-info:active:focus,
.btn-info:active:hover,
.open>.btn-info.dropdown-toggle.focus,
.open>.btn-info.dropdown-toggle:focus,
.open>.btn-info.dropdown-toggle:hover {
    color: #fff;
    background-color: #269abc;
    border-color: #1f7e9a
}

.btn-info.disabled.focus,
.btn-info.disabled:focus,
.btn-info.disabled:hover,
.btn-info:disabled.focus,
.btn-info:disabled:focus,
.btn-info:disabled:hover {
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-success {
    color: #fff;
    background-color: #5cb85c;
    border-color: #5cb85c
}

.btn-success.focus,
.btn-success:focus,
.btn-success:hover {
    color: #fff;
    background-color: #449d44;
    border-color: #419641
}

.btn-success.active,
.btn-success:active,
.open>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #449d44;
    border-color: #419641;
    background-image: none
}

.btn-success.active.focus,
.btn-success.active:focus,
.btn-success.active:hover,
.btn-success:active.focus,
.btn-success:active:focus,
.btn-success:active:hover,
.open>.btn-success.dropdown-toggle.focus,
.open>.btn-success.dropdown-toggle:focus,
.open>.btn-success.dropdown-toggle:hover {
    color: #fff;
    background-color: #398439;
    border-color: #2d672d
}

.btn-success.disabled.focus,
.btn-success.disabled:focus,
.btn-success.disabled:hover,
.btn-success:disabled.focus,
.btn-success:disabled:focus,
.btn-success:disabled:hover {
    background-color: #5cb85c;
    border-color: #5cb85c
}

.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning.focus,
.btn-warning:focus,
.btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #eb9316
}

.btn-warning.active,
.btn-warning:active,
.open>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    border-color: #eb9316;
    background-image: none
}

.btn-warning.active.focus,
.btn-warning.active:focus,
.btn-warning.active:hover,
.btn-warning:active.focus,
.btn-warning:active:focus,
.btn-warning:active:hover,
.open>.btn-warning.dropdown-toggle.focus,
.open>.btn-warning.dropdown-toggle:focus,
.open>.btn-warning.dropdown-toggle:hover {
    color: #fff;
    background-color: #d58512;
    border-color: #b06d0f
}

.btn-warning.disabled.focus,
.btn-warning.disabled:focus,
.btn-warning.disabled:hover,
.btn-warning:disabled.focus,
.btn-warning:disabled:focus,
.btn-warning:disabled:hover {
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-danger {
    color: #fff;
    background-color: #de6764;
    border-color: #de6764
}

.btn-danger.focus,
.btn-danger:focus,
.btn-danger:hover {
    color: #fff;
    background-color: #d53e3a;
    border-color: #d33632
}

.btn-danger.active,
.btn-danger:active,
.open>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #d53e3a;
    border-color: #d33632;
    background-image: none
}

.btn-danger.active.focus,
.btn-danger.active:focus,
.btn-danger.active:hover,
.btn-danger:active.focus,
.btn-danger:active:focus,
.btn-danger:active:hover,
.open>.btn-danger.dropdown-toggle.focus,
.open>.btn-danger.dropdown-toggle:focus,
.open>.btn-danger.dropdown-toggle:hover {
    color: #fff;
    background-color: #c22d29;
    border-color: #a02522
}

.btn-danger.disabled.focus,
.btn-danger.disabled:focus,
.btn-danger.disabled:hover,
.btn-danger:disabled.focus,
.btn-danger:disabled:focus,
.btn-danger:disabled:hover {
    background-color: #de6764;
    border-color: #de6764
}

.btn-primary-outline {
    color: #2c3e50;
    background-image: none;
    background-color: transparent;
    border-color: #2c3e50
}

.btn-primary-outline.active,
.btn-primary-outline.focus,
.btn-primary-outline:active,
.btn-primary-outline:focus,
.btn-primary-outline:hover,
.open>.btn-primary-outline.dropdown-toggle {
    color: #fff;
    background-color: #2c3e50;
    border-color: #2c3e50
}

.btn-primary-outline.disabled.focus,
.btn-primary-outline.disabled:focus,
.btn-primary-outline.disabled:hover,
.btn-primary-outline:disabled.focus,
.btn-primary-outline:disabled:focus,
.btn-primary-outline:disabled:hover {
    border-color: #507192
}

.btn-secondary-outline {
    color: #ccc;
    background-image: none;
    background-color: transparent;
    border-color: #ccc
}

.btn-secondary-outline.active,
.btn-secondary-outline.focus,
.btn-secondary-outline:active,
.btn-secondary-outline:focus,
.btn-secondary-outline:hover,
.open>.btn-secondary-outline.dropdown-toggle {
    color: #fff;
    background-color: #ccc;
    border-color: #ccc
}

.btn-secondary-outline.disabled.focus,
.btn-secondary-outline.disabled:focus,
.btn-secondary-outline.disabled:hover,
.btn-secondary-outline:disabled.focus,
.btn-secondary-outline:disabled:focus,
.btn-secondary-outline:disabled:hover {
    border-color: #fff
}

.btn-info-outline {
    color: #5bc0de;
    background-image: none;
    background-color: transparent;
    border-color: #5bc0de
}

.btn-info-outline.active,
.btn-info-outline.focus,
.btn-info-outline:active,
.btn-info-outline:focus,
.btn-info-outline:hover,
.open>.btn-info-outline.dropdown-toggle {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info-outline.disabled.focus,
.btn-info-outline.disabled:focus,
.btn-info-outline.disabled:hover,
.btn-info-outline:disabled.focus,
.btn-info-outline:disabled:focus,
.btn-info-outline:disabled:hover {
    border-color: #b0e1ef
}

.btn-success-outline {
    color: #5cb85c;
    background-image: none;
    background-color: transparent;
    border-color: #5cb85c
}

.btn-success-outline.active,
.btn-success-outline.focus,
.btn-success-outline:active,
.btn-success-outline:focus,
.btn-success-outline:hover,
.open>.btn-success-outline.dropdown-toggle {
    color: #fff;
    background-color: #5cb85c;
    border-color: #5cb85c
}

.btn-success-outline.disabled.focus,
.btn-success-outline.disabled:focus,
.btn-success-outline.disabled:hover,
.btn-success-outline:disabled.focus,
.btn-success-outline:disabled:focus,
.btn-success-outline:disabled:hover {
    border-color: #a3d7a3
}

.btn-warning-outline {
    color: #f0ad4e;
    background-image: none;
    background-color: transparent;
    border-color: #f0ad4e
}

.btn-warning-outline.active,
.btn-warning-outline.focus,
.btn-warning-outline:active,
.btn-warning-outline:focus,
.btn-warning-outline:hover,
.open>.btn-warning-outline.dropdown-toggle {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning-outline.disabled.focus,
.btn-warning-outline.disabled:focus,
.btn-warning-outline.disabled:hover,
.btn-warning-outline:disabled.focus,
.btn-warning-outline:disabled:focus,
.btn-warning-outline:disabled:hover {
    border-color: #f8d9ac
}

.btn-danger-outline {
    color: #de6764;
    background-image: none;
    background-color: transparent;
    border-color: #de6764
}

.btn-danger-outline.active,
.btn-danger-outline.focus,
.btn-danger-outline:active,
.btn-danger-outline:focus,
.btn-danger-outline:hover,
.open>.btn-danger-outline.dropdown-toggle {
    color: #fff;
    background-color: #de6764;
    border-color: #de6764
}

.btn-danger-outline.disabled.focus,
.btn-danger-outline.disabled:focus,
.btn-danger-outline.disabled:hover,
.btn-danger-outline:disabled.focus,
.btn-danger-outline:disabled:focus,
.btn-danger-outline:disabled:hover {
    border-color: #f0b9b8
}

.btn-link {
    font-weight: 400;
    color: #2c3e50;
    border-radius: 0
}

.btn-link,
.btn-link.active,
.btn-link:active,
.btn-link:disabled {
    background-color: transparent
}

.btn-link,
.btn-link:active,
.btn-link:focus,
.btn-link:hover {
    border-color: transparent
}

.btn-link:focus,
.btn-link:hover {
    color: #11181f;
    text-decoration: underline;
    background-color: transparent
}

.btn-link:disabled:focus,
.btn-link:disabled:hover {
    color: #818a91;
    text-decoration: none
}

.btn-group-lg>.btn,
.btn-lg {
    padding: .75rem 1.25rem;
    font-size: 1.25rem;
    line-height: 1.33333
}

.btn-group-sm>.btn,
.btn-sm {
    padding: .25rem .75rem;
    font-size: .875rem;
    line-height: 1.5
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: 5px
}

input[type=button].btn-block,
input[type=reset].btn-block,
input[type=submit].btn-block {
    width: 100%
}

.fade {
    opacity: 0;
    transition: opacity .15s linear
}

.fade.in {
    opacity: 1
}

.collapse {
    display: none
}

.collapse.in {
    display: block
}

.collapsing {
    height: 0;
    overflow: hidden;
    transition-timing-function: ease;
    transition-duration: .35s;
    transition-property: height
}

.collapsing,
.dropdown,
.dropup {
    position: relative
}

.dropdown-toggle:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-right: .25rem;
    margin-left: .25rem;
    vertical-align: middle;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-left: .3em solid transparent
}

.dropdown-toggle:focus {
    outline: 0
}

.dropup .dropdown-toggle:after {
    border-top: 0;
    border-bottom: .3em solid
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 8;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: .875rem;
    color: #373a3c;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .15)
}

.dropdown-divider {
    height: 1px;
    margin: .5rem 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.5;
    color: #373a3c;
    text-align: inherit;
    white-space: nowrap;
    background: none;
    border: 0
}

.dropdown-item:focus,
.dropdown-item:hover {
    color: #2b2d2f;
    text-decoration: none;
    background-color: #f5f5f5
}

.dropdown-item.active,
.dropdown-item.active:focus,
.dropdown-item.active:hover {
    color: #fff;
    text-decoration: none;
    background-color: #2c3e50;
    outline: 0
}

.dropdown-item.disabled,
.dropdown-item.disabled:focus,
.dropdown-item.disabled:hover {
    color: #818a91
}

.dropdown-item.disabled:focus,
.dropdown-item.disabled:hover {
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent;
    background-image: none;
    filter: "progid:DXImageTransform.Microsoft.gradient(enabled = false)"
}

.open>.dropdown-menu {
    display: block
}

.open>a {
    outline: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: .875rem;
    line-height: 1.5;
    color: #818a91;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 7
}

.pull-right>.dropdown-menu {
    right: 0;
    left: auto
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
    content: "";
    border-top: 0;
    border-bottom: .3em solid
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 2px
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.btn-group-vertical>.btn,
.btn-group>.btn {
    position: relative;
    float: left
}

.btn-group-vertical>.btn.active,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:hover,
.btn-group>.btn.active,
.btn-group>.btn:active,
.btn-group>.btn:focus,
.btn-group>.btn:hover {
    z-index: 1
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    margin-left: -5px
}

.btn-toolbar:after {
    content: "";
    display: table;
    clear: both
}

.btn-toolbar .btn-group,
.btn-toolbar .input-group {
    float: left
}

.btn-toolbar>.btn,
.btn-toolbar>.btn-group,
.btn-toolbar>.input-group {
    margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child {
    margin-left: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn-group>.btn+.dropdown-toggle {
    padding-right: 8px;
    padding-left: 8px
}

.btn-group-lg.btn-group>.btn+.dropdown-toggle,
.btn-group>.btn-lg+.dropdown-toggle {
    padding-right: 12px;
    padding-left: 12px
}

.btn .caret {
    margin-left: 0
}

.btn-group-lg>.btn .caret,
.btn-lg .caret {
    border-width: .3em .3em 0;
    border-bottom-width: 0
}

.dropup .btn-group-lg>.btn .caret,
.dropup .btn-lg .caret {
    border-width: 0 .3em .3em
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group,
.btn-group-vertical>.btn-group>.btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%
}

.btn-group-vertical>.btn-group:after {
    content: "";
    display: table;
    clear: both
}

.btn-group-vertical>.btn-group>.btn {
    float: none
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
    border-top-right-radius: .25rem
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-bottom-left-radius: .25rem
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

[data-toggle=buttons]>.btn-group>.btn input[type=checkbox],
[data-toggle=buttons]>.btn-group>.btn input[type=radio],
[data-toggle=buttons]>.btn input[type=checkbox],
[data-toggle=buttons]>.btn input[type=radio] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate
}

.input-group .form-control {
    position: relative;
    z-index: 1;
    float: left;
    width: 100%;
    margin-bottom: 0
}

.input-group .form-control:active,
.input-group .form-control:focus,
.input-group .form-control:hover {
    z-index: 2
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
    display: table-cell
}

.input-group-addon,
.input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: .375rem .75rem;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1;
    color: #55595c;
    text-align: center;
    background-color: #eceeef;
    border: 1px solid #ccc
}

.input-group-addon.form-control-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn {
    padding: .275rem .75rem;
    font-size: .875rem
}

.input-group-addon.form-control-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn {
    padding: .75rem 1.25rem;
    font-size: 1.25rem
}

.input-group-addon input[type=checkbox],
.input-group-addon input[type=radio] {
    margin-top: 0
}

.input-group-addon:first-child {
    border-right: 0
}

.input-group-addon:last-child {
    border-left: 0
}

.input-group-btn {
    font-size: 0;
    white-space: nowrap
}

.input-group-btn,
.input-group-btn>.btn {
    position: relative
}

.input-group-btn>.btn+.btn {
    margin-left: -1px
}

.input-group-btn>.btn:active,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:hover {
    z-index: 2
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
    margin-right: -1px
}

.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group {
    z-index: 1;
    margin-left: -1px
}

.input-group-btn:last-child>.btn-group:active,
.input-group-btn:last-child>.btn-group:focus,
.input-group-btn:last-child>.btn-group:hover,
.input-group-btn:last-child>.btn:active,
.input-group-btn:last-child>.btn:focus,
.input-group-btn:last-child>.btn:hover {
    z-index: 2
}

.c-input {
    position: relative;
    display: inline;
    padding-left: 1.5rem;
    color: #555;
    cursor: pointer
}

.c-input>input {
    position: absolute;
    z-index: -1;
    opacity: 0
}

.c-input>input:checked~.c-indicator {
    color: #fff;
    background-color: #0074d9
}

.c-input>input:focus~.c-indicator {
    box-shadow: 0 0 0 .075rem #fff, 0 0 0 .2rem #0074d9
}

.c-input>input:active~.c-indicator {
    color: #fff;
    background-color: #84c6ff
}

.c-input+.c-input {
    margin-left: 1rem
}

.c-indicator {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    font-size: 65%;
    line-height: 1rem;
    color: #eee;
    text-align: center;
    user-select: none;
    background-color: #eee;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 50% 50%
}

.c-checkbox .c-indicator {
    border-radius: .25rem
}

.c-checkbox input:checked~.c-indicator {
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgOCA4IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA4IDgiIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTYuNCwxTDUuNywxLjdMMi45LDQuNUwyLjEsMy43TDEuNCwzTDAsNC40bDAuNywwLjdsMS41LDEuNWwwLjcsMC43bDAuNy0wLjdsMy41LTMuNWwwLjctMC43TDYuNCwxTDYuNCwxeiINCgkvPg0KPC9zdmc+DQo=)
}

.c-checkbox input:indeterminate~.c-indicator {
    background-color: #0074d9;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB3aWR0aD0iOHB4IiBoZWlnaHQ9IjhweCIgdmlld0JveD0iMCAwIDggOCIgZW5hYmxlLWJhY2tncm91bmQ9Im5ldyAwIDAgOCA4IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxwYXRoIGZpbGw9IiNGRkZGRkYiIGQ9Ik0wLDN2Mmg4VjNIMHoiLz4NCjwvc3ZnPg0K)
}

.c-radio .c-indicator {
    border-radius: 50%
}

.c-radio input:checked~.c-indicator {
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNy4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgOCA4IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA4IDgiIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTQsMUMyLjMsMSwxLDIuMywxLDRzMS4zLDMsMywzczMtMS4zLDMtM1M1LjcsMSw0LDF6Ii8+DQo8L3N2Zz4NCg==)
}

.c-inputs-stacked .c-input {
    display: inline
}

.c-inputs-stacked .c-input:after {
    display: block;
    margin-bottom: .25rem;
    content: ""
}

.c-inputs-stacked .c-input+.c-input {
    margin-left: 0
}

.c-select {
    display: inline-block;
    max-width: 100%;
    padding: .375rem 1.75rem .375rem .75rem;
    padding-right: .75rem\9;
    color: #55595c;
    vertical-align: middle;
    background: #fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAUCAMAAACzvE1FAAAADFBMVEUzMzMzMzMzMzMzMzMKAG/3AAAAA3RSTlMAf4C/aSLHAAAAPElEQVR42q3NMQ4AIAgEQTn//2cLdRKppSGzBYwzVXvznNWs8C58CiussPJj8h6NwgorrKRdTvuV9v16Afn0AYFOB7aYAAAAAElFTkSuQmCC) no-repeat right .75rem center;
    background-image: none\9;
    background-size: 8px 10px;
    border: 1px solid #ccc;
    -moz-appearance: none;
    -webkit-appearance: none
}

.c-select:focus {
    border-color: #51a7e8;
    outline: none
}

.c-select::-ms-expand {
    opacity: 0
}

.c-select-sm {
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 12px
}

.c-select-sm:not([multiple]) {
    height: 26px;
    min-height: 26px
}

.file {
    position: relative;
    display: inline-block;
    height: 2.5rem;
    cursor: pointer
}

.file input {
    min-width: 14rem;
    margin: 0;
    filter: alpha(opacity=0);
    opacity: 0
}

.file-custom {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 3;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #555;
    user-select: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: .25rem
}

.file-custom:after {
    content: "Choose file..."
}

.file-custom:before {
    position: absolute;
    top: -.075rem;
    right: -.075rem;
    bottom: -.075rem;
    z-index: 4;
    display: block;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #555;
    content: "Browse";
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 0 .25rem .25rem 0
}

.nav {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: inline-block
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.nav-link.disabled {
    color: #818a91
}

.nav-link.disabled,
.nav-link.disabled:focus,
.nav-link.disabled:hover {
    color: #818a91;
    cursor: not-allowed;
    background-color: transparent
}

.nav-inline .nav-item {
    display: inline-block
}

.nav-inline .nav-item+.nav-item,
.nav-inline .nav-link+.nav-link {
    margin-left: 1rem
}

.nav-tabs {
    border-bottom: 1px solid #ddd
}

.nav-tabs:after {
    content: "";
    display: table;
    clear: both
}

.nav-tabs .nav-item {
    float: left;
    margin-bottom: -1px
}

.nav-tabs .nav-item+.nav-item {
    margin-left: .2rem
}

.nav-tabs .nav-link {
    display: block;
    padding: .5em 1em;
    border: 1px solid transparent
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border-color: #eceeef #eceeef #ddd
}

.nav-tabs .nav-link.disabled,
.nav-tabs .nav-link.disabled:focus,
.nav-tabs .nav-link.disabled:hover {
    color: #818a91;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-item.open .nav-link,
.nav-tabs .nav-item.open .nav-link:focus,
.nav-tabs .nav-item.open .nav-link:hover,
.nav-tabs .nav-link.active,
.nav-tabs .nav-link.active:focus,
.nav-tabs .nav-link.active:hover {
    color: #55595c;
    background-color: #fff;
    border-color: #ddd #ddd transparent
}

.nav-pills:after {
    content: "";
    display: table;
    clear: both
}

.nav-pills .nav-item {
    float: left
}

.nav-pills .nav-item+.nav-item {
    margin-left: .2rem
}

.nav-pills .nav-link {
    display: block;
    padding: .5em 1em
}

.nav-pills .nav-item.open .nav-link,
.nav-pills .nav-item.open .nav-link:focus,
.nav-pills .nav-item.open .nav-link:hover,
.nav-pills .nav-link.active,
.nav-pills .nav-link.active:focus,
.nav-pills .nav-link.active:hover {
    color: #fff;
    cursor: default;
    background-color: #2c3e50
}

.nav-stacked .nav-item {
    display: block;
    float: none
}

.nav-stacked .nav-item+.nav-item {
    margin-top: .2rem;
    margin-left: 0
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.nav-tabs .dropdown-menu {
    margin-top: -1px
}

.navbar {
    position: relative;
    padding: .5rem 1rem
}

.navbar:after {
    content: "";
    display: table;
    clear: both
}

.navbar-full {
    z-index: 8
}

.navbar-fixed-bottom,
.navbar-fixed-top {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 9
}

.navbar-fixed-top {
    top: 0
}

.navbar-fixed-bottom {
    bottom: 0
}

.navbar-sticky-top {
    position: sticky;
    top: 0;
    z-index: 9;
    width: 100%
}

.navbar-brand {
    float: left;
    padding-top: .25rem;
    padding-bottom: .25rem;
    margin-right: 1rem;
    font-size: 1.25rem
}

.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none
}

.navbar-brand>img {
    display: block
}

.navbar-divider {
    float: left;
    width: 1px;
    padding-top: .425rem;
    padding-bottom: .425rem;
    margin-right: 1rem;
    margin-left: 1rem;
    overflow: hidden
}

.navbar-divider:before {
    content: "\00a0"
}

.navbar-toggler {
    padding: .5rem .75rem;
    font-size: 1.25rem;
    line-height: 1;
    background: none;
    border: 1px solid transparent
}

.navbar-toggler:focus,
.navbar-toggler:hover {
    text-decoration: none
}

@media (min-width:544px) {
    .navbar-toggleable-xs {
        display: block!important
    }
}

@media (min-width:768px) {
    .navbar-toggleable-sm {
        display: block!important
    }
}

@media (min-width:992px) {
    .navbar-toggleable-md {
        display: block!important
    }
}

.navbar-nav .nav-item {
    float: left
}

.navbar-nav .nav-link {
    display: block;
    padding-top: .425rem;
    padding-bottom: .425rem
}

.navbar-nav .nav-item+.nav-item,
.navbar-nav .nav-link+.nav-link {
    margin-left: 1rem
}

.navbar-light .navbar-brand,
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, .8)
}

.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, .3)
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, .6)
}

.navbar-light .navbar-nav .active>.nav-link,
.navbar-light .navbar-nav .active>.nav-link:focus,
.navbar-light .navbar-nav .active>.nav-link:hover,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.active:focus,
.navbar-light .navbar-nav .nav-link.active:hover,
.navbar-light .navbar-nav .nav-link.open,
.navbar-light .navbar-nav .nav-link.open:focus,
.navbar-light .navbar-nav .nav-link.open:hover,
.navbar-light .navbar-nav .open>.nav-link,
.navbar-light .navbar-nav .open>.nav-link:focus,
.navbar-light .navbar-nav .open>.nav-link:hover {
    color: rgba(0, 0, 0, .8)
}

.navbar-light .navbar-divider {
    background-color: rgba(0, 0, 0, .075)
}

.navbar-dark .navbar-brand,
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
    color: #fff
}

.navbar-dark .navbar-nav .nav-link {
    color: hsla(0, 0%, 100%, .5)
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
    color: hsla(0, 0%, 100%, .75)
}

.navbar-dark .navbar-nav .active>.nav-link,
.navbar-dark .navbar-nav .active>.nav-link:focus,
.navbar-dark .navbar-nav .active>.nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.active:focus,
.navbar-dark .navbar-nav .nav-link.active:hover,
.navbar-dark .navbar-nav .nav-link.open,
.navbar-dark .navbar-nav .nav-link.open:focus,
.navbar-dark .navbar-nav .nav-link.open:hover,
.navbar-dark .navbar-nav .open>.nav-link,
.navbar-dark .navbar-nav .open>.nav-link:focus,
.navbar-dark .navbar-nav .open>.nav-link:hover {
    color: #fff
}

.navbar-dark .navbar-divider {
    background-color: hsla(0, 0%, 100%, .075)
}

.card {
    position: relative;
    display: block;
    margin-bottom: .625rem;
    background-color: #fff;
    border: 1px solid #e5e5e5
}

.card-block {
    padding: .875rem;
}

.card-block-product {
    padding: .875rem;
    max-width: 98%;
    height: 250px;
}

.card-title {
    margin-bottom: .625rem
}

.card-subtitle {
    margin-top: -.3125rem
}

.card-subtitle,
.card-text:last-child {
    margin-bottom: 0
}

.card-link:hover {
    text-decoration: none
}

.card-link+.card-link {
    margin-left: .875rem
}

.card-header {
    border-bottom: 1px solid #e5e5e5
}

.card-footer,
.card-header {
    padding: .625rem .875rem;
    background-color: #f5f5f5
}

.card-footer {
    border-top: 1px solid #e5e5e5
}

.card-primary {
    background-color: #2c3e50;
    border-color: #2c3e50
}

.card-success {
    background-color: #5cb85c;
    border-color: #5cb85c
}

.card-info {
    background-color: #5bc0de;
    border-color: #5bc0de
}

.card-warning {
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.card-danger {
    background-color: #de6764;
    border-color: #de6764
}

.card-primary-outline {
    background-color: transparent;
    border-color: #2c3e50
}

.card-secondary-outline {
    background-color: transparent;
    border-color: #ccc
}

.card-info-outline {
    background-color: transparent;
    border-color: #5bc0de
}

.card-success-outline {
    background-color: transparent;
    border-color: #5cb85c
}

.card-warning-outline {
    background-color: transparent;
    border-color: #f0ad4e
}

.card-danger-outline {
    background-color: transparent;
    border-color: #de6764
}

.card-inverse .card-footer,
.card-inverse .card-header {
    border-bottom: 1px solid hsla(0, 0%, 100%, .2)
}

.card-inverse .card-blockquote,
.card-inverse .card-footer,
.card-inverse .card-header,
.card-inverse .card-title {
    color: #fff
}

.card-inverse .card-blockquote>footer,
.card-inverse .card-link,
.card-inverse .card-text {
    color: hsla(0, 0%, 100%, .65)
}

.card-inverse .card-link:focus,
.card-inverse .card-link:hover {
    color: #fff
}

.card-blockquote {
    padding: 0;
    margin-bottom: 0;
    border-left: 0
}

.card-img-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 1.25rem
}

@media (min-width:544px) {
    .card-deck {
        display: table;
        table-layout: fixed;
        border-spacing: 1.25rem 0
    }
    .card-deck .card {
        display: table-cell;
        width: 1%;
        vertical-align: top
    }
    .card-deck-wrapper {
        margin-right: -1.25rem;
        margin-left: -1.25rem
    }
}

@media (min-width:544px) {
    .card-group {
        display: table;
        width: 100%;
        table-layout: fixed
    }
    .card-group .card {
        display: table-cell;
        vertical-align: top
    }
    .card-group .card+.card {
        margin-left: 0;
        border-left: 0
    }
}

@media (min-width:544px) {
    .card-columns {
        column-count: 3;
        column-gap: 1.25rem
    }
    .card-columns .card {
        display: inline-block;
        width: 100%
    }
}

.breadcrumb {
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #eceeef
}

.breadcrumb:after {
    content: "";
    display: table;
    clear: both
}

.breadcrumb>li {
    float: left
}

.breadcrumb>li+li:before {
    padding-right: .5rem;
    padding-left: .5rem;
    color: #818a91;
    content: "/"
}

.breadcrumb>.active {
    color: #818a91
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin-top: 1rem;
    margin-bottom: 1rem
}

.page-item {
    display: inline
}

.page-item:first-child .page-link {
    margin-left: 0
}

.page-item.active .page-link,
.page-item.active .page-link:focus,
.page-item.active .page-link:hover {
    z-index: 1;
    color: #fff;
    cursor: default;
    background-color: #2c3e50;
    border-color: #2c3e50
}

.page-item.disabled .page-link,
.page-item.disabled .page-link:focus,
.page-item.disabled .page-link:hover {
    color: #818a91;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #ddd
}

.page-link {
    position: relative;
    float: left;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.5;
    color: #2c3e50;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd
}

.page-link:focus,
.page-link:hover {
    color: #11181f;
    background-color: #eceeef;
    border-color: #ddd
}

.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    line-height: 1.33333
}

.pagination-sm .page-link {
    padding: .275rem .75rem;
    font-size: .875rem;
    line-height: 1.5
}

.pager {
    padding-left: 0;
    margin-top: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    list-style: none
}

.pager:after {
    content: "";
    display: table;
    clear: both
}

.pager li {
    display: inline
}

.pager li>a,
.pager li>span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px
}

.pager li>a:focus,
.pager li>a:hover {
    text-decoration: none;
    background-color: #eceeef
}

.pager .disabled>a,
.pager .disabled>a:focus,
.pager .disabled>a:hover,
.pager .disabled>span {
    color: #818a91;
    cursor: not-allowed;
    background-color: #fff
}

.pager-next>a,
.pager-next>span {
    float: right
}

.pager-prev>a,
.pager-prev>span {
    float: left
}

.label {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline
}

.label:empty {
    display: none
}

.btn .label {
    position: relative;
    top: -1px
}

a.label:focus,
a.label:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.label-pill {
    padding-right: .6em;
    padding-left: .6em
}

.label-default {
    background-color: #818a91
}

.label-default[href]:focus,
.label-default[href]:hover {
    background-color: #687077
}

.label-primary {
    background-color: #2c3e50
}

.label-primary[href]:focus,
.label-primary[href]:hover {
    background-color: #1a252f
}

.label-success {
    background-color: #5cb85c
}

.label-success[href]:focus,
.label-success[href]:hover {
    background-color: #449d44
}

.label-info {
    background-color: #5bc0de
}

.label-info[href]:focus,
.label-info[href]:hover {
    background-color: #31b0d5
}

.label-warning {
    background-color: #f0ad4e
}

.label-warning[href]:focus,
.label-warning[href]:hover {
    background-color: #ec971f
}

.label-danger {
    background-color: #de6764
}

.label-danger[href]:focus,
.label-danger[href]:hover {
    background-color: #d53e3a
}

.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: #eceeef
}

@media (min-width:544px) {
    .jumbotron {
        padding: 4rem 2rem
    }
}

.jumbotron-hr {
    border-top-color: #d0d5d8
}

.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0
}

.alert {
    padding: 15px;
    margin-bottom: 1rem;
    border: 1px solid transparent
}

.alert>p,
.alert>ul {
    margin-bottom: 0
}

.alert>p+p {
    margin-top: 5px
}

.alert-heading {
    color: inherit
}

.alert-link {
    font-weight: 700
}

.alert-dismissible {
    padding-right: 35px
}

.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit
}

.alert-success {
    background-color: #5cb85c;
    border-color: #4cae4c;
    color: #fff
}

.alert-success hr {
    border-top-color: #449d44
}

.alert-success .alert-link {
    color: #e6e6e6
}

.alert-info {
    background-color: #5bc0de;
    border-color: #3db5d8;
    color: #fff
}

.alert-info hr {
    border-top-color: #2aabd2
}

.alert-info .alert-link {
    color: #e6e6e6
}

.alert-warning {
    background-color: #f0ad4e;
    border-color: #eea236;
    color: #fff
}

.alert-warning hr {
    border-top-color: #ec971f
}

.alert-warning .alert-link {
    color: #e6e6e6
}

.alert-danger {
    background-color: #de6764;
    border-color: #da524f;
    color: #fff
}

.alert-danger hr {
    border-top-color: #d53e3a
}

.alert-danger .alert-link {
    color: #e6e6e6
}

@keyframes a {
    0% {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

.progress {
    display: block;
    width: 100%;
    height: 1rem;
    margin-bottom: 1rem
}

.progress[value] {
    color: #0074d9;
    border: 0;
    appearance: none
}

.progress[value]::-webkit-progress-bar {
    background-color: #eee
}

.progress[value]::-webkit-progress-value:before {
    content: attr(value)
}

.progress[value]::-webkit-progress-value {
    background-color: #0074d9;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.progress[value="100"]::-webkit-progress-value {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem
}

@media screen and (min-width:0\0) {
    .progress {
        background-color: #eee
    }
    .progress-bar {
        display: inline-block;
        height: 1rem;
        text-indent: -999rem;
        background-color: #0074d9;
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem
    }
    .progress[width^="0"] {
        min-width: 2rem;
        color: #818a91;
        background-color: transparent;
        background-image: none
    }
    .progress[width="100%"] {
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem
    }
}

.progress-striped[value]::-webkit-progress-value {
    background-image: linear-gradient(45deg, hsla(0, 0%, 100%, .15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, .15) 0, hsla(0, 0%, 100%, .15) 75%, transparent 0, transparent);
    background-size: 1rem 1rem
}

.progress-striped[value]::-moz-progress-bar {
    background-image: linear-gradient(45deg, hsla(0, 0%, 100%, .15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, .15) 0, hsla(0, 0%, 100%, .15) 75%, transparent 0, transparent);
    background-size: 1rem 1rem
}

@media screen and (min-width:0\0) {
    .progress-bar-striped {
        background-image: linear-gradient(45deg, hsla(0, 0%, 100%, .15) 25%, transparent 0, transparent 50%, hsla(0, 0%, 100%, .15) 0, hsla(0, 0%, 100%, .15) 75%, transparent 0, transparent);
        background-size: 1rem 1rem
    }
}

.progress-animated[value]::-webkit-progress-value {
    animation: a 2s linear infinite
}

.progress-animated[value]::-moz-progress-bar {
    animation: a 2s linear infinite
}

@media screen and (min-width:0\0) {
    .progress-animated .progress-bar-striped {
        animation: a 2s linear infinite
    }
}

.progress-success[value]::-webkit-progress-value {
    background-color: #5cb85c
}

.progress-success[value]::-moz-progress-bar {
    background-color: #5cb85c
}

@media screen and (min-width:0\0) {
    .progress-success .progress-bar {
        background-color: #5cb85c
    }
}

.progress-info[value]::-webkit-progress-value {
    background-color: #5bc0de
}

.progress-info[value]::-moz-progress-bar {
    background-color: #5bc0de
}

@media screen and (min-width:0\0) {
    .progress-info .progress-bar {
        background-color: #5bc0de
    }
}

.progress-warning[value]::-webkit-progress-value {
    background-color: #f0ad4e
}

.progress-warning[value]::-moz-progress-bar {
    background-color: #f0ad4e
}

@media screen and (min-width:0\0) {
    .progress-warning .progress-bar {
        background-color: #f0ad4e
    }
}

.progress-danger[value]::-webkit-progress-value {
    background-color: #de6764
}

.progress-danger[value]::-moz-progress-bar {
    background-color: #de6764
}

@media screen and (min-width:0\0) {
    .progress-danger .progress-bar {
        background-color: #de6764
    }
}

.media {
    margin-top: 15px
}

.media:first-child {
    margin-top: 0
}

.media,
.media-body {
    overflow: hidden;
    zoom: 1
}

.media-body {
    width: 10000px
}

.media-body,
.media-left,
.media-right {
    display: table-cell;
    vertical-align: top
}

.media-middle {
    vertical-align: middle
}

.media-bottom {
    vertical-align: bottom
}

.media-object {
    display: block
}

.media-object.img-thumbnail {
    max-width: none
}

.media-right {
    padding-left: 10px
}

.media-left {
    padding-right: 10px
}

.media-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.media-list {
    padding-left: 0;
    list-style: none
}

.list-group {
    padding-left: 0;
    margin-bottom: 0
}

.list-group-item {
    position: relative;
    display: block;
    padding: .75rem 1.25rem;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd
}

.list-group-item:last-child {
    margin-bottom: 0
}

.list-group-flush .list-group-item {
    border-width: 1px 0;
    border-radius: 0
}

.list-group-flush:first-child .list-group-item:first-child {
    border-top: 0
}

.list-group-flush:last-child .list-group-item:last-child {
    border-bottom: 0
}

a.list-group-item,
button.list-group-item {
    width: 100%;
    color: #555;
    text-align: inherit
}

a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
    color: #333
}

a.list-group-item:focus,
a.list-group-item:hover,
button.list-group-item:focus,
button.list-group-item:hover {
    color: #555;
    text-decoration: none;
    background-color: #f5f5f5
}

.list-group-item.disabled,
.list-group-item.disabled:focus,
.list-group-item.disabled:hover {
    color: #818a91;
    cursor: not-allowed;
    background-color: #eceeef
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading {
    color: inherit
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text {
    color: #818a91
}

.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
    z-index: 1;
    color: #fff;
    background-color: #2c3e50;
    border-color: #2c3e50
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading>.small,
.list-group-item.active .list-group-item-heading>small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading>.small,
.list-group-item.active:focus .list-group-item-heading>small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading>.small,
.list-group-item.active:hover .list-group-item-heading>small {
    color: inherit
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:focus .list-group-item-text,
.list-group-item.active:hover .list-group-item-text {
    color: #8aa4be
}

.list-group-item-success {
    color: #fff;
    background-color: #5cb85c
}

a.list-group-item-success,
button.list-group-item-success {
    color: #fff
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
    color: inherit
}

a.list-group-item-success:focus,
a.list-group-item-success:hover,
button.list-group-item-success:focus,
button.list-group-item-success:hover {
    color: #fff;
    background-color: #4cae4c
}

a.list-group-item-success.active,
a.list-group-item-success.active:focus,
a.list-group-item-success.active:hover,
button.list-group-item-success.active,
button.list-group-item-success.active:focus,
button.list-group-item-success.active:hover {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.list-group-item-info {
    color: #fff;
    background-color: #5bc0de
}

a.list-group-item-info,
button.list-group-item-info {
    color: #fff
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
    color: inherit
}

a.list-group-item-info:focus,
a.list-group-item-info:hover,
button.list-group-item-info:focus,
button.list-group-item-info:hover {
    color: #fff;
    background-color: #46b8da
}

a.list-group-item-info.active,
a.list-group-item-info.active:focus,
a.list-group-item-info.active:hover,
button.list-group-item-info.active,
button.list-group-item-info.active:focus,
button.list-group-item-info.active:hover {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.list-group-item-warning {
    color: #fff;
    background-color: #f0ad4e
}

a.list-group-item-warning,
button.list-group-item-warning {
    color: #fff
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
    color: inherit
}

a.list-group-item-warning:focus,
a.list-group-item-warning:hover,
button.list-group-item-warning:focus,
button.list-group-item-warning:hover {
    color: #fff;
    background-color: #eea236
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:focus,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active,
button.list-group-item-warning.active:focus,
button.list-group-item-warning.active:hover {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.list-group-item-danger {
    color: #fff;
    background-color: #de6764
}

a.list-group-item-danger,
button.list-group-item-danger {
    color: #fff
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
    color: inherit
}

a.list-group-item-danger:focus,
a.list-group-item-danger:hover,
button.list-group-item-danger:focus,
button.list-group-item-danger:hover {
    color: #fff;
    background-color: #da524f
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:focus,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active,
button.list-group-item-danger.active:focus,
button.list-group-item-danger.active:hover {
    color: #fff;
    background-color: #fff;
    border-color: #fff
}

.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3
}

.embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden
}

.embed-responsive .embed-responsive-item,
.embed-responsive embed,
.embed-responsive iframe,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
}

.embed-responsive-21by9 {
    padding-bottom: 42.85714%
}

.embed-responsive-16by9 {
    padding-bottom: 56.25%
}

.embed-responsive-4by3 {
    padding-bottom: 75%
}

.embed-responsive-1by1 {
    padding-bottom: 100%
}

.close {
    float: right;
    font-size: 1.3125rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .2
}

.close:focus,
.close:hover {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: .5
}

button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none
}

.modal,
.modal-open {
    overflow: hidden
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 11;
    display: none;
    outline: 0;
    -webkit-overflow-scrolling: touch
}

.modal.fade .modal-dialog {
    transition: transform .3s ease-out;
    transform: translateY(-25%)
}

.modal.in .modal-dialog {
    transform: translate(0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px
}

.modal-content {
    position: relative;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    background-color: #000
}

.modal-backdrop.fade {
    opacity: 0
}

.modal-backdrop.in {
    opacity: .5
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5
}

.modal-header:after {
    content: "";
    display: table;
    clear: both
}

.modal-header .close {
    margin-top: -2px
}

.modal-title {
    margin: 0;
    line-height: 1.5
}

.modal-body {
    position: relative;
    padding: 15px
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5
}

.modal-footer:after {
    content: "";
    display: table;
    clear: both
}

.modal-footer .btn+.btn {
    margin-bottom: 0;
    margin-left: 5px
}

.modal-footer .btn-group .btn+.btn {
    margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
    margin-left: 0
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width:544px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto
    }
    .modal-sm {
        width: 300px
    }
}

@media (min-width:768px) {
    .modal-lg {
        width: 900px
    }
}

.tooltip {
    position: absolute;
    z-index: 13;
    display: block;
    font-family: Open Sans, Helvetica Neue, Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: .875rem;
    opacity: 0
}

.tooltip.in {
    opacity: .9
}

.tooltip.bs-tether-element-attached-bottom,
.tooltip.tooltip-top {
    padding: 5px 0;
    margin-top: -3px
}

.tooltip.bs-tether-element-attached-bottom .tooltip-arrow,
.tooltip.tooltip-top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.bs-tether-element-attached-left,
.tooltip.tooltip-right {
    padding: 0 5px;
    margin-left: 3px
}

.tooltip.bs-tether-element-attached-left .tooltip-arrow,
.tooltip.tooltip-right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.tooltip.bs-tether-element-attached-top,
.tooltip.tooltip-bottom {
    padding: 5px 0;
    margin-top: 3px
}

.tooltip.bs-tether-element-attached-top .tooltip-arrow,
.tooltip.tooltip-bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bs-tether-element-attached-right,
.tooltip.tooltip-left {
    padding: 0 5px;
    margin-left: -3px
}

.tooltip.bs-tether-element-attached-right .tooltip-arrow,
.tooltip.tooltip-left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12;
    display: block;
    max-width: 276px;
    padding: 1px;
    font-family: Open Sans, Helvetica Neue, Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: .875rem;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2)
}

.popover.bs-tether-element-attached-bottom,
.popover.popover-top {
    margin-top: -10px
}

.popover.bs-tether-element-attached-bottom .popover-arrow,
.popover.popover-top .popover-arrow {
    bottom: -11px;
    left: 50%;
    margin-left: -11px;
    border-top-color: rgba(0, 0, 0, .25);
    border-bottom-width: 0
}

.popover.bs-tether-element-attached-bottom .popover-arrow:after,
.popover.popover-top .popover-arrow:after {
    bottom: 1px;
    margin-left: -10px;
    content: "";
    border-top-color: #fff;
    border-bottom-width: 0
}

.popover.bs-tether-element-attached-left,
.popover.popover-right {
    margin-left: 10px
}

.popover.bs-tether-element-attached-left .popover-arrow,
.popover.popover-right .popover-arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-right-color: rgba(0, 0, 0, .25);
    border-left-width: 0
}

.popover.bs-tether-element-attached-left .popover-arrow:after,
.popover.popover-right .popover-arrow:after {
    bottom: -10px;
    left: 1px;
    content: "";
    border-right-color: #fff;
    border-left-width: 0
}

.popover.bs-tether-element-attached-top,
.popover.popover-bottom {
    margin-top: 10px
}

.popover.bs-tether-element-attached-top .popover-arrow,
.popover.popover-bottom .popover-arrow {
    top: -11px;
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: rgba(0, 0, 0, .25)
}

.popover.bs-tether-element-attached-top .popover-arrow:after,
.popover.popover-bottom .popover-arrow:after {
    top: 1px;
    margin-left: -10px;
    content: "";
    border-top-width: 0;
    border-bottom-color: #fff
}

.popover.bs-tether-element-attached-right,
.popover.popover-left {
    margin-left: -10px
}

.popover.bs-tether-element-attached-right .popover-arrow,
.popover.popover-left .popover-arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: rgba(0, 0, 0, .25)
}

.popover.bs-tether-element-attached-right .popover-arrow:after,
.popover.popover-left .popover-arrow:after {
    right: 1px;
    bottom: -10px;
    content: "";
    border-right-width: 0;
    border-left-color: #fff
}

.popover-title {
    padding: 8px 14px;
    margin: 0;
    font-size: .875rem;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb
}

.popover-content {
    padding: 9px 14px
}

.popover-arrow,
.popover-arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover-arrow {
    border-width: 11px
}

.popover-arrow:after {
    content: "";
    border-width: 10px
}

.carousel,
.carousel-inner {
    position: relative
}

.carousel-inner {
    width: 100%;
    overflow: hidden
}

.carousel-inner>.carousel-item {
    position: relative;
    display: none;
    transition: .6s ease-in-out left
}

.carousel-inner>.carousel-item>a>img,
.carousel-inner>.carousel-item>img {
    line-height: 1
}

@media (-webkit-transform-3d),
all and (transform-3d) {
    .carousel-inner>.carousel-item {
        transition: transform .6s ease-in-out;
        backface-visibility: hidden;
        perspective: 1000px
    }
    .carousel-inner>.carousel-item.active.right,
    .carousel-inner>.carousel-item.next {
        left: 0;
        transform: translate3d(100%, 0, 0)
    }
    .carousel-inner>.carousel-item.active.left,
    .carousel-inner>.carousel-item.prev {
        left: 0;
        transform: translate3d(-100%, 0, 0)
    }
    .carousel-inner>.carousel-item.active,
    .carousel-inner>.carousel-item.next.left,
    .carousel-inner>.carousel-item.prev.right {
        left: 0;
        transform: translateZ(0)
    }
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 15%;
    font-size: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
    opacity: .5
}

.carousel-control.left {
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001));
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1)
}

.carousel-control.right {
    right: 0;
    left: auto;
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5));
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1)
}

.carousel-control:focus,
.carousel-control:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: .9
}

.carousel-control .icon-next,
.carousel-control .icon-prev {
    position: absolute;
    top: 50%;
    z-index: 3;
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    font-family: serif;
    line-height: 1
}

.carousel-control .icon-prev {
    left: 50%;
    margin-left: -10px
}

.carousel-control .icon-next {
    right: 50%;
    margin-right: -10px
}

.carousel-control .icon-prev:before {
    content: "\2039"
}

.carousel-control .icon-next:before {
    content: "\203a"
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 6;
    width: 60%;
    padding-left: 0;
    margin-left: -30%;
    text-align: center;
    list-style: none
}

.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid #fff;
    border-radius: 10px
}

.carousel-indicators .active {
    width: 12px;
    height: 12px;
    margin: 0;
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 5;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6)
}

.carousel-caption .btn {
    text-shadow: none
}

@media (min-width:544px) {
    .carousel-control .icon-next,
    .carousel-control .icon-prev {
        width: 30px;
        height: 30px;
        margin-top: -15px;
        font-size: 30px
    }
    .carousel-control .icon-prev {
        margin-left: -15px
    }
    .carousel-control .icon-next {
        margin-right: -15px
    }
    .carousel-caption {
        right: 20%;
        left: 20%;
        padding-bottom: 30px
    }
    .carousel-indicators {
        bottom: 20px
    }
}

.clearfix:after {
    content: "";
    display: table;
    clear: both
}

.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.pull-xs-left {
    float: left!important
}

.pull-xs-right {
    float: right!important
}

.pull-xs-none {
    float: none!important
}

@media (min-width:544px) {
    .pull-sm-left {
        float: left!important
    }
    .pull-sm-right {
        float: right!important
    }
    .pull-sm-none {
        float: none!important
    }
}

@media (min-width:768px) {
    .pull-md-left {
        float: left!important
    }
    .pull-md-right {
        float: right!important
    }
    .pull-md-none {
        float: none!important
    }
}

@media (min-width:992px) {
    .pull-lg-left {
        float: left!important
    }
    .pull-lg-right {
        float: right!important
    }
    .pull-lg-none {
        float: none!important
    }
}

@media (min-width:1200px) {
    .pull-xl-left {
        float: left!important
    }
    .pull-xl-right {
        float: right!important
    }
    .pull-xl-none {
        float: none!important
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

.invisible {
    visibility: hidden!important
}

.text-hide {
    font: "0/0" a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.text-justify {
    text-align: justify!important
}

.text-nowrap {
    white-space: nowrap!important
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-xs-left {
    text-align: left!important
}

.text-xs-right {
    text-align: right!important
}

.text-xs-center {
    text-align: center!important
}

@media (min-width:544px) {
    .text-sm-left {
        text-align: left!important
    }
    .text-sm-right {
        text-align: right!important
    }
    .text-sm-center {
        text-align: center!important
    }
}

@media (min-width:768px) {
    .text-md-left {
        text-align: left!important
    }
    .text-md-right {
        text-align: right!important
    }
    .text-md-center {
        text-align: center!important
    }
}

@media (min-width:992px) {
    .text-lg-left {
        text-align: left!important
    }
    .text-lg-right {
        text-align: right!important
    }
    .text-lg-center {
        text-align: center!important
    }
}

@media (min-width:1200px) {
    .text-xl-left {
        text-align: left!important
    }
    .text-xl-right {
        text-align: right!important
    }
    .text-xl-center {
        text-align: center!important
    }
}

.text-lowercase {
    text-transform: lowercase!important
}

.text-uppercase {
    text-transform: uppercase!important
}

.text-capitalize {
    text-transform: capitalize!important
}

.font-weight-normal {
    font-weight: 400
}

.font-weight-bold {
    font-weight: 700
}

.font-italic {
    font-style: italic
}

.text-muted {
    color: #818a91
}

.text-primary {
    color: #2c3e50!important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #1a252f
}

.text-success {
    color: #5cb85c!important
}

a.text-success:focus,
a.text-success:hover {
    color: #449d44
}

.text-info {
    color: #5bc0de!important
}

a.text-info:focus,
a.text-info:hover {
    color: #31b0d5
}

.text-warning {
    color: #f0ad4e!important
}

a.text-warning:focus,
a.text-warning:hover {
    color: #ec971f
}

.text-danger {
    color: #de6764!important
}

a.text-danger:focus,
a.text-danger:hover {
    color: #d53e3a
}

.bg-inverse {
    color: #eceeef;
    background-color: #373a3c
}

.bg-faded {
    background-color: #f7f7f9
}

.bg-primary {
    color: #fff!important;
    background-color: #2c3e50!important
}

a.bg-primary:focus,
a.bg-primary:hover {
    background-color: #1a252f
}

.bg-success {
    color: #fff!important;
    background-color: #5cb85c!important
}

a.bg-success:focus,
a.bg-success:hover {
    background-color: #449d44
}

.bg-info {
    color: #fff!important;
    background-color: #5bc0de!important
}

a.bg-info:focus,
a.bg-info:hover {
    background-color: #31b0d5
}

.bg-warning {
    color: #fff!important;
    background-color: #f0ad4e!important
}

a.bg-warning:focus,
a.bg-warning:hover {
    background-color: #ec971f
}

.bg-danger {
    color: #fff!important;
    background-color: #de6764!important
}

a.bg-danger:focus,
a.bg-danger:hover {
    background-color: #d53e3a
}

.m-x-auto {
    margin-right: auto!important;
    margin-left: auto!important
}

.m-a-0 {
    margin: 0!important
}

.m-t-0 {
    margin-top: 0!important
}

.m-r-0 {
    margin-right: 0!important
}

.m-b-0 {
    margin-bottom: 0!important
}

.m-l-0,
.m-x-0 {
    margin-left: 0!important
}

.m-x-0 {
    margin-right: 0!important
}

.m-y-0 {
    margin-top: 0!important;
    margin-bottom: 0!important
}

.m-a-1 {
    margin: 1rem!important
}

.m-t-1 {
    margin-top: 1rem!important
}

.m-r-1 {
    margin-right: 1rem!important
}

.m-b-1 {
    margin-bottom: 1rem!important
}

.m-l-1,
.m-x-1 {
    margin-left: 1rem!important
}

.m-x-1 {
    margin-right: 1rem!important
}

.m-y-1 {
    margin-top: 1rem!important;
    margin-bottom: 1rem!important
}

.m-a-2 {
    margin: 1.5rem!important
}

.m-t-2 {
    margin-top: 1.5rem!important
}

.m-r-2 {
    margin-right: 1.5rem!important
}

.m-b-2 {
    margin-bottom: 1.5rem!important
}

.m-l-2,
.m-x-2 {
    margin-left: 1.5rem!important
}

.m-x-2 {
    margin-right: 1.5rem!important
}

.m-y-2 {
    margin-top: 1.5rem!important;
    margin-bottom: 1.5rem!important
}

.m-a-3 {
    margin: 3rem!important
}

.m-t-3 {
    margin-top: 3rem!important
}

.m-r-3 {
    margin-right: 3rem!important
}

.m-b-3 {
    margin-bottom: 3rem!important
}

.m-l-3,
.m-x-3 {
    margin-left: 3rem!important
}

.m-x-3 {
    margin-right: 3rem!important
}

.m-y-3 {
    margin-top: 3rem!important;
    margin-bottom: 3rem!important
}

.p-a-0 {
    padding: 0!important
}

.p-t-0 {
    padding-top: 0!important
}

.p-r-0 {
    padding-right: 0!important
}

.p-b-0 {
    padding-bottom: 0!important
}

.p-l-0,
.p-x-0 {
    padding-left: 0!important
}

.p-x-0 {
    padding-right: 0!important
}

.p-y-0 {
    padding-top: 0!important;
    padding-bottom: 0!important
}

.p-a-1 {
    padding: 1rem!important
}

.p-t-1 {
    padding-top: 1rem!important
}

.p-r-1 {
    padding-right: 1rem!important
}

.p-b-1 {
    padding-bottom: 1rem!important
}

.p-l-1,
.p-x-1 {
    padding-left: 1rem!important
}

.p-x-1 {
    padding-right: 1rem!important
}

.p-y-1 {
    padding-top: 1rem!important;
    padding-bottom: 1rem!important
}

.p-a-2 {
    padding: 1.5rem!important
}

.p-t-2 {
    padding-top: 1.5rem!important
}

.p-r-2 {
    padding-right: 1.5rem!important
}

.p-b-2 {
    padding-bottom: 1.5rem!important
}

.p-l-2,
.p-x-2 {
    padding-left: 1.5rem!important
}

.p-x-2 {
    padding-right: 1.5rem!important
}

.p-y-2 {
    padding-top: 1.5rem!important;
    padding-bottom: 1.5rem!important
}

.p-a-3 {
    padding: 3rem!important
}

.p-t-3 {
    padding-top: 3rem!important
}

.p-r-3 {
    padding-right: 3rem!important
}

.p-b-3 {
    padding-bottom: 3rem!important
}

.p-l-3,
.p-x-3 {
    padding-left: 3rem!important
}

.p-x-3 {
    padding-right: 3rem!important
}

.p-y-3 {
    padding-top: 3rem!important;
    padding-bottom: 3rem!important
}

.pos-f-t {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 9
}

.hidden-xs-up {
    display: none!important
}

@media (max-width:543px) {
    .hidden-xs-down {
        display: none!important
    }
}

@media (min-width:544px) {
    .hidden-sm-up {
        display: none!important
    }
}

@media (max-width:767px) {
    .hidden-sm-down {
        display: none!important
    }
}

@media (min-width:768px) {
    .hidden-md-up {
        display: none!important
    }
}

@media (max-width:991px) {
    .hidden-md-down {
        display: none!important
    }
}

@media (min-width:992px) {
    .hidden-lg-up {
        display: none!important
    }
}

@media (max-width:1199px) {
    .hidden-lg-down {
        display: none!important
    }
}

@media (min-width:1200px) {
    .hidden-xl-up {
        display: none!important
    }
}

.hidden-xl-down,
.visible-print-block {
    display: none!important
}

@media print {
    .visible-print-block {
        display: block!important
    }
}

.visible-print-inline {
    display: none!important
}

@media print {
    .visible-print-inline {
        display: inline!important
    }
}

.visible-print-inline-block {
    display: none!important
}

@media print {
    .visible-print-inline-block {
        display: inline-block!important
    }
}

@media print {
    .hidden-print {
        display: none!important
    }
}

.login-page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    background: #2c3e50;
    text-align: center;
    color: #fff;
    padding: 3em
}

.login-page .col-lg-4 {
    padding: 0
}

.login-page a {
    cursor: pointer
}

.login-page .input-lg {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px!important;
    line-height: 1.3333333;
    border-radius: 0
}

.login-page .input-underline {
    background: 0 0;
    border: none;
    box-shadow: none;
    border-bottom: 2px solid hsla(0, 0%, 100%, .5);
    color: #fff;
    border-radius: 0
}

.login-page .input-underline:focus {
    border-bottom: 2px solid #fff;
    box-shadow: none
}

.login-page .rounded-btn {
    border-radius: 50px;
    color: hsla(0, 0%, 100%, .8);
    background: #2c3e50;
    border: 2px solid hsla(0, 0%, 100%, .8);
    font-size: 18px;
    line-height: 40px;
    padding: 0 25px
}

.login-page .rounded-btn:active,
.login-page .rounded-btn:focus,
.login-page .rounded-btn:hover,
.login-page .rounded-btn:visited {
    color: #fff;
    border: 2px solid #fff;
    outline: none
}

.login-page h1 {
    font-weight: 300;
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 36px
}

.login-page h1 small {
    color: hsla(0, 0%, 100%, .7);
    font-size: 20px;
    font-weight: 300
}

.login-page .form-group {
    padding: 8px 0
}

.login-page .form-group input::-webkit-input-placeholder {
    color: hsla(0, 0%, 100%, .6)!important
}

.login-page .form-group input:-moz-placeholder,
.login-page .form-group input::-moz-placeholder {
    color: hsla(0, 0%, 100%, .6)!important
}

.login-page .form-group input:-ms-input-placeholder {
    color: hsla(0, 0%, 100%, .6)!important
}

.login-page .form-content {
    padding: 40px 0
}

#sidebar {
    width: 235px;
    position: fixed;
    top: 0;
    bottom: 0;
    color: #fff;
    background: #2c3e50
}

#sidebar a {
    cursor: pointer
}

#sidebar .sidenav-outer {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden
}

#sidebar .sidenav-outer .menu-body {
    border-top: 1px solid hsla(0, 0%, 100%, .2)
}

#sidebar .sidenav-outer .menu-body .nav li {
    border-bottom: 1px solid hsla(0, 0%, 100%, .2);
    border-left: 3px solid transparent;
    list-style-type: none
}

#sidebar .sidenav-outer .menu-body .nav li .website-name {
    padding: 15px;
    display: block;
    text-align: center;
    font-size: 20px;
    color: hsla(0, 0%, 100%, .6)
}

#sidebar .sidenav-outer .menu-body .nav li .logo {
    padding: 15px;
    display: block;
    text-align: center;
    margin-left: -5px
}

#sidebar .sidenav-outer .menu-body .nav li .logo p {
    margin-top: 15px;
    margin-bottom: 5px
}

#sidebar .sidenav-outer .menu-body .nav li a {
    transition: all .2s ease-in-out;
    display: block;
    color: #fff
}

#sidebar .sidenav-outer .menu-body .nav li a:focus {
    text-decoration: none
}

#sidebar .sidenav-outer .menu-body .nav li a i {
    display: inline-block;
    width: 50px;
    text-align: center;
    font-size: 20px;
    transition: all .3s ease;
    line-height: 50px;
    vertical-align: middle
}

#sidebar .sidenav-outer .menu-body .nav li .submenu {
    height: 0;
    display: none;
    transition: height .5s ease
}

#sidebar .sidenav-outer .menu-body .nav li.expand ul.submenu {
    height: auto!important;
    display: block!important;
    margin-left: 9px
}

#sidebar .sidenav-outer .menu-body .nav li.expand ul.submenu li {
    border-bottom: none;
    padding: 5px 15px 15px
}

#sidebar .sidenav-outer .menu-body .nav li.expand ul.submenu li a {
    display: block
}

#sidebar .sidenav-outer .menu-body .nav li:first-child {
    border-bottom: none
}

@media screen and (max-width:768px) {
    #sidebar {
        left: -235px
    }
    .main-container {
        margin-left: 0!important
    }
}

.sidebar-left-zero {
    left: 0!important
}

.main-container-ml-zero {
    margin-left: 235px!important;
    margin-right: -235px!important
}

.btn-bordered,
.btn-rounded {
    border-radius: 30px
}

.btn-bordered {
    background: #fff;
    border-width: 2px
}

.btn-bordered:hover {
    color: #fff
}

.btn-bordered.btn-primary {
    border-color: #2c3e50;
    color: #2c3e50
}

.btn-bordered.btn-primary:hover {
    background: #2c3e50;
    color: #fff
}

.btn-bordered.btn-info {
    border-color: #5bc0de;
    color: #5bc0de
}

.btn-bordered.btn-info:hover {
    background: #5bc0de;
    color: #fff
}

.btn-bordered.btn-warning {
    border-color: #f0ad4e;
    color: #f0ad4e
}

.btn-bordered.btn-warning:hover {
    background: #f0ad4e;
    color: #fff
}

.btn-bordered.btn-success {
    border-color: #5cb85c;
    color: #5cb85c
}

.btn-bordered.btn-success:hover {
    background: #5cb85c;
    color: #fff
}

.btn-bordered.btn-danger {
    border-color: #de6764;
    color: #de6764
}

.btn-bordered.btn-danger:hover {
    background: #de6764;
    color: #fff
}

.btn-bordered.disabled {
    color: #717171
}

.btn-circle {
    border-radius: 50%
}
.rideDetails{
        /* text-align: center; */
        float: right;
        /* right: 100px; */
        padding-right: 131px;
        color: red
}

.main-container {
    margin-left: 235px;
    margin-top: 0;
    padding: 10px;
    overflow: hidden
}

.main-container,
body {
    background: #ecf0f1
}

body a:focus,
body a:hover {
    text-decoration: none
}

body .card .card-header .card-title {
    margin-top: 5px;
    margin-bottom: 5px;
    font-size: 1rem
}

body .carousel .carousel-control {
    cursor: pointer
}

body .carousel .carousel-inner .fa-chevron-left:before {
    position: absolute;
    top: 47%;
    left: 30px
}

body .carousel .carousel-inner .fa-chevron-right:before {
    position: absolute;
    top: 47%;
    right: 30px
}

.animate {
    -webkit-animation: b .456s 1 ease;
    -moz-animation: b .456s 1 ease;
    -o-animation: b .456s 1 ease;
    padding:8px;
}

@-webkit-keyframes b {
    0% {
        -webkit-transform: rotate(0deg) scale(.955) skew(0deg) translate(0)
    }
    to {
        -webkit-transform: rotate(0deg) scale(1) skew(0deg) translate(0)
    }
}

.badge_style{
    position: absolute;
    left: 21%;
    background: red !important;
    width: 20px;
    height: 20px;
}

.badge_style_yellow{
    position: absolute;
    left: 21%;
    background: yellow !important;
    width: 20px;
    height: 20px;
}