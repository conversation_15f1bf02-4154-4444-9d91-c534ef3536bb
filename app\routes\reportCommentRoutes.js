import express from 'express';
import ReportCommentController from '../controllers/reportCommentController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initreportCommentRoutes = () => {
  const reportCommentRoutes = express.Router();

  reportCommentRoutes.post('/report',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.report);
  reportCommentRoutes.get('/viewComments/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.viewComments);
  reportCommentRoutes.get('/viewCommentsProcessed/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.viewCommentsProcessed);
  reportCommentRoutes.delete('/removeReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, ReportCommentController.removeReview);
  reportCommentRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.update);
 
  reportCommentRoutes.post('/checkReportedBy/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.checkReportedBy);
  reportCommentRoutes.put('/hideReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.hideReview);
  reportCommentRoutes.post('/reviewCount/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportCommentController.reviewCount);


  return reportCommentRoutes;
};

export default initreportCommentRoutes;
