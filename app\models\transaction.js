import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
import Transaction from '../models/transaction';
import User from '../models/user';
const timeZone = require('mongoose-timezone');



const TransactionSchema = new Schema({
  serial_number:Number,
  user_id: ObjectId,
  transaction_date: Date,
  transaction_id: String,
  transaction_type: String,
  transaction_reason: String,
  reason:String,
  amount: Number,
  due_to_user : ObjectId,
  due_to_user_name : String,
  subscribeFeePercentage:Number,
	origin:String,
	car_reg_no:String,
  referred_by_code:String,
  referred_by_name:String,
  total_amount:Number,
  gstAmount:Number,
  boostTripGstAmount:Number,
  payout_id:Number,
  status:{type:Boolean,default:false},
  
});

 TransactionSchema.plugin(timeZone, { paths: ['transaction_date'] });

/*

// Use pre middleware
TransactionSchema.pre('save', function (next) {

    // Only increment when the document is new
    if (this.isNew) {
        Transaction.count().then(res => {
            this.serial_number = res + 1; // Increment count
            next();
        });
    } else {
        next();
    }
});

TransactionSchema.post('save', function (next) {


  Transaction.findOne({"user_id":next.user_id,"serial_number": {$lt:next.serial_number}}).
              sort({"serial_number":-1}).then(lastTransaction => {
                console.log('postsave-------  ',lastTransaction);

       if(lastTransaction){         
    
      if (next.transaction_type == "CR") {

         var updatebalance = lastTransaction.total_amount + next.amount;
          Transaction.update({"transaction_id": next.transaction_id},
            {$set:{total_amount: updatebalance}}).then(res1 => {
              console.log('transaction CR update balance',res1)
            });

            
            User.update({"_id": next.user_id},
            {$set:{wallet_balance:updatebalance}}).then(resWallet => {
              console.log('transaction CR update balance',resWallet)
            });            
      }else if (next.transaction_type == "DR"){
         var updatebalance = lastTransaction.total_amount - next.amount;
          Transaction.update({"transaction_id": next.transaction_id},
            {$set:{total_amount: updatebalance}}).then(res => {
              console.log('transaction DR update balance',res)

             });

             User.update({"_id": next.user_id},
            {$set:{wallet_balance: updatebalance}}).then(resWalletDec => {
              console.log('transaction CR update balance',resWalletDec)
            });
      }
}
else{
         if (next.transaction_type == "CR") {


             Transaction.update({"transaction_id": next.transaction_id},
            {$set:{total_amount: next.amount}}).then(res1 => {
              console.log('transaction first time CR update balance',res1)
            });

            
            User.update({"_id": next.user_id},
            {$set:{wallet_balance:next.amount}}).then(resWallet => {
              console.log('transaction CR update balance',resWallet)
            });  
          }
          else{

             Transaction.update({"transaction_id": next.transaction_id},
            {$inc:{total_amount: -next.amount}}).then(res1 => {
              console.log('transaction first time CR update balance',res1)
            });

            
            User.update({"_id": next.user_id},
            {$inc:{wallet_balance:next.amount}}).then(resWallet => {
              console.log('transaction CR update balance',resWallet)
            });  

          }
}

  });



});
*/

export default mongoose.model('Transaction', TransactionSchema);
