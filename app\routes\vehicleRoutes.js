import express from 'express';
import VehicleController from '../controllers/vehicleController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initVehicleRoutes = () => {
  const vehicleRoutes = express.Router();

  vehicleRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.page);
  vehicleRoutes.get('/:user_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.show);
  vehicleRoutes.get('/count/:user_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.count);
  vehicleRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON><PERSON><PERSON> ,  VehicleController.create);
  vehicleRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON><PERSON><PERSON> ,  VehicleController.update);
  vehicleRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.remove);
  vehicleRoutes.post('/filterVehicleList',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.filterVehicleList);
  vehicleRoutes.post('/filterByUserVehicleList',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.filterByUserVehicleList);
  vehicleRoutes.post('/getVehicleListPagination',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.getVehicleListPagination);
  vehicleRoutes.post('/totalVehicles',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.totalVehicles);
  vehicleRoutes.post('/totalVehiclesForFirstTime',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.totalVehiclesForFirstTime);
  vehicleRoutes.post('/filterVehicleDetailsVehicleList',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleController.filterVehicleDetailsVehicleList);




  // vehicleRoutes.get('/', VehicleController.page);
  // vehicleRoutes.get('/:user_id', VehicleController.show);
  // vehicleRoutes.get('/count/:user_id', VehicleController.count);
  // vehicleRoutes.post('/', VehicleController.create);
  // vehicleRoutes.put('/update/:vehicle_id', VehicleController.update);
  // vehicleRoutes.delete('/:vehicle_id', VehicleController.remove);

  return vehicleRoutes;
};

export default initVehicleRoutes;
