import express from 'express';
import TransactionController from '../controllers/transactionController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initTransactionRoutes = () => {
  const transactionRoutes = express.Router();


  transactionRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.show);
  transactionRoutes.post('/transactionNoPayout/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.transactionNoPayout);
  // transactionRoutes.get('/all/usrs',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.page);
  transactionRoutes.get('/all/usrs/count',passport.authenticate('jwt', { session: false }), jwtAuthE<PERSON>r<PERSON><PERSON><PERSON> ,  TransactionController.count);
  transactionRoutes.post('/all/usrs/countPost',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  TransactionController.countPost);
  transactionRoutes.post('/all/usrs/countPostForFirstTime',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.countPostForFirstTime);
  transactionRoutes.post('/all/usrs',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.page);
  transactionRoutes.post('/all/usrs/filtertrancsactionsCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.filtertrancsactionsCount);
  // transactionRoutes.post('/details',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.productdetails);
  transactionRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.create);
  transactionRoutes.put('/:productId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.update);
  transactionRoutes.delete('/:productId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.remove);
  transactionRoutes.post('/generate_checksum',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.generateChecksum);
  transactionRoutes.post('/verify_checksum',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.verifyChecksum);
  transactionRoutes.get('/get_referral_transactions/:user_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getReferralTransactions);
  transactionRoutes.post('/search_transactions_for_date',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.searchTransactionsForDate);
  transactionRoutes.post('/search_referral_transactions_for_date',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.searchReferralTransactionsForDate);
  transactionRoutes.post('/getTransactionsforThisMonth',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getTransactionsforThisMonth);
  transactionRoutes.post('/searchReferralTransactionsForThisMonth',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.searchReferralTransactionsForThisMonth);
  transactionRoutes.post('/getAllReferralTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getAllReferralTransactions);
  transactionRoutes.post('/getAllReferralTransactionsByUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getAllReferralTransactionsByUser);
  transactionRoutes.post('/getTransactionsForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getTransactionsForUser);
  transactionRoutes.post('/getUserTransactionsForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getUserTransactionsForDate);
  transactionRoutes.post('/getFilterByDateForAllUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getFilterByDateForAllUsers);
  transactionRoutes.post('/getFilterByFivePerForAllUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getFilterByFivePerForAllUsers);
  transactionRoutes.post('/getFilterByTwentyPerForAllUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getFilterByTwentyPerForAllUsers);
  transactionRoutes.post('/getFilterByFivePerForSelectedUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getFilterByFivePerForSelectedUser);
  transactionRoutes.post('/getDebitTrancsactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getDebitTrancsactions);
  transactionRoutes.post('/getDebitTrancsactionsLength',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getDebitTrancsactionsLength);
  transactionRoutes.post('/getCreditTransactionsLength',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getCreditTransactionsLength);
  transactionRoutes.post('/getCreditTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getCreditTransactions);
  transactionRoutes.post('/updateTransaction',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.updateTransaction);
  transactionRoutes.post('/getTransactionBalance',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getTransactionBalance);
  transactionRoutes.get('/findLatestTransaction/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.findLatestTransaction);
  transactionRoutes.post('/updateTransactionForPayout',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.updateTransactionForPayout);
  transactionRoutes.post('/forPayout_id/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.forPayoutId);
  transactionRoutes.post('/foruser_id/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.foruser_id);
  transactionRoutes.post('/myTotalReferralIncome/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.myTotalReferralIncome);
  transactionRoutes.post('/filterTransactions/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.filterTransactions);
  transactionRoutes.post('/filterTransactionDetail/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.filterTransactionDetail);
  transactionRoutes.post('/getFilterByTwentyPerForSelectedUser/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getFilterByTwentyPerForSelectedUser);
  transactionRoutes.post('/filterUserTransactionsForDate/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.filterUserTransactionsForDate);
  transactionRoutes.post('/getTransactionsForPhoneNumber/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getTransactionsForPhoneNumber);

  transactionRoutes.post('/countReferralTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.countReferralTransactions);
  transactionRoutes.post('/filterReferralUserTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.filterReferralUserTransactions);
  transactionRoutes.post('/updateUserTransaction',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.updateUserTransaction);
  transactionRoutes.post('/countFilterByDateForAllUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.countFilterByDateForAllUsers);
  transactionRoutes.post('/lastTransactionForPayout',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.lastTransactionForPayout);
  transactionRoutes.post('/getTransactionsForReferralCode/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.getTransactionsForReferralCode);
  transactionRoutes.post('/totalEarningTransactions/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.totalEarningTransactions);
  transactionRoutes.post('/showReferTrc',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.showReferTrc);
  transactionRoutes.post('/totalReferTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.totalReferTransactions);
  //gateway txns
  transactionRoutes.post('/gatewayfilterTransactions',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.gatewayFilterTransactions);
  transactionRoutes.post('/gatewayfilterTransactionDetail',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TransactionController.gatewayFilterTransactionDetail);






 //  transactionRoutes.get('/:id', TransactionController.show);
 //  transactionRoutes.get('/all/usrs', TransactionController.page);
 // // transactionRoutes.post('/details',TransactionController.productdetails);
 //  transactionRoutes.post('/', TransactionController.create);
 //  transactionRoutes.put('/:productId', TransactionController.update);
 //  transactionRoutes.delete('/:productId', TransactionController.remove);
 //  transactionRoutes.post('/generate_checksum',TransactionController.generateChecksum);
 //  transactionRoutes.post('/verify_checksum',TransactionController.verifyChecksum);
 //  transactionRoutes.get('/get_referral_transactions/:user_id',TransactionController.getReferralTransactions);
 //  transactionRoutes.post('/search_transactions_for_date',TransactionController.searchTransactionsForDate);
  return transactionRoutes;
};

export default initTransactionRoutes;
