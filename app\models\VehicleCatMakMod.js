import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;

const VehicleCatMakModSchema = new Schema({
    
    // maker:String,
	vehicleMaker_id:ObjectId,
	vehicleModels_id:ObjectId,
	vehicleType_id:ObjectId,


    category: String,
    vehical_make: String,
    vehical_model: String,
    admin_id:String,

});

export default mongoose.model('VehicleCatMakMod', VehicleCatMakModSchema);
