/**
 * Notification Resource for AdminJS
 * Defines the AdminJS resource for Notification model
 * PRD Reference: Sections 4.6, 10.4
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Notification from '../../../../models/Notification.js';
import Customer from '../../../../models/Customer.js';
import Driver from '../../../../models/Driver.js';
import notificationService from '../../../../services/notificationService.js';
import logger from '../../../../utils/logger.js';
import { format } from 'date-fns';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register json Viewer component
const jsonViewerComponent = componentLoader.add(
  'jsonViewer', 
  join(__dirname, '../components/json-viewer.jsx') // Path to your React component
);

// Register dateRangeFilter component
const dateRangeFilterComponent = componentLoader.add(
  'dateRangeFilter', 
  join(__dirname, '../components/date-range-filter') // Path to your component
);
// Register send Bulk Notification component
const sendBulkNotificationComponent = componentLoader.add(
  'sendBulkNotification', 
  join(__dirname, '../components/send-bulk-notification.jsx') // Path to your React component
);

// Register notification Analytics component
const notificationAnalyticsComponent = componentLoader.add(
  'notificationAnalytics', 
  join(__dirname, '../components/notification-analytics.jsx') // Path to your component
);
// Register emailTemplateManager component
const emailTemplateManagerComponent = componentLoader.add(
  'emailTemplateManager', 
  join(__dirname, '../components/email-template-manager.jsx') // Path to your React component
);


 



/**
 * Notification resource configuration
 */
export default {
  resource: Notification,
  options: {
    navigation: {
      name: 'Notification Management',
      icon: 'Bell',
    },
    // Configure pagination for the list view
    listProperties: ['title', 'type', 'recipientModel', 'isRead', 'fcmSent', 'mqttSent', 'createdAt'],
    filterProperties: ['type', 'recipientModel', 'isRead', 'fcmSent', 'mqttSent', 'createdAt'],
    editProperties: ['isRead'],
    showProperties: [
      'title', 'body', 'type', 'recipient', 'recipientModel', 'reference', 'referenceModel',
      'data', 'isRead', 'fcmSent', 'mqttSent', 'createdAt'
    ],
    properties: {
      title: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        isTitle: true,
      },
      body: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'textarea',
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'bid', label: 'Bid' },
          { value: 'trip', label: 'Trip' },
          { value: 'chat', label: 'Chat' },
          { value: 'system', label: 'System' },
        ],
      },
      recipient: {
        isVisible: { list: false, filter: true, show: true, edit: false },
        reference: 'User',
      },
      recipientModel: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'Customer', label: 'Customer' },
          { value: 'Driver', label: 'Driver' },
        ],
      },
      reference: {
        isVisible: { list: false, filter: true, show: true, edit: false },
      },
      referenceModel: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        availableValues: [
          { value: 'Trip', label: 'Trip' },
          { value: 'Bid', label: 'Bid' },
          { value: 'ChatMessage', label: 'Chat Message' },
        ],
      },
      data: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'mixed',
        components: {
          show: jsonViewerComponent,
        },
      },
      isRead: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean',
      },
      fcmSent: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'boolean',
      },
      mqttSent: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'boolean',
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
        components: {
          filter: dateRangeFilterComponent,
        },
      },
    },
    // Configure pagination options
    perPage: 10,
    perPageOptions: [10, 25, 50],
    sort: {
      sortBy: 'createdAt',
      direction: 'desc',
    },
    actions: {
      // Custom action to send bulk notifications
      sendBulkNotifications: {
        actionType: 'resource',
        icon: 'Send',
        component: sendBulkNotificationComponent,
        handler: async (request, response, context) => {
          try {
            const { title, body, type, recipientModel, filters = {} } = request.payload;
            
            // Validate required fields
            if (!title || !body || !type || !recipientModel) {
              return {
                notice: {
                  message: 'Missing required fields',
                  type: 'error',
                },
              };
            }
            
            // Determine which model to query based on recipientModel
            const Model = recipientModel === 'Customer' ? Customer : Driver;
            
            // Build query based on filters
            const query = {};
            
            if (filters.city) {
              query['address.city'] = filters.city;
            }
            
            if (filters.state) {
              query['address.state'] = filters.state;
            }
            
            if (filters.status) {
              query.status = filters.status;
            }
            
            // Find users matching the criteria
            const users = await Model.find(query).select('_id fcm_token');
            
            if (users.length === 0) {
              return {
                notice: {
                  message: 'No users found matching the criteria',
                  type: 'error',
                },
              };
            }
            
            // Prepare users array for bulk notification
            const usersForNotification = users.map(user => ({
              userId: user._id,
              userModel: recipientModel,
              fcmToken: user.fcm_token,
            }));
            
            // Send bulk notifications
            const notifications = await notificationService.sendBulkNotifications({
              users: usersForNotification,
              title,
              body,
              data: { ...filters },
              type,
            });
            
            // Log the action
            logger.info('Admin sent bulk notifications', {
              adminId: context.currentAdmin.id,
              recipientCount: users.length,
              type,
              filters,
            });
            
            return {
              notice: {
                message: `Successfully sent ${notifications.length} notifications`,
                type: 'success',
              },
              redirectUrl: '/admin/resources/Notification',
            };
          } catch (error) {
            logger.error('Failed to send bulk notifications', { error: error.message });
            return {
              notice: {
                message: `Error: ${error.message}`,
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view notification analytics
      notificationAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        component: notificationAnalyticsComponent,
        handler: async (request, response, context) => {
          try {
            // Fetch data for analytics
            const notifications = await Notification.find();
            
            // Calculate delivery success rate
            const totalCount = notifications.length;
            const fcmSentCount = notifications.filter(n => n.fcmSent).length;
            const mqttSentCount = notifications.filter(n => n.mqttSent).length;
            const readCount = notifications.filter(n => n.isRead).length;
            
            const fcmSuccessRate = totalCount > 0 ? (fcmSentCount / totalCount) * 100 : 0;
            const mqttSuccessRate = totalCount > 0 ? (mqttSentCount / totalCount) * 100 : 0;
            const readRate = totalCount > 0 ? (readCount / totalCount) * 100 : 0;
            
            // Calculate notification volume by type
            const typeVolume = {};
            notifications.forEach(notification => {
              const type = notification.type;
              typeVolume[type] = (typeVolume[type] || 0) + 1;
            });
            
            // Calculate notification volume by recipient model
            const recipientModelVolume = {};
            notifications.forEach(notification => {
              const model = notification.recipientModel;
              recipientModelVolume[model] = (recipientModelVolume[model] || 0) + 1;
            });
            
            // Calculate notification volume by date
            const dateVolume = {};
            notifications.forEach(notification => {
              const date = format(notification.createdAt, 'yyyy-MM-dd');
              dateVolume[date] = (dateVolume[date] || 0) + 1;
            });
            
            // Sort dates for time series chart
            const sortedDates = Object.keys(dateVolume).sort();
            const sortedDateVolume = {};
            sortedDates.forEach(date => {
              sortedDateVolume[date] = dateVolume[date];
            });
            
            return {
              stats: {
                totalCount,
                fcmSentCount,
                mqttSentCount,
                readCount,
                fcmSuccessRate,
                mqttSuccessRate,
                readRate,
              },
              charts: {
                typeVolume,
                recipientModelVolume,
                dateVolume: sortedDateVolume,
              },
            };
          } catch (error) {
            logger.error('Failed to generate notification analytics', { error: error.message });
            return {
              error: error.message,
            };
          }
        },
      },
      // Custom action to manage email templates
      manageEmailTemplates: {
        actionType: 'resource',
        icon: 'Email',
        component: emailTemplateManagerComponent,
        handler: async (request, response, context) => {
          // This would be implemented with actual email template management
          // For now, we'll return a placeholder response
          return {
            message: 'Email template management would be implemented here',
          };
        },
      },
    },
  },
};