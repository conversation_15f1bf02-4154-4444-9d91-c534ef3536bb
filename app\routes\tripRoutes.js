import express from 'express';
import TripController from '../controllers/tripController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initTripRoutes = () => {
  const tripRoutes = express.Router();

  


  tripRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.page);
  tripRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.show);
  tripRoutes.get('/tripbyvehid/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.showTripByVehicleId);
  tripRoutes.get('/getCancel/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,  TripController.showSt);
  tripRoutes.get('/cancelRequestsForUser/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON><PERSON>r<PERSON><PERSON><PERSON> ,  TripController.cancelRequestsForUser);
  
  
  tripRoutes.get('/getriphistory/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripHistory);
  tripRoutes.get('/book/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequset);
  tripRoutes.get('/bookBackend/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequsetBackend);
  //Trip flow
  tripRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.create); //trip create
  tripRoutes.post('/search',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.search);
  tripRoutes.post('/searchLocal',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchLocal);
  
  tripRoutes.get('/search/count',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.simpleSearchCount);
  tripRoutes.post('/search/:requestType',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.simpleSearch);
  tripRoutes.post('/public',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.publicTrips);
  tripRoutes.post('/public/create', jwtAuthErrorHandler ,  TripController.createPublic); //trip create
  // tripRoutes.post('/search/:intercity',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.simpleSearchNonLocal);
  
  // request list - send 
  tripRoutes.get('/sendRequest/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.sendRequest); //send

  //request list - cancel 
  tripRoutes.get('/cancelSendRequests/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelRequest);
  
  //request sent
  tripRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.updateForBookingReq);
  
  //upcoming bookings
  tripRoutes.get('/bookUpcoming/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequestUpcoming);
  
  tripRoutes.post('/bookingRequests',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequests);
  tripRoutes.post('/bookingRequestsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestsPassenger);

  //Update trip
  tripRoutes.put('/updateS/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.update);
  
  tripRoutes.post('/boostsearch',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchForBoostTrip);
  tripRoutes.put('/boostTrip/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostTrip);
  tripRoutes.delete('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.remove);
  tripRoutes.get('/get_counts/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getCounts);
  tripRoutes.post('/upcomingTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTrips);
  tripRoutes.post('/upcomingTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsPassenger);
  tripRoutes.post('/pendingTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTrips);
  tripRoutes.post('/pendingTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsPassenger);
  tripRoutes.post('/cancelTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTrips);
  tripRoutes.post('/cancelTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripsPassenger);
  tripRoutes.post('/boostedTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTrips);
  tripRoutes.post('/boostedTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsPassenger);
  tripRoutes.post('/tripsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistory);
  tripRoutes.post('/tripsHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryPassenger);
  tripRoutes.post('/upcomingTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForVehicle);
  tripRoutes.post('/pendingTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsForVehicle);
  tripRoutes.post('/cancelTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripsForVehicle);
  tripRoutes.post('/boostedTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForVehicle);
  tripRoutes.post('/bookingRequestsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestsForVehicle);
  tripRoutes.post('/tripHistoryForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripHistoryForVehicle);
  tripRoutes.post('/upcomingTripsForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForDate);
  tripRoutes.post('/upcomingTripsForDatePassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForDatePassenger);
  tripRoutes.post('/pendingTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForDate);
  tripRoutes.post('/pendingTripForDatePassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForDatePassenger);
  tripRoutes.post('/tripsHistoryByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryByUserId);
  tripRoutes.post('/tripsHistoryByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryByUserIdPassenger);
  tripRoutes.post('/upcomingTripsByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsByUserId);
  tripRoutes.post('/upcomingTripsByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsByUserIdPassenger);
  tripRoutes.post('/pendingTripsByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsByUserId);
  tripRoutes.post('/pendingTripsByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsByUserIdPassenger);
  tripRoutes.post('/boostedTripsForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForUser);
  tripRoutes.post('/boostedTripsForUserPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForUserPassenger);
  tripRoutes.post('/filterTripsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistory);
  tripRoutes.post('/filterTripsHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryPassenger);
  tripRoutes.post('/filterTripsUpcoming',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsUpcoming);
  tripRoutes.post('/filterTripsUpcomingPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsUpcomingPassenger);
  tripRoutes.post('/filterPendingTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterPendingTrip);
  tripRoutes.post('/filterPendingTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterPendingTripPassenger);
  tripRoutes.post('/filterCancelTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterCancelTrip);
  tripRoutes.post('/filterCancelTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterCancelTripPassenger);
  tripRoutes.post('/filterBoostedTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTrip);
  tripRoutes.post('/filterBoostedTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTripPassenger);
  tripRoutes.post('/filterBookingRequest',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBookingRequest);
  tripRoutes.post('/filterBookingRequestPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBookingRequestPassenger);
  tripRoutes.post('/filterBoostedTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTripForUser);
  tripRoutes.post('/filterTripsHistoryForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryForUser);
  tripRoutes.post('/filterBookingRequestForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBookingRequestForUser);
  tripRoutes.post('/upcomingTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripForUser);
  tripRoutes.post('/pendingTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForUser);
  tripRoutes.post('/cancelTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripForUser);
  tripRoutes.post('/cancelTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripForDate);
  tripRoutes.post('/boostedTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripForDate);
  tripRoutes.post('/tripHistoryForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripHistoryForDate);
  tripRoutes.post('/bookingRequestForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestForDate);
  tripRoutes.post('/search_trip_history_for_date',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchTripHistoryForDate);
  tripRoutes.get('/getTripById/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripById);

  tripRoutes.post('/filterTripsDetailsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsDetailsHistory);
  tripRoutes.post('/totalTripHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.totalTripHistory);
  tripRoutes.post('/totalTripHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.totalTripHistoryPassenger);
  tripRoutes.post('/filterTripsHistoryCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryCount);
  tripRoutes.post('/searchPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchPassenger);
  tripRoutes.post('/searchPassengerCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchPassengerCount);
  tripRoutes.get('/sendRequestPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.sendRequestPassenger);
  tripRoutes.get('/bookRequsetPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequsetPassenger);
  tripRoutes.get('/bookRequsetPassengerBackend/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequsetPassengerBackend);
  tripRoutes.get('/getTripByIdPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripByIdPassenger);
  tripRoutes.get('/showPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.showPassenger);
  tripRoutes.get('/bookUpcomingPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequestUpcomingPassenger);
  tripRoutes.get('/cancelSendRequestsPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelRequestPassenger);
  tripRoutes.get('/getCancelPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.showStPassenger);
  tripRoutes.get('/getripHistoryPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripHistoryPassenger);
  tripRoutes.post('/search_trip_history_for_date_passenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchTripHistoryForDatePassenger);
  tripRoutes.get('/getSupportCounts/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getSupportCounts);



  // tripRoutes.get('/', TripController.page);
  // tripRoutes.get('/:id', TripController.show);
  // tripRoutes.get('/tripbyvehid/:id', TripController.showTripByVehicleId);
  // tripRoutes.get('/getCancel/:id', TripController.showSt);
  // tripRoutes.get('/sendRequest/:id', TripController.sendRequest);
  // tripRoutes.get('/cancelSendRequests/:id', TripController.cancelRequest);
  // tripRoutes.get('/getriphistory/:id', TripController.getTripHistory); 
  // tripRoutes.get('/book/:id', TripController.bookRequset);
  // tripRoutes.get('/bookUpcoming/:id', TripController.bookRequestUpcoming);
  // tripRoutes.post('/', TripController.create);
  // tripRoutes.post('/search', TripController.search);
  // tripRoutes.post('/boostsearch', TripController.searchForBoostTrip);
  // tripRoutes.put('/update/:id', TripController.updateForBookingReq);
  // tripRoutes.put('/boostTrip/:id', TripController.boostTrip);
  // tripRoutes.put('/updateS/:id', TripController.update);
  // tripRoutes.delete('/:id', TripController.remove);
  // tripRoutes.get('/get_counts/:id', TripController.getCounts);




  // tripRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.page
  // });

  // tripRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.show
  // });

  //  tripRoutes.get('/get_counts/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.getCounts
  // });

  return tripRoutes;
};

export default initTripRoutes;
