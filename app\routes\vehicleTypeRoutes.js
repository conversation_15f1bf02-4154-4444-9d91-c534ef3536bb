import express from 'express';
import VehicleTypeController from '../controllers/vehicleTypeController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initvehicleTypeRoutes = () => {
  const vehicleTypeRoutes = express.Router();

  // vehicleMakerRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  VehicleMakerController.create);
 

  return vehicleTypeRoutes;
};

export default initvehicleTypeRoutes;
