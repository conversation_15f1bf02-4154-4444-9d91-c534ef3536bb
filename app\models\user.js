import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
var bcrypt = require('bcrypt');
const timeZone = require('mongoose-timezone');

const UserSchema = new Schema({
    name: String,
    phone_number: String,
    email: String,
    gst:Number, 
    gstAmount:Number, 
    // affiliated_user: {type:Boolean,default:false},
    // user_added_by_admin:{type:Boolean,default:false},
    boostTripGst:Number,
    boostTripGstAmount:Number,
    profile_image_url: String,
    password:String,
    otp:Number,
    active_status: {
        type: Boolean,
        default: false
    },
    verified: Boolean,
    buisness_name:String,
    gstNumber:String,
    qrCode:String,
    shareImage:String,
    shareImageEng:String,
    shareImageHindi:String,
    location: {
        coordinates : [],     
        type : {type:String, default:'Point'}, 
    },
    live_location: { type:String, default: null},
    // lat:Number,
    // lng:Number,
    chat: {
        socketId: String,
        online: Boolean,
        lastSeen: Date
    },
    suspend:{type:Boolean,default:false},
    suspend_remarks:String,
    activate_remarks:String,
    suspend_remarks_date:Date,
    activate_remarks_date:Date,
    referal_code:String,
    referred_by_code:String,
    address: String,
    district: { type: String, default: null},
    state: String,
    created_at: Date,
    pincode: String,
    is_subscribed: {type:Boolean,default:false},
    wallet_balance: {type:Number,default:0},
    renewal_date: Date,
    lastSubscribed: Date,
    report:[], // array of objects
    rating_review:[], // array of objects
    ratings:Number,
    role:{
        type: String,
        enum: ['referalUser' ,'admin','subAdmin','normalUser'],
        default : 'normalUser'
    },
    bankDetails:{
        accountName:String,
        hiddenAccountNo:String,
        ifsceCode:String,
        bankName:String,
        branchAddress:String,
        amount:Number,
        date:String,
        reason:String,
        status:String,
        transaction_type:String,
        remarks:{type:String,default:''},
    },
    subscriptionFeesByAdmin:Number,
    freetrialperiod:Number,
    billingEmail:String,
    noReplyEmail:String,
    contactEmail:String,
    tripBoostFeesByAdmin:Number,
    fcm_registration_token: String,
    device_id: String,
    device_platform: String,
    notification: Boolean,
    notification_sound: Boolean,
    device_info: {
     cordova: String,
     model: String,
     platform: String,
     version: String,
     uuid: String,
     isVirtual: Boolean,
     manufacturer: String,
     serial: String     
    },
    documents:[ ]
     

});

 UserSchema.plugin(timeZone, { paths: ['created_at','chat.lastSeen','suspend_remarks_date','activate_remarks_date','renewal_date','lastSubscribed'] });


UserSchema.pre('save',  function(next) {
    var user = this;
 
     if (!user.isModified('password')) return next();
 
     bcrypt.genSalt(10, function(err, salt) {
         if (err) return next(err);
 
         bcrypt.hash(user.password, salt, function(err, hash) {
             if (err) return next(err);
 
             user.password = hash;
             next();
         });
     });
});



// UserSchema.methods.bupdate = function (password, cb) {
//    console.log('this.password');
//    console.log(password);

//     // bcrypt.compare(candidatePassword, this.password, (err, isMatch) => {
//     //     if (err) return cb(err);
//     //     cb(null, isMatch);
//     // });
// };

UserSchema.pre("findOneAndUpdate", function (next) {
    // console.log('ffffffffffffff');
    // console.log('this===================')
    // console.log(this)
    // console.log('next===================')

    console.log(next)
    bcrypt.hash(this.password, 10, (err, hash) => {
        this.password = hash;
        console.log('passssss');
        console.log(this.password);
        next();
    });
});

// UserSchema.pre('findOneAndUpdate',  function(next) {
//     var user = this;
//  console.log('pppppppppp'+user);
//  console.log(user.password);
//  console.log(this.password);
//      // if (!user.isModified('password')) return next();
 
//      bcrypt.genSalt(10, function(err, salt) {
//          if (err) return next(err);
//  console.log('111111'+salt);
//  console.log('111111'+err);
//          bcrypt.hash(user.password, salt, function(err, hash) {
//              if (err) return next(err);
//  console.log('111111');
 
//              user.password = hash;
//              next();
//          });
//      });
// });



UserSchema.methods.comparePassword = function (candidatePassword, cb) {
    bcrypt.compare(candidatePassword, this.password, (err, isMatch) => {
        if (err) return cb(err);
        cb(null, isMatch);
    });
};



export default mongoose.model('User', UserSchema);
