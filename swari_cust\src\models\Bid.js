/**
 * Bid Model
 * Defines the schema for bid data
 * PRD Reference: Sections 4.3, 10.3
 */

import mongoose from 'mongoose';

const BidSchema = new mongoose.Schema({
  trip_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trip',
    required: true
  },
  driver_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  vehicle_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vehicle',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'accepted', 'rejected'],
    default: 'pending'
  },
  notes: {
    type: String
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } });

// Create compound index to ensure a driver can't submit more than 2 bids per trip
BidSchema.index({ trip_id: 1, driver_id: 1 });

const Bid = mongoose.model('Bid', BidSchema);
export default Bid;