/**
 * Rating Resource for AdminJS
 * Defines the AdminJS resource for Rating model
 * PRD Reference: Sections 4.10, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Rating from '../../../../models/Rating.js';
import Customer from '../../../../models/Customer.js';
import Driver from '../../../../models/Driver.js';
import Trip from '../../../../models/Trip.js';
import Log from '../../../../models/Log.js';
import logger from '../../../../utils/logger.js';
import { format } from 'date-fns';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register related Trip Show component
const relatedTripShowComponent = componentLoader.add(
  'relatedTripShow', 
  join(__dirname, '../components/related-trip-show') // Path to your React component
);

// Register rating Score Show component
const ratingScoreShowComponent = componentLoader.add(
  'ratingScoreShow', 
  join(__dirname, '../components/rating-score-show') // Path to your component
);
// Register rating Analytics component
const ratingAnalyticsComponent = componentLoader.add(
  'ratingAnalytics', 
  join(__dirname, '../components/rating-analytics') // Path to your React component
);




/**
 * Rating resource configuration
 */
export default {
  resource: Rating,
  options: {
    navigation: {
      name: 'Rating Management',
      icon: 'Star',
    },
    // Configure pagination for the list view
    listProperties: ['trip_id', 'rater_id', 'ratee_id', 'rater_type', 'score', 'created_at'],
    filterProperties: ['trip_id', 'rater_id', 'ratee_id', 'rater_type', 'score', 'created_at'],
    editProperties: ['comment'],
    showProperties: [
      'trip_id', 'rater_id', 'ratee_id', 'rater_type', 'score', 'comment', 'created_at', 'updated_at'
    ],
    properties: {
      trip_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        reference: 'Trip',
        isRequired: true,
        components: {
          show: relatedTripShowComponent
        }
      },
      rater_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        isRequired: true,
      },
      ratee_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        isRequired: true,
      },
      rater_type: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        availableValues: [
          { value: 'customer', label: 'Customer' },
          { value: 'driver', label: 'Driver' },
        ],
      },
      score: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number',
        components: {
          list: ratingScoreShowComponent
        }
      },
      comment: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea',
      },
      created_at: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
      },
      updated_at: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime',
      },
    },
    actions: {
      // Add a custom action to view rating analytics
      ratingAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        label: 'Rating Analytics',
        component: ratingAnalyticsComponent,
        handler: async (request, response, context) => {
          try {
            // Get rating distribution
            const ratingDistribution = await Rating.aggregate([
              { $group: { _id: '$score', count: { $sum: 1 } } },
              { $sort: { _id: 1 } }
            ]);

            // Get average ratings for customers
            const customerRatings = await Customer.aggregate([
              { $match: { average_rating: { $exists: true } } },
              { $project: { _id: 1, name: 1, average_rating: 1 } },
              { $sort: { average_rating: 1 } },
              { $limit: 10 }
            ]);

            // Get average ratings for drivers
            const driverRatings = await Driver.aggregate([
              { $match: { average_rating: { $exists: true } } },
              { $project: { _id: 1, name: 1, average_rating: 1 } },
              { $sort: { average_rating: 1 } },
              { $limit: 10 }
            ]);

            // Get recent ratings
            const recentRatings = await Rating.find()
              .sort({ created_at: -1 })
              .limit(10)
              .populate('trip_id');

            // Log the analytics access
            const log = new Log({
              event_type: 'admin_action',
              user_id: context.currentAdmin._id,
              user_type: 'Admin',
              details: { action: 'view_rating_analytics' },
              ip_address: request.ip || '127.0.0.1'
            });
            await log.save();
            logger.info(`Admin ${context.currentAdmin.email} viewed rating analytics`);

            return {
              ratingDistribution,
              customerRatings,
              driverRatings,
              recentRatings
            };
          } catch (error) {
            logger.error(`Error generating rating analytics: ${error.message}`);
            throw error;
          }
        },
      },
      // Override the default edit action to log changes
      edit: {
        after: async (response, request, context) => {
          if (response.record && response.record.errors.length === 0) {
            const { record } = response;
            try {
              // Log the edit action
              const log = new Log({
                event_type: 'admin_action',
                user_id: context.currentAdmin._id,
                user_type: 'Admin',
                details: { 
                  action: 'edit_rating', 
                  rating_id: record.params._id,
                  changes: record.params
                },
                ip_address: request.ip || '127.0.0.1'
              });
              await log.save();
              logger.info(`Admin ${context.currentAdmin.email} edited rating ${record.params._id}`);
            } catch (error) {
              logger.error(`Error logging rating edit: ${error.message}`);
            }
          }
          return response;
        },
      },
      // Override the default show action to log views
      show: {
        after: async (response, request, context) => {
          if (response.record) {
            try {
              // Log the view action
              const log = new Log({
                event_type: 'admin_action',
                user_id: context.currentAdmin._id,
                user_type: 'Admin',
                details: { 
                  action: 'view_rating', 
                  rating_id: response.record.params._id 
                },
                ip_address: request.ip || '127.0.0.1'
              });
              await log.save();
              logger.info(`Admin ${context.currentAdmin.email} viewed rating ${response.record.params._id}`);
            } catch (error) {
              logger.error(`Error logging rating view: ${error.message}`);
            }
          }
          return response;
        },
      },
    },
  },
};