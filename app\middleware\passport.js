
import User from '../models/user';
var JwtStrategy = require('passport-jwt').Strategy,
    ExtractJwt  = require('passport-jwt').ExtractJwt;
var config      = require('../../config/default');
var passport = require('passport');
var myCache  = require( "../service/appCache" );
 
var opts = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: config.jwtSecret,
    passReqToCallback: true
}
 
module.exports = new JwtStrategy(opts, function (req, jwt_payload, done) {
    // console.log(" =============== JwtStrategy =================== ",jwt_payload);

    // console.log(myCache.getStats());
    var token = req.headers['authorization'].replace("Bearer ","").replace('bearer','').trim();

    if(!token) {
        token = req.headers['Authorization'].replace("Bearer ","").replace('bearer','').trim();
    }

    // console.log({token});
    // console.log({token : myCache.get(jwt_payload.id)  });
    // console.log(" my cahche token ",typeof jwt_payload.id, " === ",myCache.get(jwt_payload.id), myCache.get(""+jwt_payload.id)==token);  
    // console.log("has",myCache.has(""+jwt_payload.id+""));  
    // console.log(token);  

    /*if(!myCache.get(jwt_payload.id)  || myCache.get(jwt_payload.id)!==token) {    
        console.log("Failed to find the token");
        return done("Not authorized", false);
    }*/

    User.findById(jwt_payload.id, function (err, user) {
        if (err) {
            return done(err, false);
        }
        if (user) {

            if(user.suspend) {
                return done("Your account has been suspended", false);
            }
            return done(null, user);
        } else {
            return done(null, false);
        }
    });
});