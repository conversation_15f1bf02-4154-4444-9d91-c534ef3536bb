import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');



const WithdrawWalletSchema = new Schema({
  user_id: ObjectId,
  accountName:String,
  hiddenAccountNo:String,
  bankName:String,
  ifsceCode:String,
  branchAddress:String,
  amount:Number,
  approvalDate:Date ,
  remittDate:Date ,
  date:Date ,
  reason:String,
  status:String,
  transaction_type:String,
  remarks:{type:String,default:''},

  
});

WithdrawWalletSchema.plugin(timeZone, { paths: ['date','approvalDate','remittDate'] });

export default mongoose.model('WithdrawWallet', WithdrawWalletSchema);
