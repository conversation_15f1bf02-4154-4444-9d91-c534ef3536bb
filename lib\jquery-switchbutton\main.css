body {
    margin: 0;
    padding: 0;

    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-size: 14px;
    color: #333;
    background-color: #EFEFEF;
}

.wrapper {
    position: relative;
    width: 800px;
    margin: auto;
    padding: 20px 40px;
    background: white;
}

p {
    text-align: justify;
    max-width: 700px;
}

h2 {
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
    margin-top: 60px;
}

h3 {
    margin-top: 40px;
}

code, pre {
    margin: 0 2px;
    padding: 0px 5px;
    border: 1px solid #EAEAEA;
    background-color: #F8F8F8;
    border-radius: 3px;
    font-size: 12px;
    font-family: <PERSON><PERSON><PERSON>, "Liberation Mono", Courier, monospace;
}

pre {
    max-width: 600px;
    padding: 10px;
    margin: 20px 0;
}

.demo::before{
    content: "result";
    display: block;
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 10px;
    letter-spacing: 1px;
    color: #CCC;
    border-bottom: 1px solid #DDD;
    width: 600px;
    padding: 0 20px 5px 0;
}

.demo {
    padding: 20px 0;
}

.switch-wrapper {
    display: inline-block;
    position: relative;
    top: 3px;
}


.slider.demo .switch-button-label {
    font-size: 40px;
}
