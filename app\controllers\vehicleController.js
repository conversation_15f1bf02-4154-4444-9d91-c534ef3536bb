import Responder from '../../lib/expressResponder';
import Vehicle from '../models/vehicle';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
import Trip from '../models/trip';
// import VehicleMaker from '../models/vehicleMaker';
import VehicleType from '../models/vehicleType';
import VehicleModel from '../models/vehicleModel';



export default class vehicleController {

  static page(req, res) {
   var vehicle =[];
    Vehicle.find({})
    .then((veh)=> {vehicle=veh; return User.find({_id:{$in:_.map(veh,'user_id')}})})
    .then((user)=>{console.log("VEH====",user) ; var response=[];_.each(vehicle,us =>{
      var vehic={_id:us._id,
        name:us.name,
        category:us.category,
        no_of_seats:us.no_of_seats,
        reg_no:us.reg_no,
        user_id:us.user_id,
        ac:us.ac,
        carrier:us.carrier,
        vehical_image_url:us.vehical_image_url,
        vehical_make:us.vehical_make,
        vehical_model:us.vehical_model,
      };
    
      _.each(user,usr=>{
      if(usr._id == vehic.user_id){
        vehic.userDetails=usr;
      }
         
         
      }) 
      response.push(vehic);
    });Responder.success(res,response) })
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  static count(req, res) {
  }

  static show(req, res) {


            Vehicle.aggregate([ 
              {
                    $match: {
                      // $or:[
                        // 'user_id': req.params.id,
                        'user_id':ObjectId(req.params.user_id)
                        // {'booking_request': ObjectId(req.params.id)},

                        // ],
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
                        // 'status': 'ACCEPTED',


                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehiclemakers',
                   localField: 'vehical_make',
                   foreignField: '_id',
                   as: 'vehicleMaker'
                 }
               },
                { $lookup:
                 {
                   from: 'vehiclemodels',
                   localField: 'vehical_model',
                   foreignField: '_id',
                   as: 'vehicleModel'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicletypes',
                   localField: 'category',
                   foreignField: '_id',
                   as: 'vehicleType'
                 }
               },
                { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    // console.log(req.params.user_id)
    // Vehicle.find({user_id: req.params.user_id})
    // .then((veh)=>Responder.success(res,veh))
    // .catch((err)=>Responder.operationFailed(res,err))
   
  }


  static count(req, res) {
    var counts={};
    console.log("in vehicle count")
    console.log(req.params.user_id)
    Vehicle.count({user_id: req.params.user_id,delete_status:false},function(err,count){
      console.log(count);
      counts.vehicles = count;
      Responder.success(res,counts);
    });
   
  }


  
  static totalVehiclesForFirstTime(req, res) {
     Vehicle.count({})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
  }

  static totalVehicles(req, res) {
   

    if ((req.body.startDate != null && req.body.endDate != null) &&
          (req.body.reg_no == null && req.body.category == null &&
           req.body.vehical_model == null && req.body.buisness_name == null &&
           req.body.district == null && req.body.state == null &&
           req.body.phone_number == null  
          )){
              console.log('11111111111');

              Vehicle.aggregate([ 
                   {
                    $match: {
                              "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') }
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   },{
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  }

                  ])
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

     }

     else if (req.body.startDate != null && req.body.endDate != null &&
              req.body.vehical_model != null 
          ){
              console.log('2222222222');

              VehicleModel.aggregate([ 
                   {
                    $match: {
                             
                               'model': {$regex : "^" + req.body.vehical_model,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    // $and: [ {'role': {$ne: 'admin'}} ,
                                     "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      'vehical_model': { $in: object }

                                    // ]
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         },{
                               $group: {
                                    _id: {
                                      
                                    },
                                    myCount: { $sum: 1 } ,
                                  }
                          }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                 req.body.category != null
              ){
              console.log('333333');

              VehicleType.aggregate([ 
                   {
                    $match: {
                             
                               type: {$regex : "^" + req.body.category,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    // $and: [ {'role': {$ne: 'admin'}} ,
                                     "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      'category': { $in: object }

                                    // ]
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         },{
                             $group: {
                                  _id: {
                                    
                                  },
                                  myCount: { $sum: 1 } ,
                                }
                        }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null && 
                 req.body.buisness_name != null
              ){
              console.log('55555555555511111');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

    else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null && 
                   req.body.district != null &&
                    req.body.state == null
              ){
              console.log('5555555555552222');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null && 
                   req.body.district == null &&
                    req.body.state == null &&
                    req.body.phone_number != null
              ){
              console.log('5555555555552222');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null &&
                   req.body.district == null &&
                    req.body.state != null
              ){
              console.log('555555555555333');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


    else if (req.body.startDate != null && req.body.endDate != null &&
                req.body.reg_no != null 
            ){
              console.log('66666666666');

              Vehicle.aggregate([ 
                   {
                    $match: {
                              "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                    },
                               'reg_no': {$regex : "^" + req.body.reg_no,$options: 'i'}

                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   },{
                         $group: {
                              _id: {
                                
                              },
                              myCount: { $sum: 1 } ,
                            }
                    }

                  ])
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.reg_no != null 
            ){
              console.log('77777777777');
             Vehicle.aggregate([ 
                   {
                    $match: {
                               'reg_no': {$regex : "^" + req.body.reg_no,$options: 'i'}
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   },{
                           $group: {
                                _id: {
                                  
                                },
                                myCount: { $sum: 1 } ,
                              }
                      }

                  ])
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

      }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.category != null 
            ){
              console.log('888888888888');
                VehicleType.aggregate([ 
                   {
                    $match: {
                             
                               type: {$regex : "^" + req.body.category,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                     
                                      'category': { $in: object }

                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))

      }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.vehical_model != null 
            ){
              console.log('99999999999');
              VehicleModel.aggregate([ 
                   {
                    $match: {
                             
                               'model': {$regex : "^" + req.body.vehical_model,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    
                                      'vehical_model': { $in: object }
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }


      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.buisness_name != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.phone_number != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.district != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.state != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


      else if ((req.body.startDate == null && req.body.endDate == null) &&
                 (req.body.district != null &&
                  req.body.state != null
                 )
              ){
              console.log('1111100000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $and:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }
     

     else if ((req.body.startDate != null && req.body.endDate != null) &&
                 (req.body.district != null &&
                  req.body.state != null
                 )
              ){
              console.log('122222222200000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $and:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           },{
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            }

                          ])
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }
  }
 
  static create(req, res) {
   
    console.log(req.body);
     let vehicledetails = req.body;
     if(!(vehicledetails.reg_no && vehicledetails.vehical_model && vehicledetails.no_of_seats&& vehicledetails.year ))
     return Responder.operationFailed(res,{message:"All fields are mandatory."})
     
           if(req.body._id)
           {

                  console.log(" UPDATE COMMAND");
                 // return Responder.operationFailed(res,{message:"NEED TO UPDATE COMMAND"})
                   console.log(req.params);
                console.log(req.body);
                  let vehicledetails = req.body;
                 
                // delete req.body._id;
                if(!req.body._id)
                return Responder.operationFailed(res,{message:'Vehicle Id Is Required.'})
                 
              


                 Vehicle.findOne({reg_no:vehicledetails.reg_no , delete_status:false})
                   .then((veh)=>{
                    if (veh != null) {
                        console.log('vvvvvvvvvvvvvvvvv test',veh)
                        console.log('vvvvvvvvvvvvvvvvv ',veh._id +  'aaa' + vehicledetails._id)

                      if(veh && veh._id != vehicledetails._id) { console.log('veh---!!!',veh); return Responder.operationFailed(res,{message:"Vehicle Already Exists. "})
                       }else{
                        console.log(req.body);
                          Vehicle.findOneAndUpdate({_id:req.body._id},{$set:req.body})
                          .then((val)=>Responder.success(res,val))
                          .catch((err)=>Responder.operationFailed(res,err))

                         }
                    }else{
                      Vehicle.findOneAndUpdate({_id:req.body._id},{$set:req.body})
                      .then((val)=>Responder.success(res,val))
                      .catch((err)=>Responder.operationFailed(res,err))

                    }
                 })

                


           }
     else
     { 
     Vehicle.findOne({reg_no:vehicledetails.reg_no , delete_status:false})
     .then((veh)=>{if(veh) { console.log('veh---!!!',veh); return Responder.operationFailed(res,{message:"Vehicle Already Exists. "})
   }else{
    console.log(req.body);
    Vehicle.create(req.body)
       .then((vehicle)=>Responder.success(res,vehicle))
       .catch((err)=>Responder.operationFailed(res,err))
     }})
   }
      
  }

  static update(req, res) {
    console.log(req.params);
    console.log(req.body);
      let vehicledetails = req.body;
     if(!(vehicledetails.reg_no && vehicledetails.vehical_model && vehicledetails.no_of_seats ))
     return Responder.operationFailed(res,{message:"All fields are mandatory."})
    // delete req.body._id;
    if(!req.params.vehicle_id)
    return Responder.operationFailed(res,{message:'Vehicle Id Is Required.'})
    Vehicle.findOneAndUpdate({_id:req.params.vehicle_id},{$set:req.body})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
  }

  static remove(req, res) {
    // console.log(req.params);
    Trip.find({vehical_id:ObjectId(req.params.vehicle_id),$or:[{'status': 'ACTIVE'},{'status':'CANCELLED'},{'status': "ACCEPTED"}],'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}})
    .then((trips)=>{
        console.log('trips',trips.length)
        if (trips.length > 0) {
          Responder.success(res,{length:trips.length})
         // Vehicle.update({_id:req.params.vehicle_id},{$set:{"delete_status":true}})
         //  .then((product)=>Responder.success(res,product))
         //  .catch((err)=>Responder.operationFailed(res,err))
        }else{
          Vehicle.update({_id:req.params.vehicle_id},{$set:{"delete_status":true}})
          .then((product)=>Responder.success(res,product))
          .catch((err)=>Responder.operationFailed(res,err))
            // Vehicle.remove({_id:req.params.vehicle_id})
            // .then((product)=>Responder.success(res,product))
            // .catch((err)=>Responder.operationFailed(res,err))
        }
    })
    .catch((err)=>Responder.operationFailed(res,err))
      
  
  }

   static filterVehicleList(req, res) {


      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)


     if ((req.body.startDate != null && req.body.endDate != null) &&
          (req.body.reg_no == null && req.body.category == null &&
           req.body.vehical_model == null && req.body.buisness_name == null &&
           req.body.district == null && req.body.state == null &&
           req.body.phone_number == null  
          )){
              console.log('11111111111');

              Vehicle.aggregate([ 
                   {
                    $match: {
                              "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') }
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   }

                  ]).skip(size * (pageNo - 1)).limit(size)
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

     }

     else if (req.body.startDate != null && req.body.endDate != null &&
              req.body.vehical_model != null 
          ){
              console.log('2222222222');

              VehicleModel.aggregate([ 
                   {
                    $match: {
                             
                               'model': {$regex : "^" + req.body.vehical_model,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    // $and: [ {'role': {$ne: 'admin'}} ,
                                     "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      'vehical_model': { $in: object }

                                    // ]
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ]).skip(size * (pageNo - 1)).limit(size)
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                 req.body.category != null
              ){
              console.log('333333');

              VehicleType.aggregate([ 
                   {
                    $match: {
                             
                               type: {$regex : "^" + req.body.category,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    // $and: [ {'role': {$ne: 'admin'}} ,
                                     "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      'category': { $in: object }

                                    // ]
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ]).skip(size * (pageNo - 1)).limit(size)
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null && 
                 req.body.buisness_name != null
              ){
              console.log('55555555555511111');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

    else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null && 
                   req.body.district != null &&
                    req.body.state == null
              ){
              console.log('5555555555552222');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null && 
                   req.body.district == null &&
                    req.body.state == null &&
                    req.body.phone_number != null
              ){
              console.log('5555555555552222');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate != null && req.body.endDate != null &&
                   req.body.buisness_name == null &&
                   req.body.district == null &&
                    req.body.state != null
              ){
              console.log('555555555555333');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object }

                                      
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


    else if (req.body.startDate != null && req.body.endDate != null &&
                req.body.reg_no != null 
            ){
              console.log('66666666666');

              Vehicle.aggregate([ 
                   {
                    $match: {
                              "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                    },
                               'reg_no': {$regex : "^" + req.body.reg_no,$options: 'i'}

                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   }

                  ]).skip(size * (pageNo - 1)).limit(size)
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.reg_no != null 
            ){
              console.log('77777777777');
             Vehicle.aggregate([ 
                   {
                    $match: {
                               'reg_no': {$regex : "^" + req.body.reg_no,$options: 'i'}
                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   }

                  ]).skip(size * (pageNo - 1)).limit(size)
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

      }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.category != null 
            ){
              console.log('888888888888');
                VehicleType.aggregate([ 
                   {
                    $match: {
                             
                               type: {$regex : "^" + req.body.category,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                     
                                      'category': { $in: object }

                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ]).skip(size * (pageNo - 1)).limit(size)
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))

      }

      else if (req.body.startDate == null && req.body.endDate == null &&
                req.body.vehical_model != null 
            ){
              console.log('99999999999');
              VehicleModel.aggregate([ 
                   {
                    $match: {
                             
                               'model': {$regex : "^" + req.body.vehical_model,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    
                                      'vehical_model': { $in: object }
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ]).skip(size * (pageNo - 1)).limit(size)
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }


      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.buisness_name != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {buisness_name: {$regex : "^" + req.body.buisness_name,$options: 'i'}},
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.phone_number != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.district != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


      else if (req.body.startDate == null && req.body.endDate == null &&
                 req.body.state != null 
              ){
              console.log('1000000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }


      else if ((req.body.startDate == null && req.body.endDate == null) &&
                 (req.body.district != null &&
                  req.body.state != null
                 )
              ){
              console.log('1111100000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $and:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }
     

     else if ((req.body.startDate != null && req.body.endDate != null) &&
                 (req.body.district != null &&
                  req.body.state != null
                 )
              ){
              console.log('122222222200000000000');

              User.aggregate([ 
                   {
                    $match: {
                              $and:[
                                    {district: {$regex : "^" + req.body.district,$options: 'i'}},
                                    {state: {$regex : "^" + req.body.state,$options: 'i'}}
                              
                              ]
                                
                            }
                        
                  }

                        ])
        
                .then((users)=>{
                    var object= [];
                    users.forEach(function (user, k) {                
                      console.log('ffffffffffffffff',user._id)
                      object.push(ObjectId(user._id))

                    });

                        Vehicle.aggregate([ 
                           {
                            $match: {
                                      "created_at": {
                                      "$gte": new Date(req.body.startDate+'T00:00:00Z') ,
                                      "$lte": new Date(req.body.endDate+'T23:59:59Z') 
                                      },
                                      user_id: { $in: object } 
                                    }
                                
                          },
                          { $lookup:
                             {
                               from: 'users',
                               localField: 'user_id',
                               foreignField: '_id',
                               as: 'userDetails'
                             }
                           },
                          { $lookup:
                             {
                               from: 'vehiclemakers',
                               localField: 'vehical_make',
                               foreignField: '_id',
                               as: 'vehicleMaker'
                             }
                           },
                            { $lookup:
                             {
                               from: 'vehiclemodels',
                               localField: 'vehical_model',
                               foreignField: '_id',
                               as: 'vehicleModel'
                             }
                           },
                           { $lookup:
                             {
                               from: 'vehicletypes',
                               localField: 'category',
                               foreignField: '_id',
                               as: 'vehicleType'
                             }
                           }

                          ]).skip(size * (pageNo - 1)).limit(size)
                      .then((product)=>
                      // {
                        // console.log('ppppppppppp',product);
                        // object.push(product.data)
                        Responder.success(res,product)


                      // }
                        )
                      .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
     }
     
  }


  static filterByUserVehicleList(req, res) {

        var users = [];
    // User.find({})
    User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}},{district : {$regex : "^" + req.body.district, $options: 'i'}},{state : {$regex : "^" + req.body.state, $options: 'i'}}]})
      .then((usr) => { users = usr; return Vehicle.find({}) })
      .then((veh) => {
        // console.log("VEH====", veh);
         var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed:us.referal_code,
            is_subscribed:us.is_subscribed,
            referred_by_code:us.referred_by_code,
            fcm_registration_token:us.fcm_registration_token,
            suspend:us.suspend
          };
          trp.vehicleDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.vehicleDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }



  static getVehicleListPagination(req, res) {

    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)
    
    // Vehicle.find({}).skip(size * (pageNo - 1)).limit(size)
    // .then((veh)=>Responder.success(res,veh))
    // .catch((err)=>Responder.operationFailed(res,err))


    Vehicle.aggregate([ 
              {
                    $match: {
                      
                        }
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
              { $lookup:
                 {
                   from: 'vehiclemakers',
                   localField: 'vehical_make',
                   foreignField: '_id',
                   as: 'vehicleMaker'
                 }
               },
                { $lookup:
                 {
                   from: 'vehiclemodels',
                   localField: 'vehical_model',
                   foreignField: '_id',
                   as: 'vehicleModel'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicletypes',
                   localField: 'category',
                   foreignField: '_id',
                   as: 'vehicleType'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }


  
   static filterVehicleDetailsVehicleList(req, res) {


     if (req.body.vehical_model != null){
              console.log('1111111111111');

              VehicleModel.aggregate([ 
                   {
                    $match: {
                             
                               'model': {$regex : "^" + req.body.vehical_model,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                    
                                      'vehical_model': { $in: object },
                                      'user_id': ObjectId(req.body.user_id)
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }

      else if (req.body.category != null){
              console.log('333333');

              VehicleType.aggregate([ 
                   {
                    $match: {
                             
                               type: {$regex : "^" + req.body.category,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      Vehicle.aggregate([ 
                         {
                          $match: {
                                      'user_id': ObjectId(req.body.user_id),
                                      'category': { $in: object }

                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                        { $lookup:
                           {
                             from: 'vehiclemakers',
                             localField: 'vehical_make',
                             foreignField: '_id',
                             as: 'vehicleMaker'
                           }
                         },
                          { $lookup:
                           {
                             from: 'vehiclemodels',
                             localField: 'vehical_model',
                             foreignField: '_id',
                             as: 'vehicleModel'
                           }
                         },
                         { $lookup:
                           {
                             from: 'vehicletypes',
                             localField: 'category',
                             foreignField: '_id',
                             as: 'vehicleType'
                           }
                         }

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
     }
      else if (req.body.reg_no != null ){
              console.log('44444444444');

              Vehicle.aggregate([ 
                   {
                    $match: {
                              'user_id': ObjectId(req.body.user_id),
                              'reg_no': {$regex : "^" + req.body.reg_no,$options: 'i'}

                            }
                        
                  },
                  { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                  { $lookup:
                     {
                       from: 'vehiclemakers',
                       localField: 'vehical_make',
                       foreignField: '_id',
                       as: 'vehicleMaker'
                     }
                   },
                    { $lookup:
                     {
                       from: 'vehiclemodels',
                       localField: 'vehical_model',
                       foreignField: '_id',
                       as: 'vehicleModel'
                     }
                   },
                   { $lookup:
                     {
                       from: 'vehicletypes',
                       localField: 'category',
                       foreignField: '_id',
                       as: 'vehicleType'
                     }
                   }

                  ])
              .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

     }
     
   
 }
  
}
