import mongoose from 'mongoose';
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const userTrackingSchema = new mongoose.Schema({
  user_id: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  phone_number: String,
  fcm_token: String,
  device_id: String,
  app_active_status: { type: Boolean, default: true },
  is_uninstalled: { type: Boolean, default: false },
  last_activity: { type: Date, default: Date.now },
  created_at: { type: Date, default: Date.now },
  reinstall_date: Date,
  activity_history: [
    {
      type: {
        type: String,
        enum: ['install', 'uninstall', 'activity', 'reinstall', 'token_expired'],
      },
      timestamp: { type: Date, default: Date.now },
    },
  ],
});

userTrackingSchema.pre('save', function(next) {
  if (this.activity_history.length > 10) {
    this.activity_history = this.activity_history.slice(-10);
  }
  next();
});


userTrackingSchema.plugin(timeZone, { paths: ['created_at','last_activity','reinstall_date'] });

export default mongoose.model("UserTracking", userTrackingSchema);
