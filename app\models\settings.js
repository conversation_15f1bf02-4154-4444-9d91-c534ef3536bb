import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const SettingsSchema = new Schema({
	type: String,
	tripBoostFeesByAdmin: Number,
	subscriptionFeesByAdmin: Number,
	gst: Number,
	gstAmount: Number,
	boostTripGst: Number,
	boostTripGstAmount: Number,
	freetrialperiod: Number,
	billingEmail: String,
	noReplyEmail: String,
	contactEmail: String,
	appVersion: String
});
// ReportSchema.plugin(timeZone, { paths: ['time'] });

export default mongoose.model('Settings', SettingsSchema);
