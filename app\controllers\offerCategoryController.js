import Responder from '../../lib/expressResponder';
import OfferCategory from '../models/offerCategory';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class RateAndReviewController {



	static getOfferCategory(req, res) {
  		console.log('offerCategory to get -- ',req.body)
  	  	OfferCategory.aggregate([ 
              {
                $match: {
                        }
                    
              },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            	}, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  	
     
  	}

  static create(req, res) {
  	console.log('offerCategory to save -- ',req.body)
  	  OfferCategory.create(req.body)
        .then((trip)=>Responder.success(res,trip))
        .catch((err)=>Responder.operationFailed(res,err))
     
  }

}
