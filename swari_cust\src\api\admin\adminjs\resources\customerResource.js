/**
 * Customer Resource Configuration
 * Defines the AdminJS resource for Customer model
 * PRD Reference: Sections 4.1, 10.3
 */

import {ComponentLoader} from 'adminjs';
import Customer from '../../../../models/Customer.js';
import Transaction from '../../../../models/Transaction.js';
import CancellationLog from '../../../../models/CancellationLog.js';
import logger from '../../../../utils/logger.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Helper function to check for abuse (>3 cancellations per day)
 * @param {string} userId - User ID to check
 * @param {string} userType - Type of user ('customer' or 'driver')
 * @returns {Promise<boolean>} - True if user has more than 3 cancellations today
 */
const checkForAbuse = async (userId, userType) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const cancellations = await CancellationLog.countDocuments({
    initiator_id: userId,
    initiator_type: userType,
    created_at: { $gte: today, $lt: tomorrow }
  });
  
  return cancellations >= 3;
};

const componentLoader = new ComponentLoader();

const flagUserActionComponent = componentLoader.add(
  'FlagUserAction', // Unique name for your component
  join(__dirname, '../components/flag-user-action') // Path to your React component
);

// Register the custom component
const adjustWalletActionComponent = componentLoader.add(
  'AdjustWalletAction', // Unique name for the component
  join(__dirname, '../components/adjust-wallet-action') // Path to your component
);

/**
 * Customer resource configuration
 */
export default {
  resource: Customer,
  options: {
    navigation: {
      name: 'User Management',
      icon: 'User',
    },
    listProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'created_at'],
    filterProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'created_at'],
    editProperties: ['name', 'email', 'phone', 'wallet_balance', 'is_flagged', 'flag_reason'],
    showProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'flag_reason', 'flagged_at', 'flagged_by', 'created_at', 'updated_at'],
    actions: {
      // Flag user action
      flagUser: {
        actionType: 'record',
        icon: 'Flag',
        component: flagUserActionComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const { flag_reason } = request.payload;
          
          try {
            const customerId = record.params._id;
            const customer = await Customer.findById(customerId);
            
            if (!customer) {
              return {
                notice: {
                  message: 'Customer not found',
                  type: 'error',
                },
              };
            }
            
            customer.is_flagged = true;
            customer.flag_reason = flag_reason;
            customer.flagged_at = new Date();
            customer.flagged_by = currentAdmin._id;
            
            await customer.save();
            
            // Log the action
            logger.info('Customer flagged by admin', {
              adminId: currentAdmin._id,
              customerId: customer._id,
              reason: flag_reason
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Customer has been flagged successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error flagging customer', { error: error.message });
            return {
              notice: {
                message: 'Error flagging customer',
                type: 'error',
              },
            };
          }
        },
      },
      // Unflag user action
      unflagUser: {
        actionType: 'record',
        icon: 'Reset',
        guard: 'Are you sure you want to remove the flag from this customer?',
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          
          try {
            const customerId = record.params._id;
            const customer = await Customer.findById(customerId);
            
            if (!customer) {
              return {
                notice: {
                  message: 'Customer not found',
                  type: 'error',
                },
              };
            }
            
            customer.is_flagged = false;
            customer.flag_reason = null;
            customer.flagged_at = null;
            customer.flagged_by = null;
            
            await customer.save();
            
            // Log the action
            logger.info('Customer unflagged by admin', {
              adminId: currentAdmin._id,
              customerId: customer._id
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Flag has been removed successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error unflagging customer', { error: error.message });
            return {
              notice: {
                message: 'Error removing flag',
                type: 'error',
              },
            };
          }
        },
        isVisible: (context) => context.record?.params.is_flagged === true,
      },
      // Adjust wallet action
      adjustWallet: {
        actionType: 'record',
        icon: 'Wallet',
        component: adjustWalletActionComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const { amount, reason } = request.payload;
          
          try {
            const customerId = record.params._id;
            const customer = await Customer.findById(customerId);
            
            if (!customer) {
              return {
                notice: {
                  message: 'Customer not found',
                  type: 'error',
                },
              };
            }
            
            const parsedAmount = parseFloat(amount);
            const balanceBefore = customer.wallet_balance;
            const balanceAfter = balanceBefore + parsedAmount;
            
            // Create a transaction record
            const transaction = new Transaction({
              user_id: customerId,
              user_type: 'customer',
              transaction_type: 'admin_adjustment',
              amount: parsedAmount,
              notes: reason || 'Admin wallet adjustment',
              balance_before: balanceBefore,
              balance_after: balanceAfter
            });
            
            await transaction.save();
            
            // Update customer wallet balance
            customer.wallet_balance = balanceAfter;
            await customer.save();
            
            // Log the action
            logger.info('Customer wallet adjusted by admin', {
              adminId: currentAdmin._id,
              customerId: customer._id,
              amount: parsedAmount,
              reason: reason
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Wallet has been adjusted successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error adjusting wallet', { error: error.message });
            return {
              notice: {
                message: 'Error adjusting wallet',
                type: 'error',
              },
            };
          }
        },
      },
      // Check for abuse action
      checkAbuse: {
        actionType: 'record',
        icon: 'Alert',
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          
          try {
            const customerId = record.params._id;
            const isAbusive = await checkForAbuse(customerId, 'customer');
            
            if (isAbusive) {
              return {
                notice: {
                  message: 'Customer has more than 3 cancellations today and may be abusive',
                  type: 'warning',
                },
              };
            } else {
              return {
                notice: {
                  message: 'Customer does not show signs of abuse',
                  type: 'success',
                },
              };
            }
          } catch (error) {
            logger.error('Error checking for abuse', { error: error.message });
            return {
              notice: {
                message: 'Error checking for abuse',
                type: 'error',
              },
            };
          }
        },
      },
    },
  },
};