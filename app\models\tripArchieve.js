import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const moment = require('moment-timezone');
// const dateIndia = moment.tz(Date.now(), "Asia/Kolkata");
const dateIndia = moment.tz("Asia/Kolkata").format('YYYY-MM-DDTHH:mm:ss.SSS');

const timeZone = require('mongoose-timezone');
 
// const Schema = new mongoose.Schema({
//     date: Date
//     // subDocument: {
//     //     subDate: {
//     //         type: Date,
//     //     },
//     // },
// });

// Schema.plugin(timeZone, { paths: ['date'] });
// const indiaTime = moment.tz('YYYY-MM-DDTHH:mm:ss.SSSZ');

// console.log('indiaTime--- ',indiaTime)
 // const freetrialperiod=moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
     
     // console.log('freetrialperiod--- trip',freetrialperiod);
const TripArchieveSchema = new Schema({
    trip_type: String,
    vehical_id: ObjectId,
    user_id:ObjectId,
    time: Date,
    reg_no: String,
    origin: String,
    ac: {type:Boolean,default:false},
    carrier: {type:Boolean,default:false},
    number_of_passengers: Number,
    destination: String,
    is_trip_boost: {type:Boolean,default:false},
    address: [], // array of cities
    status:{
       type: String,
       enum: ['DONE', 'ACTIVE', 'CANCELLED','ACCEPTED','AFTER_CANCEL','NOT_BOOKED'],
       default: 'ACTIVE'
    },
    location:{
        coordinates : [],     
        type : {type:String, default:'MultiPoint'}, 
    },
    cancel_request_by:ObjectId,
    accepted_user_id: ObjectId,
    origin_location:{lat:Number, lng:Number},
    destination_location:{lat:Number, lng:Number},
    addressLatLng:[], // array of address lat lng
    booking_request: ObjectId, // array of user id,
    created_at: Date ,
    booking_request_sent: Date ,
    booking_request_accept: Date ,
    cancel_request_accept:Date ,
    cancel_request_sent:Date ,
    canceled_at: Date,
    trip_date: Date,
    trip_by_phone_no: String,
    category:ObjectId,
    vehical_model:ObjectId,
    vehical_make:ObjectId,
    preferred_vehicle: {type:String, default:'No Preference'},

});

 TripArchieveSchema.plugin(timeZone, { paths: ['created_at','booking_request_sent','booking_request_accept','canceled_at','trip_date','cancel_request_sent','cancel_request_accept','time'] });
export default mongoose.model('TripArchieve', TripArchieveSchema);
