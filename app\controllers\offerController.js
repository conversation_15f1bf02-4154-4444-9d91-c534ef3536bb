import Responder from '../../lib/expressResponder';
import Offers from '../models/offers';
import User from '../models/user';
import _ from "lodash";
var ObjectId = require('mongodb').ObjectId;
import mongoose from 'mongoose';
var moment = require('moment-timezone');

export default class OfferController {


  static show(req, res) {
    Offers.aggregate([
      {
        $match: {
          _id: ObjectId(req.params.id)
        }

      },
      {
        "$sort": {
          "date": -1,
        }
      },

    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static getOffers(req, res) {
    var currentDate = moment(new Date()).toDate();
    console.log('moment -date -- ', currentDate)
    Offers.aggregate([
      {
        $match: {
          $or: [
            { expiry: { $gt: currentDate } },
            { duration: 0 },
          ]
        }

      },
      {
        $lookup:
        {
          from: 'offercategories',
          localField: 'category_id',
          foreignField: '_id',
          as: 'Category'
        }
      },
      {
        "$sort": {
          "date": -1,
        }
      },

    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static create(req, res) {
    console.log('offer to save -- ', req.body)
    var expiry = moment.utc(req.body.date).add(req.body.duration, 'days').startOf('day').toDate()
    req.body.expiry = expiry;

    Offers.create(req.body)
      .then((trip) => Responder.success(res, trip))
      .catch((err) => Responder.operationFailed(res, err))

  }



  static uploadOfferImage(req, res) {
    console.log('uploadOfferImage -- ', req.body)
    // Responder.success(res, JSON.parse(req[0]))
  }


}
