import express from 'express';
import VehicleModelController from '../controllers/vehicleModelController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initvehicleModelRoutes = () => {
  const vehicleModelRoutes = express.Router();

  vehicleModelRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  VehicleModelController.create);
  vehicleModelRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON>andler ,  VehicleModelController.update);
  vehicleModelRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.remove);
  vehicleModelRoutes.get('/show',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON><PERSON><PERSON> ,  VehicleModelController.show);
  vehicleModelRoutes.post('/filterVehicleModels',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.filterVehicleModels);
 
  vehicleModelRoutes.post('/getVehicleMakers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.getVehicleMakers);
  vehicleModelRoutes.post('/getVehicleModels',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.getVehicleModels);
  vehicleModelRoutes.post('/getVehicleType',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.getVehicleType);
  vehicleModelRoutes.get('/getVehicleModel/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleModelController.getSelectedModel);


  return vehicleModelRoutes;
};

export default initvehicleModelRoutes;
