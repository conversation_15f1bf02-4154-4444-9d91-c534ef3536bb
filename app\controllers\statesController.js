import Responder from '../../lib/expressResponder';
import States from '../models/states';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class StatesController {


  static create(req, res) {
           States.create(req.body)
         .then((trip)=>Responder.success(res,trip))
         .catch((err)=>Responder.operationFailed(res,err))
     
    }


  static update(req, res) {
        // States.count({expire:req.body.expire})
    States.count({state:req.body.state})
     .then((val)=>{
        if (val > 0) {
          Responder.success(res,true)
        }else{
          States.findOneAndUpdate({_id:req.params.id},{$set:req.body})
           .then((val)=>Responder.success(res,val))
           .catch((err)=>Responder.operationFailed(res,err))
               }
      })
        // }



  //  static create(req, res) {
  //     console.log('expire');
  //     States.count({expire:req.body.expire})
  //     States.count({state:req.body.state})
  //    .then((val)=>{
  //       if (val > 0) {
  //         Responder.success(res,true)
  //       }else{
  //           States.create(req.body)
  //           .then((trip)=>Responder.success(res,trip))
  //           .catch((err)=>Responder.operationFailed(res,err))   
  //       }

  //    })
       
  //   }


  // static update(req, res) {


            // States.findOneAndUpdate({_id:req.params.id},{$set:req.body})
            //  .then((val)=>Responder.success(res,val))
            //  .catch((err)=>Responder.operationFailed(res,err))
       
  }

  static remove(req, res) {
    States.remove({_id:req.params.id})
    .then((product)=>Responder.success(res,product))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  static getStates(req, res) {
    
    States.aggregate([ 
          {
                $match: {
                 
                  }
                
          },

          {$sort: {"state": 1,
                  }}
          // { $lookup:
          //   {
          //     from: 'states',
          //     localField: 'expire_id',
          //     foreignField: '_id',
          //     as: 'states'
          //   }
          //   }


                ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))

  }
}
