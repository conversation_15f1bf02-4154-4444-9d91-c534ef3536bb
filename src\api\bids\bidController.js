/**
 * Bid Controller
 * Handles bid submission, retrieval, and acceptance
 * PRD Reference: Sections 4.3, 10.2, 10.4
 */

import mongoose from "mongoose";
import { validationResult } from "express-validator";
import Bid from "../../models/Bid.js";
import Trip from "../../models/Trip.js";
import Driver from "../../models/Driver.js";
import Customer from "../../models/Customer.js";
import Vehicle from "../../models/Vehicle.js";
import VehicleModel from "../../models/VehicleModel.js";
import VehicleMaker from "../../models/VehicleMaker.js";
import VehicleType from "../../models/VehicleType.js";
import logger from "../../utils/logger.js";
import mqttService from "../../services/mqttService.js";
import { getCurrentBalance } from "../../services/walletService.js";

/**
 * Submit a bid for a trip
 * @route POST /api/trips/:id/bids
 * @access Private (Driver only)
 */
export const submitBid = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn("Bid submission validation failed", { errors: errors.array() });
    return res.status(400).json({ errors: errors.array() });
  }

  const { amount, notes } = req.body;
  const tripId = req.params.id;
  const driverId = req.user.id;

  try {
    // Retrieve driver data
    const driver = await Driver.findById(driverId);
    if (!driver) {
      logger.error("Bid submission failed: Driver not found", { driverId });
      return res.status(404).json({ message: "Driver not found" });
    }

    if (driver.role !== "driver") {
      logger.warn("Bid submission failed: User is not a driver", {
        userId: driverId,
        role: driver.role,
      });
      return res
        .status(403)
        .json({ message: "Forbidden: Only drivers can submit bids" });
    }

    // Check if trip exists and is in pending status
    const trip = await Trip.findById(tripId);
    if (!trip) {
      logger.warn("Bid submission failed: Trip not found", { tripId });
      return res.status(404).json({ message: "Trip not found" });
    }

    if (trip.status !== "pending") {
      logger.warn("Bid submission failed: Trip not in pending status", {
        tripId,
        status: trip.status,
      });
      return res
        .status(400)
        .json({ message: "Bids can only be submitted for pending trips" });
    }

    // Check if driver has reached bid limit for this trip (max 2 bids)
    const bidCount = await Bid.countDocuments({
      trip_id: tripId,
      driver_id: driverId,
    });
    if (bidCount >= 2) {
      logger.warn("Bid submission failed: Driver reached bid limit", {
        driverId,
        tripId,
        bidCount,
      });
      return res
        .status(400)
        .json({ message: "You have reached the bid limit for this trip" });
    }

    // Check driver's wallet balance (already present, moved after driver role check)
    // const driver = await Driver.findById(driverId); // Already fetched
    // if (!driver) { // Already checked
    //   logger.error('Bid submission failed: Driver not found', { driverId });
    //   return res.status(404).json({ message: 'Driver not found' });
    // }

    // TODO: Implement wallet threshold validation based on admin configuration
    // For now, using a simple validation that driver should have at least 10% of bid amount
    const minBalance = amount * 0.1;

    if (driver.wallet_balance < minBalance) {
      logger.warn("Bid submission failed: Insufficient wallet balance", {
        driverId,
        walletBalance: driver.wallet_balance,
        requiredBalance: minBalance,
      });
      return res.status(400).json({
        message: "Insufficient wallet balance",
        walletBalance: driver.wallet_balance,
        requiredBalance: minBalance,
      });
    }

    // Create new bid
    const newBid = new Bid({
      trip_id: tripId,
      driver_id: driverId,
      amount,
      notes: notes || "",
      status: "pending",
    });

    await newBid.save();

    // Publish bid update via MQTT
    const bidData = {
      id: newBid._id,
      driver_id: driverId,
      driver_name: driver.name,
      amount,
      notes: notes || "",
      created_at: newBid.created_at,
    };

    mqttService.publishBidUpdate(tripId, bidData);

    logger.info("Bid submitted successfully", {
      bidId: newBid._id,
      tripId,
      driverId,
    });
    res.status(201).json({
      message: "Bid submitted successfully",
      bid: newBid,
    });
  } catch (error) {
    logger.error("Bid submission error", {
      error: error.message,
      tripId,
      driverId,
    });
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Get all bids for a trip
 * @route GET /api/trips/:id/bids
 * @access Private (Customer, Driver)
 */
export const getBidsForTrip = async (req, res) => {
  const tripId = req.params.id;
  const userId = req.user.id;
  const userRole = req.user.role;

  try {
    // Check if trip exists
    const trip = await Trip.findById(tripId);
    if (!trip) {
      logger.warn("Get bids failed: Trip not found", { tripId });
      return res.status(404).json({ message: "Trip not found" });
    }

    // If user is customer, verify they own the trip
    if (userRole === "customer" && trip.customer_id.toString() !== userId) {
      logger.warn("Get bids failed: Unauthorized access", { userId, tripId });
      return res
        .status(403)
        .json({ message: "Unauthorized access to this trip" });
    }

    // Get bids with driver information
    const bids = await Bid.find({ trip_id: tripId })
      .populate("driver_id", "name phone")
      .sort({ created_at: -1 });
      
      


    logger.info("Bids retrieved successfully", {
      tripId,
      bidCount: bids.length,
    });
    res.json(bids);
  } catch (error) {
    logger.error("Get bids error", { error: error.message, tripId });
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Accept a bid
 * @route POST /api/bids/accept
 * @access Private (Customer only)
 */
export const acceptBid = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn("Bid acceptance validation failed", { errors: errors.array() });
    return res.status(400).json({ errors: errors.array() });
  }

  const { bidId } = req.body;
  const customerId = req.user.id;

  try {
    // Find the bid
    const bid = await Bid.findById(bidId);
    if (!bid) {
      logger.warn("Bid acceptance failed: Bid not found", { bidId });
      return res.status(404).json({ message: "Bid not found" });
    }

    // Find the trip
    const trip = await Trip.findById(bid.trip_id);
    if (!trip) {
      logger.warn("Bid acceptance failed: Trip not found", {
        tripId: bid.trip_id,
      });
      return res.status(404).json({ message: "Trip not found" });
    }

    // Verify customer owns the trip
    if (trip.customer_id.toString() !== customerId) {
      logger.warn("Bid acceptance failed: Unauthorized access", {
        customerId,
        tripId: trip._id,
      });
      return res
        .status(403)
        .json({ message: "Unauthorized access to this trip" });
    }

    // Check if trip is still in pending status
    if (trip.status !== "pending") {
      logger.warn("Bid acceptance failed: Trip not in pending status", {
        tripId: trip._id,
        status: trip.status,
      });
      return res
        .status(400)
        .json({ message: "This trip is no longer accepting bids" });
    }

    // Check driver's wallet balance again
    const driver = await Driver.findById(bid.driver_id);
    if (!driver) {
      logger.error("Bid acceptance failed: Driver not found", {
        driverId: bid.driver_id,
      });
      return res.status(404).json({ message: "Driver not found" });
    }

    // Validate driver's wallet balance
    let minBalance;

    const walletBallance = await getCurrentBalance(bid.driver_id);
    if (bid.amount <= 1000) {
      minBalance = 50;
    } else {
      minBalance = bid.amount * 0.05;
      if (minBalance > 500) {
        minBalance = 500;
      }
    }

    if (walletBallance < minBalance) {
      logger.warn("Bid acceptance failed: Insufficient driver wallet balance", {
        driverId: driver._id,
        walletBalance: driver.wallet_balance,
        requiredBalance: minBalance,
      });
      return res.status(400).json({
        message: "Driver has insufficient wallet balance",
        walletBalance: driver.wallet_balance,
        requiredBalance: minBalance,
      });
    }

    // Get vehicle information and update trip
    try  {
      const vehicle = await Vehicle.findById(bid.vehicle_id);
      
      
      if (!vehicle) return;
      logger.info("vehicle model id :", {vehicle:vehicle.vehical_model});
      VehicleModel.findById(vehicle.vehical_model).then((data)=> {logger.info("VehicleModel data:", {data})})

      trip.vehicle_has_carrier = vehicle.carrier;

      // Fetch vehicle model and make in parallel
      const [vehicleModel, vehicleMake] = await Promise.all([
        VehicleModel.findById(vehicle.vehical_model),
        VehicleMaker.findById(vehicle.vehical_make),
      ]);
      if (vehicleModel) {
        trip.vehicle_model = vehicleModel.model;

        const vehicleType = await VehicleType.findById(vehicleModel.type_id);
        if (vehicleType) {
          trip.vehicle_type = vehicleType.name;
        }
      }

      // Overwrite make only if it exists in the maker (optional: remove this if `vehicleType` should take precedence)
      if (vehicleMake) {
        trip.vehicle_make = vehicleMake.maker;
      }

      

      
    } catch (vehicleError) {
      logger.warn("Failed to fetch vehicle information", {
        vehicleId: bid.vehicle_id,
        error: vehicleError.message,
      });
    }

    // Update bid status to accepted
    bid.status = "accepted";
    await bid.save();

    // Update trip status to accepted
    trip.status = "accepted";
    trip.driver_id = bid.driver_id;
    trip.bid_amount = bid.amount;
    trip.bid_id = bidId; // Store bid_id in trip
    await trip.save();

    // Update other bids for this trip to rejected
    await Bid.updateMany(
      { trip_id: trip._id, _id: { $ne: bidId } },
      { status: "rejected" }
    );

    // Publish bid acceptance via MQTT
    const acceptanceData = {
      event: "bid_accepted",
      trip_id: trip._id,
      bid_id: bid._id,
      driver_id: driver._id,
      driver_name: driver.name,
      amount: bid.amount,
    };

    mqttService.publishBidUpdate(trip._id.toString(), acceptanceData);

    logger.info("Bid accepted successfully", {
      bidId,
      tripId: trip._id,
      driverId: driver._id,
    });
    res.json({
      message: "Bid accepted successfully",
      trip: {
        id: trip._id,
        status: trip.status,
        driver: {
          id: driver._id,
          name: driver.name,
          phone: driver.phone,
        },
        bid_amount: bid.amount,
      },
    });
  } catch (error) {
    logger.error("Bid acceptance error", { error: error.message, bidId });
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

/**
 * Reject a bid
 * @route POST /api/bids/reject
 * @access Private (Customer only)
 */
export const rejectBid = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.warn("Bid rejection validation failed", { errors: errors.array() });
    return res.status(400).json({ errors: errors.array() });
  }

  const { bidId } = req.body;
  const customerId = req.user.id;

  try {
    // Find the bid
    const bid = await Bid.findById(bidId);
    if (!bid) {
      logger.warn("Bid rejection failed: Bid not found", { bidId });
      return res.status(404).json({ message: "Bid not found" });
    }

    // Find the trip
    const trip = await Trip.findById(bid.trip_id);
    if (!trip) {
      logger.warn("Bid rejection failed: Trip not found", {
        tripId: bid.trip_id,
      });
      return res.status(404).json({ message: "Trip not found" });
    }

    // Verify customer owns the trip
    if (trip.customer_id.toString() !== customerId) {
      logger.warn("Bid rejection failed: Unauthorized access", {
        customerId,
        tripId: trip._id,
      });
      return res
        .status(403)
        .json({ message: "Unauthorized access to this trip" });
    }

    // Check if trip is still in pending status
    if (trip.status !== "pending") {
      logger.warn("Bid rejection failed: Trip not in pending status", {
        tripId: trip._id,
        status: trip.status,
      });
      return res
        .status(400)
        .json({ message: "This trip is no longer accepting bid actions" });
    }

    // Check if bid is in pending status
    if (bid.status !== "pending") {
      logger.warn("Bid rejection failed: Bid not in pending status", {
        bidId,
        status: bid.status,
      });
      return res
        .status(400)
        .json({ message: "Only pending bids can be rejected" });
    }

    // Update bid status to rejected
    bid.status = "rejected";
    await bid.save();

    // Publish bid rejection via MQTT
    const rejectionData = {
      event: "bid_rejected",
      trip_id: trip._id,
      bid_id: bid._id,
      driver_id: bid.driver_id,
    };

    mqttService.publishBidUpdate(trip._id.toString(), rejectionData);

    logger.info("Bid rejected successfully", { bidId, tripId: trip._id });
    res.json({
      message: "Bid rejected successfully",
      bid: {
        id: bid._id,
        status: bid.status,
      },
    });
  } catch (error) {
    logger.error("Bid rejection error", { error: error.message, bidId });
    res.status(500).json({ message: "Server error", error: error.message });
  }
};
