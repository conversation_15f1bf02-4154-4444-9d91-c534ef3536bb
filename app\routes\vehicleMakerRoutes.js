import express from 'express';
import VehicleMakerController from '../controllers/vehicleMakerController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initvehicleMakerRoutes = () => {
  const vehicleMakerRoutes = express.Router();

  vehicleMakerRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwt<PERSON>uth<PERSON><PERSON>r<PERSON><PERSON><PERSON> ,  VehicleMakerController.create);
  vehicleMakerRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwt<PERSON>uth<PERSON>rror<PERSON><PERSON><PERSON> ,  VehicleMakerController.update);
  vehicleMakerRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  VehicleMakerController.remove);
 

  return vehicleMakerRoutes;
};

export default initvehicleMakerRoutes;
