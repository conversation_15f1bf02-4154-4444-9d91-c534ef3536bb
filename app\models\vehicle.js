import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const VehicleSchema = new Schema({
    user_id: ObjectId,
    reg_no: String,
    vehical_image_url: String,
    category: ObjectId,
    vehical_make: ObjectId,
    vehical_model: ObjectId,
    no_of_seats: Number,
    ac: {type:Boolean,default:false},
    carrier: {type:Boolean,default:false},
    created_at: Date,
    delete_status:{type:Boolean,default:false},
    year: Date,
    
});
 VehicleSchema.plugin(timeZone, { paths: ['created_at'] });


export default mongoose.model('Vehicle', VehicleSchema);
