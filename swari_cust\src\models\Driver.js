/**
 * Driver Model
 * Defines the schema for driver data
 * PRD Reference: Sections 4.1, 10.3
 */

import mongoose from 'mongoose';
// change driver to user once the db merges

const DriverSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  role: {
    type: String,
    
    default: 'driver',
    enum: ['driver']
  },
  wallet_balance: {
    type: Number,
    default: 0
  },
  average_rating: {
    type: Number,
    default: 0
  },
  vehicles: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Vehicle'
    }
  ],
  otp: {
    type: String
  },
  otp_expiry: {
    type: Date
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  // Add fields for flagging abusive users
  is_flagged: {
    type: Boolean,
    default: false
  },
  flag_reason: {
    type: String
  },
  flagged_at: {
    type: Date
  },
  flagged_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
}, { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } });

const Driver = mongoose.model('User', DriverSchema);
export default Driver;