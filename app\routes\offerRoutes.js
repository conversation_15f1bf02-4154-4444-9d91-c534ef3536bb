import express from 'express';
import OfferController from '../controllers/offerController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initofferRoutes = () => {
  const offerRoutes = express.Router();

  offerRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  OfferController.show);
  offerRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  OfferController.create);
  offerRoutes.post('/getOffers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  OfferController.getOffers);
  offerRoutes.post('/uploadOfferImage',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  OfferController.uploadOfferImage);
  
  // statesRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHand<PERSON> ,  StatesController.update);
  // statesRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  StatesController.remove);
  // statesRoutes.get('/getStates',  StatesController.getStates);
 
 return offerRoutes;

};

export default initofferRoutes;
