import Responder from '../../lib/expressResponder';
// import SupportTicket from '../models/SupportTicket';
import SupportTicket from '../models/supportTicket';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
import SupportTicketComment from '../models/supportTicketComment';
var moment = require('moment-timezone');

export default class SupportTicketController {

  static page(req, res) {

     SupportTicket.aggregate([ 
              {
                $match: {
                          'user_id':ObjectId(req.params.id),
                           status: false
                        
                        }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'supportTicket'
                 }
               },{
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
             
               
               // { $lookup:
               //   {
               //     from: 'users',
               //     localField: 'SupportTicketed_by',
               //     foreignField: '_id',
               //     as: 'SupportTicketedBy'
               //   }
               // }
               ,
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))



   
  }




  static count(req, res) {
  }

  

  static getTicketsBySubAdmin(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

       SupportTicket.aggregate([ 
              {
                    $match: {
                        // assign_user: ObjectId(req.body.user._id),
                        status: req.body.status
                      }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'assign_user',
                   foreignField: '_id',
                   as: 'subAdmin'
                 }
               }
               ,
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },

               { $lookup:
                 {
                   from: 'supportticketcomments',
                   localField: '_id',
                   foreignField: 'support_id',
                   as: 'comments'
                 }
               },
                { 
                "$sort": { 
                    reply_date: -1
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>{
      var response=[];
      var totalcomment;
          
      Responder.success(res,trc)

    }
      )
    .catch((err)=>Responder.operationFailed(res,err))
    
  }



  static getTicketsByAdmin(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

       SupportTicket.aggregate([ 
              {
                    $match: {
                        status: req.body.status
                      }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'assign_user',
                   foreignField: '_id',
                   as: 'subAdmin'
                 }
               }
               ,
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },

               { $lookup:
                 {
                   from: 'supportticketcomments',
                   localField: '_id',
                   foreignField: 'support_id',
                   as: 'comments'
                 }
               },
                { 
                "$sort": { 
                    reply_date: -1
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>{
      var response=[];
      var totalcomment;
          
      Responder.success(res,trc)

    }
      )
    .catch((err)=>Responder.operationFailed(res,err))
    
  }



  

  static getSupportTicketCount(req, res) {

       SupportTicket.aggregate([ 
              {
                    $match: {
                        assign_user: ObjectId(req.body.user._id),
                      }
                    
              },
              {
               $group: {
                    _id: {
                      
                    },
                    myCount: { $sum: 1 } ,
                  }
              },

                    ])
    .then((trc)=>{
      var response=[];
      var totalcomment;
           
      Responder.success(res,trc)

    }
      )
    .catch((err)=>Responder.operationFailed(res,err))
    
  }


  


  static getSupportTicketCountAll(req, res) {

       SupportTicket.aggregate([ 
              {
                    $match: {
                      }
                    
              },
              {
               $group: {
                    _id: {
                      
                    },
                    myCount: { $sum: 1 } ,
                  }
              },

                    ])
    .then((trc)=>{
      var response=[];
      var totalcomment;
           
      Responder.success(res,trc)

    }
      )
    .catch((err)=>Responder.operationFailed(res,err))
    
  }

  static show(req, res) {

    console.log(req.params.user_id)
    Vehicle.find({user_id: req.params.user_id})
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }
 
static getTickets(req, res) {

    // console.log('4444444444',req.params)
    // SupportTicket.find({user_id: ObjectId(req.params.user_id)})
    // .sort({ 'created_at': -1 })
    // .then((veh)=>Responder.success(res,veh))
    // .catch((err)=>Responder.operationFailed(res,err))
      SupportTicket.aggregate([ 
      {
            $match: {
                user_id: ObjectId(req.params.user_id)
                      
              }
            
      },
      { $lookup:
         {
           from: 'users',
           localField: 'user_id',
           foreignField: '_id',
           as: 'userDetails'
         }
       },
      { 
        "$sort": { 
            "created_at": -1,
        } 
      }, 


            ])
    // .sort({ 'created_at': -1 })
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err))

  }
  
  static getTicketById(req, res) {

    // SupportTicket.find({_id: ObjectId(req.params.id)})
    SupportTicket.aggregate([ 
      {
            $match: {
                _id: ObjectId(req.params.id)
                      
              }
            
      },
      { $lookup:
         {
           from: 'users',
           localField: 'user_id',
           foreignField: '_id',
           as: 'userDetails'
         }
       },
      { 
        "$sort": { 
            "created_at": -1,
        } 
      }, 


            ])
    // .sort({ 'created_at': -1 })
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }

  static create(req, res) {
   
 
    SupportTicket.create(req.body)
       .then((rep)=>Responder.success(res,rep))
       .catch((err)=>Responder.operationFailed(res,err))
      
  }


  
    static filterSupportTicket(req, res) {
       var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

      if (req.body.serial_number != null) {
      console.log('allllllllll155555')

         SupportTicket.aggregate([ 
                {
                      $match: {
                                  serial_number: req.body.serial_number,
                                  status: req.body.status

                                
                        }
                      
                },
                { $lookup:
                   {
                     from: 'users',
                     localField: 'assign_user',
                     foreignField: '_id',
                     as: 'subAdmin'
                   }
                 }
                 ,
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                { 
                  "$sort": { 
                      "time": 1,
                  } 
                }, 


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
    //   else if (req.body.status == true || req.body.status == false) {
    //   console.log('allllllllll333333333')

    //      SupportTicket.aggregate([ 
    //             {
    //                   $match: {
    //                             status: req.body.status
                                
    //                     }
                      
    //             },
    //             { $lookup:
    //                {
    //                  from: 'users',
    //                  localField: 'assign_user',
    //                  foreignField: '_id',
    //                  as: 'subAdmin'
    //                }
    //              }
    //              ,
    //               { $lookup:
    //                {
    //                  from: 'users',
    //                  localField: 'user_id',
    //                  foreignField: '_id',
    //                  as: 'userDetails'
    //                }
    //              },
    //             { 
    //               "$sort": { 
    //                   "time": 1,
    //               } 
    //             }, 


    //                   ]).skip(size * (pageNo - 1)).limit(size)
    //   .then((trc)=>Responder.success(res,trc))
    //   .catch((err)=>Responder.operationFailed(res,err))
    // }

     else if (req.body.phone_number != null) {

      console.log('allllllllll1212')

              User.aggregate([ 
                   {
                    $match: {
                              $and:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}},
                                

                              ]
                              
                            }
                        
                  }

                        ])

                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });
                             SupportTicket.aggregate([ 
                              {
                                    $match: {
                                              'user_id': { $in: object },
                                              status: req.body.status
                                      }
                                    
                              },
                              { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'assign_user',
                                   foreignField: '_id',
                                   as: 'subAdmin'
                                 }
                               }
                               ,
                                { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'user_id',
                                   foreignField: '_id',
                                   as: 'userDetails'
                                 }
                               },
                              { 
                                "$sort": { 
                                    "time": 1,
                                } 
                              }, 


                            ]).skip(size * (pageNo - 1)).limit(size)
                    .then((trc)=>Responder.success(res,trc))
                    .catch((err)=>Responder.operationFailed(res,err))

                    // Responder.success(res,object)

                    // console.log('object',object); 
                  }
                  // Responder.success(res,trc)
                  )
                .catch((err)=>Responder.operationFailed(res,err))
    }else if (req.body.status == 'All') {
      console.log('allllllllll')
      SupportTicket.aggregate([ 
                {
                      $match: {
                            status: req.body.status
                                
                        }
                      
                },
                { $lookup:
                   {
                     from: 'users',
                     localField: 'assign_user',
                     foreignField: '_id',
                     as: 'subAdmin'
                   }
                 }
                 ,
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                { 
                  "$sort": { 
                      "time": 1,
                  } 
                }, 


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }

  }


  

    static updateTicket(req,res){
       // if (!req.body.status) {
        if (!req.body.status) {
          req.body.close_date= moment(new Date()).add(5.5, 'hours').toDate();
        }
        if (req.body.status) {
          req.body.open_date= moment(new Date()).add(5.5, 'hours').toDate();
        }
        console.log('update ticket0---',req.body)
        // req.body.close_date= moment(new Date()).add(5.5, 'hours').toDate();
       // }
        // SupportTicket.findOneAndUpdate({ _id: req.params.id }, { $set: { 'status':req.body.status } })
        SupportTicket.findOneAndUpdate({ _id: req.params.id }, { $set: req.body })
        .then((data) =>Responder.success(res,data))

    }

    

    static updateForReplyStatus(req,res){
      console.log('updateForReplyStatus-- ',req.body)
        SupportTicket.findOneAndUpdate({ _id: req.params.id }, { $set: { reply_status:req.body.reply_status } })
        .then((data) =>Responder.success(res,data))
        .catch((err)=>Responder.operationFailed(res,err))

    }



    static updateForReplyDate(req,res){
      console.log('updateForReplyDate-- ',req.body)
        SupportTicket.findOneAndUpdate({ _id: req.params.id }, { $set: { reply_date:moment(new Date()).add(5.5, 'hours').toDate() } })
        .then((data) =>Responder.success(res,data))
        .catch((err)=>Responder.operationFailed(res,err))

    }
}

