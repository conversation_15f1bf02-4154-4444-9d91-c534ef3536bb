angular.module('rateReview.controllers', [])

    .controller('rateReviewCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';
     $scope.filter={};
     $scope.userDetails = [];
    $scope.completed_orders = [];
    $scope.rejected_orders = [];
    $scope.orderList = [];
    $scope.pageLimit;
  
        
    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
    };
    $scope.filterSearch= false;


    // $scope.getAllUsers = function() {




      APIService.setData({
            req_url: PrefixUrl + '/rateReview/reviewCount'
        }).then(function(resp) {
          console.log("====resp======",resp);
        $scope.totalReview=resp.data[0].myCount;
           },function(resp) {
              // This block execute in case of error.
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        });


      // $scope.$watch('settings.pageLimit', function (pageLimit) {
      //   console.log('pageLimits'+pageLimit)
      //   $scope.pageLimit= pageLimit;

      //   if($scope.filterSearch){
      //     console.log('11111111')
      //     APIService.setData({
      //         req_url: PrefixUrl + '/rateReview/filterReview' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number,buisness_name:$scope.buisness_name,district:$scope.district } 
      //     }).then(function(resp) {
      //       console.log("====respPagination======",resp);
      //       $scope.userDetails=resp.data
      //     // $scope.userDetailsLength= $scope.userDetails.length;
      //        },function(resp) {
      //           // This block execute in case of error.
      //     });
      //   }
      //   else{

      //     APIService.setData({
      //         req_url: PrefixUrl + '/rateReview/getByPostMethod',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
      //     }).then(function(resp) {
      //       console.log("====respPagination======",resp);
      //       $scope.userDetails=resp.data
      //     // $scope.userDetailsLength= $scope.userDetails.length;
      //        },function(resp) {
      //           // This block execute in case of error.
      //     });
      //   }
      // }
      // );

    $scope.$watch('settings.currentPage', function (value) {
      console.log('currentPage'+$scope.settings.currentPage)  
      console.log('userDetailslll='+$scope.userDetails.length)
      $scope.pageLimit= $scope.settings.pageLimit;

    if($scope.filterSearch){
        console.log('111111112222222222')

        APIService.setData({
              req_url: PrefixUrl + '/rateReview/filterReview' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number,buisness_name:$scope.buisness_name,district:$scope.district } 
          }).then(function(resp) {
            console.log("====respPagination======",resp);
            $scope.userDetails=resp.data
          // $scope.userDetailsLength= $scope.userDetails.length;
             },function(resp) {
                // This block execute in case of error.
          });
      }
      else{

       
        APIService.setData({
              req_url: PrefixUrl + '/rateReview/getByPostMethod',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
          }).then(function(resp) {
            console.log("====respPagination======",resp);
            $scope.userDetails=resp.data
          // $scope.userDetailsLength= $scope.userDetails.length;
             },function(resp) {
                // This block execute in case of error.
          });
      } 
      }    
    
    );



        // APIService.getData({
        //     req_url: PrefixUrl + '/rateReview'
        // }).then(function(resp) {
        //   console.log("====resp======",resp);
        // $scope.userDetails=resp.data
        //    },function(resp) {
        //       // This block execute in case of error.
        //         localStorage.removeItem("UserDeatails");
        //         localStorage.removeItem("token");
        //         $state.go('login');
        // });

        // var userData=localStorage.getItem('UserDeatails');
        // var parsedUser= JSON.parse(userData);
        // // console.log(parsedUser.user_details)
        // if (parsedUser == null || parsedUser.user_details.role != 'admin') {
        //   localStorage.removeItem("UserDeatails");
        //   localStorage.removeItem("token");
        //   $state.go('login');
        // }
    // };  

    // $scope.getAllUsers();


    

    $scope.findUserBusiness = function(user) {

      console.log(user)

      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

    };


    
    $scope.filterReview = function(user) {

      $scope.filterSearch =true;

        if ($scope.name) {
            var name= $scope.name;
        }else{
            var name= null;
        }

        if ($scope.phone_number) {
            var phone_number= $scope.phone_number;
        }else{
            var phone_number= null;
        }

        if ($scope.buisness_name) {
            var buisness_name= $scope.buisness_name;
        }else{
            var buisness_name= null;
        }

        APIService.setData({
            req_url: PrefixUrl + '/rateReview/filterReview' ,data:{ name:name,phone_number:phone_number,buisness_name:buisness_name,district:$scope.district } 
        }).then(function(resp) {
          $scope.userDetails= resp.data;
        },function(resp) {
         
        });
    };



    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }




})

