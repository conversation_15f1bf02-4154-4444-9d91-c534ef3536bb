import initUserRoutes from './userRoutes';
import initVehicleRoutes from './vehicleRoutes';
import initTripRoutes from './tripRoutes';
import initTransactionRoutes from './transactionRoutes';
import initUserMessageRoutes from './userMessageRoutes';
import initVehicleCatMakModRoutes from './vehicleCatMakModRoutes';
import initUtilityRoutes from './utilityRoutes';
import utilRoutes from './utilRoutes';
import initrateReviewRoutes from './rateReviewRoutes';
import initreportRoutes from './reportRoutes';
import initWithdrawWalletRoutes from './withdrawWalletRoutes';
import initNotificationLogControllerRoutes from './notificationLogRoutes';
import initsuspendUserLogRoutes from './suspendUserLogRoutes';
import initvehicleMakerRoutes from './vehicleMakerRoutes';
import initvehicleTypeRoutes from './vehicleTypeRoutes';
import initvehicleModelRoutes from './vehicleModelRoutes';
import initpayoutRoutes from './payoutRoutes';
import initcommonSettingsRoutes from './commonSettingsRoutes';
import initreportCommentRoutes from './reportCommentRoutes';
import inituserContactsRoutes from './userContactsRoutes';
import initsupportTicketRoutes from './supportTicketRoutes';
import initsupportTicketCommentRoutes from './supportTicketCommentRoutes';
import initstatesRoutes from './statesRoutes';
import initsettingsRoutes from './settingsRoutes';
import inittripArchieveRoutes from './tripArchieveRoutes';
import initextendDayLogRoutes from './extendDayLogRoutes';
import initofferRoutes from './offerRoutes';
import initofferCategoryRoutes from './offerCategoryRoutes';
import initofferRedeemedRoutes from './offerRedeemedRoutes';
import initUserTrackingRoutes from '../routes/userTracking'; 



var cors = require('cors');


const initRoutes = (app) => {
  app.options('*', cors()) // include before other routes
  app.use(`/user`, initUserRoutes());
  app.use(`/vehicle`,initVehicleRoutes());
  app.use(`/trip`, initTripRoutes());
  app.use(`/trancsaction`, initTransactionRoutes());
  app.use(`/usermessage`, initUserMessageRoutes());
  app.use(`/util`,utilRoutes());
  app.use(`/utility`,initUtilityRoutes());
  app.use(`/rateReview`,initrateReviewRoutes());
  app.use(`/report`,initreportRoutes());
  app.use(`/vehicleCatMakModRoutes`,initVehicleCatMakModRoutes());
  app.use(`/withdrawWallet`,initWithdrawWalletRoutes());
  app.use(`/notificationLog`,initNotificationLogControllerRoutes());
  app.use(`/suspendUserLog`,initsuspendUserLogRoutes());
  app.use(`/vehicleMaker`,initvehicleMakerRoutes());
  app.use(`/vehicleType`,initvehicleTypeRoutes());
  app.use(`/vehicleModel`,initvehicleModelRoutes());
  app.use(`/payout`,initpayoutRoutes());
  app.use(`/commonSettings`,initcommonSettingsRoutes());
  app.use(`/reportComment`,initreportCommentRoutes());
  app.use(`/userContacts`,inituserContactsRoutes());
  app.use(`/supportTicket`,initsupportTicketRoutes());
  app.use(`/supportTicketComment`,initsupportTicketCommentRoutes());
  app.use(`/states`,initstatesRoutes());
  app.use(`/settings`,initsettingsRoutes());
  app.use(`/tripArchieve`,inittripArchieveRoutes());
  app.use(`/extendDayLog`,initextendDayLogRoutes());
  app.use(`/offer`,initofferRoutes());
  app.use(`/offerCategory`,initofferCategoryRoutes());
  app.use(`/offerRedeemed`,initofferRedeemedRoutes());
  app.use(`/tracking`,initUserTrackingRoutes());
  // app.use(`/app`,initofferRedeemedRoutes());

 

 // Add headers
// app.use(function (req, res, next) {

//     // Website you wish to allow to connect
//     res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');

//     // Request methods you wish to allow
//     res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, PATCH, DELETE');

//     // Request headers you wish to allow
//     res.setHeader('Access-Control-Allow-Headers', 'X-Requested-With,content-type,Authorization,authorization');
//     // res.setHeader('Access-Control-Allow-Headers', '*');

//     // Set to true if you need the website to include cookies in the requests sent
//     // to the API (e.g. in case you use sessions)
//     res.setHeader('Access-Control-Allow-Credentials', true);

//     // Pass to next layer of middleware
//     next();
// });

  
  // set the static files location /public/img will be /img for users
  // app.get('/', function(req, res) {  });


};

export default initRoutes;
