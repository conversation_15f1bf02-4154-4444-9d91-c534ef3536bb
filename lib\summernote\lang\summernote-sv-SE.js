(function ($) {
  $.extend($.summernote.lang, {
    'sv-SE': {
      font: {
        bold: 'Fet',
        italic: 'Kursiv',
        underline: 'Understruken',
        clear: 'Radera formatering',
        height: 'Radavstånd',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        strikethrough: 'Genomstruken',
        size: 'Teckenstorlek'
      },
      image: {
        image: 'Bild',
        insert: 'Infoga bild',
        resizeFull: 'Full storlek',
        resizeHalf: 'Halv storlek',
        resizeQuarter: 'En fjärdedel i storlek',
        floatLeft: 'Vänsterjusterad',
        floatRight: 'Högerjusterad',
        floatNone: 'Ingen justering',
        dragImageHere: 'Dra en bild hit',
        selectFromFiles: 'Välj från filer',
        url: 'Länk till bild',
        remove: 'Ta bort bild'
      },
      video: {
        video: 'Filmklipp',
        videoLink: '<PERSON>änk till filmklipp',
        insert: 'Infoga filmklipp',
        url: '<PERSON>änk till filmklipp',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion eller Youku)'
      },
      link: {
        link: 'Länk',
        insert: 'Infoga länk',
        unlink: 'Ta bort länk',
        edit: 'Redigera',
        textToDisplay: 'Visningstext',
        url: 'Till vilken URL ska denna länk peka?',
        openInNewWindow: 'Öppna i ett nytt fönster'
      },
      table: {
        table: 'Tabell'
      },
      hr: {
        insert: 'Infoga horisontell linje'
      },
      style: {
        style: 'Stil',
        p: 'p',
        blockquote: 'Citat',
        pre: 'Kod',
        h1: 'Rubrik 1',
        h2: 'Rubrik 2',
        h3: 'Rubrik 3',
        h4: 'Rubrik 4',
        h5: 'Rubrik 5',
        h6: 'Rubrik 6'
      },
      lists: {
        unordered: 'Punktlista',
        ordered: 'Numrerad lista'
      },
      options: {
        help: 'Hjälp',
        fullscreen: 'Fullskärm',
        codeview: 'HTML-visning'
      },
      paragraph: {
        paragraph: 'Justera text',
        outdent: 'Minska indrag',
        indent: 'Öka indrag',
        left: 'Vänsterjusterad',
        center: 'Centrerad',
        right: 'Högerjusterad',
        justify: 'Justera text'
      },
      color: {
        recent: 'Senast använda färg',
        more: 'Fler färger',
        background: 'Bakgrundsfärg',
        foreground: 'Teckenfärg',
        transparent: 'Genomskinlig',
        setTransparent: 'Gör genomskinlig',
        reset: 'Nollställ',
        resetToDefault: 'Återställ till standard'
      },
      shortcut: {
        shortcuts: 'Kortkommandon',
        close: 'Stäng',
        textFormatting: 'Textformatering',
        action: 'Funktion',
        paragraphFormatting: 'Avsnittsformatering',
        documentStyle: 'Dokumentstil'
      },
      history: {
        undo: 'Ångra',
        redo: 'Gör om'
      }
    }
  });
})(jQuery);
