import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');

const OffersSchema = new Schema({

    title:String,
    description:String,
    image:String,
    category_id:ObjectId,
    website_url:String,
    date:{ type: Date, default: Date.now },
    duration: Number,
    expiry:Date,
    how_to_redeem: String,
	terms_and_conditions: String,
    created_at:Date,
    updated_at:Date,
});

OffersSchema.plugin(timeZone, { paths: ['created_at','updated_at'] });

export default mongoose.model('Offer', OffersSchema);