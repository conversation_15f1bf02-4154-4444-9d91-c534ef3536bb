import Responder from '../../lib/expressResponder';
import Transaction from '../models/transaction';
import GatewayTransaction from '../models/gatewayTransaction';
import paytm_config from '../paytm/paytm_config';
import checksum from '../paytm/checksum';
import User from '../models/user';
import _ from "lodash";
import nodemailer from 'nodemailer';
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
import moment from 'moment-timezone';


var transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'RabbiBani'
  }
});



export default class TransactionController {

  static page(req, res) {


    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

  
    Transaction.aggregate([ 
              {
                    $match: {
                      // $and:[
                        // 'user_id': req.params.id,
                        // 'status': 'ACTIVE',
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
   
  }

    static getReferralTransactions(req,res){
       // var counts={};

        console.log('req-----------referal '+req.params);
        console.log(req.params);
        Transaction.count({due_to_user:ObjectId(req.params.user_id),transaction_reason:'Referral Commission'}, function (err, totalValue) {
            if (err) console.log("problem~~~~~~~~1"+err);
              // Responder.success(res,response);
              console.log('counts'+totalValue)
              var totaltransactionvalue=totalValue
              console.log(totaltransactionvalue);
              var arr={};
              arr.totaltransactionvalue = totaltransactionvalue;
              Responder.success(res,arr);
        
        });
        // .then((trc)=>Responder.success(res,counts))
        // .catch((err)=>Responder.operationFailed(res,err))

        // Transaction.count({due_to_user:ObjectId(req.params.id)},{transaction_reason:'Referral Commission' }, function (err, count) {
        //   counts.total = count;
          
        //   Responder.success(res,counts);
          
        // });
    }


    static searchTransactionsForDate(req,res){
        // var counts={};
        console.log('req-----------'+req.body);
        console.log(req.body);
        // Transaction.find({user_id:req.body.id})
        // Transaction.find({$and:[{user_id:req.body.user_id}, {transaction_reason:'Referral Commission'} ,{transaction_date: { $lt: req.body.to_date } } ,{transaction_date: { $gt: req.body.from_date } }]

    // let query = Transaction.find({$and:[{user_id:req.body.user_id} ,{"transaction_date": { "$gte": new Date(req.body.from_date) , "$lte": new Date(req.body.to_date)  } } ,{transaction_date: { $gte: req.body.from_date } }]
    //     }).sort({ 'transaction_date': -1 });

    // let promise = query.exec();

    //     promise.then((trc)=>Responder.success(res,trc))
    //     .catch((err)=>Responder.operationFailed(res,err))


         Transaction.aggregate([ 
                {
                      $match: {
                            $and:[
                            {user_id:ObjectId(req.body.user_id)} ,
                            {
                              "transaction_date": { 
                                "$gte": new Date(req.body.from_date) ,
                                 "$lte": new Date(req.body.to_date)  
                               } 
                            } ,
                            // {transaction_date: { $gte: req.body.from_date } }
                        ]
                      }   
                },
                // { $lookup:
                //    {
                //      from: 'vehicles',
                //      localField: 'vehical_id',
                //      foreignField: '_id',
                //      as: 'vehicleDetails'
                //    }
                //  },
                //   { $lookup:
                //    {
                //      from: 'users',
                //      localField: 'user_id',
                //      foreignField: '_id',
                //      as: 'userDetails'
                //    }
                //  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static searchReferralTransactionsForDate(req,res){
      let query = Transaction.find({$and:[{user_id:req.body.user_id} ,{"transaction_date": { "$gte": new Date(req.body.from_date) , "$lte": new Date(req.body.to_date)  } },{ transaction_reason: 'Referral Commission' } ,{transaction_date: { $gte: req.body.from_date } }]
          }).sort({ 'transaction_date': -1 });

      let promise = query.exec();

        promise.then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    }


  static count(req, res) {
    
    // Transaction.count({})
    // .then((trc)=>Responder.success(res,trc))
  }

  

  static countPostForFirstTime(req, res) {
     Transaction.count({})
    .then((trc)=>Responder.success(res,trc))
  }


  static filtertrancsactionsCount(req, res) {
      if (req.body.type == 'DR') {
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        "transaction_type": 'DR'
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))

    }if (req.body.type == 'CR') {
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },                        "transaction_type": 'CR'

                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      
    }else if (!req.body.startDate &&  !req.body.endDate){
      console.log('11111111');
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                        // "transaction_type": 'DR',
                        // "transaction_type": 'CR'

                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
    else{
      console.log('1112222222bbb');

      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
   
  }

  static countPost(req, res) {
    
    // Transaction.count({})
    // .then((trc)=>Responder.success(res,trc))
      if (req.body.due_to_user_name != null || req.body.phone_number != null) {
      console.log('1111111111111111--')
      User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {name: {$regex : "^" + req.body.due_to_user_name,$options: 'i'}},
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
        .then((makers)=>{
            var object= [];
            makers.forEach(function (maker, k) {                
              console.log('ffffffffffffffff',maker._id)
              object.push(ObjectId(maker._id))
              
            });

                Transaction.aggregate([ 
                   {
                    $match: {
                              'user_id': { $in: object }
                              
                            }
                        
                  },
                  { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }

                  ])
              .then((product)=>
              // {
                // console.log('ppppppppppp',product),
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }

      else if (req.body.startDate != null && req.body.endDate != null && req.body.transaction_reason != null) {

         Transaction.aggregate([ 
                {
                      $match: {
                         $and:[
                            {"transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},  
                            {'transaction_reason': {$regex : "^" + req.body.transaction_reason,$options: 'i'}},
                         ]
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      }

      else{

         Transaction.aggregate([ 
                {
                      $match: {
                         $or:[
                            {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},
                            {transaction_reason: {$regex : "^" + req.body.transaction_reason,$options: 'i'}},
                            {amount:  parseInt(req.body.amount)},
                            // {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},

                          // {transaction_id:req.body.transaction_id},
                         ]
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },
                 { 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      }
  

  }


  static countReferralTransactions(req, res) {
    
    Transaction.count({'transaction_reason': 'Referral Commission'})
    .then((trc)=>Responder.success(res,trc))



  }


  static countFilterByDateForAllUsers(req, res) {
      console.log('countFilterByDateForAllUsers 0000',req.body)
    
    if (req.body.startDate != null && req.body.endDate != null && req.body.referred_by_name == null) {
      console.log('countFilterByDateForAllUsers 111')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                       })
      .then((trc)=>Responder.success(res,trc))

    }
    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name != null) {
      console.log('countFilterByDateForAllUsers 2222')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},

                       })
      .then((trc)=>Responder.success(res,trc))      
    }
    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name == null && req.body.phone_number != null) {
      console.log('countFilterByDateForAllUsers 333')
       
       User.aggregate([ 
                   {
                    $match: {
                             
                               'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{

                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                   Transaction.aggregate([ 
                            {
                                $match: {
                                  'user_id': { $in: object },
                                  'transaction_reason': 'Referral Commission',
                                }
                            },
                            {
                                 $group: {
                                      _id: {
                                      },
                                        myCount: { $sum: 1 } ,
                                    

                                    }
                                   
                            }
                            
                                    ])
                    .then((trc)=>{
                      console.log('result ### ',trc.length);
                      if (trc.length == 0) {
                        Responder.success(res, {data:0})
                      }else{
                        Responder.success(res,trc[0].myCount)

                      }
                      // if (true) {}
                      }
                      )
                    .catch((err)=>Responder.operationFailed(res,err))              
              }
          )
        .catch((err)=>Responder.operationFailed(res,err))
  
    }
    if (req.body.startDate != null && req.body.endDate != null && req.body.referred_by_name != null && req.body.phone_number == null) {
      console.log('countFilterByDateForAllUsers 4444')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},

                       })
      .then((trc)=>Responder.success(res,trc))

    }

    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name == null && req.body.phone_number == null && req.body.percent == 5) {
      console.log('countFilterByDateForAllUsers 55555')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        // "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        // 'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},
                        'subscribeFeePercentage': 8,
                       })
      .then((trc)=>Responder.success(res,trc))

    }
    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name != null && req.body.phone_number == null && req.body.percent == 5) {
      console.log('countFilterByDateForAllUsers 666')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        // "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},
                        'subscribeFeePercentage': 8,
                       })
      .then((trc)=>Responder.success(res,trc))

    }

    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name == null && req.body.phone_number == null && req.body.percent == 20) {
      console.log('countFilterByDateForAllUsers 7777')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        // "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        // 'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},
                        'subscribeFeePercentage': 30,
                       })
      .then((trc)=>Responder.success(res,trc))

    }

    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name != null && req.body.phone_number == null && req.body.percent == 20) {
      console.log('countFilterByDateForAllUsers 7777')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        // "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        'referred_by_name': {$regex : "^" + req.body.referred_by_name,$options: 'i'},
                        'subscribeFeePercentage': 30,
                       })
      .then((trc)=>Responder.success(res,trc))

    }

    if (req.body.startDate == null && req.body.endDate == null && req.body.referred_by_name == null && req.body.phone_number == null && req.body.referred_by_code != null) {
      console.log('countFilterByDateForAllUsers 8888')
      Transaction.count({'transaction_reason': 'Referral Commission',
                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , 
                        // "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        'referred_by_code': {$regex : "^" + req.body.referred_by_code,$options: 'i'},
                       })
      .then((trc)=>Responder.success(res,trc))

    }
    


  }

  static show(req, res) {
 
        Transaction.aggregate([ 
              {
                    $match: {
                      user_id: ObjectId(req.params.id)
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'due_to_user',
                   foreignField: '_id',
                   as: 'dueToUser'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  

  static transactionNoPayout(req, res) {

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)
 
        Transaction.aggregate([ 
              {
                $match: {
                  user_id: ObjectId(req.body.user_id),
                  due_to_user_name: {$ne:"Swari"} 
                }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'due_to_user',
                   foreignField: '_id',
                   as: 'dueToUser'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  static totalEarningTransactions(req, res) {
    if (req.body.startDate == null && req.body.endDate == null && req.body.phone_number == null) {
         Transaction.aggregate([ 
                {
                      $match: {
                        user_id: ObjectId(req.body.user_id),
                        due_to_user_name: {$ne:"Swari"} 
                        }
                      
                },
                 {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
      else if (req.body.startDate != null && req.body.endDate != null
              && req.body.phone_number == null) {
            Transaction.aggregate([ 
                  {
                        $match: {
                          user_id: ObjectId(req.body.user_id),
                          "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                            $or:[
                                  {transaction_reason: "Referral Commission"},
                                  {transaction_reason: "payout given to refferal user"},
                            ]
                          }
                        
                  },
                  {
                   $group: {
                        _id: {
                          
                        },
                        myCount: { $sum: 1 } ,
                      }
                  },


                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
      
      }else if (req.body.startDate != null && req.body.endDate != null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                            "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                                                $or:[
                                                            {transaction_reason: "Referral Commission"},
                                                            {transaction_reason: "payout given to refferal user"},
                                                      ]                                            
                                          }
                                      
                                },
                                {
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                                },

                                ])
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }else if (req.body.startDate == null && req.body.endDate == null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                              $or:[
                                                    {transaction_reason: "Referral Commission"},
                                                    {transaction_reason: "payout given to refferal user"},
                                              ]
                                          }
                                      
                                },
                                {
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                                },

                                ])
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }

  }

  static filterReferralUserTransactions(req, res) {
 
      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)


        if (req.body.startDate != null && req.body.endDate != null
              && req.body.phone_number == null) {
            Transaction.aggregate([ 
                  {
                        $match: {
                          user_id: ObjectId(req.body.user_id),
                          "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                            $or:[
                                  {transaction_reason: "Referral Commission"},
                                  {transaction_reason: "payout given to refferal user"},
                            ]
                          }
                        
                  },
                  { $lookup:
                     {
                       from: 'vehicles',
                       localField: 'vehical_id',
                       foreignField: '_id',
                       as: 'vehicleDetails'
                     }
                   },
                    { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                    { $lookup:
                     {
                       from: 'users',
                       localField: 'due_to_user',
                       foreignField: '_id',
                       as: 'dueToUser'
                     }
                   },
                    { $lookup:
                     {
                       from: 'users',
                       localField: 'due_to_user',
                       foreignField: '_id',
                       as: 'dueToUser'
                     }
                   },
                   { 
                    "$sort": {  
                        "transaction_date": -1,
                    } 
                  }


                        ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
      
      }else if (req.body.startDate != null && req.body.endDate != null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                            "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                                                $or:[
                                                            {transaction_reason: "Referral Commission"},
                                                            {transaction_reason: "payout given to refferal user"},
                                                      ]                                            
                                          }
                                      
                                },
                                { $lookup:
                                 {
                                   from: 'vehicles',
                                   localField: 'vehical_id',
                                   foreignField: '_id',
                                   as: 'vehicleDetails'
                                 }
                               },
                                { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'user_id',
                                   foreignField: '_id',
                                   as: 'userDetails'
                                 }
                               },
                                { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'due_to_user',
                                   foreignField: '_id',
                                   as: 'dueToUser'
                                 }
                               },{ 
                                "$sort": {  
                                    "transaction_date": -1,
                                } 
                              }

                                ]).skip(size * (pageNo - 1)).limit(size)
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }else if (req.body.startDate == null && req.body.endDate == null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                              $or:[
                                                    {transaction_reason: "Referral Commission"},
                                                    {transaction_reason: "payout given to refferal user"},
                                              ]
                                          }
                                      
                                },
                                { $lookup:
                                 {
                                   from: 'vehicles',
                                   localField: 'vehical_id',
                                   foreignField: '_id',
                                   as: 'vehicleDetails'
                                 }
                               },
                                { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'user_id',
                                   foreignField: '_id',
                                   as: 'userDetails'
                                 }
                               },
                                { $lookup:
                                 {
                                   from: 'users',
                                   localField: 'due_to_user',
                                   foreignField: '_id',
                                   as: 'dueToUser'
                                 }
                               },{ 
                                "$sort": {  
                                    "transaction_date": -1,
                                } 
                              }

                                ]).skip(size * (pageNo - 1)).limit(size)
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }
  }
 
  static create(req, res) {
   
     //let transactionDetails = req.body;
     console.log('tr---------');
     console.log(req.user.name);
  
    Transaction.create(req.body)
       .then((trnc)=>{
        Responder.success(res,trnc);
      })
       .catch((err)=>Responder.operationFailed(res,err))

      
  }

  static update(req, res) {

   
  }

  static remove(req, res) {
   
  }
  static generateChecksum(req, res){
    var paramarray = {};
         paramarray['MID'] = 'ADD_YOUR_MERCHANT_ID_PROVIDED_BY_PAYTM'
        paramarray['ORDER_ID'] = new Date().getTime().toString();//new Date().getTime(); //unique OrderId for every request
        paramarray['CUST_ID'] = 'CUSTOMER_ID' // unique customer identifier      this is change in backend code at
        paramarray['INDUSTRY_TYPE_ID'] = 'Retail'; //Provided by Paytm
        paramarray['CHANNEL_ID'] = 'WAP'; //Provided by Paytm
        paramarray['TXN_AMOUNT'] = req.body.TXN_AMOUNT; // transaction amount
        paramarray['WEBSITE'] = 'APPSTAGING'; //Provided by Paytm
        paramarray['CALLBACK_URL'] = "https://pguat.paytm.com/paytmchecksum/paytmCallback.jsp";//https://pguat.paytm.com/paytmchecksum/paytmCallback.jsp Provided by Paytm
        paramarray['EMAIL'] = req.body.email;  // customer email id
      //  paramarray['MOBILE_NO'] = req.body.MOBILE_NO;  // customer 10 digit mobile no.
       console.log("line 65 ",paramarray);
      checksum.genchecksum(paramarray, paytm_config.paytm_config.MERCHANT_KEY, function (err, res1) {
        Responder.success(res,res1)
      })
  }

  static verifyChecksum(request, response){
   var fullBody = '';
   console.log(request.body);
        request.on('data', function(chunk) {
          fullBody += chunk.toString();
        });
        request.on('end', function() {
          var decodedBody = querystring.parse(fullBody);
          response.writeHead(200, {'Content-type' : 'text/html','Cache-Control': 'no-cache'});
          if(checksum.verifychecksum(decodedBody, paytm_config.paytm_config.MERCHANT_KEY)) {
            console.log("true");
          }else{
            console.log("false");
          }
           // if checksum is validated Kindly verify the amount and status 
           // if transaction is successful 
          // kindly call Paytm Transaction Status API and verify the transaction amount and status.
          // If everything is fine then mark that transaction as successful into your DB.     
          
          response.end();
        });
  }


static searchReferralTransactionsForThisMonth(req,res){
    // var counts={};
    console.log('req-----------111'+req.body);
    console.log(req.body.referal_code);
    // Transaction.find({user_id:req.body.id})
    // Transaction.find({$and:[{user_id:req.body.user_id}, {transaction_reason:'Referral Commission'} ,{transaction_date: { $lt: req.body.to_date } } ,{transaction_date: { $gt: req.body.from_date } }]
    var timeNow = moment(new Date()).toDate();
    var dateOneMonthBefore = moment(new Date()).subtract(180,'days').toDate();
   
    // Transaction.find({$and:[{user_id:req.body.user_id} ,{ transaction_reason: 'Referral Commission' },{transaction_date: { $lte: timeNow } } ,{transaction_date: { $gte: dateOneMonthBefore } }]
    Transaction.find({$and:[{ referred_by_code: req.body.referal_code },{transaction_date: { $lte: timeNow } },{ transaction_reason: 'Referral Commission' } ,{transaction_date: { $gte: dateOneMonthBefore } }]
    }).sort({ 'transaction_date': -1 })
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
}



static getTransactionsforThisMonth(req, res) {
  console.log('req.getTransactionsforThisMonth')
  console.log(req.body)
   console.log('dddddddddddd'+new Date().toISOString().slice(0, 10));
    //var timeNow = new Date().getTime();
    var currentDate= moment.tz('Asia/Kolkata').format();
    var dateString=moment(currentDate).format('YYYY-MM-DDTHH:mm:ss.SSS');
    var dateOneMonthBefore=moment(dateString).subtract(30,'days').format('YYYY-MM-DDTHH:mm:ss.SSS');

   // var onlyDateString= new Date(dateString).toISOString().slice(0,10);
   // var onlydateOneMonthBefore= new Date(dateOneMonthBefore).toISOString().slice(0,10);

    console.log('dateString  ',dateString)
    console.log('dateOneMonthBefore  ',dateOneMonthBefore)
    console.log('dateOneMonthBefore2212  ',Date(dateString))
    //var dateOneMonthBefore = new Date(timeNow - 1000 * 60 * 60 * 168).toISOString();
    //console.log('dateOneMonthBefore 11',timeNow)
    //console.log('dateOneMonthBefore 11222',dateOneMonthBefore)
    // let query = Transaction.find({$and:[{user_id:req.body.user_id} ,{transaction_date: { $lte: timeNow } } ,{transaction_date: { $gte: dateOneMonthBefore } }]
    // }).sort({ 'transaction_date': -1 });

    // let promise = query.exec(); sar

    //     promise.then((trc)=>Responder.success(res,trc))
    //     .catch((err)=>Responder.operationFailed(res,err))

         Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  
                    $and:[
                        {user_id:ObjectId(req.body.user_id)},
                         {"transaction_date": { "$gte": moment(dateString).subtract(30,'days').format('YYYY-MM-DDTHH:mm:ss.SSS')  , "$lte": moment(currentDate).format('YYYY-MM-DDTHH:mm:ss.SSS')}},

                                                  // {"created_at" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                                            ] 
                  // $and:[{user_id:ObjectId(req.body.user_id)} ,
                        
                  //  {transaction_date: { $lte: timeNow } } ,{transaction_date: { $gte: dateOneMonthBefore } }
                  //       ]
                }
            },
            // { $lookup:
            //    {
            //      from: 'users',
            //      localField: 'user_id',
            //      foreignField: '_id',
            //      as: 'userDetails'
            //    }
            //   },
            //   { $lookup:
            //    {
            //      from: 'users',
            //      localField: 'due_to_user',
            //      foreignField: '_id',
            //      as: 'dueToUser'
            //    }
              // }
              // ,
              { 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }
  

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

    // Transaction.find({$and:[{user_id:req.body.user_id} ,{transaction_date: { $lte: timeNow } } ,{transaction_date: { $gte: dateOneMonthBefore } }]
    
    // }).then((trc)=>Responder.success(res,trc))
    // .catch((err)=>Responder.operationFailed(res,err))
}


static getAllReferralTransactions(req,res){
    console.log('getAllReferralTransactions');
   
    // Transaction.find({ transaction_reason: 'Referral Commission' })
    // .then((trc)=>Responder.success(res,trc))
    // .catch((err)=>Responder.operationFailed(res,err))

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission'
                }
            },
            { $lookup:
               {
                 from: 'users',
                 localField: 'user_id',
                 foreignField: '_id',
                 as: 'userDetails'
               }
              },
              { $lookup:
               {
                 from: 'users',
                 localField: 'due_to_user',
                 foreignField: '_id',
                 as: 'dueToUser'
               }
              },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }
              // ,
            // {
            //      $group: {
            //           _id: {
            //             year: {
            //               $year: '$transaction_date'
            //             },
            //             month: {
            //               $month: '$transaction_date',
            //             },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
                    

            //         }
                  
            //  // $project: { month:  
            //  //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
            //  //                { $month: '$transaction_date' }, -1] 
            //  //                }
            //  //            } 
            // }

                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


    

}



static getAllReferralTransactionsByUser(req,res){
    console.log('getAllReferralTransactionsByUser');
   
    // Transaction.find({ transaction_reason: 'Referral Commission' })
    // .then((trc)=>Responder.success(res,trc))
    // .catch((err)=>Responder.operationFailed(res,err))


    Transaction.aggregate([ 
            {
                /* Filter out users who have not yet subscribed */
                $match: {
                  /* "joined" is an ISODate field */
                  'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission'
                }
            }
            // ,
            // {
            //      $group: {
            //                 _id: '$due_to_user_name'
            //                 ,
            //                 total: { $sum: { $add: ["$amount"] } },
            //                 obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}


static getTransactionsForUser(req,res){
    console.log('getTransactionsForUser',req.body.userName);

        var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

   
    Transaction.aggregate([ 
            {
                $match: {
                  // 'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission',
                  'referred_by_name': {$regex : "^" + req.body.userName,$options: 'i'},

                }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             // total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}



static getUserTransactionsForDate(req,res){
    console.log('getUserTransactionsForDate');
    console.log(req.body.startDate);



     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)
    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Referral Commission',
                  "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                  'referred_by_name': {$regex : "^" + req.body.userName,$options: 'i'},

                }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            //   } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}



static getFilterByDateForAllUsers(req,res){
    console.log('getFilterByDateForAllUsers');
    // console.log(req.body.user._id);
    console.log(req.body.startDate);

     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Referral Commission',
                  "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                }
            },
             { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}


static getFilterByFivePerForAllUsers(req,res){
    console.log('getFilterByFivePerForAllUsers');
    // console.log(req.body.user._id);

     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    Transaction.aggregate([ 
            {
                $match: {
                  // 'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission',
                  'subscribeFeePercentage': 8,
                  // 'transaction_date':  { $gte:  req.body.startDate} ,
                  // "transaction_date": { "$gte": new Date(req.body.startDate) , "$lte": new Date(req.body.endDate)  }

                  // 'transaction_date': {transaction_date: { $lte:  req.body.startDate} },
                }
            },
             { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}



static getFilterByTwentyPerForAllUsers(req,res){
    console.log('getFilterByTwentyPerForAllUsers');
    // console.log(req.body.user._id);
    var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)
   

    Transaction.aggregate([ 
            {
                $match: {
                  // 'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission',
                  'subscribeFeePercentage': 30,
                  // 'transaction_date':  { $gte:  req.body.startDate} ,
                  // "transaction_date": { "$gte": new Date(req.body.startDate) , "$lte": new Date(req.body.endDate)  }

                  // 'transaction_date': {transaction_date: { $lte:  req.body.startDate} },
                }
            },
             { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}


static getFilterByFivePerForSelectedUser(req,res){
    console.log('getFilterByFivePerForSelectedUser');
    // console.log(req.body.user._id);
     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)
   
    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Referral Commission',
                  'subscribeFeePercentage': 8,
                  'referred_by_name': {$regex : "^" + req.body.userName,$options: 'i'},
                }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              }
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}




static getFilterByTwentyPerForSelectedUser(req,res){
    console.log('getFilterByTwentyPerForSelectedUser');
    // console.log(req.body.user._id);

    var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Referral Commission',
                  'subscribeFeePercentage': 30,
                  'referred_by_name': {$regex : "^" + req.body.userName,$options: 'i'},
                }
            },
             { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
            // {
            //      $group: {

            //          _id: {
            //             // year: {
            //             //   $year: '$transaction_date'
            //             // },
            //             // month: {
            //             //   $month: '$transaction_date',
            //             // },
            //             // due_to_user: {
            //             //   $due_to_user: '$due_to_user'
            //             // }
            //           },
            //             total: { $sum: { $add: ["$amount"] } },
            //             myCount: { $sum: 1 } ,
            //             obj: { $push : "$$ROOT" }
                      
            //                 // _id: '$due_to_user_name'
            //                 // ,
            //                 // total: { $sum: { $add: ["$amount"] } },
            //                 // obj: { $push : "$$ROOT" }
                      
               

            //         }
               
            // } 
                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}



    static getDebitTrancsactions(req,res){
        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)
        
         Transaction.aggregate([ 
              {
                    $match: {
                          transaction_type:'DR',
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    }


    
    static getDebitTrancsactionsLength(req, res) {
    
        Transaction.count({transaction_type:'DR' }, function (err, count) {
            if (err) console.log("problem~~~~~~~~1"+err);
            // Responder.success(res,response);
        })
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }

    static getCreditTransactionsLength(req, res) {
    
        Transaction.count({transaction_type:'CR' }, function (err, count) {
            if (err) console.log("problem~~~~~~~~1"+err);
            // Responder.success(res,response);
        })
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
    
    }


    static getCreditTransactions(req,res){
        var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
        console.log('size--'+size)

       

        Transaction.aggregate([ 
              {
                    $match: {
                          transaction_type:'CR',
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    }

    // findOneAndUpdate

    static getTransactionBalance(req,res){
        console.log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        console.log(req.body.user_id)
        console.log("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        Transaction.aggregate([
            { $match: { "user_id": ObjectId(req.body.user_id) }}, //remove match if not required.
            {$group: {
                "_id": { // group by both fields, "user_id" and "branch_id"
                    "user_id": "$user_id",
                },
                "cr_total": {
                    $sum: { // calculate the sum of all "total" values
                        $cond: {
                            if: { $eq: [ "$transaction_type", "CR" ] }, // in case of "DEBIT", we want the stored value for "total"
                            then: "$amount", 
                            else: {}
                        }
                    }
                },
                "dr_total": {
                    $sum: { // calculate the sum of all "total" values
                        $cond: {
                            if: { $eq: [ "$transaction_type", "DR" ] }, // in case of "DEBIT", we want the stored value for "total"
                            then: "$amount",
                            else: {}
                        }
                    }
                },
            },
            },{
                "$project": {
                    "cr":"$cr_total",
                    "dr":"$dr_total",
                    "balance": { "$subtract": [ "$cr_total", "$dr_total" ] }
            }}]
        ).then((trc)=>Responder.success(res,trc))
            .catch((err)=>Responder.operationFailed(res,err))

        // Transaction.aggregate({
        //     $group: { 
        //         "_id": { // group by both fields, "user_id" and "branch_id"
        //             "user_id": req.body.user_id,
                    
        //         },
        //         "final_dr": {
        //             $sum: { // calculate the sum of all "total" values
        //                 $cond: {
        //                     if: { $eq: [ "$transaction_type", "DR" ] }, // in case of "DEBIT", we want the stored value for "total"
        //                     then: "$amount", 
        //                    else: { },
        //                      // in case of "DEBIT", we want the stored value for "total"
        //                     // elseif:
        //                     // {$eq: [ "$transaction_type", "CR" ] }, // in case of "DEBIT", we want the stored value for "total"
        //                     // then: "$amount",
        //                     // else:{},
        //                     // { $multiply: [ "$amount", -1 ] } // otherwise we want the stored value for "total" times -1
        //                 }
        //             }
        //         },

        //          "final_cr": {
        //             $sum: { // calculate the sum of all "total" values
        //                 $cond: {
        //                     if: { $eq: [ "$transaction_type", "CR" ] }, // in case of "DEBIT", we want the stored value for "total"
        //                     then: "$amount", 
        //                    else: { },
        //                      // in case of "DEBIT", we want the stored value for "total"
        //                     // elseif:
        //                     // {$eq: [ "$transaction_type", "CR" ] }, // in case of "DEBIT", we want the stored value for "total"
        //                     // then: "$amount",
        //                     // else:{},
        //                     // { $multiply: [ "$amount", -1 ] } // otherwise we want the stored value for "total" times -1
        //                 }
        //             }
        //         }
        //     }
        // }).then((trc)=>Responder.success(res,trc))
        //     .catch((err)=>Responder.operationFailed(res,err))
       
            // Transaction.aggregate([ 
            //         {
            //             /* Filter out users who have not yet subscribed */
            //         $or: [ 
            //             {    
            //                 $match: {
            //                   /* "joined" is an ISODate field */
            //                   // 'transaction_date': {$ne: null},
            //                   'transaction_type': 'DR',
            //                   'user_id': req.body.user_id
            //                 },
            //                 $match: {
            //                   /* "joined" is an ISODate field */
            //                   // 'transaction_date': {$ne: null},
            //                   'transaction_type': 'CR',
            //                   'user_id': req.body.user_id
            //                 }
            //             }
            //             ]
            //         },
            //         {
            //              $group: {
            //                         _id: '$due_to_user_name'
            //                         ,
            //                         totaldr: { $sum: { $add: ["$amount"] } },
            //                         obj: { $push : "$$ROOT" }
                              
                       

            //                 },
            //                 $group: {
            //                         _id: '$due_to_user_name'
            //                         ,
            //                         totalcr: { $sum: { $add: ["$amount"] } },
            //                         obj: { $push : "$$ROOT" }
                              
                       

            //                 }
                       
            //         } 
            //                 ])
            // .then((trc)=>Responder.success(res,trc))
            // .catch((err)=>Responder.operationFailed(res,err))






        // Transaction.find({user_id: req.body.user_id,transaction_type:"DR"})
        // .then((DR)=>
        //     // Responder.success(res,trc)
        //     {
        //     console.log('DR');
        //     console.log();
        //         Transaction.find({user_id: req.body.user_id,transaction_type:"CR"})
        //         .then((CR)=>
        //         {
        //             console.log('CR')
        //             console.log(CR)
        //         }
        //         )

        //     }
        //     )
        // .catch((err)=>Responder.operationFailed(res,err))




    //     Transaction.findOneAndUpdate({ _id: req.params.id }, { $set: req.body })
    // .then((data) =>{ return User.findOne({_id:data._id })})  
    // .then((val) => Responder.success(res, val))
    // .catch((err) => Responder.operationFailed(res, err))

    }


    static updateTransaction(req,res){


        // Transaction.findOneAndUpdate({ transaction_id: req.body.transaction_id }, { $set: req.body} )
        // .then((data) =>Responder.success(res,data))


        Transaction.findOneAndUpdate({ transaction_id: req.body.transaction_id }, { $set: { total_amount:req.body.balance} })
        .then((data) =>Responder.success(res,data))

    }


    static findLatestTransaction(req,res){
        Transaction.findOne({user_id: req.params.id}).sort({ 'transaction_date': -1 }).limit(1)
        .then((data) =>Responder.success(res,data))

    }


    static updateTransactionForPayout(req,res){
      console.log('111111')
      console.log(req.body)

        // Transaction.update({ status:false  ,user_id:ObjectId(req.body.user_id)}, { $set: req.body} ,{multi: true})

        Transaction.update({ status:false  ,user_id:ObjectId(req.body.user_id)}, { $set: {status:true,payout_id:req.body.payout_id}} ,{multi: true})
        .then((data) =>Responder.success(res,data))


        // Transaction.findOneAndUpdate({ transaction_id: req.body.transaction_id }, { $set: { total_amount:req.body.balance} })
        // .then((data) =>Responder.success(res,data))

    }

    static forPayoutId(req,res){
      console.log('aaaaa')
      console.log(req.body)
      Transaction.aggregate([ 
              {
                    $match: {
                      // $and:[
                        'payout_id': req.body.payout_id,
                        'user_id': ObjectId(req.body.user_id)
                        // 'status': 'ACTIVE',
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'due_to_user',
                   foreignField: '_id',
                   as: 'dueToUser'
                 }
               }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    }


     static lastTransactionForPayout(req,res){
      console.log('aaaaa')
      console.log(req.body)

      Transaction.find({payout_id: req.body.payout_id ,
            user_id:ObjectId(req.body.user_id),
            due_to_user_name:"Swari"
           })
        .then((transaction) =>
        {
          var str1= transaction[0].transaction_reason;
          var str2= ' '+req.body.remarks;
          var str= str1.concat(str2);
          console.log('transaction  ?? ',str)

          Transaction.findOneAndUpdate({payout_id: req.body.payout_id ,
            user_id:ObjectId(req.body.user_id),
            due_to_user_name:"Swari"
           },{$set:{transaction_reason:str}}
           )
          .then((data) =>Responder.success(res,data))

        }

          )

     
    }


    

    static foruser_id(req,res){
      Transaction.aggregate([ 
              {
                    $match: {
                     
                        'user_id': ObjectId(req.body.user_id),
                        'payout_id': req.body.payout_id

                       
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'due_to_user',
                   foreignField: '_id',
                   as: 'dueToUser'
                 }
               }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
    }

    
    
    static myTotalReferralIncome(req,res){ 
      Transaction.aggregate([ 
            {
                $match: {
                  'transaction_reason': 'Referral Commission',
                  'user_id': ObjectId(req.body.user_id),
                  "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },

                  // "transaction_date": { "$gte": new Date(req.body.startDate) , "$lte": new Date(req.body.endDate)  }
                }
            },
            {
                 $group: {

                     _id: {
                       
                      },
                        total: { $sum: { $add: ["$amount"] } },
                        // obj: { $push : "$$ROOT" }

                    }
               
            } 
                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }



  static filterTransactions(req, res) {

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

    if (req.body.type == 'DR') {
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                        "transaction_type": 'DR'
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))

    }if (req.body.type == 'CR') {
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },                        "transaction_type": 'CR'

                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      
    }else if (!req.body.startDate &&  !req.body.endDate){
      console.log('11111111');
      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},
                        // "transaction_type": 'DR',
                        // "transaction_type": 'CR'

                        // "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
    else{
      console.log('1112222222aaaaa');

      Transaction.aggregate([ 
                {
                      $match: {
                        // "transaction_date" : { $gte : new Date(req.body.startDate+'T00:00:00Z'), $lte:  new Date(req.body.endDate+'T23:59:59Z') }},

                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    }
   
  }


  

  static filterTransactionDetail(req, res) {

      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)


    if (req.body.due_to_user_name != null || req.body.phone_number != null) {
      console.log('1111111111111111--')
      User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {name: {$regex : "^" + req.body.due_to_user_name,$options: 'i'}},
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
        .then((makers)=>{
            var object= [];
            makers.forEach(function (maker, k) {                
              console.log('ffffffffffffffff',maker._id)
              object.push(ObjectId(maker._id))
              
            });

                Transaction.aggregate([ 
                   {
                    $match: {
                              'user_id': { $in: object }
                              
                            }
                        
                  },
                  { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }

                  ]).skip(size * (pageNo - 1)).limit(size)
              .then((product)=>
              // {
                // console.log('ppppppppppp',product),
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }

      else if (req.body.startDate != null && req.body.endDate != null && req.body.transaction_reason != null) {

         Transaction.aggregate([ 
                {
                      $match: {
                         $and:[
                            {"transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }},  
                            {'transaction_reason': {$regex : "^" + req.body.transaction_reason,$options: 'i'}},
                         ]
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      }

      else{

         Transaction.aggregate([ 
                {
                      $match: {
                         $or:[
                            {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},
                            {transaction_reason: {$regex : "^" + req.body.transaction_reason,$options: 'i'}},
                            {amount:  parseInt(req.body.amount)},
                            // {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},

                          // {transaction_id:req.body.transaction_id},
                         ]
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
      }
    
  }


  
  static filterUserTransactionsForDate(req, res) {

        Transaction.aggregate([ 
                {
                      $match: {
                        "user_id":ObjectId(req.body.user_id),
                        "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
                        }
                      
                },
                { $lookup:
                   {
                     from: 'vehicles',
                     localField: 'vehical_id',
                     foreignField: '_id',
                     as: 'vehicleDetails'
                   }
                 },
                  { $lookup:
                   {
                     from: 'users',
                     localField: 'user_id',
                     foreignField: '_id',
                     as: 'userDetails'
                   }
                 },{ 
                  "$sort": {  
                      "transaction_date": -1,
                  } 
                }


                      ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))
    
   
  }


  


static getTransactionsForPhoneNumber(req,res){
    
     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

   

     User.aggregate([ 
                   {
                    $match: {
                             
                               'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'}
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{

                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                   Transaction.aggregate([ 
                            {
                                $match: {
                                  'user_id': { $in: object },
                                  'transaction_reason': 'Referral Commission',
                                }
                            },
                             { $lookup:
                               {
                                 from: 'users',
                                 localField: 'user_id',
                                 foreignField: '_id',
                                 as: 'userDetails'
                               }
                              },
                              { $lookup:
                               {
                                 from: 'users',
                                 localField: 'due_to_user',
                                 foreignField: '_id',
                                 as: 'dueToUser'
                               }
                              },{ 
                                "$sort": {  
                                    "transaction_date": 1,
                                } 
                              },
                            // {
                            //      $group: {
                            //           _id: {
                            //             // year: {
                            //             //   $year: '$transaction_date'
                            //             // },
                            //             // month: {
                            //             //   $month: '$transaction_date',
                            //             // },
                            //             // due_to_user: {
                            //             //   $due_to_user: '$due_to_user'
                            //             // }
                            //           },
                            //             // total: { $sum: { $add: ["$amount"] } },
                            //             myCount: { $sum: 1 } ,
                            //             obj: { $push : "$$ROOT" }
                                      
                                    

                            //         }
                                  
                            //  // $project: { month:  
                            //  //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
                            //  //                { $month: '$transaction_date' }, -1] 
                            //  //                }
                            //  //            } 
                            // }
                            
                                    ]).skip(size * (pageNo - 1)).limit(size)
                    .then((trc)=>Responder.success(res,trc))
                    .catch((err)=>Responder.operationFailed(res,err))              
              }
          )
        .catch((err)=>Responder.operationFailed(res,err))
  

}



static getTransactionsForReferralCode(req,res){
    console.log('getTransactionsForReferralCode',req.body.referred_by_code);
     var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

   

       Transaction.aggregate([ 
            {
                $match: {
                  // 'transaction_date': {$ne: null},
                  'transaction_reason': 'Referral Commission',
                  'referred_by_code': {$regex : "^" + req.body.referred_by_code,$options: 'i'},


                }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'user_id',
               foreignField: '_id',
               as: 'userDetails'
             }
            },
            { $lookup:
             {
               from: 'users',
               localField: 'due_to_user',
               foreignField: '_id',
               as: 'dueToUser'
             }
            },{ 
              "$sort": {  
                  "transaction_date": 1,
              } 
            }
            ,
                            // {
                            //      $group: {
                            //           _id: {
                            //             // year: {
                            //             //   $year: '$transaction_date'
                            //             // },
                            //             // month: {
                            //             //   $month: '$transaction_date',
                            //             // },
                            //             // due_to_user: {
                            //             //   $due_to_user: '$due_to_user'
                            //             // }
                            //           },
                            //             // total: { $sum: { $add: ["$amount"] } },
                            //             myCount: { $sum: 1 } ,
                            //             obj: { $push : "$$ROOT" }
                                      
                                    

                            //         }
                                  
                            //  // $project: { month:  
                            //  //                { $cond: [{ $ifNull: ['$transaction_date', 0] },
                            //  //                { $month: '$transaction_date' }, -1] 
                            //  //                }
                            //  //            } 
                            // }
                             ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))


  

}



  static showReferTrc(req, res) {
      var pageNo= req.body.currentPage + 1;
      console.log('pageNo--'+pageNo)
      var size = req.body.page_limit;
      console.log('size--'+size)

        Transaction.aggregate([ 
              {
                    $match: {
                      user_id: ObjectId(req.body.user_id)
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'due_to_user',
                   foreignField: '_id',
                   as: 'dueToUser'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }



    static totalReferTransactions(req, res) {
 
      if (req.body.startDate == null && req.body.endDate == null
              && req.body.phone_number == null) {
        
            Transaction.aggregate([ 
                    {
                          $match: {
                            user_id: ObjectId(req.body.user_id)
                            }
                          
                    },
                    
                     {
                             $group: {
                                  _id: {
                                    
                                  },
                                  myCount: { $sum: 1 } ,
                                }
                        },


                          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))

      }


        else if (req.body.startDate != null && req.body.endDate != null
              && req.body.phone_number == null) {
            Transaction.aggregate([ 
                  {
                        $match: {
                          user_id: ObjectId(req.body.user_id),
                          "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                            $or:[
                                  {transaction_reason: "Referral Commission"},
                                  {transaction_reason: "payout given to refferal user"},
                            ]
                          }
                        
                  },
                  {
                             $group: {
                                  _id: {
                                    
                                  },
                                  myCount: { $sum: 1 } ,
                                }
                        },



                        ])
        .then((trc)=>Responder.success(res,trc))
        .catch((err)=>Responder.operationFailed(res,err))
      
      }else if (req.body.startDate != null && req.body.endDate != null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                            "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') },
                                                $or:[
                                                            {transaction_reason: "Referral Commission"},
                                                            {transaction_reason: "payout given to refferal user"},
                                                      ]                                            
                                          }
                                      
                                },
                              {
                                 $group: {
                                      _id: {
                                        
                                      },
                                      myCount: { $sum: 1 } ,
                                    }
                            },

                                ])
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }else if (req.body.startDate == null && req.body.endDate == null
                && req.body.phone_number != null
                ) {
                  
                   User.aggregate([ 
                   {
                    $match: {
                              $or:[
                                {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                              ]
                              
                            }
                        
                  }

                        ])
        
                      .then((makers)=>{
                          var object= [];
                          makers.forEach(function (maker, k) {                
                            console.log('ffffffffffffffff',maker._id)
                            object.push(ObjectId(maker._id))
                            
                          });

                              Transaction.aggregate([ 
                                 {
                                  $match: {
                                            'due_to_user': { $in: object },
                                              $or:[
                                                    {transaction_reason: "Referral Commission"},
                                                    {transaction_reason: "payout given to refferal user"},
                                              ]
                                          }
                                      
                                },
                                {
                                   $group: {
                                        _id: {
                                          
                                        },
                                        myCount: { $sum: 1 } ,
                                      }
                              },

                                ])
                            .then((product)=>
                            // {
                              // console.log('ppppppppppp',product),
                              // object.push(product.data)
                              Responder.success(res,product)


                            // }
                              )
                            .catch((err)=>Responder.operationFailed(res,err))

                          // Responder.success(res,object)

                          // console.log('object',object); 
                        }
                        // Responder.success(res,trc)
                        )
                      .catch((err)=>Responder.operationFailed(res,err))
      }
  }

  


  static updateUserTransaction(req,res){
     Transaction.update({ due_to_user: req.body._id }, { $set: {due_to_user_name: req.body.name}},{multi:true})
        .then((data) =>{
          Transaction.update({ user_id: req.body._id }, { $set: {referred_by_name: req.body.name }},{multi:true})
          .then((data) =>{
            Responder.success(res,"success")
          })
        })
  }

  static gatewayFilterTransactions(req, res) {

    if(!req.body.currentPage ) {
      req.body.currentPage = 0;
    }
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)


    if(!req.body.page_limit ) {
      req.body.page_limit = 20;
    }
    var size = req.body.page_limit;
    console.log('size--'+size)


    let match = {};
    if(req.body.startDate && req.body.endDate) {
      match = { "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  }
    }

    console.log({match})
    GatewayTransaction.aggregate([ 
        {
          $match: match
              
        },            
          { $lookup:
            {
              from: 'users',
              localField: 'user_id',
              foreignField: '_id',
              as: 'userDetails'
            }
          },{ 
          "$sort": {  
              "transaction_date": -1,
          } 
        }
      ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>{
      console.log(" trc",trc)
      Responder.success(res,trc)
    })
    .catch((err)=>{
      Responder.operationFailed(res,err)
    })  
  }catch(err) {
    console.log(err);
    Responder.operationFailed(res,err)
  }  



static gatewayFilterTransactionDetail(req, res) {

    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)


    let match = {};
    if(req.body.startDate && req.body.endDate) {
      match = { "transaction_date": { "$gte": new Date(req.body.startDate+'T00:00:00Z') , "$lte": new Date(req.body.endDate+'T23:59:59Z') }  
    }


  if (req.body.due_to_user_name != null || req.body.phone_number != null) {
    console.log('1111111111111111--')
    User.aggregate([ 
                 {
                  $match: {
                            $or:[
                              {name: {$regex : "^" + req.body.due_to_user_name,$options: 'i'}},
                              {phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'}}
                            ]
                            
                          }
                      
                }

                      ])
      
      .then((makers)=>{
          var object= [];
          makers.forEach(function (maker, k) {                
            console.log('ffffffffffffffff',maker._id)
            object.push(ObjectId(maker._id))
            
          });

              GatewayTransaction.aggregate([ 
                 {
                  $match: {
                            'user_id': { $in: object }
                            
                          }
                      
                },              
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": {  
                    "transaction_date": -1,
                } 
              }

                ]).skip(size * (pageNo - 1)).limit(size)
            .then((product)=>
            // {
              // console.log('ppppppppppp',product),
              // object.push(product.data)
              Responder.success(res,product)


            // }
              )
            .catch((err)=>Responder.operationFailed(res,err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
        // Responder.success(res,trc)
        )
      .catch((err)=>Responder.operationFailed(res,err))
    }

    else{

      GatewayTransaction.aggregate([ 
        {
              $match: {
                  $or:[
                    {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},
                    {state: {$regex : "^" + req.body.transaction_reason,$options: 'i'}},
                    {amount:  parseInt(req.body.amount)},
                    // {transaction_id: {$regex : "^" + req.body.transaction_id,$options: 'i'}},

                  // {transaction_id:req.body.transaction_id},
                  ]
                }
              
        },             
          { $lookup:
            {
              from: 'users',
              localField: 'user_id',
              foreignField: '_id',
              as: 'userDetails'
            }
          },{ 
          "$sort": {  
              "transaction_date": -1,
          } 
        }
     ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>{
      console.log(" trc",trc)
      Responder.success(res,trc)
    })
    .catch((err)=>Responder.operationFailed(res,err))
    }
  
}
}




}
