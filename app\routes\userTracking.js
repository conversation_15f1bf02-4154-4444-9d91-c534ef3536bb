import express from "express";

import  UserTracking from "../models/userTracking";
import  UserModel from "../models/user";
import { findLast<PERSON>ey } from "lodash";

var passport = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

// Store/Update FCM Token
const initUserTrackingRoutes = () => {
  const userTrackingRoutes = express.Router();

  userTrackingRoutes.post("/token", passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler,  async (req, res) => {
  const { fcm_token, device_id } = req.body;
  try {
    const user = await UserModel.findById(req.user._id);
    let tracking = await UserTracking.findOne({
      phone_number: user.phone_number,
    });
    if (tracking) {
      if (tracking.is_uninstalled) {
        // User reinstalled the app tracking.reinstall_date = new Date();
        tracking.activity_history.push({ type: "reinstall" });
      }
      tracking.fcm_token = fcm_token;
      tracking.device_id = device_id;
      tracking.app_active_status = true;
      tracking.is_uninstalled = false;
      tracking.last_activity = new Date();
    } else {
      tracking = new UserTracking({
        user_id: user._id,
        phone_number: user.phone_number,
        fcm_token,
        device_id,
        activity_history: [{ type: "install" }],
      });
    }
    await tracking.save();
    res.status(200).json({ message: "Token updated successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
    });

    // Track Activity
    userTrackingRoutes.post("/activity", passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, async (req, res) => {
    try {
        const tracking = await UserTracking.findOne({ user_id: req.user._id });
        if (tracking) {
        tracking.last_activity = new Date();
        tracking.app_active_status = true;
        tracking.is_uninstalled = false;
        // tracking.activity_history.push({ type: "activity" });
        await tracking.save();
        }
        res.status(200).json({ message: "Activity logged" });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
    });

    // Track Uninstall
    userTrackingRoutes.post("/uninstall", passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler,  async (req, res) => {
    const { fcm_token } = req.body;
    try {
        const tracking = await UserTracking.findOne({ fcm_token });
        if (tracking) {
        tracking.is_uninstalled = true;
        tracking.app_active_status = false;
        tracking.activity_history.push({ type: "uninstall" });
        await tracking.save();
        }
        res.status(200).json({ message: "Uninstall tracked" });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
    });

    // // Verify Session
    userTrackingRoutes.post("/verify-session", passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler,  async (req, res) => {
    try {
        const { fcm_token } = req.body;
        try {
          const tracking = await UserTracking.findOne({ fcm_token });
          if (!tracking) {
            return res.status(404).json({ error: 'Tracking data not found' });
          }
          const status = await verificationHelpers.updateTrackingStatus(tracking);
          res.status(200).json(status);
        } catch (error) {
          res.status(500).json({ error: error.message });
        }
      
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
    });

    // Get Tracking Metrics
    userTrackingRoutes.get("/metrics", async (req, res) => {
    const {
        start_date,
        end_date,
        phone_number,
        status_type,
        page = 1,
        limit = 50,
    } = req.query;
    try {
        const query = {};
        if (start_date && end_date) {
        query.last_activity = {
            $gte: new Date(start_date),
            $lte: new Date(end_date),
        };
        }
        if (phone_number) query.phone_number = phone_number;
        if (status_type === "uninstalled") query.is_uninstalled = true;
        if (status_type === "inactive") {
        query.app_active_status = false;
        query.is_uninstalled = false;
        }
        const skip = (page - 1) * parseInt(limit);
        const [users, totalCount] = await Promise.all([
        UserTracking.find(query)
            .populate("user_id", "name phone_number email state district")
            .skip(skip)
            .limit(parseInt(limit))
            .sort({ last_activity: -1 }),
        UserTracking.countDocuments(query),
        ]);

        // Calculate statistics
        //
        const stats = {
        total_users: await UserTracking.countDocuments(),
        active_users: await UserTracking.countDocuments({
            app_active_status: true,
            is_uninstalled: false,
        }),
        inactive_users: await UserTracking.countDocuments({
            app_active_status: false,
            is_uninstalled: false,
        }),
        uninstalled_users: await UserTracking.countDocuments({
            is_uninstalled: true,
        }),
        reinstalled_users: await UserTracking.countDocuments({
            reinstall_date: { $exists: true },
        }),
        };
        res
        .status(200)
        .json({
            users,
            stats,
            pagination: {
            current_page: parseInt(page),
            total_pages: Math.ceil(totalCount / parseInt(limit)),
            total_records: totalCount,
            },
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
    });

    return userTrackingRoutes;
}

export default initUserTrackingRoutes;
