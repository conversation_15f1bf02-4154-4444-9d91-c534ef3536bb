angular.module('withdrawWallet.controllers', [])

    .controller('withdrawWalletCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.page = 'main';
		$scope.withdrawWalletGetDataForApprove;
    $scope.withdrawWalletGetDataForRemmittes;
    $scope.withdrawWalletGetDataForPaidToBank;
    $scope.WaitingForApproval=true;
    $scope.waitingForRemmittess=false;
    $scope.PayToBanks=false;
    $scope.adminRemarks;
    $scope.updatedata ={};
    $scope.pageLimit;
    $scope.walletLength;



    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
    };


  
 


     $scope.exportAsExcel = function () {
        console.log('**********')
          var blob = new Blob([document.getElementById('exportable').innerHTML], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
          });
          saveAs(blob, new Date().toISOString().slice(0, 10)+"withdraw.xls");
          
      };

        

    $scope.addUser = function(value){
      if (!angular.isArray($scope.checkboxList)){
        $scope.checkboxList = [];
      }
      if (-1 === $scope.checkboxList.indexOf(value)){
        $scope.checkboxList.push(value);
        console.log($scope.checkboxList)
      }
    }
    $scope.remove = function(value){
      if (!angular.isArray($scope.checkboxList)) {
        return;
      }
      var index = $scope.checkboxList.indexOf(value);
      if (-1 !== index) {
        $scope.checkboxList.splice(index, 1);
        console.log($scope.checkboxList)

      }
    }
      




        


    $scope.approve = function(id,walletData) {
      console.log('approve date',new Date())
      if (confirm("sure to approve")) {
         APIService.getData({
              req_url: PrefixUrl + '/user/'+walletData.user_id

          }).then(function(user) {
            console.log('uuuuuuuuuuu  '+user.data[0].wallet_balance+'   '+walletData.amount)
            console.log(user)
              if (user.data[0].wallet_balance >= walletData.amount) {

                APIService.updateData({
                    req_url: PrefixUrl + '/withdrawWallet/update/'+ id,data:{status:'waiting for remmittes',approvalDate:true}
                }).then(function(resp) {
                  // console.log("====resp======",resp);
                  alert('updated successfully')
                  // $state.go('app.withdrawWallet');
                  location.reload(); 
                  
                  console.log('reeeeeeeeee'+resp.suspend)
                   },function(resp) {
                      // This block execute in case of error.
                }); 
                 }else{
                alert('You need atleast RS 500 in your wallet for withdrawl request')
              }
           

             },function(resp) {
          }); 
      }else{
        
      }
      
    }; 

    
    $scope.approveSelected = function(id) {
      
      if (confirm("sure to approve")) {

      console.log($scope.checkboxList)
      $scope.checkboxList.forEach(function (obj, k) { 
        console.log('id'+obj._id)
        $scope.approveMultiple(obj._id);
      });
      alert('updated successfully')
      location.reload(); 
      }else{
        
      }


    }; 


    $scope.selectAll = function(selectAll) {
      console.log(selectAll)
      $scope.checkboxList= [];
      if (selectAll) {
        $scope.withdrawWalletGetDataForApprove.forEach(function (obj, k) { 
        obj.checked = true;
        $scope.checkboxList.push(obj);
        console.log(obj);
        console.log($scope.checkboxList);
        });
      }else{
        $scope.withdrawWalletGetDataForApprove.forEach(function (obj, k) { 
        obj.checked = false;
        // $scope.checkboxList.push(obj);
        console.log(obj);
        // console.log($scope.checkboxList);

        });
      }

      
      // $scope.withdrawWalletGetDataForApprove
    }; 


    $scope.approveMultiple = function(id) {
      // if (confirm("sure to approve")) {
        APIService.updateData({
            req_url: PrefixUrl + '/withdrawWallet/update/'+ id,data:{status:'waiting for remmittes',approvalDate: true}
        }).then(function(resp) {
          
          console.log('reeeeeeeeee'+resp.suspend)
           },function(resp) {
              // This block execute in case of error.
        });  
      // }else{
        // 
      // }
      
    }; 


    // if ($scope.PayToBanks) {

    $scope.checkPagination = function(id) {

      // if ($scope.PayToBanks) {
         $scope.$watch('settings.pageLimit', function (pageLimit) {
            console.log('pageLimits'+pageLimit)
            $scope.pageLimit= pageLimit;
              if ($scope.PayToBanks) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForPaidToBank',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForPaidToBank= resp.data;
                  console.log('withdrawWalletGetDataForPaidToBank')
                  console.log($scope.withdrawWalletGetDataForPaidToBank)

                   },function(resp) {
                   
                });


              }

              if ($scope.waitingForRemmittess) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForRemmittes',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForRemmittes= resp.data;
                $scope.user_id= resp.data.user_id;
                 

                   },function(resp) {
                });
              }

              if ($scope.WaitingForApproval) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForApprove',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForApprove= resp.data;
                $scope.walletLength= resp.data.length;
                
                 

                   },function(resp) {

                      
                });

                 var userData=localStorage.getItem('UserDeatails');
                var parsedUser= JSON.parse(userData);
                // console.log(parsedUser.user_details)
                if (parsedUser == null || parsedUser.user_details.role != 'admin') {
                  localStorage.removeItem("UserDeatails");
                  localStorage.removeItem("token");
                  $state.go('login');
                }
              }
          }
          );


          $scope.$watch('settings.currentPage', function (value) {
            console.log('currentPage'+$scope.settings.currentPage)  
            // console.log('userDetailslll='+$scope.userDetails.length)
              if ($scope.PayToBanks) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForPaidToBank',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForPaidToBank= resp.data;
                  console.log('withdrawWalletGetDataForPaidToBank')
                  console.log($scope.withdrawWalletGetDataForPaidToBank)

                   },function(resp) {
                });
              }

              if ($scope.waitingForRemmittess) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForRemmittes',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForRemmittes= resp.data;
                  

                   },function(resp) {
                });
              }


              if ($scope.WaitingForApproval) {
                APIService.setData({
                    req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForApprove',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                $scope.withdrawWalletGetDataForApprove= resp.data;
                $scope.walletLength= resp.data.length;
                 

                   },function(resp) {
                });
              }
          }
          );
        // }

        // if ($scope.waitingForRemmittess == true) {
        //     $scope.$watch('settings.pageLimit', function (pageLimit) {
        //     console.log('pageLimits'+pageLimit)
        //     $scope.pageLimit= pageLimit;
        //       if ($scope.PayToBanks) {
        //         APIService.setData({
        //             req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForRemmittes',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
        //         }).then(function(resp) {
        //         $scope.withdrawWalletGetDataForRemmittes= resp.data;
                 

        //            },function(resp) {
        //         });
        //       }
        //   }
        //   );


        //   $scope.$watch('settings.currentPage', function (value) {
        //     console.log('currentPage'+$scope.settings.currentPage)  
        //     // console.log('userDetailslll='+$scope.userDetails.length)
        //       if ($scope.PayToBanks) {
        //         APIService.setData({
        //             req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForRemmittes',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
        //         }).then(function(resp) {
        //         $scope.withdrawWalletGetDataForRemmittes= resp.data;
                  

        //            },function(resp) {
        //         });
        //       }
        //   }
        //   );
        // }
      }
      
      $scope.checkPagination();


    // }



    $scope.payMoneyToBank = function(id,walletData) {
      console.log('walletData.wallet_balance   '+walletData  )
      console.log(walletData  )
         APIService.getData({
              req_url: PrefixUrl + '/user/'+walletData.user_id

          }).then(function(user) {
            console.log('uuuuuuuuuuu  '+user.data[0].wallet_balance+'   '+walletData.amount)
            console.log(user)
              if (user.data[0].wallet_balance >= walletData.amount) {

                  var obj=[];
                  obj.push({'id':id});
                  obj.push({'walletData':walletData});
                  
                  $state.go("app.payToBankRemarks",{data:JSON.stringify(obj)})
                  
              }else{
                alert('you need atleast rs 500 in your wallet to withdraw amount to your bank account')
              }
           

             },function(resp) {
          });
      

      // console.log('remarks')
      // console.log($scope.adminRemarks)
      //   APIService.updateData({
      //       req_url: PrefixUrl + '/withdrawWallet/update/'+ id,data:{status:'paid to bank',remarks:$scope.adminRemarks,date:new Date()}
      //   }).then(function(resp) {
      //     $scope.updatedata.user_id=walletData.user_id;
      //     $scope.updatedata.transaction_date=new Date();
      //     $scope.updatedata.transaction_id=Date.parse(new Date().toISOString());
      //     $scope.updatedata.transaction_reason=walletData.reason;
      //     $scope.updatedata.transaction_type=walletData.transaction_type;
      //     $scope.updatedata.amount=walletData.amount;
      //     $scope.updatedata.transaction_reason=$scope.adminRemarks;

      //         APIService.setData({

      //             req_url: PrefixUrl + '/trancsaction/',data:$scope.updatedata
      //         }).then(function(resp) {
      //             //update user wallet
      //               APIService.updateData({

      //                   req_url: PrefixUrl + '/user/update/'+walletData.user_id,data:{wallet_balance:walletData.userDetails.wallet_balance - walletData.amount}
      //               }).then(function(resp) {
      //                 // alert('money transfered to the bank')
      //                 // location.reload(); 
                      
      //                  },function(resp) {
      //               });
      //           alert('money transfered to the bank')
      //           location.reload(); 
                
      //            },function(resp) {
      //         });
      //     // $state.go('app.withdrawWallet');

          
      //      },function(resp) {
      //   });
    }; 


    $scope.waitingForApprovals=function(){

        $scope.WaitingForApproval=true;
        $scope.waitingForRemmittess=false;
        $scope.PayToBanks=false;

          APIService.getData({
              req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForApproveLength'
          }).then(function(resp) {
          $scope.walletLength= resp.data.length;
            // console.log('responsewithdrawWalletGetData')
            // console.log($scope.withdrawWalletGetDataForApprove)

             },function(resp) {
                // This block execute in case of error.
               
          });

    }

    $scope.waitingForRemmittes=function(){

        $scope.WaitingForApproval=false;
        $scope.waitingForRemmittess=true;
        $scope.PayToBanks=false;


        APIService.getData({
            req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForRemmittesLength'
        }).then(function(resp) {
        // $scope.withdrawWalletGetDataForRemmittes= resp.data;
        $scope.walletLength= resp.data.length;
        $scope.checkPagination();
          // console.log('responsewithdrawWalletGetData')
          // console.log($scope.withdrawWalletGetDataForRemmittes)

           },function(resp) {
        });


    }

    $scope.payToBank=function(){

        $scope.WaitingForApproval=false;
        $scope.waitingForRemmittess=false;
        $scope.PayToBanks=true;

        APIService.getData({
            req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForPaidToBankLength'
        }).then(function(resp) {
        $scope.walletLength= resp.data.length;
        $scope.checkPagination();

          // console.log('withdrawWalletGetDataForPaidToBank')
          // console.log($scope.withdrawWalletGetDataForPaidToBank)

           },function(resp) {
        });
     

    }



    $scope.filterPaidToBankCount = function(){    
      APIService.setData({
        req_url: PrefixUrl + '/withdrawWallet/filterPaidToBankCount',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, phone_number:$scope.phone_number }
      }).then(function(resp) {
        console.log("====resp======",resp);
        $scope.walletLength = resp.data[0].myCount;
      });
    }


    $scope.filterPaidToBank = function(phone_number) {

      // $scope.filterSearch =true;

      console.log('filterPaidToBank  ',phone_number)
        if (phone_number) {
            $scope.phone_number= phone_number;
        }else{
            $scope.phone_number= null;
        }

        APIService.setData({
            req_url: PrefixUrl + '/withdrawWallet/filterPaidToBank' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, phone_number:$scope.phone_number } 
        }).then(function(resp) {
          $scope.withdrawWalletGetDataForPaidToBank= resp.data;
          $scope.filterPaidToBankCount();
        },function(resp) {
         
        });
    };


    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

    };


})
