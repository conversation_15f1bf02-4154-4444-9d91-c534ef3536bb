angular.module('broadcastMessage.controllers', [])

    .controller('broadcastMessageCtrl', function ($scope,APIService, $state,$stateParams) {


      $scope.getBroadcastMessages= function(){
        APIService.setData({
            req_url: PrefixUrl + '/userMessage/getBroadcastMessages/'
        }).then(function(resp) {
            $scope.broadcastMessages= resp.data;  
            $scope.message= '';  
           },function(resp) {
              // This block execute in case of error.
        });
      }

      $scope.getBroadcastMessages();

      $scope.broadcastMessage= function(){
        var messageObj={};
        messageObj.myId= 'SWARI'
        messageObj.message= $scope.message;
        messageObj.receiver= 'ALL USERS';
        messageObj.sender= 'SWARI';
        messageObj.senderId= '5cdd012309e48a143bf6f152';
        messageObj.recieverId= '5cdd012309e48a143bf6f152';


          APIService.setData({
              req_url: PrefixUrl + '/userMessage/broadcastMessage/',data:messageObj
          }).then(function(resp) {
              $scope.ticket= resp.data; 
                  // send push notification start
                   APIService.setData({
                      req_url: PrefixUrl + '/user/getAllUsersOnce'
                  }).then(function(resp) {
                  // $scope.userDetails=resp.data
                  var obj= {};
                  var fcm_token_obj= [];
                  var phone_number_obj= [];
                  var email_obj= [];
                  resp.data.forEach(function (user, k) {                
                      fcm_token_obj.push(user.fcm_registration_token);
                      phone_number_obj.push(user.phone_number);
                      email_obj.push(user.email);
                     
                                     
                  });

                  console.log('objeeee')
                  console.log(fcm_token_obj)
                  console.log(email_obj)
                  console.log(phone_number_obj)


                      var obj= {};
                      obj.id_list =fcm_token_obj;
                      obj.title =$scope.message;
                        APIService.setData({
                            req_url: PrefixUrl + '/user/sendPushNotification' , data: obj 
                        }).then(function(resp121) {
                        
                          if (confirm("message sent?")) {
                            $scope.getBroadcastMessages();
                          }  
                             

                        console.log('resp')
                        console.log(resp)
                           },function(resp) {
                              // This block execute in case of error.
                        });


             

                     },function(resp) {
                        // This block execute in case of error.
                  });
                  // send push notification end
              

             },function(resp) {
                // This block execute in case of error.
          });

        }

})

