angular.module('otp.controllers', [])

.controller('otpCtrl', function($scope, $state,APIService,$stateParams,$timeout) {   
  $scope.page = 'main';
  $scope.userDetails;
  $scope.isValid= false;
  $scope.userdata ={};
  $scope.count= 60;
  $scope.counter = 60;


    // if($stateParams.data){
      console.log('$stateParams')
      console.log(JSON.parse( $stateParams.data))
      $scope.jsonData= {};
      $scope.jsonData= JSON.parse( $stateParams.data);
      $scope.jsonData.otp= $scope.userdata.otp;

    $scope.verifyOtp = function() {
      $scope.jsonData.otp= $scope.userdata.otp;
      console.log('ooooooooo')
      console.log($scope.jsonData)

      APIService.setData({
            // req_url: PrefixUrl + '/user/checkotp/',data:$scope.jsonData
            req_url: PrefixUrl + '/user/checkotpforgot/',data:{mobile:$scope.jsonData.mobile.phone_number,otp:$scope.userdata.otp}

        }).then(function(resp) {
             console.log("====resp======",resp);  

              if (resp.data._id) {
                $state.go("resetPassword", {data:JSON.stringify(resp.data)});
              }
              
                 
               },function(err) {
                console.log("====err======",err);  

                alert(err.data.reason);
                  // This block execute in case of error.
                  // localStorage.setItem('UserDeatails', JSON.stringify(resp.data));
                  // $state.go("app.UserDetails");
          });
    };  


    

    $scope.resendOtp = function() {
        var location={};
        location.coordinates= [74.8736788,31.6343083];
        $scope.userdata.location= location;

         APIService.setData({ req_url: PrefixUrl + "/user/otp",data:$scope.jsonData.mobile})
          .then(function (res4) {
              if (res4.data.success) {
                  console.log("otp"+res4.data.message)

                  // $scope.jsonData.otp= res4.data.message;
                  // $scope.jsonData.mobile= $scope.userdata;
                  // $state.go("otp",{data:JSON.stringify($scope.jsonData)});
                  alert("OTP sent Successfully.")

                  
              }
          
          },function(er){

          })
    };  



      $scope.checkOtp= function(otp){
        console.log('aaa',(''+otp).length)
        if ((''+otp).length <= 0) {
          $scope.isValid= false;
        }else{
          $scope.isValid= true;

        }
      }

      $scope.countdown = function() {
        stopped = $timeout(function() {
           console.log($scope.counter);
         if ($scope.counter > 0) {
            $scope.counter--;   
          }else{
          }
         $scope.countdown();   
        }, 1000);
      };

      $scope.countdown();

      // setInterval(function(){
      //   console.log('count');
      //   $scope.count= $scope.count-1;
      // }, 1000)

    //   $scope.countDown= function(){

    //   var count= 60;
    //   this.sub = Observable.interval(1000).subscribe(x => {
    //     this.count= this.count-1;
    //     console.log(this.count)
    //     if (this.count == 0) {
    //       this.sub.unsubscribe();
    //       this.disableSendOtp= false;

    //     }
    //   });
    // }

   //   APIService.setData({ req_url: PrefixUrl + "/user/checkotp",data:JSON.parse( $stateParams.data)})
   //      .then(function (res4) {
   //          if (res4.data) {
   //          console.log('rrrrrrrrrr')
   //          console.log(res4.d)
   //            },function(er){
             
      // }
      // });
    // }
    //  $scope.filter={};
    //  $scope.usersDetails = {};
    //  $scope.userDetails = {};
    // $scope.userDetails = JSON.parse(localStorage.getItem("UserDeatails"));
    // $scope.userDetails.referal_code =  $scope.userDetails.referal_code;
    // console.log(  $scope.userDetails.wallet_balance)
  

    // $scope.getAllUsers = function() {
    //     APIService.getData({
    //         req_url: PrefixUrl + '/user/referuser/'+ $scope.userDetails.referal_code
    //     }).then(function(resp) {
    //       console.log("====resp======",resp);
    //     $scope.usersDetails=resp.data
    //        },function(resp) {
    //           // This block execute in case of error.
    //     });
    // };  
    
    // $scope.getAllUsers();


})