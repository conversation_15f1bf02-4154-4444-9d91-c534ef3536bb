angular.module('tripDetailsPassenger.controllers', [])

    .controller('tripDetailsPassengerCtrl', function ($scope, APIService ,$state, $stateParams) {
        $scope.page = 'main';
        $scope.cancelTrip=false;
        $scope.upcomingTrip=true;
        $scope.pendingTrip=false;
        $scope.tripHistory=false;
        $scope.boostedTrip=false;
        $scope.bookingRequest=false;
        $scope.urlJson;
        $scope.upcomingTripsData;
        $scope.pendingTripsData;
        $scope.cancelTripsData;
        $scope.boostedTripsData;
        $scope.bookingRequestsData;
        $scope.tripHistoryData;
        $scope.userId;
        $scope.TripsLength;
        
        
        $scope.settings = {
          currentPage: 0,
          offset: 0,
          pageLimit: 10,
          pageLimits: [2, 5, 10,20,100]
        };


        

        
        $scope.tripDetailsCtrl={};
        console.log('JSON.parse($stateParams.data)')
        console.log(JSON.parse($stateParams.data))
        $scope.urlJson= JSON.parse($stateParams.data);
        console.log($scope.urlJson.reg_no)
        if($scope.urlJson.origin){
            $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
            $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
            $scope.vehicleid =$scope.tripDetailsCtrl.vehical_id;
            console.log($scope.vehicleidw)
        }

        if($scope.urlJson.reg_no){
            $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
            $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
            // $scope.vehicleid =$scope.tripDetailsCtrl._id;
            if ($scope.urlJson.vehical_id) {
                $scope.vehicleid =$scope.urlJson.vehical_id;
            }else{
                $scope.vehicleid =$scope.urlJson._id;
            }

            console.log($scope.vehicleidw)
        }


         if($scope.urlJson.phone_number){
            $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
            // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
            $scope.userId =$scope.tripDetailsCtrl._id;
            console.log('$scope.userId'+$scope.userId)
            APIService.setData({ req_url: PrefixUrl + "/trip/tripsHistoryByUserIdPassenger",data:{userId:$scope.userId}}).then(function (res) {
                console.log(res)
    
                $scope.vehiclesDetails=res.data;

            },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login')
             
            })

            var userData=localStorage.getItem('UserDeatails');
            var parsedUser= JSON.parse(userData);
            // console.log(parsedUser.user_details)
            if (parsedUser == null || parsedUser.user_details.role != 'admin') {
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
            }
        }


        $scope.getProductDetails = function(){
            APIService.getData({ req_url: PrefixUrl + "/trip/tripbyvehid/"+$scope.vehicleid}).then(function (res) {
                console.log(res)
    
                $scope.vehiclesDetails=res.data;

                $scope.tripDetails.forEach(function (trp, k) {                
                    if (trp.accepted_user_id && trp.status =='ACCEPTED') {
                        // $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
                        trp.trp_time= Date.parse(trp.time);
                    }
                });


                $scope.upcomingTripsCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    if (trp.accepted_user_id && trp.status =='ACCEPTED') {
                        $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
                    }
                });

                $scope.pendingTripsCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    if (trp.status =='ACTIVE') {
                        $scope.pendingTripsCount = $scope.pendingTripsCount + 1;                    
                    }
                });

                $scope.cancelTripCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    if (trp.status == 'CANCELLED') {
                        $scope.cancelTripCount = $scope.cancelTripCount + 1;                    
                    }
                });

                $scope.boostedTripCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    if (trp.is_trip_boost == true) {
                        $scope.boostedTripCount = $scope.boostedTripCount + 1;                    
                    }
                });

                $scope.tripHistoryCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    // if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
                        $scope.tripHistoryCount = $scope.tripHistoryCount + 1;                    
                    // }
                });

                $scope.bookingRequestCount=0;
                $scope.vehiclesDetails.forEach(function (trp, k) {                
                    if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
                        $scope.bookingRequestCount = $scope.bookingRequestCount + 1;                    
                    }
                });

                angular.forEach( $scope.vehiclesDetails, function(value, key) {
                    
                    value.buisness_name= $scope.tripDetailsCtrl.userDetails.name;
                    console.log('bn--'+value.buisness_name);
                  });

            
            },function(er){
                localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');

            })

            var userData=localStorage.getItem('UserDeatails');
            var parsedUser= JSON.parse(userData);
            // console.log(parsedUser.user_details)
            if (parsedUser == null || parsedUser.user_details.role != 'admin') {
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
            }
    
        }
         $scope.getProductDetails();

        // $scope.removeProduct = function(productId){
        //     APIService.removeData({ req_url: PrefixUrl + "/product/"+productId}).then(function (res) {
        //         console.log(res)
        //         alert("Product is deleted.");
        //         $scope.getProductDetails();
               
            
        //     },function(er){
             
        //     })
    
        // }

        $scope.tripsHistory=function(){

            $scope.tripHistory=true;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.cancelTrip=false;
            $scope.boostedTrip=false;
            $scope.bookingRequest=false;

           

            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                APIService.setData({ req_url: PrefixUrl + "/trip/tripsHistoryByUserIdPassenger",data:{userId:$scope.userId}}).then(function (res) {
                    console.log(res)
        
                    // $scope.tripHistoryData=res.data;
                    $scope.TripsLength=res.data.length;


                },function(er){
                 
                })



            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/tripsHistoryByUserIdPassenger' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.tripHistoryData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/tripsHistoryByUserIdPassenger' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

            }
            else{
                APIService.setData({ req_url: PrefixUrl + "/trip/tripHistoryForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    // $scope.tripHistoryData= res.data;
                    $scope.TripsLength=res.data.length;
                    
                },function(er){

                })


            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/tripHistoryForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.tripHistoryData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/tripHistoryForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

            }

        }
        
        $scope.pendingTrips=function(){

            $scope.tripHistory=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=true;
            $scope.boostedTrip=false;
            $scope.cancelTrip=false;
            $scope.bookingRequest=false;

            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                // APIService.setData({ req_url: PrefixUrl + "/trip/pendingTripsByUserId",data:{userId:$scope.userId}}).then(function (res) {
                APIService.getData({ req_url: PrefixUrl + "/trip/showPassenger/"+$scope.userId}).then(function (res) {
                    console.log(res)
        
                    $scope.pendingTripsData=res.data;

                },function(er){
                 
                })
            }
            else{

                APIService.setData({ req_url: PrefixUrl + "/trip/pendingTripsForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    $scope.pendingTripsData= res.data;
                },function(er){

                })
            }

        }

        $scope.upcomingTrips=function(){

            $scope.tripHistory=false;
            $scope.upcomingTrip=true;
            $scope.pendingTrip=false;
            $scope.cancelTrip=false;
            $scope.boostedTrip=false;
            $scope.bookingRequest=false;

            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTripsByUserIdPassenger",data:{userId:$scope.userId}}).then(function (res) {
                    console.log(res)
        
                    $scope.TripsLength=res.data.length;

                },function(er){
                 
                })


                $scope.$watch('settings.pageLimit', function (pageLimit) {
                  console.log('pageLimits'+pageLimit)
                  $scope.pageLimit= pageLimit;
                    APIService.setData({
                        req_url: PrefixUrl + '/trip/upcomingTripsByUserIdPassenger' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                    }).then(function(resp) {
                      console.log("====respPagination======",resp);
                      $scope.upcomingTripsData=resp.data
                       },function(resp) {
                          // This block execute in case of error.
                    });
                }
                );


                // $scope.$watch('settings.currentPage', function (value) {
                //   console.log('currentPage'+$scope.settings.currentPage)  
                //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
                //     APIService.setData({
                //           req_url: PrefixUrl + '/trip/upcomingTrips' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                //       }).then(function(resp) {
                //         console.log("====respPagination======",resp);
                //         $scope.upcomingTripsData=resp.data
                //          },function(resp) {
                //             // This block execute in case of error.
                //       });
                // }
                // );
            }
            else{

                APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTripsForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    // $scope.upcomingTripsData= res.data;
                    $scope.TripsLength=res.data.length;
                    
                },function(er){

                })

                $scope.$watch('settings.pageLimit', function (pageLimit) {
                  console.log('pageLimits'+pageLimit)
                  $scope.pageLimit= pageLimit;
                    APIService.setData({
                        req_url: PrefixUrl + '/trip/upcomingTripsForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                    }).then(function(resp) {
                      console.log("====respPagination======",resp);
                      $scope.upcomingTripsData=resp.data
                       },function(resp) {
                          // This block execute in case of error.
                    });
                }
                );


                $scope.$watch('settings.currentPage', function (value) {
                  console.log('currentPage'+$scope.settings.currentPage)  
                  console.log('userDetailslll='+$scope.upcomingTripsData.length)
                    APIService.setData({
                          req_url: PrefixUrl + '/trip/upcomingTripsForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                      }).then(function(resp) {
                        console.log("====respPagination======",resp);
                        $scope.upcomingTripsData=resp.data
                         },function(resp) {
                            // This block execute in case of error.
                      });
                }
                );
            }

        }

        $scope.cancelTrips=function(){

            $scope.tripHistory=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.cancelTrip=true;
            $scope.boostedTrip=false;
            $scope.bookingRequest=false;

            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                APIService.getData({ req_url: PrefixUrl + "/trip/cancelRequestsForUser/"+$scope.userId}).then(function (res) {
                    console.log(res)
        
                    $scope.cancelTripsData=res.data;

                },function(er){
                 
                })
            }
            else{

                APIService.setData({ req_url: PrefixUrl + "/trip/cancelTripsForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    $scope.cancelTripsData= res.data;
                },function(er){

                })
            }

        }


        $scope.boostedTrips=function(){

            $scope.tripHistory=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.cancelTrip=false;
            $scope.boostedTrip=true;
            $scope.bookingRequest=false;



            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                APIService.setData({ req_url: PrefixUrl + "/trip/boostedTripsForUser/",data:{userId:$scope.userId}}).then(function (res) {
                    console.log(res)
        
                    // $scope.boostedTripsData=res.data;
                    $scope.TripsLength=res.data.length;


                },function(er){
                 
                })

            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/boostedTripsForUser' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.boostedTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/boostedTripsForUser' ,data:{userId:$scope.userId,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.boostedTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );
            }
            else{

                APIService.setData({ req_url: PrefixUrl + "/trip/boostedTripsForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    // $scope.boostedTripsData= res.data;
                    $scope.TripsLength=res.data.length;

                },function(er){

                })

            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/boostedTripsForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.boostedTripsData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/boostedTripsForVehicle' ,data:{vehicleid:$scope.vehicleid,currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.boostedTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

            }
            
        }

          $scope.bookingRequests=function(){

            $scope.tripHistory=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.cancelTrip=false;
            $scope.boostedTrip=false;
            $scope.bookingRequest=true;


            if($scope.urlJson.phone_number){
                $scope.tripDetailsCtrl = JSON.parse($stateParams.data)
                // $scope.vehicleidw =$scope.tripDetailsCtrl.buisness_name;
                $scope.userId =$scope.tripDetailsCtrl._id;
                console.log('$scope.userId'+$scope.userId)
                APIService.getData({ req_url: PrefixUrl + "/trip/bookRequsetPassengerBackend/"+$scope.userId}).then(function (res) {
                    console.log(res)
        
                    $scope.bookingRequestsData=res.data;

                },function(er){
                 
                })
            }
            else{

                APIService.setData({ req_url: PrefixUrl + "/trip/bookingRequestsForVehicle/" ,data:{vehicleid:$scope.vehicleid}})
                .then(function (res) {
                    console.log(res)
                    $scope.bookingRequestsData= res.data;
                },function(er){

                })
            }
            
        }



            $scope.filterTrips = function(startDate,endDate) {


        if (startDate) {
            var startDate= startDate;
        }else{
            var startDate= null;
        }

        if (endDate) {
            var endDate= endDate;
        }else{
            var endDate= null;
        }

        if ($scope.origin) {
            var origin= $scope.origin;
        }else{
            var origin= null;
        }

        if ($scope.destination) {
            var destination= $scope.destination;
        }else{
            var destination= null;
        }

        if ($scope.buisness_name) {
            var buisness_name= $scope.buisness_name;
        }else{
            var buisness_name= null;
        }

        if ($scope.phone_number) {
            var phone_number= $scope.phone_number;
        }else{
            var phone_number= null;
        }


        if ($scope.upcomingTrip) {
          console.log('upcomingTrip')

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsUpcoming' ,data:{ origin:$scope.origin, destination:$scope.destination} 
          }).then(function(resp) {
            $scope.upcomingTripsData= resp.data;
          },function(resp) {
           
          });

        }else if ($scope.tripHistory) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsDetailsHistory' ,data:{ vehical_id:$scope.vehicleid,startDate:startDate,endDate:endDate, origin:origin, destination:destination, buisness_name:buisness_name, phone_number:phone_number} 
          }).then(function(resp) {
            $scope.tripHistoryData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.pendingTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterPendingTrip' ,data:{ origin:$scope.origin, destination:$scope.destination} 
          }).then(function(resp) {
            $scope.pendingTripsData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.cancelTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterCancelTrip' ,data:{ origin:$scope.origin, destination:$scope.destination} 
          }).then(function(resp) {
            $scope.cancelTripsData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.boostedTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBoostedTrip' ,data:{ origin:$scope.origin, destination:$scope.destination} 
          }).then(function(resp) {
            $scope.boostedTripsData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.bookingRequest) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBookingRequest' ,data:{ origin:$scope.origin, destination:$scope.destination} 
          }).then(function(resp) {
            $scope.bookingRequestsData= resp.data;
          },function(resp) {
           
          });
        }


      }


    $scope.upcomingTrips();

    $scope.findUserBusinessTrips = function(user) {
     
      console.log(user)
      $state.go('app.tripDetailsPassenger',{data:JSON.stringify(user)});

    };

  $scope.passengerTrips = function(){
            //$state.go("main.products",{})
            $state.go('app.passengerTrips',{},{reload:true});
             setTimeout(function () {
             location.reload()
              }, 100);
             
          }

    
})