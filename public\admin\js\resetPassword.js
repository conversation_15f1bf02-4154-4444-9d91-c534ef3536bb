angular.module('resetPassword.controllers', [])

.controller('resetPasswordCtrl', function($scope, $state,APIService,$stateParams) {   
	$scope.page = 'main';
  $scope.password;
  $scope.confirmPassword;
	$scope.jsonData={};
	// $scope.jsonData.new_password= $scope.password;
	// $scope.jsonData.password= $scope.confirmPassword;
	$scope.jsonData.role= 'subAdmin';


    var jsonUserData= JSON.parse( $stateParams.data);
    console.log('ddddddddddddd')
    console.log(jsonUserData)
	$scope.jsonData.userDetails= jsonUserData;

   
    $scope.resetPassword = function() {
        if ($scope.jsonData.password == $scope.jsonData.confirmPassword) {
              if ($scope.jsonData.password.length < 6) {
                alert('Password must not be less then 6 digits')
              }else{            
              		  APIService.setData({
                          req_url: PrefixUrl + '/user/resetPassword/',data:$scope.jsonData
                      }).then(function(resp) {
                        console.log("====resp======",resp);
                         		$scope.userDetails=resp.data;
                              $scope.userdata ={};
                              alert("Password reset Successfully.")
                              // localStorage.setItem('UserDeatails', JSON.stringify(resp.data));
                              $state.go("login");
              			// $state.go("app.UserDetails",{data:JSON.stringify($scope.jsonData)});

                      // $scope.usersDetails=resp.data
                         },function(resp) {
                            // This block execute in case of error.
                      });
              }
            }else{
              alert('Password mismatch')
            }
    }


})