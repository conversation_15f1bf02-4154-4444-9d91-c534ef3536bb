angular.module('reports.controllers', [])

    .controller('reportsCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';
     $scope.filter={};
     $scope.userDetails = [];
    $scope.completed_orders = [];
    $scope.rejected_orders = [];
    $scope.orderList = [];
    $scope.newRecord=true;
    $scope.processedRecord=false;

 

    $scope.getAllUsers = function() {
        APIService.getData({
            req_url: PrefixUrl + '/report'
        }).then(function(resp) {
          console.log("====resp======",resp);
        $scope.userDetails=resp.data
           },function(resp) {
              // This block execute in case of error.
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        });

        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }
    }; 


    $scope.pageProcessed = function() {
        APIService.getData({
            req_url: PrefixUrl + '/report/pageProcessed'
        }).then(function(resp) {
          console.log("====resp======",resp);
        $scope.userDetails=resp.data
           },function(resp) {
              // This block execute in case of error.
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        });

        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }
    };  
    

    $scope.findUserBusiness = function(user) {

      console.log(user)

      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

    };



    $scope.filterReports = function(user) {


        if ($scope.name) {
            var name= $scope.name;
        }else{
            var name= null;
        }

        if ($scope.phone_number) {
            var phone_number= $scope.phone_number;
        }else{
            var phone_number= null;
        }

        if ($scope.buisness_name) {
            var buisness_name= $scope.buisness_name;
        }else{
            var buisness_name= null;
        }


        APIService.setData({
            req_url: PrefixUrl + '/report/filterReports' ,data:{ name:name,phone_number:phone_number,buisness_name:buisness_name,district:$scope.district } 
        }).then(function(resp) {
          $scope.userDetails= resp.data;
        },function(resp) {
         
        });
    };


    $scope.newRecords=function(){

            $scope.newRecord=true;
            $scope.processedRecord=false;
            
            $scope.getAllUsers();
            
            
        }


        $scope.processedRecords=function(){

            $scope.newRecord=false;
            $scope.processedRecord=true;
            $scope.pageProcessed();
            
             
            
        }


      $scope.verify= function(obj_id,processedComment){
          console.log('sssssssssssss',processedComment)
          
          if (confirm("Are you sure?")) {
            APIService.updateData({
                req_url: PrefixUrl + '/report/update/'+ obj_id,data:{processedComment:processedComment}
            }).then(function(resp) {
              // console.log("====resp======",resp);
              alert('verified successfully')
              location.reload();

              console.log('reeeeeeeeee'+resp.suspend)
               },function(resp) {
                  // This block execute in case of error.
            });
          }
      }


      // $scope.deleteComment = function(report_id){

      //       if (confirm("Are you sure?")) {
      //          APIService.removeData({ req_url: PrefixUrl + "/report/removeReport/"+report_id }).then(function (res) {
      //             alert('removed successfully')
      //             // location.reload(); 

      //           },function(er){

      //           })
      //        }

      // }



      $scope.deleteComment = function(report_id){

            if (confirm("Are you sure?")) {
               APIService.updateData({ req_url: PrefixUrl + "/report/hideReport/"+report_id }).then(function (res) {
                  alert('Hide successfully')
                  location.reload(); 

                },function(er){

                })
             }

      }

    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }

      $scope.newRecords();


})

