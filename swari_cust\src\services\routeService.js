/**
 * Route Service
 * Handles route calculation using external routing API
 */

import logger from '../utils/logger.js';

class RouteService {
  constructor() {
    this.routeApiUrl = 'http://95.217.103.228:8002/route';
  }

  /**
   * Calculate route distance between pickup and dropoff coordinates
   * @param {Array} pickupCoords - [longitude, latitude]
   * @param {Array} dropoffCoords - [longitude, latitude]
   * @returns {Promise<number|null>} Distance in kilometers or null if failed
   */
  async calculateDistance(pickupCoords, dropoffCoords) {
    try {
      const requestBody = {
        locations: [
          { lat: pickupCoords[1], lon: pickupCoords[0] }, // Convert [lon, lat] to {lat, lon}
          { lat: dropoffCoords[1], lon: dropoffCoords[0] }
        ],
        costing: 'auto',
        directions_options: {
          units: 'kilometers'
        }
      };

      const response = await fetch(this.routeApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        logger.warn('Route API request failed', {
          status: response.status,
          statusText: response.statusText
        });
        return null;
      }

      const data = await response.json();
      
      // Extract distance from trip.summary.length
      if (data.trip && data.trip.summary && typeof data.trip.summary.length === 'number') {
        logger.info('Route distance calculated successfully', {
          distance: parseFloat(data.trip.summary.length)
        });
        return parseFloat(data.trip.summary.length.toFixed(2));
      }

      logger.warn('Invalid route API response structure', { response: data });
      return null;

    } catch (error) {
      logger.error('Error calculating route distance', {
        error: error.message,
        stack: error.stack,
        pickupCoords,
        dropoffCoords
      });
      return null;
    }
  }
}

export default new RouteService();