angular.module('userBusinessProfile.controllers', [])

    .controller('userBusinessProfileCtrl', function ($scope,APIService, $state,$stateParams) {
        
        $scope.page = 'main';
    	 $scope.businessProfile;
        $scope.businessProfileTab=false;
        $scope.transactionTab=false;
        $scope.referalUserTab=false;
        $scope.documentTab=false;

        $scope.user_id;
        $scope.userTransactions;
	    $scope.transAmountTotal=0;
		  $scope.expiresIn;
        var startDate;
        var endDate;

    	// if($stateParams.data){
     //     console.log('ddddddddddddddd')
     //     console.log(JSON.parse($stateParams.data));
     //     $scope.businessProfile= JSON.parse($stateParams.data);
     //    }


     




	    if($stateParams.data){
	     console.log('ddddddddddddddd111')
	     console.log(JSON.parse($stateParams.data))
	     $scope.user_id= JSON.parse($stateParams.data);
	     // console.log(JSON.parse($stateParams.data));
	       APIService.getData({
	            req_url: PrefixUrl + '/user/'+$scope.user_id
	        }).then(function(resp) {
	        $scope.businessProfile= resp.data;
	         	// var oneDay = 24*60*60*1000; // hours*minutes*seconds*milliseconds
			    // var firstDate = new Date($scope.businessProfile[0].renewal_date);
			    // var secondDate = new Date();

          var a = moment.utc($scope.businessProfile[0].renewal_date,"YYYY-MM-DD");
          var b = moment.utc().format("YYYY-MM-DDT00:00:00.000Z");
          var dateShow = moment.duration(a.diff(b)).asDays();
          console.log('dateShow?? ',a)
          console.log('dateShowbb?? ',b)
          $scope.expiresIn = dateShow;
			    // $scope.expiresIn = Math.round((firstDate.getTime() - secondDate.getTime())/(oneDay));
			    console.log('dateeeeeeeeeeee-+')
			    console.log($scope.expiresIn)

	           },function(resp) {
	              // This block execute in case of error.
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
	        });

            var userData=localStorage.getItem('UserDeatails');
            var parsedUser= JSON.parse(userData);
            // console.log(parsedUser.user_details)
            if (parsedUser == null || parsedUser.user_details.role != 'admin') {
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
            }
	     // $scope.businessProfile= JSON.parse($stateParams.data);
	    }



	    $scope.transactionsTab=function(){
            $scope.transactionTab=true;
            $scope.businessProfileTab=false;
            $scope.referalUserTab=false;
            $scope.documentTab=false;
	    	    $scope.transAmountTotal=0;



            APIService.getData({
	            req_url: PrefixUrl + '/trancsaction/'+$scope.user_id
	        }).then(function(resp) {
	        	var crTotal= 0;
	        	var drTotal= 0;

	        resp.data.forEach(function (trans, k) { 
	        	if (trans.transaction_type == "CR") {
	        		 crTotal= trans.amount + crTotal;

	        	}else{
	        		 drTotal= trans.amount + drTotal;

	        	}
	        	// $scope.transAmountTotal= trans.amount + $scope.transAmountTotal;
	        	$scope.transAmountTotal= crTotal - drTotal;
	        });
	        console.log('transAmountTotal'+$scope.transAmountTotal)
	        // console.log(resp)
	        // resp.data.push({'transAmountTotal':transAmountTotal});
	        $scope.userTransactions= resp.data;
	        console.log(resp.data)


	           },function(resp) {
	              // This block execute in case of error.
	        });
            

        }

	    $scope.businessProfilesTab=function(){

            $scope.transactionTab=false;
            $scope.businessProfileTab=true;
            $scope.referalUserTab=false;
            $scope.documentTab=false;          
        }


        $scope.referalUsersTab=function(){

          console.log("referalUsersTab    ================= toggle");
            $scope.transactionTab=false;
            $scope.businessProfileTab=false;
            $scope.referalUserTab=true;
            $scope.documentTab=false;
            
            $scope.getAllReferalUsers();            

        }

        $scope.documentTabFun = function(){
          console.log("documentTabFun    ================= toggle");
            $scope.getUserProfile();            
            $scope.transactionTab=false;
            $scope.businessProfileTab=false;
            $scope.referalUserTab=false;
            $scope.documentTab=true;
            

        }


    $scope.getAllReferalUsers = function() {      
        APIService.getData({
            req_url: PrefixUrl + '/user/referuser/'+ $scope.businessProfile[0].referal_code
        }).then(function(resp) {
          console.log("====resp======",resp);
            $scope.usersDetails=resp.data
           },function(resp) {
              // This block execute in case of error.
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
        });
    }; 

    $scope.getUserProfile = function() {
      console.log(" =========== getUserProfile ");
      APIService.getData({
        req_url: PrefixUrl + '/user/'+$scope.user_id
      }).then(function(resp) {
        console.log("====documentTabFun documentTabFun======",resp.data);          
          if(resp.data.length) {
            $scope.usersData=resp.data[0]
            $scope.docs=resp.data[0]
          }
         },function(resp) {
            // This block execute in case of error.
            localStorage.removeItem("UserDeatails");
            localStorage.removeItem("token");
            $state.go('login');
      });
  }; 

    $scope.suspendUser = function(id) {
    	var obj={};
    	obj.id=id;
    	obj.suspend_remarks=true;
      	// obj.push({'id':id});
      
      $state.go("app.userSuspendRemarks",{data:JSON.stringify(obj)})

        // APIService.updateData({
        //     req_url: PrefixUrl + '/user/update/'+ id,data:{suspend:true}
        // }).then(function(resp) {
        //   // console.log("====resp======",resp);
        //   alert('Account suspended successfully')
        //   $state.go('app.UserDetails');

        //   console.log('reeeeeeeeee'+resp.suspend)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });
    }; 

    $scope.activateUser = function(id) {

    	var obj={};
    	obj.id=id;
    	obj.activate_remarks=true;
      	// obj.push({'id':id});
      
      	$state.go("app.userSuspendRemarks",{data:JSON.stringify(obj)})
        // APIService.updateData({
        //     req_url: PrefixUrl + '/user/update/'+ id,data:{suspend:false}
        // }).then(function(resp) {
        //   // console.log("====resp======",resp);
        //   alert('Account Activated successfully')
        //   $state.go('app.UserDetails');

          
        //   console.log('reeeeeeeeee'+resp.suspend)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });
    }; 


    $scope.$watch('startDate', function (value) {
      try {
       startDate = new Date(value).toISOString().slice(0, 10);
       console.log('startDate'+startDate);

      } catch(e) {}
   
      if (!startDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



    $scope.$watch('endDate', function (value) {
      try {
       endDate = new Date(value).toISOString().slice(0, 10);
       console.log('enddate'+endDate);

      } catch(e) {}
   
      if (!endDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });


    $scope.filterTransaction = function() {

       APIService.setData({
          req_url: PrefixUrl + '/trancsaction/filterUserTransactionsForDate' ,data:{startDate:startDate,endDate:endDate,user_id:$scope.user_id } 
      }).then(function(resp) {
        $scope.userTransactions= resp.data;
      },function(resp) {
       
      });
    }


    $scope.forCarDetails=function(user){
        console.log(user);
        $state.go('app.vehicleDetails',{data:JSON.stringify(user )});
    }
 $scope.user = function()

          {
            //$state.go("main.products",{})
            $state.go('app.UserDetails',{},{reload:true});
             setTimeout(function () {
                location.reload()
            }, 100);

            
          }
    $scope.findUserBusiness = function(user) {
            
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    };
})
