import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const OfferCategorySchema = new Schema({
    category_name: String,
    // created_at:{ type: Date, default: Date.now },
});
OfferCategorySchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('OfferCategory', OfferCategorySchema);
