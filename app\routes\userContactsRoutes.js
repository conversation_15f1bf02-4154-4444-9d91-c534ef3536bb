import express from 'express';
import UserContactsController from '../controllers/userContactsController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const inituserContactsRoutes = () => {
  const userContactsRoutes = express.Router();


  userContactsRoutes.post('/create',  UserContactsController.create);
  userContactsRoutes.get('/show',  UserContactsController.show);
  userContactsRoutes.post('/showForUser/',  UserContactsController.showAllUserContacts);
  userContactsRoutes.post('/showAll',  UserContactsController.showAllUserContactsAll);
  userContactsRoutes.post('/userContactsCount',  UserContactsController.userContactsCount);
  userContactsRoutes.post('/filterUserContacts',  UserContactsController.filterUserContacts);
  userContactsRoutes.post('/showAllFiltered',  UserContactsController.showAllFiltered);
  userContactsRoutes.post('/filterUserContactsAll',  UserContactsController.filterUserContactsAll);
  userContactsRoutes.post('/filterUserContactsAllCount',  UserContactsController.filterUserContactsAllCount);
  userContactsRoutes.post('/showForUserCount',  UserContactsController.showForUserCount);
  userContactsRoutes.post('/filterUserContactsCount',  UserContactsController.filterUserContactsCount);

  // rateReviewRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.show);
  // rateReviewRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.page);
  // rateReviewRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.create);
  // rateReviewRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.update);
  // rateReviewRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.remove);
  // rateReviewRoutes.post('/reviewBy',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.reviewBy);
  // rateReviewRoutes.delete('/removeReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.removeReview);
  // rateReviewRoutes.post('/filterReview',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.filterReview);
  // rateReviewRoutes.post('/getByPostMethod',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.getByPostMethod);
  // rateReviewRoutes.post('/reviewCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.reviewCount);


  return userContactsRoutes;
};

export default inituserContactsRoutes;
