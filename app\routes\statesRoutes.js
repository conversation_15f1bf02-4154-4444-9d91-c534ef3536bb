import express from 'express';
import StatesController from '../controllers/statesController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initstatesRoutes = () => {
  const statesRoutes = express.Router();

  statesRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  StatesController.create);
  statesRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  StatesController.update);
  statesRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  StatesController.remove);
  statesRoutes.get('/getStates',  StatesController.getStates);
 
 return statesRoutes;

};

export default initstatesRoutes;
