/**
 * Dashboard Routes
 * Defines routes for dashboard data endpoints
 * PRD Reference: Section 11 - Dashboard Overview
 */

import express from 'express';
const router = express.Router();
import dashboardController from '../controllers/dashboardController.js';
import { verifyToken } from '../../../auth/authMiddleware.js';

/**
 * @route GET /api/admin/dashboard/overview
 * @desc Get dashboard overview data with metrics and module summaries
 * @access Private (Admin only)
 */
router.get('/overview', verifyToken , dashboardController.getDashboardOverview);

/**
 * @route GET /api/admin/dashboard/alerts
 * @desc Get system alerts and anomalies
 * @access Private (Admin only)
 */
router.get('/alerts', verifyToken , dashboardController.getSystemAlerts);

/**
 * @route GET /api/admin/dashboard/modules/:module
 * @desc Get detailed data for a specific module
 * @access Private (Admin only)
 */
router.get('/modules/:module', verifyToken , dashboardController.getModuleData);

export default router;