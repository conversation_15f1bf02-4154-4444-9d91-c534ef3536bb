import config from 'config';
import * as express from './express';
import * as mongoose from './mongoose';
import logger from './logger';
var cors = require('cors');
var Authorization = require('../app/middleware/authorization');

//require cron here
require('../cron')

var passport = require('passport');

const start = () => {
  const port = config.get('port');

  const appStartMessage = () => {
    const env = process.env.NODE_ENV;
    logger.debug(`API is Initialized`);
    logger.info(`App Name : ${config.app.title}`);
    logger.info(`Server Name : ${config.app.name}`);
    logger.info(`Environment  : ${env || 'development'}`);
    logger.info(`App Port : ${port}`);
    logger.info(`Process Id : ${process.pid}`);
  };

    const app = express.init();
  // Use the passport package in our application
  app.use(cors()) // Use this after the variable declaration

 // app.options('*', cors());
// app.use(function (req, res, next) {
//     res.header("Access-Control-Allow-Origin", "http://localhost:4200");  //* will allow from all cross domain
//     res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept,Authorization");
//     res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
//     next();
// });


// // Add headers
app.use(function (req, res, next) {
    res.header("Access-Control-Allow-Origin", "*");
    res.header('X-Frame-Options', 'ALLOWALL');

    // Website you wish to allow to connect
    // res.setHeader('Access-Control-Allow-Origin', 'http://localhost:64000');
    res.setHeader('Access-Control-Allow-Origin', 'https://panel.triva.in');
    // res.setHeader('Access-Control-Allow-Origin', 'http://panel.triva.in');


    // Request methods you wish to allow
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE');

    // Request headers you wish to allow
    res.header('Access-Control-Allow-Headers', 'X-Requested-With,content-type,Authorization,authorization');
    // res.setHeader('Access-Control-Allow-Headers', '*');

    // Set to true if you need the website to include cookies in the requests sent
    // to the API (e.g. in case you use sessions)
    res.header('Access-Control-Allow-Credentials', true);
    // res.header('preflightContinue', false);

    


    // Pass to next layer of middleware
    next();
});
app.use(passport.initialize());
var passportMiddleware = require('../app/middleware/passport');
passport.use(passportMiddleware);

  
  mongoose.connect(() => {
    app.listen(port, appStartMessage);
  });

};

export default start;
