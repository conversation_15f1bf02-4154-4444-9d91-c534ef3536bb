angular.module('categoryAdd.controllers', [])

    .controller('categoryAddCtrl', function ($scope, APIService, $state,$rootScope) {

        $scope.updatedata ={};
        $scope.showUpdate= false;
        $scope.adminData =JSON.parse(localStorage.getItem("UserDeatails"));

      


        $scope.Update = function(){
          

            APIService.updateData({ req_url: PrefixUrl + "/vehicleModel/update/"+$scope.updatedata._id,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                if (res.data == true) {
                    alert("Model already exist")
                }else{     
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= false;
                    alert("Data is Updated Successfully.")
                    location.reload();
                }
            
            },function(er){
             
            })


            // APIService.updateData({ req_url: PrefixUrl + "/vehicleCatMakModRoutes/update/"+$scope.updatedata._id,data:$scope.updatedata}).then(function (res) {
            //     console.log(res)
    
            //     $scope.expireProduct=res.data;
            //     $scope.updatedata ={};
            //     $scope.showUpdate= false;
            //     alert("Data is Updated Successfully.")
            
            // },function(er){
             
            // })
    
        }


         $scope.UpdateRemove = function(object){
            if (confirm("Are you sure?")) {
              console.log('$scope.updatedata');
              console.log(object);

                APIService.updateData({ req_url: PrefixUrl + "/vehicleModel/remove/"+object._id,data:$scope.updatedata}).then(function (res) {
                    console.log(res)
        
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= false;
                    alert("Data is Removed Successfully.")
                    location.reload(); 

                
                },function(er){
                 
                })
            }
        }

        $scope.addCat = function(){
            const nameCapitalized = $scope.updatedata.model.charAt(0).toUpperCase() + $scope.updatedata.model.slice(1);
            $scope.updatedata.model= nameCapitalized;

            APIService.setData({ req_url: PrefixUrl + "/vehicleModel/create" ,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                if (res.data == true) {
                    alert("Model already exist")
                }else{                    
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    alert("Data is Add Successfully.")
                    location.reload(); 
                }


            
            },function(er){
             
            })




            // APIService.setData({ req_url: PrefixUrl + "/vehicleCatMakModRoutes" ,data:$scope.updatedata}).then(function (res) {
            //     console.log(res)
    
            //     $scope.expireProduct=res.data;
            //     $scope.updatedata ={};
            //     alert("Data is Add Successfully.")
            
            // },function(er){
             
            // })
    
        }

        $scope.getCat = function(){
            APIService.getData({ req_url: PrefixUrl + "/vehicleCatMakModRoutes"}).then(function (res) {
                console.log(res)
    
                $scope.getALLCat=res.data;
             
            
            },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
            })


            
        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }
    
        }

        
        $scope.getVehicleMakers = function(){
            APIService.setData({ req_url: PrefixUrl + "/vehicleModel/getVehicleMakers"}).then(function (res) {
                console.log(res);
                $scope.vehicleMaker= res.data;
                // $scope.getALLCat=res.data;
             
            
            },function(er){
               
            })
    
        }




        $scope.getVehicleModels = function(){
            APIService.setData({ req_url: PrefixUrl + "/vehicleModel/getVehicleModels"}).then(function (res) {
                console.log(res);
                $scope.vehicleModels= res.data;
                // $scope.getALLCat=res.data;
             
            
            },function(er){
               
            })
    
        }



        $scope.getVehicleType = function(){
            APIService.setData({ req_url: PrefixUrl + "/vehicleModel/getVehicleType"}).then(function (res) {
                console.log(res);
                $scope.vehicleType= res.data;
                // $scope.getALLCat=res.data;
             
            
            },function(er){
               
            })
    
        }


        $scope.getCat();

        $scope.getVehicleMakers();
        $scope.getVehicleModels();
        $scope.getVehicleType();


        $scope.UpdateShow = function(user){
            $scope.updatedata =user;
            $scope.showUpdate= true;
        }
      //  $scope.updateForFees();
   
})