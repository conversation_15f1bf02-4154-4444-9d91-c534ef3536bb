import express from 'express';
import morgan from 'morgan';
import bodyParser from 'body-parser';
import methodOverride from 'method-override';
import logger from './logger';
import initRoutes from './../app/routes';
import Responder from './expressResponder';
import Socket from 'socket.io';
import UserService from './../app/service/userService';
import MessageService from './../app/service/messageService';
import User from './../app/models/user';
import fs from 'fs';

// Initialize express app
const app = express();
const io = new Socket(3002);

function initMiddleware() {
  // Showing stack errors
  app.set('showStackError', true);

  // Enable jsonp
  app.enable('jsonp callback');

  // Enable logger (morgan)
  app.use(morgan('combined', { stream: logger.stream }));

  app.use('/',express.static(__dirname+ '/../public'));
    console.log(__dirname+ '/public');
  // Environment dependent middleware
  if (process.env.NODE_ENV === 'development') {
    // Disable views cache
    app.set('view cache', false);
  } else if (process.env.NODE_ENV === 'production') {
    app.locals.cache = 'memory';
  }

  // Request body parsing middleware should be above methodOverride
  app.use(bodyParser.urlencoded({
    extended: true,
  }));



  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(methodOverride());

 /* app.use('/',(req,res, next)=>{

    let bo = {
      "url" : req.originalUrl,
      "method" : req.method,
      "payload" : req.body,
    }
      fs.writeFile('api.txt',JSON.stringify(bo), (e) => { 

    });
    next();
  })*/
}

function initErrorRoutes() {
  app.use((err, req, res, next) => {
    // If the error object doesn't exists
    if (!err) {
      next();
    }

    // Return error
    return Responder.operationFailed(res, err);
  });
}

function socketIoInitialize(){
  io.on('connection', socket => {
    UserService.updateUser(socket.handshake.query.userId,{socketId:socket.id,online:true}).then(res=>{},err=>{});
    socket.on('joinGroup', function(group){
      socket.join(group.group_id);
     
    });
    socket.on("groupMessage",message=>{
      MessageService.addGroupMessage(message).then((messag)=>{})
      socket.to(message.group_id).emit(message.groupId,message.message);
     
    })

    
    socket.on('disconnect',() => {
      UserService.updateLastSeen(socket.handshake.query.userId).then(res=>{});
    });

    socket.on('chat', messageObject => {
      console.log("chhhhhhhhhhhh",messageObject);
      const { message, senderId, recieverId } = messageObject;
      let reciever;
      User.findOne({ _id: recieverId })
        .then(user => {
          reciever = user;
          return User.findOne({ _id: senderId })
        })
        .then(sender => {
          console.log('rrrrrr',reciever.chat.socketId);
          if(reciever.chat && reciever.chat.socketId){
            console.log('11111',reciever.chat.socketId);
            socket.to(reciever.chat.socketId).emit('chat', messageObject);
          }
           
           // io.sockets.socket(reciever.chat.socketId).emit('chat',messageObject);
        })
        .then(() => MessageService.add(message, senderId, recieverId))
        
    });

    socket.on("leaveGroup",group=>{
      socket.leave(group.group_id);
    })
    
  });
}

export function init() {

  // Initialize Express middleware
  initMiddleware();

  // Initialize modules server routes
  initRoutes(app);

  // Initialize error routes
  initErrorRoutes();

  socketIoInitialize();

  return app;
}
