import Responder from '../../lib/expressResponder';
import VehicleMaker from '../models/vehicleMaker';
import VehicleModel from '../models/vehicleModel';
import VehicleType from '../models/vehicleType';
import Trip from '../models/trip';

import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class VehicleModelController {


    static create(req, res) {
      console.log('model');
      VehicleModel.count({model:req.body.model})
     .then((val)=>{
        if (val > 0) {
          Responder.success(res,true)
        }else{
            VehicleModel.create(req.body)
            .then((trip)=>Responder.success(res,trip))
            .catch((err)=>Responder.operationFailed(res,err))   
        }

     })
       
    }


  static update(req, res) {

    VehicleModel.count({model:req.body.model})
     .then((val)=>{
        if (val > 0) {
          Responder.success(res,true)
        }else{
            VehicleModel.findOneAndUpdate({_id:req.params.id},{$set:req.body})
             .then((val)=>Responder.success(res,val))
             .catch((err)=>Responder.operationFailed(res,err))
           }
      })
  }

  static remove(req, res) {
    VehicleModel.remove({_id:req.params.id})
    .then((product)=>Responder.success(res,product))
    .catch((err)=>Responder.operationFailed(res,err))
  }


    static show(req, res) {
      console.log('model');
    }

    static getVehicleModels(req, res) {
    // ghjkhj
        VehicleModel.aggregate([ 
              {
                    $match: {
                     
                      }
                    
              },
              { $lookup:
                {
                  from: 'vehiclemakers',
                  localField: 'maker_id',
                  foreignField: '_id',
                  as: 'vehicle'
                }
                },

                { $lookup:
                {
                  from: 'vehicletypes',
                  localField: 'type_id',
                  foreignField: '_id',
                  as: 'vehicleType'
                }
                },
                {$sort: {"vehicle.maker": 1,
                          "model": 1
                  }},
                // {$unwind: '$vehicle[0].maker'}, 

    
                    ])
    .then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
    .catch((err)=>Responder.operationFailed(res,err))
    
      }


      
      
      static filterVehicleModels(req, res) {
        console.log('arrrrr')
        console.log(req.body)
            VehicleModel.aggregate([ 
              {
                    $match: {
                        $and:[
                          {type_id:ObjectId(req.body.type_id)},
                          {maker_id:ObjectId(req.body.maker_id)}
                          ]
                      }
                    
              },
              {"$sort": {"model": 1}},

    
              ])
        .then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
        .catch((err)=>Responder.operationFailed(res,err))
    
      }
/* 
    static getVehicleMakers(req, res) {
        VehicleMaker.aggregate([ 
              {
                $match: {
                  
                  }                    
              },
              {"$sort": {"maker": 1}},    
             ])
            .then((vehModels)=>{ 
		          
              let temp = [];
              let topMakers = [
                "Toyota",                
                "Tata",
                "Maruti",
                "Mahindra",
                "Hyundai",
                "Honda",
                "Force",
                "Ford"
              ];

              let filteredMakers = vehModels.filter((v)=> {                
                if(topMakers.indexOf(v.maker)<0) {
                  return true;
                } else {
                  temp.push({
                    maker : v.maker,
                    _id : v._id,
                  })
                }                
              });

              temp.sort();

              let respData = temp.concat(filteredMakers);
	        console.log(respData)
              Responder.success(res,respData);
	}).catch((err)=>Responder.operationFailed(res,err))
    
      }
 */
      static getVehicleMakers(req, res) {
        VehicleMaker.aggregate([
          {
            $match: {},
          },
        //  { $sort: { maker: 1 } },
        {"$sort": {"maker": 1}}, 
        ])
          .then((vehModels) => {
            let temp = [];
            let topMakersOrder = [
              "Toyota",
              "Tata",
              "Maruti",
              "Mahindra",
              "Force",
              "Hyundai",
              "Honda",
              "Ford",
            ];
      
            let filteredMakers = vehModels.filter((v) => {
              if (topMakersOrder.includes(v.maker)) {
                temp.push({
                  maker: v.maker,
                  _id: v._id,
                });
                return false;
              }
              return true;
            });
      
            temp.sort((a, b) => {
              return topMakersOrder.indexOf(a.maker) - topMakersOrder.indexOf(b.maker);
            });
      
            let respData = temp.concat(filteredMakers);
            console.log(respData);
            Responder.success(res, respData);
          })
          .catch((err) => Responder.operationFailed(res, err));
      }
      







    static getVehicleType(req, res) {
        VehicleType.aggregate([ 
              {
                    $match: {
                     
                      }
                    
              },
              {"$sort": {"serial_number": 1}},

    
                    ])
    .then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
    .catch((err)=>Responder.operationFailed(res,err))
    
      }

      

    static getSelectedModel(req, res) {
        VehicleModel.aggregate([ 
              {
                    $match: {
                       _id:ObjectId(req.params.id)
                      }
                    
              }
    
                    ])
    .then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
    .catch((err)=>Responder.operationFailed(res,err))
    
      }
}
