const async = require('async');
const { PutObjectCommand, GetObjectCommand, GetObjectAclCommand,S3Client } = require('@aws-sdk/client-s3');
const fs = require('fs');
const baseSpaces = "https://swaritest.sgp1.digitaloceanspaces.com";
// Step 2: The s3Client function validates your request and directs it to your Space's specified endpoint using the AWS SDK.
const s3Client = new S3Client({
    endpoint: "https://sgp1.digitaloceanspaces.com", // Find your endpoint in the control panel, under Settings. Prepend "https://".
    forcePathStyle: false,
    region: "sgp1", // Must be "us-east-1" when creating new Spaces. Otherwise, use the region in your endpoint (e.g. nyc3).
    credentials: {
      accessKeyId: "DO00DDZBQ27VM24JGC8W", // Access key pair. You can create access key pairs using the control panel or API.
      secretAccessKey: "zF2lMVNnNkZYL9M6pfdcyqZPe3Hw5wSgzMc3yCFx96E" // Secret access key defined through an environment variable.
    }
});

function generateQRCode() {
 

    let req = {
        body: {
            referal_code: "Gurpreet singh",
            phone_number: "999999",
            name: "TEGTCODE",
        }
    }

    async.waterfall([
      function(callback){
        console.log('done ---------111')

            const fs = require('fs');
            const qrcode = require('qrcode');

            run().catch(error => console.error(error.stack));

            async function run() {
                const qrCodeShare = await qrcode.toDataURL('https://app.swari.in/register?refferal_code='+req.body.referal_code);
                var base64Data = qrCodeShare.replace(/^data:image\/png;base64,/, "");
                callback(null,base64Data,qrCodeShare);
              }
      },
      function(base64Data,qrCodeShare,callback) {
        console.log('done ---------22')

        require("fs").writeFileSync('../../referImage/'+req.body.phone_number+'qrCode.png', base64Data, 'base64');
        // , function(err) {
        //   console.log('err 11 refer_image',err);
        // });

          console.log(" qr created ");
          callback(null,true,qrCodeShare);      
      },
      function(success,qrCodeShare,callback){
        // console.log('done ---------333')

             /* AWS.config.update({
                  accessKeyId: "********************",
                secretAccessKey: "4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t",
                });

              var s3 = new AWS.S3();

              console.log('s3  -- ',s3)
              // console.log('refer_image-- 1222',refer_image);

              */

              //configuring parameters
              var uploadParams = {
                Bucket: 'swaritest',
                ACL: "public-read",
                Body : fs.createReadStream('../../referImage/'+req.body.phone_number+'qrCode.png'),
                Key : "qrs/"+req.body.phone_number+'qrCode.png'
              };

              var params = {
                Bucket: 'swaritest',
                // Body : fs.createReadStream(file.path),
                Key : "qrs/"+req.body.phone_number+'qrCode.png'
              };
             

              s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {				                 
              // console.log('Bucket locaction -- ',s3.getPublicUrl('triva-in', "folder/file-1574407048917.jpg", ["https://triva-in.s3.amazonaws.com/"]));
              // s3.upload(uploadParams, function (err, data) {
                //handle error
                // if (err) {
                //   console.log("Error", err);
                // }

                //success
                if (data) {
                  console.log("Uploaded in:", data);
                  // require("fs").unlinkSync('referImage/'+req.body.phone_number+'qrCode.png');                    
                  //User.update({phone_number:req.body.phone_number},{$set:{qrCode: baseSpaces+"/"+params.Key}})
                         //.then((val)=>{
                          //console.log('valueeeeeee ',val)
			callback(null,true,qrCodeShare);

                        //});
                  }
                }).catch((err)=>{
                  console.log(" err ", err);
                  callback(null,true,qrCodeShare);
                });
      },
      function(success,qrCodeShare,callback){
        console.log('done ---------444',qrCodeShare)

            const nodeHtmlToImage = require('node-html-to-image')
            nodeHtmlToImage({
              output: '../../referImage/'+req.body.phone_number+'shareImageHindi.png',
              html : `<!DOCTYPE html>
<html lang="hi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>स्वारी ऐप निमंत्रण</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&display=swap" rel="stylesheet">
  <style type="text/css" >
    .headers{
        background:linear-gradient(to bottom, rgba(12, 251, 251, 0.62) 0%, #1ad4d4 33%, #108a8a 100%);
        color:white;
        padding: 5px;
        position: relative;
        display: block;
        border: 1px solid #e5e5e5;

      }
   .pic{
        border-radius: 50% !important;
      padding: 3px;
      background: white;
  }
  </style>
</head>
<body style="font-family: 'Noto Sans Devanagari', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f9f9f9; color: #333;">
  <table border="5" cellpadding="2" cellspacing="2" align="center" style="border-color:#1c9aab; border-collapse: collapse;">
    <tr>
      <td>
   	 <div style="max-width: 1080px; margin: 0 auto; background-color: #ffffff; border: 1px solid #0073aa; padding: 20px; border-radius: 5px; box-shadow: 0;">
 		 <div style="text-align: center;">
   		<img src="https://panel.swari.in/img/swari-qrlogo.png" height='100' width='400' alt="स्वारी लोगो">       
 		 </div>
 		 <div style="text-align: center; margin-top: 20px;">
   		 <h2 style="color: #0073aa;">{{name}} आपसे भारत का पहला टैक्सी और ट्रैवल एजेंसी कनेक्टिंग ऐप इंस्टॉल करने का अनुरोध कर रहा है</h2>
   		 <p>कृपया नीचे दिए गए <strong>QR कोड</strong> को स्कैन करके SWARI एप्लिकेशन डाउनलोड करें या पंजीकरण के दौरान रेफरल कोड <strong>{{referalCode}}</strong> का उपयोग करें।</p>
 		 </div>
 		 <div style="text-align: center; margin: 20px 0;">
   		 <img src="{{qr_code}}" alt="क्यूआर कोड" style="width: 150px; height: 150px;">
 		 </div>
 		 <div style="margin-top: 20px;">
   		 <h2 style="color: #0073aa; border-bottom: 2px solid #0073aa; padding-bottom: 5px; margin-bottom: 10px;">ऐप की विशेषताएँ</h2>
   		 <ul style="list-style-type: none; padding-left: 0;">
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">कई टैक्सियों को जोड़ें:</span>
       		 </strong> एक खाते के तहत कई टैक्सियों का निर्बाध रूप से प्रबंधन करें।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">यात्राओं पर कोई कमीशन नहीं:</span>
       		 </strong> टैक्सी व्यवसाय किराए पर कोई कमीशन नहीं देते हैं।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">रूट चयन:</span>
       		 </strong> BLABLA CAR जैसे रूट चुनें और रास्ते में यात्रियों को लेने के लिए कई स्टॉप जोड़ें।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">प्रत्यक्ष व्यापार विनिमय:</span>
       		 </strong> अन्य टैक्सी ऑपरेटरों के साथ आसानी से व्यापार का आदान-प्रदान करें।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">व्हाट्सएप ग्रुप की आवश्यकता नहीं:</span>
       		 </strong> व्यापार के सुचारू आदान-प्रदान के लिए कई व्हाट्सएप ग्रुपों की परेशानी से बचें।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">अपनी टैक्सी पहले से बुक करें:</span>
       		 </strong> बाहर जाने वाले शहरों के लिए अपनी यात्राओं की पहले से योजना बनाएं और उस शहर में अपनी टैक्सी को बुक करने के लिए उपलब्ध कराएं।
     		 </li>
     		 <li>
       		 <strong>
         		 <span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg></span>
         		 <span style="color: #0073aa;">भारत भर में उपलब्धता:</span>
       		 </strong> आप भारत में कहीं भी सेवा प्रदान कर सकते हैं, जिससे आप सभी ग्राहकों की सेवा कर सकते हैं और अधिक पैसे कमा सकते हैं।
     		 </li>
   		 </ul>
 		 </div>
 		 <div style="margin-top: 30px; text-align: center;">
   		 <h4 style="color: #E70021; border-bottom: 2px solid #E70021; padding-bottom: 5px; margin-bottom: 10px; font-size: 24px;">
     		 <strong>टैक्सी बिज़नेस को रेफर करके पैसे कमाएं!</strong>
   		 </h4>
   		 <p style="text-align: center;">पहले महीने के सब्सक्रिप्शन शुल्क का <strong>30%</strong> और बाद के महीनों के सब्सक्रिप्शन शुल्क का <strong>8%</strong> अर्जित करें।</p>
   		 <p style="text-align: center;">
     		 <strong>अधिक जानकारी के लिए, हमसे <span style="color: #0073aa;">+91 97803 87103</span> पर संपर्क करें।</strong>
   		 </p>
   		 <p style="color: #0073aa; text-align: center;">
     		 <strong>https://app.swari.in</strong>
   		 </p>
 		 </div>
   	 </div>
      </td>
    </tr>
  </table>
</body>
</html>
`,
              content: { name: req.body.name,referalCode: req.body.referal_code, qr_code: qrCodeShare },
              puppeteerArgs:{args: ['--no-sandbox']}

            }).then((success) =>{
                callback(null,true,qrCodeShare);
            })         
      },
       function(success,qrCodeShare,callback){
        console.log('done ---------English started',qrCodeShare)

        const nodeHtmlToImage = require('node-html-to-image')
        nodeHtmlToImage({
          output: '../../referImage/'+req.body.phone_number+'shareImage.png',
          html : `<!DOCTYPE html>
<html lang="en">
  <head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Swari App Invitation
	</title>
  </head>
  <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f9f9f9; color: #333;">
	<table border="5" cellpadding="2" cellspacing="2" align="center" style="border-color:#1c9aab; border-collapse: collapse;">
  	<tr>
    	<td>
      	<div style="max-width: 1080px; margin: 0 auto; background-color: #ffffff; border: 3px solid #0073aa; padding: 20px; border-radius: 5px;">
        	<div style="text-align: center;">
          	<img src="https://panel.swari.in/img/swari-qrlogo.png" height='100' width='400' >
        	</div>
        	<div style="text-align: center; margin-top: 20px;">
          	<h2 style="color: #0073aa;">{{name}} is requesting you to install India’s first Taxi & Travel Agency connecting APP
          	</h2>
          	<p>Please download the SWARI Application by scanning the QR code below or by using the Referral code
            	<strong>{{referalCode}}
            	</strong> during registration.
          	</p>
        	</div>
        	<div style="text-align: center; margin: 20px 0;">
          	<img src="{{qr_code}}" alt="QR Code" style="width: 160px; height: 160px;">
        	</div>
        	<div style="margin-top: 15px;">
          	<h2 style="color: #0073aa; border-bottom: 2px solid #0073aa; padding-bottom: 5px; margin-bottom: 10px;">App Features
          	</h2>
          	<ul style="list-style-type: none; padding-left: 0;">
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">Add Multiple Taxis:
                	</span>
              	</strong> Manage multiple taxis under one account seamlessly.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">No Commission on Trips:
                	</span>
              	</strong> Taxi businesses don’t pay any commission on fares.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">Route Selection:
                	</span>
              	</strong> Choose routes like BLABLA CAR and add multiple stops to pick up passengers along the way.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">Direct Business Exchange:
                	</span>
              	</strong> Easily exchange business with other taxi operators.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">No WhatsApp Groups Required:
                	</span>
              	</strong> Avoid the hassle of multiple WhatsApp groups for smoother business exchange.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">Pre-Book Your Taxi:
                	</span>
              	</strong> Plan your trips in advance for outstation bookings and make your taxi available in that city.
            	</li>
            	<li>
              	<strong>
                	<span style="color: #0073aa;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#0073aa" width="16px" height="16px">
        <path d="M0 0h24v24H0V0z" fill="none"/>
        <path d="M9 16.2l-4.2-4.2L3.4 13l5.6 5.6 12-12-1.4-1.4z"/>
      </svg>
                	</span>
                	<span style="color: #0073aa;">Pan India Availability:
                	</span>
              	</strong> Provide service anywhere in India, serving all customers and earning more money.
            	</li>
          	</ul>
        	</div>
        	<div style="margin-top: 30px; text-align: center;">
          	<h4 style="color: #E70021; border-bottom: 2px solid #E70021; padding-bottom: 5px; margin-bottom: 10px; font-size: 24px;">
            	<strong>Refer a Taxi Business & Earn:
            	</strong>
          	</h4>
          	<p style="text-align: center;">Earn
            	<strong>30%
            	</strong> of the first month's subscription fee and
            	<strong>8%
            	</strong> of the subscription fee for subsequent months.
          	</p>
          	<p style="text-align: center;">
            	<strong>For more information, contact us at
              	<span style="color: #0073aa;">+91 98703 87103
              	</span>
            	</strong>
          	</p>
          	<p style="text-align: center;color: #0073aa">
            	<strong>https://app.swari.in
            	</strong>
          	</p>
        	</div>
      	</div>
    	</td>
  	</tr>
	</table>
  </body>
</html>
`,
          content: { name: req.body.name,referalCode: req.body.referal_code, qr_code: qrCodeShare },
          puppeteerArgs:{args: ['--no-sandbox']}

        }).then((success) =>{
            callback(null,true);
          })           
      },
      function(success,callback){                        
          //configuring parameters
          var uploadParams = {
            Bucket: 'swaritest',
            Body : fs.createReadStream('../../referImage/'+req.body.phone_number+'shareImageHindi.png'),
            ACL: 'public-read',
            Key : "qrs/"+req.body.phone_number+'shareImageHindi.png'
          };

          var params = {
            Bucket: 'swaritest',
            // Body : fs.createReadStream(file.path),
            Key : "qrs/"+req.body.phone_number+'shareImageHindi.png'
          };
         
          s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		              
            //success
            if (data) {
              console.log("Uploaded hindi in:", data);                
  //            User.update({phone_number:req.body.phone_number},{$set:{shareImageHindi: baseSpaces+"/"+params.Key}})
//              .then((val)=>{
    //          console.log('valueeeeeee ',val)
             //   callback(null);              
        //      })
          //    .catch((err)=>{
            //    console.log('errrrrrrrrrr ',err)
             // })

            }
          });


          //configuring parameters
          var uploadParams = {
            Bucket: 'swaritest',
            Body : fs.createReadStream('../../referImage/'+req.body.phone_number+'shareImage.png'),
            ACL: 'public-read',
            Key : "qrs/"+req.body.phone_number+'shareImage.png'
          };

          var params = {
            Bucket: 'swaritest',
            // Body : fs.createReadStream(file.path),
            Key : "qrs/"+req.body.phone_number+'shareImage.png'
          };
         
          s3Client.send(new PutObjectCommand(uploadParams)).then( (data)=> {		              
            //success
            if (data) {
              console.log("Uploaded english in:", data);                
             // User.update({phone_number:req.body.phone_number},{$set:{shareImage: baseSpaces+"/"+params.Key}})
              //.then((val)=>{
             // console.log('valueeeeeee ',val)
              callback(null);
              //
             // })
              //.catch((err)=>{
              //console.log('errrrrrrrrrr ',err)

             // })

            }
          });
      },                  
  ], function (err, result) {
      // result now equals 'done'
      console.log('done')
      console.log("===================== SUCCESS ========================");
  });
}


generateQRCode();
