angular.module('states.controllers', [])

    .controller('statesCtrl', function ($scope,$state,APIService,$stateParams) {
     // $scope.page = 'main';

        $scope.updatedata ={};
        $scope.showUpdate= false;
        $scope.adminData =JSON.parse(localStorage.getItem("UserDeatails"));

  $scope.getStates = function(){
            APIService.getData({ req_url: PrefixUrl + "/States/getStates"}).then(function (res) {
                console.log(res);
                $scope.states= res.data;
                // $scope.getALLCat=res.data;
             
            
            },function(er){
               
            })
    
        }



   $scope.addState = function(){
            const nameCapitalized = $scope.updatedata.state.charAt(0).toUpperCase() + $scope.updatedata.state.slice(1);
               $scope.updatedata.state= nameCapitalized;
             // const nameCapitalized = $scope.updatedata.state.charAt(0).toUpperCase() + $scope.updatedata.state.slice(1);
              // $scope.updatedata.state= nameCapitalized;
        
            APIService.setData({ req_url: PrefixUrl + "/States/create" ,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                if (res.data == true) {
                    alert("State already exist")
                }else{                    
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    alert("Data is Add Successfully.")
                    location.reload(); 
                }

              })
          }

        $scope.UpdateShow = function(user){
            $scope.updatedata =user;
            $scope.showUpdate= true;
        }

        $scope.Update = function(){
          

            APIService.updateData({ req_url: PrefixUrl + "/States/update/"+$scope.updatedata._id,data:$scope.updatedata}).then(function (res) {
                console.log(res)
                //  if (res.data == true) {
                //     alert("State already exist")
                // }else{ 
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= true;
                    alert("Data is Updated Successfully.")
                    location.reload(); 
                // }

            
            },function(er){
             
            })
        }




           $scope.UpdateRemove = function(object){
            if (confirm("Are you sure?")) {
              console.log('$scope.updatedata');
              console.log(object);

                APIService.updateData({ req_url: PrefixUrl + "/States/remove/"+object._id,data:$scope.updatedata}).then(function (res) {
                    console.log(res)
        
                    $scope.expireProduct=res.data;
                    $scope.updatedata ={};
                    $scope.showUpdate= false;
                    alert("Data is Removed Successfully.")
                    location.reload(); 

                
                },function(er){
                 
                })
            }
        }
          $scope.getStates();

        
       //  $scope.viewContacts= function(user){
       //     $state.go("app.viewUserContacts",{data:JSON.stringify(user)});
       //  }
})