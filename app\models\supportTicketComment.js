import mongoose from 'mongoose';
import { stringify } from 'querystring';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const SupportTicketCommentSchema = new Schema({

    support_id:ObjectId,
    title:String,
    comment:String,
    submitted_id:ObjectId,
    created_at:Date ,
    seen: {
          type:Boolean,
          default:false
        },
    
});

 SupportTicketCommentSchema.plugin(timeZone, { paths: ['created_at'] });


export default mongoose.model('SupportTicketComment', SupportTicketCommentSchema);





