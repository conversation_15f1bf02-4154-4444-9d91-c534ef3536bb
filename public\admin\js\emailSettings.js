angular.module('emailSettings.controllers', [])

    .controller('emailSettingsCtrl', function ($scope,APIService, $state,$stateParams) {

    	$scope.template= {};
    	$scope.template.emailBody
        $scope.template1= {};
        $scope.template2= {};
        $scope.template3= {};
        $scope.template4= {};
        $scope.template5= {};
        $scope.template6= {};
        $scope.template7= {};


        APIService.setData({ req_url: PrefixUrl + "/commonsettings/createType/" ,
                                data:{type:'user_register',type:'forgot_password',
                                type:'transactionCR',type:'transactionDR',
                                type:'transactionWithdraw',type:'transactionRazorpay',
                                type:'transactionBoostTrip',type:'transactionSubscriptionFees'},
                            }).then(function (res) {
            console.log(res)
            // $scope.userDetails= res.data;
            // $scope.setTransactions(user.wallet_balance,$scope.payout_id,user._id);
        
        },function(er){

        })


    $scope.updateRegisterationTemplate = function(){

    	APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForRegister/" ,data:{html_content:$scope.template}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }



    $scope.editRegisterationTemplate = function(){

      $scope.template.emailBody= $scope.emailBody;
      // $scope.template.subject= $scope.subject;
    }


    $scope.getRegisterationTemplate = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getRegisterationTemplate/" }).then(function (res) {
            console.log(res)
            $scope.template.emailBody=res.data[0].emailBody;
            $scope.template.subject=res.data[0].subject;
            document.getElementById("demo").innerHTML = $scope.template.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.getForgotPasswordTemplate = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getForgotPasswordTemplate/" }).then(function (res) {
            console.log(res)
            $scope.template1.emailBody=res.data[0].emailBody;
            $scope.template1.subject=res.data[0].subject;
            document.getElementById("demo1").innerHTML = $scope.template1.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.getTransactionCRTemplate = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionCRTemplate/" }).then(function (res) {
            console.log(res)
            $scope.template2.emailBody=res.data[0].emailBody;
            $scope.template2.subject=res.data[0].subject;
            document.getElementById("demo2").innerHTML = $scope.template2.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }

    $scope.getTransactionDRTemplate = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionDRTemplate/" }).then(function (res) {
            console.log(res)
            $scope.template3.emailBody=res.data[0].emailBody;
            $scope.template3.subject=res.data[0].subject;
            document.getElementById("demo3").innerHTML = $scope.template3.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.getTransactionTemplateWithdraw = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionTemplateWithdraw/" }).then(function (res) {
            console.log(res)
            $scope.template4.emailBody=res.data[0].emailBody;
            $scope.template4.subject=res.data[0].subject;
            document.getElementById("demo4").innerHTML = $scope.template4.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.getTransactionTemplateRazorpay = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionTemplateRazorpay/" }).then(function (res) {
            console.log(res)
            $scope.template5.emailBody=res.data[0].emailBody;
            $scope.template5.subject=res.data[0].subject;
            document.getElementById("demo5").innerHTML = $scope.template5.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }

    

    $scope.getTransactionTemplateBoostTrip = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionTemplateBoostTrip/" }).then(function (res) {
            console.log(res)
            $scope.template6.emailBody=res.data[0].emailBody;
            $scope.template6.subject=res.data[0].subject;
            document.getElementById("demo6").innerHTML = $scope.template6.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.getTransactionSubscriptionFees = function(){

        APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionSubscriptionFees/" }).then(function (res) {
            console.log(res)
            $scope.template7.emailBody=res.data[0].emailBody;
            $scope.template7.subject=res.data[0].subject;
            document.getElementById("demo7").innerHTML = $scope.template7.emailBody;

            // $scope.html_content=JSON.parse( res.data[0].html_content);
        
        },function(er){

        })
    }


    $scope.updateTransactionCRTemplate = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionCR/" ,data:{html_content:$scope.template2}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }



    // $scope.getTransactionDRTemplate = function(){

    //     APIService.getData({ req_url: PrefixUrl + "/commonsettings/getTransactionDRTemplate/" }).then(function (res) {
    //         console.log(res)
    //         $scope.template3.emailBody=res.data[0].emailBody;
    //         $scope.template3.subject=res.data[0].subject;
    //         document.getElementById("demo3").innerHTML = $scope.template3.emailBody;

    //         // $scope.html_content=JSON.parse( res.data[0].html_content);
        
    //     },function(er){

    //     })
    // }


    $scope.updateTransactionDRTemplate = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionDR/" ,data:{html_content:$scope.template3}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }


    $scope.updateDataForTransactionWithdraw = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionWithdraw/" ,data:{html_content:$scope.template4}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }


    $scope.updateDataForTransactionRazorpay = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionRazorpay/" ,data:{html_content:$scope.template5}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }



    $scope.updateDataForTransactionBoostTrip = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionBoostTrip/" ,data:{html_content:$scope.template6}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }


    $scope.updateDataForTransactionSubscriptionFees = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForTransactionSubscriptionFees/" ,data:{html_content:$scope.template7}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }

    


    $scope.updateForgotPasswordTemplate = function(){

        APIService.updateData({ req_url: PrefixUrl + "/commonsettings/updateDataForForgotPassword/" ,data:{html_content:$scope.template1}}).then(function (res) {
            console.log(res)
            alert('updated successfully');
            location.reload(); 


        
        },function(er){

        })
    }

    $scope.editForgotPasswordTemplate = function(){

      $scope.template.emailBody= $scope.emailBody;
      $scope.template.subject= $scope.subject;
    }



    

    $scope.getRegisterationTemplate();
    $scope.getForgotPasswordTemplate();
    $scope.getTransactionCRTemplate();
    $scope.getTransactionDRTemplate();
    $scope.getTransactionTemplateWithdraw();
    $scope.getTransactionTemplateRazorpay();
    $scope.getTransactionTemplateBoostTrip();
    $scope.getTransactionSubscriptionFees ();



})