import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;

const VehicleSchema = new Schema({
    user_id: ObjectId,
    reg_no: String,
    vehical_image_url: String,
    category: ObjectId,
    vehical_make: ObjectId,
    vehical_model: ObjectId,
    no_of_seats: Number,
    ac: {type: Boolean, default: false},
    carrier: {type: Boolean, default: false},
    created_at: {
        type: Date,
        default: Date.now,
        get: (date) => date ? date.toISOString() : null
    },
    delete_status: {type: Boolean, default: false},
    year: Date,
}, {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true }
});

// Add a pre-save middleware to ensure created_at is set
VehicleSchema.pre('save', function(next) {
    if (!this.created_at) {
        this.created_at = new Date();
    }
    next();
});

export default mongoose.model('Vehicle', VehicleSchema);