import mongoose from 'mongoose';
import Transaction from '../models/Transaction.js';

async function addTestTransactions() {
    // Replace this with your test user ID
    const testUserId = new mongoose.Types.ObjectId('683492f918d4fd28244ed58e'); // example user ID

    // Credit transactions
    const creditAmounts = [10, 17, 100, 50, 80];
    // Debit transactions
    const debitAmounts = [200, 300, 10, 50, 60, 90];

    try {
        // Add credit transactions
        for (const amount of creditAmounts) {
            const transaction = new Transaction({
                status: true,
                amount: amount,
                transaction_reason: 'Test Credit Transaction',
                transaction_type: 'CR',
                transaction_id: `CR-TEST-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                user_id: testUserId,
                total_amount: amount,
            });
            await transaction.save();
            console.log(`Created CR transaction of amount ${amount}`);
        }

        // Add debit transactions
        for (const amount of debitAmounts) {
            const transaction = new Transaction({
                status: true,
                amount: amount,
                transaction_reason: 'Test Debit Transaction',
                transaction_type: 'DR',
                transaction_id: `DR-TEST-${Date.now()}-${Math.random().toString(36).substring(7)}`,
                user_id: testUserId,
                total_amount: amount,
            });
            await transaction.save();
            console.log(`Created DR transaction of amount ${amount}`);
        }

        console.log('All test transactions created successfully');
    } catch (error) {
        console.error('Error creating test transactions:', error);
    }
}

export default addTestTransactions;