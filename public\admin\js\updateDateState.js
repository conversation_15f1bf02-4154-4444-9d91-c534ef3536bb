angular.module('updateDateState.controllers', [])

    .controller('updateDateStateCtrl', function ($scope, APIService, $state,$rootScope) {
        // $scope.renewal_date="dd/MM/yyyy hh:mm:ss";
        $scope.updatedata ={};
        $scope.showUpdate= false;
        $scope.adminData =JSON.parse(localStorage.getItem("UserDeatails"));


        $scope.addRecord=true;
        $scope.loggedRecord=false;

        $scope.getUserState = function(){
            $scope.addRecord=true;
            $scope.loggedRecord=false;

            console.log("hhh",$scope.getUserState)
            APIService.getData({ req_url: PrefixUrl + "/States/getStates"}).then(function (res) {
                console.log(res);
                 console.log("====re======",res);
                $scope.states= res.data;
                // $scope.getALLCat=res.data;
    
             
            
            },function(er){
               
            })
        }


        
        $scope.loggedRecords = function(){
            $scope.addRecord=false;
            $scope.loggedRecord=true;
            APIService.setData({ req_url: PrefixUrl + "/extenddaylog/showData"}).then(function (res) {
                console.log(res);
                 console.log("====re======",res);
                $scope.stateLog= res.data;
                // $scope.getALLCat=res.data;
    
             
            
            },function(er){
               
            })
        }


        $scope.UpdateShow = function(user){
            $scope.updatedata =user;
            $scope.showUpdate= true;
        }

  $scope.Update = function(){

    if ($scope.startDate) {
        $scope.startDate= $scope.startDate;
    }else{
        $scope.startDate= null;
    }

    if ($scope.endDate) {
        $scope.endDate= $scope.endDate;
    }else{
        $scope.endDate= null;
    }
          
    $scope.updatedata.startDate= $scope.startDate;
    $scope.updatedata.endDate= $scope.endDate;




   APIService.updateData({ req_url: PrefixUrl + "/user/stateUpdate/",data:$scope.updatedata}).then(function (res) {
        console.log(res)
        //  if (res.data == true) {
        //     alert("State already exist")
        // }else{ 
            $scope.expireProduct=res.data;
           

                APIService.setData({ req_url: PrefixUrl + "/extenddaylog/createLog/",data:$scope.updatedata}).then(function (res) {
                    $scope.updatedata ={};
                    $scope.showUpdate= true;
                    alert("Data is Updated Successfully.")
                    location.reload(); 

                },function(er){

                })


        // }

    
    },function(er){
     
    })

    // APIService.updateData({ req_url: PrefixUrl + "/user/stateUpdate/" ,data:$scope.updatedata}).then(function (res) {
    //     console.log(res)
    //     console.log("====rfgfg======",res);
    //     $scope.renewal_date= res.data;
    //         $scope.expireProduct=res.data;
    //         $scope.updatedata ={};
    //         $scope.showUpdate= true;
    //         alert("Data is Updated Successfully.")
    //         // location.reload(); 
    //     // }

    
    // },function(er){
     
    // })

}

$scope.getUserState();  

    })
