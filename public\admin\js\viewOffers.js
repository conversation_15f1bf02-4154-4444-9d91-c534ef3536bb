angular.module('viewOffers.controllers', [])

    .controller('viewOffersCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.offerObj={};
      
      	$scope.getOffers= function(){
	      	APIService.setData({
	          req_url: PrefixUrl + '/offer/getOffers' ,data:{} 
	      	}).then(function(resp) {
				$scope.offers=resp.data
				//    $scope.getOfferCategory();
	             },function(resp) {
	            // This block execute in case of error.
	         });			
	    } 


		$scope.getOffers();
		// $scope.getOfferCategory= function(){
	    //     console.log('offerObj -- ',$scope.offerObj)
	    //   	APIService.setData({
	    //       req_url: PrefixUrl + '/offerCategory/getOfferCategory' ,data:{} 
	    //   	}).then(function(resp) {
	    //         $scope.offerCategories=resp.data
	    //          },function(resp) {
	    //             // This block execute in case of error.
	    //      });

	    //  }

    

    });