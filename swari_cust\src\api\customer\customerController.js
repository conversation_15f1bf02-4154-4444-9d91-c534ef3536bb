/**
 * Customer Controller
 * Handles customer resources
 * PRD Reference: Sections 4.1, 10.2
 */

import jwt from "jsonwebtoken";
import bcrypt from "bcrypt";
import { validationResult } from "express-validator";
import Customer from "../../models/Customer.js";
import Driver from "../../models/Driver.js";
import logger from "../../utils/logger.js";
import { generateOTP, verifyOTP } from "../../services/otpService.js";
import config from "../../config/config.js";
import dotenv from "dotenv";

/**
 * authenticate customer
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */

export const VerifyCustomerOTP = async (req, res) => {
  // Check validation errors
  // const errors = validationResult(req);
  // if (!errors.isEmpty()) {
  //     return res.status(400).json({ errors: errors.array() });
  // }
  // logger.info('verify-otp', {req: req.body.test});
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error("Customer login validation failed", {
        errors: errors.array(),
      });
      return res.status(400).json({ errors: errors.array() });
    }

    const { phone, otp } = req.body;

    // Find customer by phone
    const customer = await Customer.findOne({ phone });
    if (!customer) {
      logger.warn("Customer login attempt with non-existent phone number", {
        phone,
      });
      return res.status(400).json({ message: "Phone number not registered" });
    }

    // Verify OTP
    const isValidOTP = verifyOTP(customer, otp);
    logger.info(isValidOTP);
    if (!isValidOTP) {
      logger.warn("Customer login attempt with invalid OTP", { phone });
      return res.status(400).json({ message: "Invalid OTP" });
    }

    // Clear OTP after successful verification
    customer.otp = undefined;
    customer.otp_expiry = undefined;
    await customer.save();

    // Generate JWT token
    const token = jwt.sign(
      { id: customer._id, role: customer.role },
      config.jwtSecret,
      { expiresIn: "24h" }
    );

    logger.info("Customer logged in successfully", {
      customerId: customer._id,
    });

    return res.status(200).json({
      message: "Login successful",
      token,
      user: {
        id: customer._id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        role: customer.role,
      },
    });
  } catch (error) {
    logger.error("Error in customer login", { error: error.message });
    return res
      .status(500)
      .json({ message: "Server error", error: error.message });
  }
};

export const getCustomerProfile = async (req, res) => {
  try {
    const customerId = req.user.id;

    const customer = await Customer.findById(customerId).select(
      "name phone email wallet_balance average_rating is_profile_complete is_flagged"
    );

    if (!customer) {
      logger.warn("Customer not found");
      return res.status(404).json({ message: "Customer not found" });
    }
    logger.info("customer", { customer });
    res.status(200).json(customer);
  } catch (error) {
    logger.error("Error fetching customer profile:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
export const updateCustomerProfile = async (req, res) => {
  try {
    const customerId = req.user.id; 
    const allowedUpdates = ["name", "email"];
    const updateData = {};

    // Filter out only allowed fields from req.body
    Object.keys(req.body).forEach((key) => {
      if (allowedUpdates.includes(key)) {
        updateData[key] = req.body[key];
      }
    });
    

    if (Object.keys(updateData).length === 0) {
      return res
        .status(400)
        .json({ message: "No valid fields provided for update" });
    }
    updateData['is_profile_complete'] = true;
    const updatedCustomer = await Customer.findByIdAndUpdate(
      customerId,
      updateData,
      { new: true, runValidators: true }
    ).select(allowedUpdates.join(" "));

    if (!updatedCustomer) {
      logger.warn("Customer not found");
      return res.status(404).json({ message: "Customer not found" });
    }

    res.status(200).json({
      message: "Customer updated successfully!",
      data: updatedCustomer
    });
    
  } catch (error) {
    logger.error("Error updating customer profile:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
