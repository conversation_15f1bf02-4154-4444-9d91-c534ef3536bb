var NodeCache = require( "node-cache" );
var myCache = new NodeCache();

var uniqueToken = function (req, res, next) {
    next();
    /*
    console.log(" uniqueTokenuniqueToken uniqueToken validating the unique ",req.headers);
    var token = req.headers['x-access-token'];
   
    var msg = {auth: false, message: 'No token provided.'};
    if (!token) res.status(401).send(msg);

    console.log("validating the unique ", req.user._id);
    console.log(myCache.get(req.user._id));
    if(req.user) {
        if(myCache.get(req.user._id)  && myCache.get(req.user._id)===token) {
            next();
        } else {
            console.log("Failed to find the token");
            res.status(401).send({auth:false, message: 'Not authorized'});
        }
    }
    */
}

module.exports = uniqueToken;