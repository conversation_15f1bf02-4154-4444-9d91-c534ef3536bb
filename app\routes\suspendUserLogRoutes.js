import express from 'express';
import SuspendUserLogController from '../controllers/suspendUserLogController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initsuspendUserLogRoutes = () => {
  const suspendUserLogRoutes = express.Router();


  suspendUserLogRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SuspendUserLogController.create);
  suspendUserLogRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SuspendUserLogController.show);
  // suspendUserLogRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  RateAndReviewController.page);
  // suspendUserLogRoutes.post('/',passport.authenticate('jwt', { session: false }), jwt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,  RateAndReviewController.create);
  // suspendUserLogRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.update);
  // suspendUserLogRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.remove);




  return suspendUserLogRoutes;
};

export default initsuspendUserLogRoutes;
