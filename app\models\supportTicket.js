import mongoose from 'mongoose';
import { stringify } from 'querystring';
import SupportTicket from '../models/supportTicket';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const moment = require('moment-timezone');
const timeZone = require('mongoose-timezone');


const SupportTicketSchema = new Schema({

    serial_number:Number,
    user_id:ObjectId,
    title:String,
    description:String,
    image_url_a: String,
    image_url_b:String,
    image_url_c:String,
    created_at: Date,
    status:{type:Boolean,default:true},
    close_date: Date,
    open_date: Date,
    assign_user:ObjectId,
    category:String,
    reply_status:{type:Number,default:2},
    reply_date: Date,

    
});

 SupportTicketSchema.plugin(timeZone, { paths: ['created_at','close_date','open_date','reply_date'] });

    // Use pre middleware
    SupportTicketSchema.pre('save', function (next) {

        // Only increment when the document is new
        if (this.isNew) {
            SupportTicket.count().then(res => {
                this.serial_number = res + 1; // Increment count
                next();
            });
        } else {
            next();
        }
    });



export default mongoose.model('SupportTicket', SupportTicketSchema);
