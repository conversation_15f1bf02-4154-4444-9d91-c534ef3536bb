import express from 'express';
import UtilityController from '../controllers/utilityController';

import moment from 'moment';

const initUtilityRoutes = () => {
  const utilityRoutes = express.Router();
  // utilityRoutes.post('/images', UtilityController.uploadImage);
  utilityRoutes.post('/images', UtilityController.uploadImageSpaces);
  
  utilityRoutes.post('/profile', (req, res, next)=>{ 
    req.folder='user'; 
    return next();
   },UtilityController.uploadImageSpaces);
	
	utilityRoutes.get('/', (req, res) => {
      return res.send('Hello, this is the API!');
  
  utilityRoutes.post('/ticket', (req, res, next)=>{ 

    let mmt = moment().format('YYYY/M');
    req.folder=`tickets/${mmt}`;

    return next(); },UtilityController.uploadImageSpaces);

  

  return utilityRoutes;
};

export default initUtilityRoutes;
