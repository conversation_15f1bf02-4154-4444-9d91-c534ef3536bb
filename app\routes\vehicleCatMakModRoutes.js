import express from 'express';
import vehicleCatMakModRoutesController from '../controllers/vehicleCatMakModRoutesController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initVehicleCatMakModRoutes = () => {
  const vehicleCatMakModRoutes = express.Router();



  vehicleCatMakModRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  vehicleCatMakModRoutesController.show);
  vehicleCatMakModRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  vehicleCatMakModRoutesController.create);
  vehicleCatMakModRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  vehicleCatMakModRoutesController.update);
  vehicleCatMakModRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  vehicleCatMakModRoutesController.remove);
  vehicleCatMakModRoutes.get('/getVehicleCategories/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  vehicleCatMakModRoutesController.getVehicleCategories);


  // //vehicleCatMakModRoutes.get('/', VehicleController.page);
  // vehicleCatMakModRoutes.get('/', vehicleCatMakModRoutesController.show);
  // vehicleCatMakModRoutes.post('/', vehicleCatMakModRoutesController.create);
  // vehicleCatMakModRoutes.put('/update/:id', vehicleCatMakModRoutesController.update);
  // vehicleCatMakModRoutes.delete('/:vehicle_id', vehicleCatMakModRoutesController.remove);

  return vehicleCatMakModRoutes;
};

export default initVehicleCatMakModRoutes;
