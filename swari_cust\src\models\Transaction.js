/**
 * Transaction Model
 * Defines the schema for wallet transaction logs
 * PRD Reference: Sections 4.8, 10.3
 */

import mongoose from 'mongoose';

const TransactionSchema = new mongoose.Schema({
  _id: {
    type: mongoose.Schema.Types.ObjectId,
    auto: true
  },
  status: { 
    type: Boolean, 
    required: true,
    default: false
  },
  amount: { 
    type: Number, 
    required: true 
  },
  transaction_reason: { 
    type: String, 
    required: true 
  },
  reason: { 
    type: String 
  },
  transaction_date: { 
    type: Date, 
    required: true,
    default: Date.now 
  },
  transaction_type: { 
    type: String, 
    enum: ['CR', 'DR'], 
    required: true 
  },
  transaction_id: { 
    type: String, 
    required: true, 
    unique: true 
  },
  user_id: { 
    type: mongoose.Schema.Types.ObjectId, 
    required: true, 
    ref: 'User' 
  },
  total_amount: { 
    type: Number, 
    required: true 
  },
  __v: { 
    type: Number 
  }
}, { timestamps: true });

// Static method to get all transactions by user_id
TransactionSchema.statics.getTransactionsByUserId = async function(userId) {
  try {
    const transactions = await this.find({ user_id: userId })
      .sort({ transaction_date: -1 })
      .lean()
      .exec()
      ;
    return transactions;
  } catch (error) {
    throw new Error(`Error fetching transactions: ${error.message}`);
  }
};

const Transaction = mongoose.model('Transaction', TransactionSchema);
export default Transaction;