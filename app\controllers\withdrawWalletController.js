import Responder from '../../lib/expressResponder';
import Transaction from '../models/transaction';
import paytm_config from '../paytm/paytm_config';
import checksum from '../paytm/checksum';
import User from '../models/user';
import WithdrawWallet from '../models/withdrawWallet';
import _ from "lodash";
import nodemailer from 'nodemailer';
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';
import CommonSettings from '../models/commonSettings';
import http from 'http';
var dateFormat = require('dateformat');
var moment = require('moment-timezone');
import Settings from '../models/settings';


var transporter = nodemailer.createTransport({
    host: 'server.gxhosts.com',
  service: 'SMTP',
  port: 587,
  secure: false,
  auth: {
     user: '<EMAIL>',
    pass: 'GSNfn2hqVZ'
  },
  tls: {
          rejectUnauthorized: false
      }
});

export default class WithdrawWalletController {


	static create(req, res) {
 
   console.log('withdrawWalletController-------');
   console.log(req.params);
      WithdrawWallet.create(req.body)
     .then((trnc)=>{
      Responder.success(res,trnc)
      console.log('trccccc')
      console.log(trnc)
     });
 	}



    static withdrawWalletGetDataForApproveCount(req, res) {
 
   console.log('withdrawWalletGetDataForApproveCount-------');


     WithdrawWallet.aggregate([ 
              {
                    $match: {
                        'status': 'waiting for aproval',
                      }
                    
              },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },
             


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  static withdrawWalletGetDataForApprove(req, res) {
 
   console.log('withdrawWalletGetDataForApprove-------');
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

     WithdrawWallet.aggregate([ 
              {
                    $match: {
                        'status': 'waiting for aproval',
                      }
                    
              },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": { 
                    "date": -1,
                } 
            }, 


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

  }


  static withdrawWalletGetDataForRemmittes(req, res) {
 
   console.log('withdrawWalletGetDataForRemmittes-------');
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

   
     WithdrawWallet.aggregate([ 
              {
                    $match: {
                        'status': 'waiting for remmittes',
                      }
                    
              },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },{ 
                "$sort": { 
                    "date": -1,
                } 
            }, 



                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }

  static withdrawWalletGetDataForRemmittesLength(req, res) {
 
   console.log('withdrawWalletGetDataForRemmittesLength-------');
      var wallet=[];
      var user=[];

     WithdrawWallet.find({status:'waiting for remmittes'})
        .then((usr)=> {wallet=usr; return User.find({_id:{$in:_.map(usr,'user_id')}})})
        .then((user)=>{console.log('aaaaaaaaa',user);var response=[];_.each(wallet,tr =>{
          var trp={_id:tr._id,
            user_id:tr.user_id,
            user_id:tr.user_id,
            date: tr.date,
            reason: tr.reason,
            status: tr.status,
            transaction_type: tr.transaction_type,
            accountName: tr.accountName,
            hiddenAccountNo: tr.hiddenAccountNo,
            bankName: tr.bankName,
            ifsceCode: tr.ifsceCode,
            branchAddress: tr.branchAddress,
            amount: tr.amount,
            
           
          };
          
          _.each(user,usr=>{
            
            if(trp && usr._id == trp.user_id){
          
              trp.userDetails=usr;
              console.log(trp)

            }
          }) 
          response.push(trp);
          console.log(response)
        });Responder.success(res,response) })
        .catch((err)=>Responder.operationFailed(res,err))

      // WithdrawWallet.find( {status:'waiting for remmittes'} )
      // .then((trc)=>Responder.success(res,trc))
      //   .catch((err)=>Responder.operationFailed(res,err))
  }

  static withdrawWalletGetDataForPaidToBank(req, res) {
 
   console.log('withdrawWalletGetDataForPaidToBank-------');
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

     WithdrawWallet.aggregate([ 
              {
                    $match: {
                        'status': 'paid to bank',
                      }
                    
              },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
                { 
                "$sort": { 
                    "date": -1,
                } 
            }, 


                    ]).skip(size * (pageNo - 1)).limit(size)
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }



  static withdrawWalletGetDataForPaidToBankLength(req, res) {
 
   console.log('withdrawWalletGetDataForPaidToBankLength-------');
   

    var wallet=[];
      var user=[];
      
     WithdrawWallet.find({status:'paid to bank'})
        .then((usr)=> {wallet=usr; return User.find({_id:{$in:_.map(usr,'user_id')}})})
        .then((user)=>{console.log('aaaaaaaaa',user);var response=[];_.each(wallet,tr =>{
          var trp={_id:tr._id,
            user_id:tr.user_id,
            user_id:tr.user_id,
            date: tr.date,
            reason: tr.reason,
            status: tr.status,
            transaction_type: tr.transaction_type,
            accountName: tr.accountName,
            hiddenAccountNo: tr.hiddenAccountNo,
            bankName: tr.bankName,
            ifsceCode: tr.ifsceCode,
            branchAddress: tr.branchAddress,
            amount: tr.amount,
            remarks: tr.remarks,
            
           
          };
          
          _.each(user,usr=>{
            
            if(trp && usr._id == trp.user_id){
          
              trp.userDetails=usr;
              console.log(trp)

            }
          }) 
          response.push(trp);
          console.log(response)
        });Responder.success(res,response) })
        .catch((err)=>Responder.operationFailed(res,err))


      // WithdrawWallet.find( {status:'paid to bank'} )
      // .then((trc)=>Responder.success(res,trc))
      //   .catch((err)=>Responder.operationFailed(res,err))
  }

  static update(req, res) {

   console.log('update-------');
   if (req.body.approvalDate) {
    console.log('approvalDate-------');
    req.body.approvalDate=moment(new Date()).add('hours',5.5).toDate();
   }
   if (req.body.remittDate) {
    console.log('remmittesdate-------');
    req.body.remittDate=moment(new Date()).add('hours',5.5).toDate();
    }
   console.log(req.body);

   // // console.log(req.params.id);
    delete ObjectId(req.body._id);
    if (!req.params.id)
      return Responder.operationFailed(res, { message: 'User Id Is Required.' })
    WithdrawWallet.findOneAndUpdate({ _id: ObjectId(req.params.id) }, { $set: req.body })
    .then((data) =>{ return WithdrawWallet.findOne({_id:data._id })})  
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
    
 
   // console.log('update-------');
   // console.log(req.body);
      // WithdrawWallet.findOneAndUpdate({ _id: req.body.id }, { $addToSet: { 'status':'waiting for remmittes' } })
      // .then((val) => Responder.success(res, val))
      // .catch((err) => Responder.operationFailed(res, err))
  }


  
  static getArroveAndRemittesLength(req, res) {
     WithdrawWallet.find({ user_id:req.params.id,
                            $or:[{status:'waiting for aproval'},
                                {status:'waiting for remmittes'}]})
   
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   
  } 


  
  static withdrawWalletGetDataForApproveLength(req, res) {
 
   console.log('withdrawWalletGetDataForApproveLength-------');
   

    WithdrawWallet.aggregate([ 
              {
                    $match: {
                      status:'waiting for aproval'
                      }
                    
              },
              { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
               { 
                "$sort": { 
                    "date": -1,
                } 
            }, 


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))

      // var wallet=[];
      // var user=[];
      //  WithdrawWallet.find({status:'waiting for aproval'})
      //   .then((usr)=> {wallet=usr; return User.find({_id:{$in:_.map(usr,'user_id')}})})
      //   .then((user)=>{console.log('aaaaaaaaa',user);var response=[];_.each(wallet,tr =>{
      //     var trp={_id:tr._id,
      //       user_id:tr.user_id,
      //       user_id:tr.user_id,
      //       date: tr.date,
      //       reason: tr.reason,
      //       status: tr.status,
      //       transaction_type: tr.transaction_type,
      //       accountName: tr.accountName,
      //       hiddenAccountNo: tr.hiddenAccountNo,
      //       bankName: tr.bankName,
      //       ifsceCode: tr.ifsceCode,
      //       branchAddress: tr.branchAddress,
      //       amount: tr.amount,
            
           
      //     };
          
      //     _.each(user,usr=>{
            
      //       if(trp && usr._id == trp.user_id){
          
      //         trp.userDetails=usr;
      //         console.log(trp)

      //       }
      //     }) 
      //     response.push(trp);
      //     console.log(response)
      //   });Responder.success(res,response) })
      //   .catch((err)=>Responder.operationFailed(res,err))

      // WithdrawWallet.find( {status:'waiting for aproval'} )
      // .then((trc)=>Responder.success(res,trc))
      //   .catch((err)=>Responder.operationFailed(res,err))
  }


  static sendEmailForWithdrawWallet(req, res) {


      User.findOne({ _id: req.body.user_id })
      .then((user) =>{
          CommonSettings.find({type:'transactionWithdraw'})
           .then((res)=>
           {
            console.log('rrrrresponse')
            console.log(res)
            console.log('rrrrresponse')

                var oldsubject1=res[0].subject;
                var userName= user.name;
                var newSubject1= oldsubject1.replace("{name}",userName);
                var newSubject= newSubject1.replace("{amount}",req.body.amount);

                var oldBodySubject1=res[0].emailBody;
                var userName=user.name;
                var newBodySubject1= oldBodySubject1.replace("{name}",userName);
                var newBodySubject= newBodySubject1.replace("{amount}",req.body.amount);

                 Settings.findOne({ type: 'app_settings' })
                .then((app_settings) =>{ console.log('app_settings ww.........',app_settings);


                var mailOptions = {
                  from: 'Swari <'+app_settings.billingEmail+'>',
                  to: user.email,
                  subject:newSubject ,
                  html: newBodySubject,
                };

                transporter.sendMail(mailOptions, function(error, info){
                  if (error) {
                    console.log(error);
                  } else {
                    console.log('Email sent: ' + info.response);
                      // send message start
                    
                    /*  var extServerOptionsPost = {
                          hostname: "***************",
                          path: '/rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71',
                          method: 'POST',
                          port: '80',
                          headers: {
                            'Content-Type': 'application/json'
                          }
                        }
                        console.log('Email sent: 111111111111');

                        // const token = otplib.authenticator.generate(secret);
                        // console.log(token);

                        var reqPost = http.request(extServerOptionsPost, function (response) {

                          response.on('data', function (data) {
                            console.log("line no: 87 ", data);
                          });
                        });
                        */

                        var body =
                          {
                            "smsContent": 'Rs. '+ req.body.amount +' remitted to your Bank Account No XXXX'+ req.body.account_no+' on ' + dateFormat(new Date(req.body.date), "dd-mm-yyyy h:MM:ss TT") +' Transaction ID.'+ req.body.transaction_id +'. A/c Bal: Rs. '+ req.body.balance +'. Thanks. www.swari.in',
                            "routeId": "1",
                            "mobileNumbers": user.phone_number,
                            // "mobileNumbers": '**********,**********',
                            "senderId": "SWARII",
                            "signature": "signature",
                            "smsContentType": "english"
                          }

                          sendTXTGuruSMS("1507166609354613662",body);
                          return Responder.success(res, { success: true, message: "" }) 

                        /*
                        console.log('Email sent: **************');
                        reqPost.write(JSON.stringify(body));
                        reqPost.end();
                        reqPost.on('error', function (e) {
                          console.error("line: 102 " + e);
                        });
                        */
                        // send message end
                  }
                }); 

                })
                .catch((err) => Responder.operationFailed(res, err))


           })
          .then((trc)=>Responder.success(res,trc))
           .catch((err)=>Responder.operationFailed(res,err)) 
      } 
        )
      .catch((err) => Responder.operationFailed(res, err))

     
    // console.log('sendEmailForWithdrawWallet'+req.body)
    // console.log(req.body)

    
    // var helpers = require("./helpers/mailFormatForWithdrawWallet");
    // var obj = {} // empty Object
    // var key = 'data';
    // obj[key] = [];
    // obj[key].push({'transaction_amount':req.body.transaction_amount});
    // obj[key].push({'transaction_id':req.body.transaction_id});
    // obj[key].push({'email_id':req.body.email_id});


    // console.log('obj')
    // helpers.sendMail(obj);
    // Responder.success(res, 'success')
        


  }

  
  static filterPaidToBank(req, res) {


    console.log('filterPaidToBank-------');
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)


    User.aggregate([ 
                   {
                    $match: {
                              phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'},
                              
                            },
                        
                  }

                ])
        .then((makers)=>{
            var object= [];
            makers.forEach(function (maker, k) {                
              console.log('ffffffffffffffff',maker.name)
              object.push(ObjectId(maker._id))
            });
            console.log('ooooooo',object)
               WithdrawWallet.aggregate([ 
                  {
                        $match: {
                            'status': 'paid to bank',
                            // 'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                               user_id: { $in: object }

                          }
                        
                  },
                    { $lookup:
                     {
                       from: 'users',
                       localField: 'user_id',
                       foreignField: '_id',
                       as: 'userDetails'
                     }
                   },
                    { 
                    "$sort": { 
                        "date": -1,
                    } 
                }, 
                    ]).skip(size * (pageNo - 1)).limit(size)
                    .then((trc)=>Responder.success(res,trc))

              .catch((err)=>Responder.operationFailed(res,err))

          
          }
          )
        .catch((err)=>Responder.operationFailed(res,err))
   
  }


  
   static filterPaidToBankCount(req, res) {
        
    User.aggregate([ 
                   {
                    $match: {
                              phone_number: {$regex : "^" + req.body.phone_number,$options: 'i'},
                              
                            },
                        
                  }

                ])
        .then((makers)=>{
            var object= [];
            makers.forEach(function (maker, k) {                
              console.log('ffffffffffffffff',maker.name)
              object.push(ObjectId(maker._id))
            });
            console.log('ooooooo',object)
               WithdrawWallet.aggregate([ 
                  {
                        $match: {
                            // 'status': 'paid to bank',
                            // 'phone_number': {$regex : "^" + req.body.phone_number,$options: 'i'},
                               user_id: { $in: object }

                          }
                        
                  },
                    
                 {
                     $group: {
                          _id: {
                            
                          },
                         
                            myCount: { $sum: 1 } ,
                        }
                
                }
                    ])
                    .then((trc)=>{
                      if (trc.length > 0) {
                        Responder.success(res,trc)
                      }else{
                        // var arr={};
                        // arr.data['0'].myCount= 0;
                        Responder.success(res,{'0':{'myCount':'0'}})
                      }
                    }
                      )

              .catch((err)=>Responder.operationFailed(res,err))

          
          }
          )
        .catch((err)=>Responder.operationFailed(res,err))
    }


}
