/**
 * Trip Controller
 * Handles trip creation, retrieval, and listing
 * PRD Reference: Sections 4.2, 10.2
 */

import Trip from "../../models/Trip.js";
import Driver from "../../models/Driver.js";
import logger from "../../utils/logger.js";
import { validationResult } from "express-validator";
import mqttService from "../../services/mqttService.js";
import routeService from "../../services/routeService.js"; // Add route service import

/**
 * Create a new trip
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createTrip = async (req, res) => {
  // Validation is handled by middleware

  try {
    // Extract trip data from request body
    const {
      trip_type,
      pickup_coords,
      dropoff_coords,
      pickup_address,
      dropoff_address,
      date_time,
      return_date_time,
      custom_days,
      car_type,
      notes,
    } = req.body;

    // Calculate route distance
    const distance = await routeService.calculateDistance(pickup_coords, dropoff_coords);
    
    // calculate maximum cancel time 
    let start_date = new Date(date_time);
    let cancel_time = start_date.getTime() - 1000 * 60 * 60 * 2;
    
    logger.info("cancel time: ", {time: new Date(cancel_time).toISOString()});
    
    // Create new trip object
    const newTrip = new Trip({
      customer_id: req.user.id, // From auth middleware
      trip_type,
      pickup_coords: {
        type: "Point",
        coordinates: pickup_coords, // [longitude, latitude]
      },
      dropoff_coords: {
        type: "Point",
        coordinates: dropoff_coords, // [longitude, latitude]
      },
      pickup_address,
      dropoff_address,
      date_time,
      cancel_by: new Date(cancel_time).toISOString(),
      car_type,
      status: "pending",
      distance: distance // Add calculated distance
    });

    // Add optional fields based on trip type
    if (trip_type === "round-trip" && return_date_time) {
      newTrip.return_date_time = return_date_time;
    }

    if (trip_type === "custom-days" && custom_days && custom_days.length > 0) {
      newTrip.custom_days = custom_days;
    }

    if (notes) {
      newTrip.notes = notes;
    }

    // Save trip to database
    const savedTrip = await newTrip.save();

    logger.info("Trip created successfully", {
      tripId: savedTrip._id,
      customerId: req.user.id,
    });

    // Publish trip creation event via MQTT
    mqttService.publishTripStatusUpdate(savedTrip._id, { event: 'created', trip: savedTrip });

    res.status(201).json({
      success: true,
      message: "Trip created successfully",
      data: savedTrip,
    });
  } catch (error) {
    logger.error("Error creating trip", {
      error: error.message,
      stack: error.stack,
      userId: req.user.id,
    });
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};

/**
 * Get trip by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getTripById = async (req, res) => {
  try {
    const tripId = req.params.id;

    // Find trip by ID
    const trip = await Trip.findById(tripId);

    // Check if trip exists
    if (!trip) {
      logger.warn("Trip not found", { tripId });
      return res.status(404).json({
        success: false,
        message: "Trip not found",
      });
    }

    // Check if user is authorized to view this trip
    if (
      trip.customer_id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      logger.warn("Unauthorized trip access attempt", {
        tripId,
        userId: req.user.id,
      });
      return res.status(403).json({
        success: false,
        message: "Not authorized to view this trip",
      });
    }
    let driverOBJ = {} ;
    // Add driver information if trip has a driver assigned
    if (trip.driver_id) {
      try {
        const driver = await Driver.findById(trip.driver_id);

        logger.info("driver info", {driver});
        if (driver) {
          
          driverOBJ.driver_name = driver.name;
          logger.info("trip driver name", {name:trip.driver_name});

          // Get total trips count for this driver
          const driverTrips = await Trip.find({ driver_id: trip.driver_id });
          driverOBJ.driver_total_trips = driverTrips.length;
          logger.info("trip driver total trips", {trip});
        }
      } catch (driverError) {
        logger.warn('Failed to fetch driver information', {
          driverId: trip.driver_id,
          error: driverError.message
        });
      }
    }

    logger.info("Trip retrieved successfully", { tripId });
    res.status(200).json({
      success: true,
      data: {trip, driver_data : driverOBJ},
    });
  } catch (error) {
    logger.error("Error retrieving trip", {
      error: error.message,
      stack: error.stack,
      tripId: req.params.id,
    });
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};

/**
 * Get all trips for logged-in user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getUserTrips = async (req, res) => {
  try {
    // Find all trips for the logged-in user
    const trips = await Trip.find({ customer_id: req.user.id }).sort({
      created_at: -1,
    }); // Sort by creation date, newest first

    logger.info("User trips retrieved successfully", {
      userId: req.user.id,
      count: trips.length,
    });
    res.status(200).json({
      success: true,
      data: trips,
    });
  } catch (error) {
    logger.error("Error retrieving user trips", {
      error: error.message,
      stack: error.stack,
      userId: req.user.id,
    });
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};

/**
 * Get trips for the logged-in customer
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getTripByCustomer = async (req, res) => {
  try {
    const userId = req.user.id;

    // Find all trips for the customer
    const trips = await Trip.find({ customer_id: userId }).sort({
      created_at: -1,
    }); // Sort by creation date, newest first

    logger.info("Customer trips retrieved successfully", {
      userId,
      count: trips.length,
    });
    res.status(200).json({
      success: true,
      data: trips,
    });
  } catch (error) {
    logger.error("Error retrieving customer trips", {
      error: error.message,
      stack: error.stack,
      userId: req.user.id,
    });
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};
/**
 * Cancel a trip
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const cancelTrip = async (req, res) => {
  try {
    const tripId = req.params.id;
    const userId = req.user.id;

    // Find trip by ID
    const trip = await Trip.findById(tripId);

    // Check if trip exists
    if (!trip) {
      logger.warn("Trip not found", { tripId });
      return res.status(404).json({
        success: false,
        message: "Trip not found",
      });
    }
    logger.info("cutomer id in Trip : " , {request: userId, model : trip.customer_id, is_equal: trip.customer_id.toString() !== userId});
    // Check if user is authorized to cancel this trip
    if (trip.customer_id.toString() !== userId) {
      logger.warn("Unauthorized trip cancellation attempt", { tripId, userId });
      return res.status(403).json({
        success: false,
        message: "Not authorized to cancel this trip",
      });
    }

    // Check trip status
    if (trip.status === "pending") {
      trip.status = "cancelled";
      await trip.save();

      logger.info("Trip cancelled successfully", { tripId, userId });
      
      // Publish trip cancellation event via MQTT
      mqttService.publishTripStatusUpdate(trip._id, { event: 'cancelled', trip: trip });

      return res.status(200).json({
        success: true,
        message: "Trip cancelled successfully",
      });
    } else if (trip.status === "accepted") {
      logger.warn("Trip in accepted status, cannot be cancelled", {
        tripId,
        userId,
      });
      return res.status(400).json({
        success: false,
        message: "Trip in accepted status cannot be cancelled",
      });
    } else {
      logger.warn("Trip cannot be cancelled in current status", {
        tripId,
        userId,
        status: trip.status,
      });
      return res.status(400).json({
        success: false,
        message: "Trip cannot be cancelled at this stage",
      });
    }
  } catch (error) {
    logger.error("Error cancelling trip", {
      error: error.message,
      stack: error.stack,
      userId: req.user.id,
    });
    return res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};

/**
 * Complete a trip
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const completeTrip = async (req, res) => {
  try {
    const tripId = req.params.id;
    const userId = req.user.id;
    // const userRole = req.user.role; // Assuming role is set by middleware

    // Find trip by ID
    const trip = await Trip.findById(tripId);

    // Check if trip exists
    if (!trip && !trip.status === "in-progress") {
      logger.warn("Trip not found for completion", { tripId });
      return res.status(404).json({
        success: false,
        message: "Trip not found",
      });
    }
    // return res.status(403).json({
    //   success: false,
    //   user_id: trip.customer_id.toString(),
    //   id: userId,
    //   driver_id: trip.driver_id.toString(),

    // });

    // Authorization check
    // For customers, they must be the owner of the trip
    // For drivers, as there's no driver_id on the trip model, we assume if they are authenticated (via verifySwariNodeToken)
    // and have the tripId, they are authorized. More specific driver assignment logic would be outside this scope.
    if (trip.customer_id.toString() !== userId  && trip.driver_id.toString() !== userId) {
      logger.warn("Unauthorized trip completion attempt by customer or driver", { tripId, userId });
      return res.status(403).json({
        success: false,
        message: "Not authorized to complete this trip",
      });
    }
    // No specific check for driver beyond authentication, due to lack of driver_id on trip

    // Check if trip can be completed (e.g., not already completed or cancelled)
    if (trip.status === 'completed') {
      logger.info("Trip already completed", { tripId });
      return res.status(400).json({
        success: false,
        message: "Trip is already completed",
      });
    }

    if (trip.status === 'cancelled') {
      logger.warn("Cannot complete a cancelled trip", { tripId });
      return res.status(400).json({
        success: false,
        message: "Cannot complete a cancelled trip",
      });
    }

    // Update trip status to completed
    trip.status = 'completed';
    await trip.save();

    logger.info("Trip completed successfully", { tripId, userId});

    // Publish trip completion event via MQTT
    mqttService.publishTripStatusUpdate(trip._id, { event: 'completed', trip: trip });

    res.status(200).json({
      success: true,
      message: "Trip completed successfully",
      data: trip,
    });

  } catch (error) {
    logger.error("Error completing trip", {
      error: error.message,
      stack: error.stack,
      tripId: req.params.id,
      userId: req.user.id,
    });
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
};
