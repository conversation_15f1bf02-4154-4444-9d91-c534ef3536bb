import Responder from '../../lib/expressResponder';
import NotificationLog from '../models/notificationLog';
import User from '../models/user';
import _ from "lodash";
import mongoose from 'mongoose';
var ObjectId= require('mongodb').ObjectId;

export default class notificationLogController {


  static create(req, res) {
  	console.log('jjjjjjjjjjj')
  	console.log(req.body);
  	NotificationLog.create(req.body)
     .then((trnc)=>{
      Responder.success(res,trnc)
      console.log('trccccc')
      // console.log(trnc)
     });
  	// Responder.success(res,'success')
  }


  static show(req, res) {
    var pageNo= req.body.currentPage + 1;
    console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    console.log('size--'+size)

     NotificationLog.aggregate([ 
              {
                    $match: {
                      // $and:[
                        // 'user_id': req.params.id,
                        // 'status': 'ACTIVE',
                        // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               },
                { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                }
                } 


                    ]).skip(size * (pageNo - 1)).limit(size).sort({ 'created_at': -1 })
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  
  static logEntriesFilterCountForFirstTime(req, res) {
     NotificationLog.count({}, function (err, count) {
      Responder.success(res,count)
    });
  }


  static getCount(req, res) {
   
        if ((req.body.obj.startDate != null && req.body.obj.endDate != null) && (
                req.body.obj.name == null &&
                req.body.obj.email == null &&
                req.body.obj.phone_number == null)) {
            NotificationLog.aggregate([ 
                    {
                          $match: {
                              $or:[
                              {"created_at" : { 
                                $gte : new Date(req.body.obj.startDate+'T00:00:00Z'),
                                $lte:  new Date(req.body.obj.endDate+'T23:59:59Z') }
                               } 
                                // 'user_id': req.params.id,
                                // 'status': 'ACTIVE',
                                // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                                ]
                            }
                          
                    },
                    { $lookup:
                       {
                         from: 'vehicles',
                         localField: 'vehical_id',
                         foreignField: '_id',
                         as: 'vehicleDetails'
                       }
                     },
                      { $lookup:
                       {
                         from: 'users',
                         localField: 'user_id',
                         foreignField: '_id',
                         as: 'userDetails'
                       }
                     },
                     {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                    },
                     { 
                      "$sort": { 
                          "created_at": -1,
                      }
                      } 


                          ])
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))

      }

       else if ((req.body.obj.startDate == null && req.body.obj.endDate == null) && (
                req.body.obj.name != null ||
                req.body.obj.email != null ||
                req.body.obj.phone_number != null)) {
        console.log('dddddddddddddd')
              
              User.aggregate([ 
                   {
                    $match: {

                              $or:[                             
                                    {'name': {$regex : "^" + req.body.obj.name,$options: 'i'}},
                                    {'email': {$regex : "^" + req.body.obj.email,$options: 'i'}},
                                    {'phone_number': {$regex : "^" + req.body.obj.phone_number,$options: 'i'}},
                              ]
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      NotificationLog.aggregate([ 
                         {
                          $match: {
                                      'user_id': { $in: object }
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'vehicles',
                             localField: 'vehical_id',
                             foreignField: '_id',
                             as: 'vehicleDetails'
                           }
                         },
                          { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                         {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                        },
                         { 
                          "$sort": { 
                              "created_at": -1,
                          }
                          } 

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }


       else if ((req.body.obj.startDate != null && req.body.obj.endDate != null) && (
                req.body.obj.name != null ||
                req.body.obj.email != null ||
                req.body.obj.phone_number != null)) {
        console.log('aaaaaaaaaaaaaaaaa')
              
              User.aggregate([ 
                   {
                    $match: {

                              $or:[                             
                                    {'name': {$regex : "^" + req.body.obj.name,$options: 'i'}},
                                    {'email': {$regex : "^" + req.body.obj.email,$options: 'i'}},
                                    {'phone_number': {$regex : "^" + req.body.obj.phone_number,$options: 'i'}},
                              ]
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  // console.log('makers',makers)
                  makers.forEach(function (maker, k) {                
                    // console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      NotificationLog.aggregate([ 
                         {
                          $match: {
                                      "created_at" : { 
                                      $gte : new Date(req.body.obj.startDate+'T00:00:00Z'),
                                      $lte:  new Date(req.body.obj.endDate+'T23:59:59Z') 
                                      },
                                      'user_id': { $in: object }
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'vehicles',
                             localField: 'vehical_id',
                             foreignField: '_id',
                             as: 'vehicleDetails'
                           }
                         },
                          { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                         {
                          $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                        },
                         { 
                          "$sort": { 
                              "created_at": -1,
                          }
                          } 

                        ])
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }else{
          Responder.success(res,false)

      }


  }


  static getLogsForSelectedDate(req, res) {

    var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    var size = req.body.page_limit;
    // console.log('size--'+size)

    // console.log(req.body.obj.startDate)
    
        if ((req.body.obj.startDate != null && req.body.obj.endDate != null) && (
                req.body.obj.name == null &&
                req.body.obj.email == null &&
                req.body.obj.phone_number == null)) {
            NotificationLog.aggregate([ 
                    {
                          $match: {
                              $or:[
                              {"created_at" : { 
                                $gte : new Date(req.body.obj.startDate+'T00:00:00Z'),
                                $lte:  new Date(req.body.obj.endDate+'T23:59:59Z') }
                               } 
                                // 'user_id': req.params.id,
                                // 'status': 'ACTIVE',
                                // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                                ]
                            }
                          
                    },
                    { $lookup:
                       {
                         from: 'vehicles',
                         localField: 'vehical_id',
                         foreignField: '_id',
                         as: 'vehicleDetails'
                       }
                     },
                      { $lookup:
                       {
                         from: 'users',
                         localField: 'user_id',
                         foreignField: '_id',
                         as: 'userDetails'
                       }
                     },
                     { 
                      "$sort": { 
                          "created_at": -1,
                      }
                      } 


                          ]).skip(size * (pageNo - 1)).limit(size).sort({ 'created_at': -1 })
          .then((trc)=>Responder.success(res,trc))
          .catch((err)=>Responder.operationFailed(res,err))

      }

       else if ((req.body.obj.startDate == null && req.body.obj.endDate == null) && (
                req.body.obj.name != null ||
                req.body.obj.email != null ||
                req.body.obj.phone_number != null)) {
        // console.log('dddddddddddddd')
              
              User.aggregate([ 
                   {
                    $match: {

                              $or:[                             
                                    {'name': {$regex : "^" + req.body.obj.name,$options: 'i'}},
                                    {'email': {$regex : "^" + req.body.obj.email,$options: 'i'}},
                                    {'phone_number': {$regex : "^" + req.body.obj.phone_number,$options: 'i'}},
                              ]
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  makers.forEach(function (maker, k) {                
                    // console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      NotificationLog.aggregate([ 
                         {
                          $match: {
                                      'user_id': { $in: object }
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'vehicles',
                             localField: 'vehical_id',
                             foreignField: '_id',
                             as: 'vehicleDetails'
                           }
                         },
                          { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                         { 
                          "$sort": { 
                              "created_at": -1,
                          }
                          } 

                        ]).skip(size * (pageNo - 1)).limit(size).sort({ 'created_at': -1 })
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            // console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }


       else if ((req.body.obj.startDate != null && req.body.obj.endDate != null) && (
                req.body.obj.name != null ||
                req.body.obj.email != null ||
                req.body.obj.phone_number != null)) {
        console.log('aaaaaaaaaaaaaaaaa')
              
              User.aggregate([ 
                   {
                    $match: {

                              $or:[                             
                                    {'name': {$regex : "^" + req.body.obj.name,$options: 'i'}},
                                    {'email': {$regex : "^" + req.body.obj.email,$options: 'i'}},
                                    {'phone_number': {$regex : "^" + req.body.obj.phone_number,$options: 'i'}},
                              ]
                              
                            }
                        
                  }

                        ])
        
              .then((makers)=>{
                  var object= [];
                  console.log('makers',makers)
                  makers.forEach(function (maker, k) {                
                    console.log('ffffffffffffffff',maker._id)
                    object.push(ObjectId(maker._id))
                  });

                      NotificationLog.aggregate([ 
                         {
                          $match: {
                                      "created_at" : { 
                                      $gte : new Date(req.body.obj.startDate+'T00:00:00Z'),
                                      $lte:  new Date(req.body.obj.endDate+'T23:59:59Z') 
                                      },
                                      'user_id': { $in: object }
                                    
                                  }
                              
                        },
                        { $lookup:
                           {
                             from: 'vehicles',
                             localField: 'vehical_id',
                             foreignField: '_id',
                             as: 'vehicleDetails'
                           }
                         },
                          { $lookup:
                           {
                             from: 'users',
                             localField: 'user_id',
                             foreignField: '_id',
                             as: 'userDetails'
                           }
                         },
                         { 
                          "$sort": { 
                              "created_at": -1,
                          }
                          } 

                        ]).skip(size * (pageNo - 1)).limit(size).sort({ 'created_at': -1 })
                    .then((product)=>
              // {
                // console.log('ppppppppppp',product);
                // object.push(product.data)
                Responder.success(res,product)


              // }
                )
              .catch((err)=>Responder.operationFailed(res,err))

            // Responder.success(res,object)

            console.log('object',object); 
          }
          // Responder.success(res,trc)
          )
        .catch((err)=>Responder.operationFailed(res,err))
      }else{
          Responder.success(res,false)

      }


  }



}
