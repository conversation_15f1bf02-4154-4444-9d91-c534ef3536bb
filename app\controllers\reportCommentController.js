import Responder from '../../lib/expressResponder';
import ReportComment from '../models/reportComment';
import User from '../models/user';
import _ from "lodash";
import mongoose from 'mongoose';
var ObjectId= require('mongodb').ObjectId;


export default class ReportCommentController {

	static report(req, res) {
       ReportComment.create(req.body)
     .then((trip)=>Responder.success(res,trip))
     .catch((err)=>Responder.operationFailed(res,err))
    }

    


    static viewComments(req, res) {
        ReportComment.aggregate([ 
              {
                    $match: {
                        processed: false,
                        delete_status: false
                      }
                    
              },
              { $lookup:
                {
                  from: 'users',
                  localField: 'reprorted_to',
                  foreignField: '_id',
                  as: 'reprorted_to'
                }
                },

                { $lookup:
                  {
                    from: 'users',
                    localField: 'reprorted_by',
                    foreignField: '_id',
                    as: 'reprorted_by'
                  }
                },

                { $lookup:
                  {
                    from: 'ratereviews',
                    localField: 'rate_review_id',
                    foreignField: '_id',
                    as: 'rate_review'
                  }
                },
                { 
                "$sort": { 
                    "created_at": -1,
                } 
              },  
    
                    ])
		.then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
		.catch((err)=>Responder.operationFailed(res,err))

      }

      static viewCommentsProcessed(req, res) {
        ReportComment.aggregate([ 
              {
                    $match: {
                        processed: true,
                        delete_status: false
                      }
                    
              },
              { $lookup:
                {
                  from: 'users',
                  localField: 'reprorted_to',
                  foreignField: '_id',
                  as: 'reprorted_to'
                }
                },

                { $lookup:
                  {
                    from: 'users',
                    localField: 'reprorted_by',
                    foreignField: '_id',
                    as: 'reprorted_by'
                  }
                },

                { $lookup:
                  {
                    from: 'ratereviews',
                    localField: 'rate_review_id',
                    foreignField: '_id',
                    as: 'rate_review'
                  }
                },
                { 
                "$sort": { 
                    "created_at": -1,
                } 
              },  
    
                    ])
    .then((trip)=>{ console.log("dsjf0"); console.log(trip); Responder.success(res,trip);})
    .catch((err)=>Responder.operationFailed(res,err))

      }


  static removeReview(req, res) {
    console.log(req.params);
    ReportComment.deleteOne({ _id: req.params.id })
    .then((val) => Responder.success(res, val))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static update(req, res) {
    console.log(req.params.id);
    ReportComment.update({ _id: ObjectId(req.params.id) }, { $set: {processed:true,processedComment:req.body.processedComment} })
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
  }


    
  static checkReportedBy(req, res) {

    ReportComment.aggregate([ 
          {
                $match: {
                    'reprorted_by':ObjectId(req.body.user_id),
                    'rate_review_id':ObjectId(req.body.feedbackToId)
                  }
                
          },
          
             {
                $group: {
                      _id: {
                       
                      },
                        myCount: { $sum: 1 } ,
                    }
              }


                ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
   

  }


  

  static hideReview(req, res) {

    ReportComment.update({ _id: ObjectId(req.params.id) }, { $set: {delete_status:true} })
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
   

  }


  static reviewCount(req, res) {
      
       ReportComment.aggregate([ 
          {
                $match: {
                    processed:true,
                    delete_status:false
                  }
                
          },
          
             {
                $group: {
                      _id: {
                       
                      },
                        myCount: { $sum: 1 } ,
                    }
              }


                ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))   

  }
  
}
