import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const ReportCommentSchema = new Schema({
    
	reason:String,
	reprorted_by:ObjectId,
	reprorted_to:ObjectId,
    created_at: { type: Date, default: Date.now },
	rate_review_id:ObjectId,
	processed:{type:Boolean,default:false},
	processedComment:String,
	delete_status:{type:Boolean,default:false},
});
ReportCommentSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('ReportComment', ReportCommentSchema);
