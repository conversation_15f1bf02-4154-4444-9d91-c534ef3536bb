import express from 'express';
import PayoutController from '../controllers/payoutController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initpayoutRoutes = () => {
  const payoutRoutes = express.Router();

  payoutRoutes.get('/getReferalUsers',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.getReferalUsers);
  payoutRoutes.get('/getLatestRecord',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.getLatestRecord);
  payoutRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.create);
  payoutRoutes.put('/updateForUser/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,  PayoutController.updateForUser);
  payoutRoutes.get('/createPayout',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.createPayout);
  payoutRoutes.get('/getPayoutCreatedReport',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.getPayoutCreatedReport);
  payoutRoutes.get('/viewPayout/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.viewPayout);
  payoutRoutes.post('/submitForPay',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.submitForPay);
  payoutRoutes.get('/viewPayoutForReferralUser/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.viewPayoutForReferralUser);
  payoutRoutes.put('/addRemarks',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.addRemarks);
  payoutRoutes.post('/payoutCreatedReportForMonth',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.payoutCreatedReportForMonth);
  payoutRoutes.get('/getPayoutTotal',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.getPayoutTotal);
  payoutRoutes.post('/submitForRemarksForMultiple',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  PayoutController.submitForRemarksForMultiple);
 

 
  
  
  // payoutRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleMakerController.update);
  // payoutRoutes.put('/remove/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  VehicleMakerController.remove);
 

  return payoutRoutes;
};

export default initpayoutRoutes;
