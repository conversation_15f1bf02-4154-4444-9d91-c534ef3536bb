import express from 'express';
import UserMessageController from '../controllers/userMessageController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initUserMessageRoutes = () => {
  const userMessageRoutes = express.Router();

  userMessageRoutes.post('/message',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.show);
  userMessageRoutes.get('/usermessage/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.getAllUserpage);
  userMessageRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.create);
  userMessageRoutes.put('/:reportId',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  UserMessageController.update);
  userMessageRoutes.put('delete',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.remove);
  userMessageRoutes.post('/getSenderDetail',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.getSenderDetail);
  userMessageRoutes.post('/getRecieverDetail',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.getRecieverDetail);
  userMessageRoutes.post('/userMessageSeen/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.userMessageSeen);
  userMessageRoutes.post('/broadcastMessage/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.broadcastMessage);
  userMessageRoutes.post('/getBroadcastMessages/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  UserMessageController.getBroadcastMessages);





  // userMessageRoutes.post('/message', UserMessageController.show);
  // userMessageRoutes.get('/usermessage/:id', UserMessageController.getAllUserpage);
  // userMessageRoutes.post('/', UserMessageController.create);
  // userMessageRoutes.put('/:reportId', UserMessageController.update);
  // userMessageRoutes.delete('/:reportId', UserMessageController.remove);

  return userMessageRoutes;
};

export default initUserMessageRoutes;
