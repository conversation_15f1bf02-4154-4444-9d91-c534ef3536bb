import http from 'http';
const https = require('https');


const providers = [
    { //txtguru
        senderId: "SWARI"
    },
    { //"msgclud"
        senderId: "SWARI",
        templates: {
            1:"", //<PERSON>,
            2:"", //<PERSON>,
            3:"", //<PERSON>,
        }
    }
]

function chooseRandomProvider(body) {

  
}

// https://www.textguru.in/api/v22.0/?username=username&password=password&source=HeaderName&dmobile=918284047608,918284047606&dlttempid=templated_id&message=Approved_Template
///rest/services/sendSMS/sendGroupSms?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71 
function sendTXTGuruSMS(templateId, body) {


  console.log("Sending SMS to ", body.mobileNumbers);

  let mobileNumbers = body.mobileNumber;
  if(typeof mobileNumbers === "string") {
    mobileNumbers = mobileNumbers.length<11?"91"+mobileNumbers:mobileNumbers
  } 
  console.log({sms: body.smsContent});
    var extServerOptionsGet = {
      hostname: 'www.textguru.in',
      path: '/api/v22.0/?username=navpreet.au&password=Nav!@txt9&source='+body.senderId+'&dmobile='+body.mobileNumbers+'&dlttempid='+templateId+'&message='+body.smsContent,
      method: 'GET',
    };
    
    // let d = body.mobileNumbers.split(",");
    // let nums = '';
    // d.forEach((e){
    //   nums + = '91'+e;
    // })
    var extServerOptionsGet = {
      hostname: 'www.textguru.in',
      path: '/api/v22.0/?username=' + encodeURIComponent('navpreet.au') +
            '&password=' + encodeURIComponent('Nav!@txt9') +
            '&source=' + encodeURIComponent(body.senderId) +
            '&dmobile=91' + body.mobileNumbers +
            '&dlttempid=' + encodeURIComponent(templateId) +
            '&message=' + encodeURIComponent(body.smsContent),
      method: 'GET',
    };
    
    console.log(extServerOptionsGet);
    
    console.log(extServerOptionsGet);
    var reqGet = https.request(extServerOptionsGet, function (response) {
      let responseData = '';
    
      // Collect the data chunks
      response.on('data', function (chunk) {
        responseData += chunk;
      });
    
      // Handle the end of the response
      response.on('end', function () {
        console.log("TXTGURU SMS Sent ", responseData);
        // You can process the responseData here or send a response back to the client
        return;
      });
    });
    
    // Handle request errors
    reqGet.on('error', function (e) {
      console.error("TXTGURU SMS Send Error: ", e);
      return;
    });
    
    // End the request
    reqGet.end();
    
}

export default sendTXTGuruSMS;