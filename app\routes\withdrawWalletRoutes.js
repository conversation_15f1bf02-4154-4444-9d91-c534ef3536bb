import express from 'express';
import WithdrawWalletController from '../controllers/withdrawWalletController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');
console.log('withdrawroute1');

const initWithdrawWalletRoutes = () => {
  const withdrawWalletRoutes = express.Router();
  console.log('withdrawroute');

  withdrawWalletRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.create);
  withdrawWalletRoutes.post('/withdrawWalletGetDataForApprove',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForApprove);
  withdrawWalletRoutes.post('/withdrawWalletGetDataForRemmittes',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  WithdrawWalletController.withdrawWalletGetDataForRemmittes);
  withdrawWalletRoutes.post('/withdrawWalletGetDataForPaidToBank',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForPaidToBank);
  withdrawWalletRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.update);
  withdrawWalletRoutes.get('/getArroveAndRemittesLength/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.getArroveAndRemittesLength);
  withdrawWalletRoutes.get('/withdrawWalletGetDataForPaidToBankLength',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForPaidToBankLength);
  withdrawWalletRoutes.get('/withdrawWalletGetDataForRemmittesLength',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForRemmittesLength);
  withdrawWalletRoutes.get('/withdrawWalletGetDataForApproveLength',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForApproveLength);
  withdrawWalletRoutes.post('/sendEmailForWithdrawWallet',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.sendEmailForWithdrawWallet);
  withdrawWalletRoutes.post('/withdrawWalletGetDataForApproveCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.withdrawWalletGetDataForApproveCount);
  withdrawWalletRoutes.post('/filterPaidToBank',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.filterPaidToBank);
  withdrawWalletRoutes.post('/filterPaidToBankCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.filterPaidToBankCount);
  



  // withdrawWalletRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.create);
  // withdrawWalletRoutes.put('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.create);


  return withdrawWalletRoutes;
};

export default initWithdrawWalletRoutes;
