import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const NotificationLogSchema = new Schema({
  user_id: ObjectId,
  type:Number, //0 for mail, notfication, message
  status:Boolean,
  title:String,
  body:String,
  created_at: Date,
  send_by:Number, //0 for admin ,1 for subAdmin ,2 for normalUser
  state:String,
  city:String,
  other:String,
  totaluser:Number
  
});
 NotificationLogSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('NotificationLog', NotificationLogSchema);
