

angular.module('subscription.controllers', [])

    .controller('SubscriptionCtrl', function ($scope,APIService) {

    	var userData=localStorage.getItem('UserDeatails');
		var parsedUser= JSON.parse(userData);
		console.log(parsedUser)
		if (parsedUser.role != 'admin') {
		  localStorage.removeItem("UserDeatails");
		  localStorage.removeItem("token");
		  $state.go('login');
		}


        $scope.getProductDetails = function(){
            APIService.putData({ req_url: PrefixUrl + "/update/"+avc,data:BADFAMILY}).then(function (res) {
                console.log(res)
    
                $scope.expireProduct=res.data;
               
            
            },function(er){
             
            })
    
        }
       // $scope.getProductDetails();
    

    
})