import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const RateReviewSchema = new Schema({
    user_id: ObjectId,
    rated_by: ObjectId,
    review: String,
    rate: Number,
    created_at:{ type: Date, default: Date.now },
});
RateReviewSchema.plugin(timeZone, { paths: ['created_at'] });

export default mongoose.model('RateReview', RateReviewSchema);
