import Multer from 'multer'

const imageConfigStorage = Multer.diskStorage({
  destination: function (req, file, cb) {
      cb(null, './public/uploads/')
  },
  filename: function (req, file, cb) {
      var datetimestamp = Date.now();
      if(file.mimetype == 'image/png' || file.mimetype == 'uploads/png')
        cb(null, `${file.fieldname}-${datetimestamp}.png`)
      else
        cb(null, file.fieldname + '-' + datetimestamp + '.' + file.originalname.split('.')[file.originalname.split('.').length -1])
  }
});

export default class MulterService {
  static uploadImage(req, res) {
    return new Promise((resolve, reject) => {
      try {
        let uploadSingle = Multer({ storage: imageConfigStorage }).single('file');
        uploadSingle(req, res, (err) => err ? reject(err) : resolve(req.file));  
      } catch (error) {
        console.log(error);
      }
    });
  }
}
