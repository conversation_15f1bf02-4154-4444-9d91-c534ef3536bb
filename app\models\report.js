import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const ReportSchema = new Schema({
    user_id: ObjectId,
    reported_by: ObjectId,
    report_message: String,
    time: { type: Date, default: Date.now },
	processed:{type:Boolean,default:false},
	processedComment:String,
	delete_status:{type:Boolean,default:false},
	

});
ReportSchema.plugin(timeZone, { paths: ['time'] });

export default mongoose.model('Report', ReportSchema);
