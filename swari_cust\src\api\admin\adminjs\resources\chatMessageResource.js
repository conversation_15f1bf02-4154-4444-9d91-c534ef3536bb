/**
 * Chat Message Resource for AdminJS
 * Defines the resource configuration for chat messages
 * PRD Reference: Sections 4.4, 10.2, 10.4
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import ChatMessage from '../../../../models/ChatMessage.js';
import Customer from '../../../../models/Customer.js';
import Driver from '../../../../models/Driver.js';
import Trip from '../../../../models/Trip.js';
import logger from '../../../../utils/logger.js';
import mqttService from '../../../../services/mqttService.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register media Url Show component
const mediaUrlShowComponent = componentLoader.add(
  'mediaUrlShow', 
  join(__dirname, '../components/media-url-show.jsx') // Path to your React component
);

// Register chat By Trip component
const chatByTripComponent = componentLoader.add(
  'chatByTrip', 
  join(__dirname, '../components/chat-by-trip.jsx') // Path to your component
);
// Register chatMonitor component
const chatMonitorComponent = componentLoader.add(
  'chatMonitor', 
  join(__dirname, '../components/chat-monitor.jsx') // Path to your React component
);




/**
 * Format user information for display
 * @param {Object} userId - User ID
 * @param {String} type - User type (customer, driver)
 * @returns {String} - Formatted user information
 */
const formatUserInfo = async (userId, type) => {
  try {
    if (!userId) return 'N/A';
    
    let user;
    if (type === 'customer') {
      user = await Customer.findById(userId).lean();
    } else if (type === 'driver') {
      user = await Driver.findById(userId).lean();
    } else {
      return 'Unknown';
    }
    
    if (!user) return 'User not found';
    
    return `${user.name || 'Unknown'} (${type})`;
  } catch (error) {
    logger.error('Error formatting user info', { error: error.message });
    return 'Error';
  }
};

/**
 * Chat Message resource configuration
 */
const chatMessageResource = {
  resource: ChatMessage,
  options: {
    id: 'chat-messages',
    navigation: {
      name: 'Chat',
      icon: 'Message',
    },
    listProperties: [
      'trip_id',
      'sender_type',
      'content',
      'read',
      'created_at'
    ],
    showProperties: [
      'trip_id',
      'sender_id',
      'receiver_id',
      'sender_type',
      'content',
      'media_url',
      'read',
      'created_at'
    ],
    filterProperties: [
      'trip_id',
      'sender_type',
      'read',
      'created_at'
    ],
    properties: {
      trip_id: {
        position: 1,
        isTitle: true,
      },
      sender_id: {
        position: 2,
      },
      receiver_id: {
        position: 3,
      },
      sender_type: {
        position: 4,
        availableValues: [
          { value: 'customer', label: 'Customer' },
          { value: 'driver', label: 'Driver' },
        ],
      },
      content: {
        position: 5,
        type: 'textarea',
      },
      media_url: {
        position: 6,
        components: {
          show: mediaUrlShowComponent,
        },
      },
      read: {
        position: 7,
        type: 'boolean',
      },
      created_at: {
        position: 8,
        type: 'datetime',
      },
    },
    actions: {
      new: {
        isAccessible: false, // Only create through API
      },
      edit: {
        isAccessible: false, // Chat messages should not be edited
      },
      delete: {
        isAccessible: false, // Chat messages should not be deleted
      },
      // Custom action to view chat history by trip
      viewChatByTrip: {
        actionType: 'resource',
        component: chatByTripComponent,
        handler: async (request, response, context) => {
          try {
            const { tripId, limit = 50, skip = 0 } = request.query;
            
            // If no tripId provided, return list of trips with chat messages
            if (!tripId) {
              const trips = await ChatMessage.aggregate([
                { $group: { _id: '$trip_id', messageCount: { $sum: 1 }, lastMessage: { $max: '$created_at' } } },
                { $sort: { lastMessage: -1 } },
                { $limit: 20 }
              ]);
              
              const tripsWithDetails = await Promise.all(trips.map(async (trip) => {
                const tripDetails = await Trip.findById(trip._id).lean();
                return {
                  _id: trip._id,
                  messageCount: trip.messageCount,
                  lastMessage: trip.lastMessage,
                  tripDetails: tripDetails || { pickup_location: 'Unknown', dropoff_location: 'Unknown' }
                };
              }));
              
              return { trips: tripsWithDetails };
            }
            
            // Get chat messages for the specified trip
            const messages = await ChatMessage.find({ trip_id: tripId })
              .sort({ created_at: -1 })
              .skip(parseInt(skip))
              .limit(parseInt(limit))
              .lean();
            
            // Get trip details
            const trip = await Trip.findById(tripId).lean();
            
            // Enhance messages with user information
            const enhancedMessages = await Promise.all(messages.map(async (message) => {
              const senderInfo = await formatUserInfo(message.sender_id, message.sender_type);
              const receiverInfo = await formatUserInfo(message.receiver_id, 
                message.sender_type === 'customer' ? 'driver' : 'customer');
              
              return {
                ...message,
                senderInfo,
                receiverInfo
              };
            }));
            
            return {
              messages: enhancedMessages,
              trip,
              pagination: {
                total: await ChatMessage.countDocuments({ trip_id: tripId }),
                limit: parseInt(limit),
                skip: parseInt(skip)
              }
            };
          } catch (error) {
            logger.error('Error retrieving chat messages', { error: error.message });
            return { error: error.message };
          }
        },
      },
      // Custom action to view real-time chat monitoring
      monitorChat: {
        actionType: 'resource',
        component: chatMonitorComponent,
        handler: async (request, response, context) => {
          return {}; // Component will handle MQTT subscription
        },
      },
      // Custom action to get latest messages for real-time monitoring
      getLatestMessages: {
        actionType: 'resource',
        isVisible: false, // Hidden from UI, used by chat-monitor component
        handler: async (request, response, context) => {
          try {
            // Get the latest messages (last 10 minutes)
            const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
            
            const messages = await ChatMessage.find({
              created_at: { $gte: tenMinutesAgo }
            })
              .sort({ created_at: -1 })
              .limit(20)
              .lean();
            
            // Enhance messages with user information
            const enhancedMessages = await Promise.all(messages.map(async (message) => {
              const senderInfo = await formatUserInfo(message.sender_id, message.sender_type);
              const receiverInfo = await formatUserInfo(message.receiver_id, 
                message.sender_type === 'customer' ? 'driver' : 'customer');
              
              return {
                ...message,
                senderInfo,
                receiverInfo
              };
            }));
            
            return { messages: enhancedMessages };
          } catch (error) {
            logger.error('Error retrieving latest chat messages', { error: error.message });
            return { error: error.message };
          }
        },
      },
    },
  },
};

export default chatMessageResource;