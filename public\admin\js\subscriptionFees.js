angular.module('subscriptionFees.controllers', [])

    .controller('subscriptionFeesCtrl', function ($scope, APIService, $state,$rootScope) {

        $scope.updatedata ={};
        // $scope.updatedata.gst = '28';
        $scope.adminData =JSON.parse(localStorage.getItem("UserDeatails"));
        console.log(localStorage.getItem("UserDeatails"));
        console.log($scope.adminData)

            var parsedUser;

            APIService.setData({ req_url: PrefixUrl + "/settings/createType/" ,
                        data:{type:'app_settings'},
                    }).then(function (res) {
                console.log(res)
                // $scope.userDetails= res.data;
                // $scope.setTransactions(user.wallet_balance,$scope.payout_id,user._id);
            
            },function(er){

            })

            APIService.setData({ req_url: PrefixUrl + "/settings/getSetting/"}).then(function (res) {
                console.log(res)
                $scope.updatedata= res.data;
                // $scope.pendingTripsData=res.data;

            },function(er){
             
            })

            APIService.setData({
                req_url: PrefixUrl + '/user/getCityList'  
            }).then(function(resp) {
              $scope.cityList= resp.data;
            },function(resp) {
              // This block execute in case of error.
               // $scope.logout = function() {
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
              console.log('error')
            });

            // var userData=localStorage.getItem('UserDeatails');
            // var parsedUser= JSON.parse(userData);
            // console.log(parsedUser.user_details)
            // if (parsedUser == null || parsedUser.user_details.role != 'admin') {
            //   localStorage.removeItem("UserDeatails");
            //   localStorage.removeItem("token");
            //   $state.go('login');
            // }




        if ($scope.adminData.user_details) {
            console.log('a')

            $scope.updatedata.tripBoostFeesByAdmin=$scope.adminData.user_details.tripBoostFeesByAdmin;
            $scope.updatedata.subscriptionFeesByAdmin=$scope.adminData.user_details.subscriptionFeesByAdmin;
            $scope.updatedata.id = $scope.adminData.user_details._id;
            $scope.updatedata.gst = $scope.adminData.user_details.gst;
            $scope.updatedata.gstAmount = $scope.adminData.user_details.gstAmount;
            $scope.updatedata.boostTripGst = $scope.adminData.user_details.boostTripGst;
            $scope.updatedata.boostTripGstAmount = $scope.adminData.user_details.boostTripGstAmount;
            $scope.updatedata.freetrialperiod = $scope.adminData.user_details.freetrialperiod;
            $scope.updatedata.billingEmail = $scope.adminData.user_details.billingEmail
            $scope.updatedata.noReplyEmail = $scope.adminData.user_details.noReplyEmail
            $scope.updatedata.contactEmail = $scope.adminData.user_details.contactEmail
            $scope.updatedata.appVersion = $scope.adminData.user_details.appVersion
            

        }else{
            console.log('b')

            $scope.updatedata.tripBoostFeesByAdmin=$scope.adminData.tripBoostFeesByAdmin;
            $scope.updatedata.subscriptionFeesByAdmin=$scope.adminData.subscriptionFeesByAdmin;
            $scope.updatedata.id = $scope.adminData._id;
            $scope.updatedata.gst = $scope.adminData.gst;
            $scope.updatedata.gstAmount = $scope.adminData.user_details.gstAmount;
            $scope.updatedata.boostTripGst = $scope.adminData.boostTripGst;
            $scope.updatedata.boostTripGstAmount = $scope.adminData.user_details.boostTripGstAmount;
            $scope.updatedata.freetrialperiod = $scope.adminData.freetrialperiod;
            $scope.updateData.billingEmail = $scope.adminData.billingEmail;
            $scope.updateData.noReplyEmail = $scope.adminData.noReplyEmail;
            $scope.updateData.contactEmail = $scope.adminData.contactEmail;
            $scope.updateData.appVersion = $scope.adminData.appVersion;


        }

        $scope.updateForFees = function(){
           console.log('updatedata [[[',$scope.updatedata)
            if( $scope.updatedata.tripBoostFeesByAdmin != null &&
                  $scope.updatedata.subscriptionFeesByAdmin != null &&
                   $scope.updatedata.gst != null &&
                   $scope.updatedata.gstAmount != null &&
                   $scope.updatedata.boostTripGst != null &&
                   $scope.updatedata.boostTripGstAmount != null &&
                   $scope.updatedata.freetrialperiod != null &&
                   $scope.updatedata.appVersion != null
                   ){

                        APIService.updateData({ req_url: PrefixUrl + "/settings/update/",data:$scope.updatedata}).then(function (res) {
                        console.log(res)
                        var userData={}; 
                        userData.user_details= res.data;
                          // localStorage.setItem('UserDeatails', JSON.stringify(userData));

            
                        $scope.expireProduct=res.data;
                        $scope.updatedata ={};
                        alert("Settings Updated Successfully.")
                        location.reload(); 

                    
                    },function(er){
                     
                    })

            }else{
                alert("Please fill all the fields.")
            }
    
        }
      //  $scope.updateForFees();
   
})