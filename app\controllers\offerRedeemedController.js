import Responder from '../../lib/expressResponder';
import OfferRedeemed from '../models/offerRedeemed';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class OfferRedeemedController {

	// redeemOffer
	//  user_id: ObjectId,
    // offer_id: ObjectId,
    // user_city: String,
    // coupon_code: String,
    // created_at:{ type: Date, default: Date.now },
    // redeemed_date: Date
	static redeemOffer(req, res) {
	  	console.log('offer to save -- ',req.body)
	 	

	  	   function myPromise(){
			     return new Promise((resolve, reject) => {
			      
			        let couponCode = "";
			        var possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
			                console.log('000000000000  ');  

			        for (var i = 0; i < 8; i++){
			          couponCode += possible.charAt(Math.floor(Math.random() * possible.length));
			        }
			        
			        OfferRedeemed.count({'couponCode':couponCode}).exec()
			    .then(function(count) {

			      if(count>0){
			            console.log('000000000foundddd'  ,count + '  '+couponCode);  

			          myPromise();
			        }else{
			            console.log('1111111111111111222  ',couponCode);  
			            	req.body.couponCode= couponCode;
			            	req.body.created_at= new Date();
			            	// req.body.redeemed_date= new Date();

					  	  OfferRedeemed.create(req.body)
					        .then((trip)=>Responder.success(res,trip))
					        .catch((err)=>Responder.operationFailed(res,err))
			     

			        }

			      })
			    })
			}

			myPromise();

	 	
     
  	}


  	static getRedeemOffer(req, res) {
	    OfferRedeemed.aggregate([  
	              {
	                $match: {
	                        user_id:ObjectId(req.body.user_id)
	                        }
	                    
	              },

	                { $lookup:
	                 {
	                   from: 'offers',
	                   localField: 'offer_id',
	                   foreignField: '_id',
	                   as: 'offerList'
	                 }
				   },
				   
	                 
	               { 
	                "$sort": { 
	                    "created_at": -1,
	                } 
	              }, 

	                    ])
	    .then((trc)=>Responder.success(res,trc))
	    .catch((err)=>Responder.operationFailed(res,err))
	}
	static getViewRedeemOffer(req, res) {

		var pageNo= req.body.currentPage + 1;
        console.log('pageNo--'+pageNo)
        var size = req.body.page_limit;
		console.log('size--'+size)
		var search = {};
		
		if(req.body.city && req.body.state)
		{
			 search = {
				$match: {
					user_city: {$regex : "^" + req.body.city,$options: 'i'},
					user_state: {$regex : "^" + req.body.state,$options: 'i'}
					

				}
			}
		}


		else if ((req.body.startDate  && req.body.endDate ) &&
		(!req.body.city  && !req.body.state 
		)){
			console.log('11111111111');

			search = {
				$match: {
							"created_at": {
									"$gte": new Date(req.body.startDate+'T00:00:00Z') ,
									"$lte": new Date(req.body.endDate+'T23:59:59Z') }
						  }
					  
				} 
			}
		
		else if (req.body.city && !req.body.state)
		{
			search = {
				$match: {
					user_city: {$regex : "^" + req.body.city,$options: 'i'}
			
				}
			}
		}
		else if (req.body.state && !req.body.city)
		{
			search = {
				$match: {
					user_state: {$regex : "^" + req.body.state,$options: 'i'}
			
				}
			}
		}
		else{
			search = {
				$match: {
				}
			
			}
		}
		console.log('search',search);
	    OfferRedeemed.aggregate([  
			

				search,
			
			{ $lookup:
				{
				  from: 'users',
				  localField: 'user_id',
				  foreignField: '_id',
				  as: 'userDetails'
				}
			  },
			  { $lookup:
	                 {
	                   from: 'offers',
	                   localField: 'offer_id',
	                   foreignField: '_id',
	                   as: 'offerList'
	                 }
				   },
				   
			{
				 $group: {
					  _id: {
					   
							offer_id:"$offer_id",
					  
								  
					   
					  },
					  books: { $push: "$$ROOT" }, 
					
						
						myCount: { $sum: 1 } ,
						
					}
			}

	                    ]).skip(size * (pageNo - 1)).limit(size)
						.then((trc)=>Responder.success(res,trc))
						.catch((err)=>Responder.operationFailed(res,err))

					
	}




	

	static checkRedeemOffer(req, res) {
	    OfferRedeemed.aggregate([ 
	              {
	                $match: {
	                        user_id:ObjectId(req.body.user_id),
	                        offer_id:ObjectId(req.body.offer_id)
	                        }
	                    
	              },
	              {
                       $group: {
                            _id: {
                              
                            },
                            myCount: { $sum: 1 } ,
                          }
                  },

	                 
			])
	    .then((count)=>
              {
				var counts={};
                console.log('count  ++++++++ ',count)
                if (count.length > 0) {
                  if (count[0].myCount) {
                    counts.total_redeemed= count[0].myCount;
                  }
                }else{
                    counts.total_redeemed= 0;
                  }
                Responder.success(res,counts)
              })
	    .catch((err)=>Responder.operationFailed(res,err))
	}

}
