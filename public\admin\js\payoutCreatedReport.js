angular.module('payoutCreatedReport.controllers', [])

    .controller('payoutCreatedReportCtrl', function ($scope, APIService, $state,$rootScope) {

            $scope.exportAsExcel = function () {
              console.log('1111111111')
                var blob = new Blob([document.getElementById('exportable').innerHTML], {
                    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                });
                saveAs(blob, new Date().toISOString().slice(0, 10)+"payout.xls")
                
            };
            // $scope.items = [{
            //     "Name": "ANC101",
            //     "Date": "10/02/2014",
            //     "Terms": ["samsung", "nokia", "apple"]
            // }, {
            //     "Name": "ABC102",
            //     "Date": "10/02/2014",
            //     "Terms": ["motrolla", "nokia", "iPhone"]
            // }]

        $scope.getPayoutCreatedReport = function(){
            APIService.getData({ req_url: PrefixUrl + "/payout/getPayoutCreatedReport"}).then(function (res) {
           console.log('result')
           console.log(res)
           $scope.payoutReport= res.data;

            $scope.totalPayout= 0;
            $scope.payoutReport.forEach(function (payout, k) {
            console.log(payout)                
                $scope.totalPayout= $scope.totalPayout + payout.total;
              });
            
            },function(er){

            })

    
        }


        
        $scope.viewPayout = function(payout_id){
            $state.go("app.viewPayout",{data:JSON.stringify(payout_id)})

		}

        $scope.getPayoutCreatedReport();

    });