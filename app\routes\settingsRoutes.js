import express from 'express';
import SettingsController from '../controllers/settingsController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initsettingsRoutes = () => {
  const settingsRoutes = express.Router();

  settingsRoutes.post('/createType',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, SettingsController.createType);
  settingsRoutes.put('/update/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, SettingsController.update);
  settingsRoutes.post('/getSetting/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, SettingsController.getSetting);


  return settingsRoutes;
};

export default initsettingsRoutes;
