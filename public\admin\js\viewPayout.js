angular.module('viewPayout.controllers', [])

    .controller('viewPayoutCtrl', function ($scope, APIService, $state,$rootScope,$stateParams) {

        $scope.processing= false;
        $scope.report= {};
        $scope.report.remarks1= null;

    
        $scope.exportAsExcel = function () {
          console.log('1111111111')
            var blob = new Blob([document.getElementById('exportable').innerHTML], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
            });
            saveAs(blob, new Date().toISOString().slice(0, 10)+"viewPayout.xls")
            
        };


        $scope.checkItem = function(checked,obj){
            console.log('checkstatus  '+obj)
          if (!angular.isArray($scope.checkboxList)){
            $scope.checkboxList = [];
          }
          if (-1 === $scope.checkboxList.indexOf(obj)){
           
            $scope.checkboxList.push(obj);
            // $scope.checkboxList.push(value);
            console.log($scope.checkboxList)
          }
        }


        
        $scope.selectAll = function(checked){
            console.log('checkstatus  ',checked)
          $scope.checkboxList= [];
          if (checked) {
            $scope.payoutReport.forEach(function (payout, k) {
              $scope.checkboxList.push(payout._id);
            })
          }else{

          }
        }        

        $scope.payout_id= JSON.parse($stateParams.data);
        console.log('$scope.payout_id'+$scope.payout_id)
        // $scope.viewPayout($scope.payout_id);
       
        // $scope.viewPayout = function(payout_id){
            APIService.getData({ req_url: PrefixUrl + "/payout/viewPayout/"+ $scope.payout_id}).then(function (res) {
           console.log('result')
           console.log(res)
           $scope.payoutReport= res.data;
            if (res.data[0].remarks) {
              // $scope.report.remarks= res.data[0].remarks;
            }
            $scope.totalPayout= 0;
            $scope.paymentGiven= 0;
            $scope.payoutReport.forEach(function (payout, k) {
            console.log(payout)                
                $scope.totalPayout= $scope.totalPayout + payout.amount;
                if (payout.status) {
                  $scope.paymentGiven= $scope.paymentGiven + payout.amount;
                }
              });
            
            $scope.totalPendingPayment= $scope.totalPayout - $scope.paymentGiven;
            
            },function(er){

            })

    
        // }

        $scope.viewTransactions = function(payout_id,user_id){
          var obj= {};
          obj.payout_id= payout_id;
          obj.user_id= user_id;
          $state.go("app.viewPayoutTransactions",{data:JSON.stringify(obj)})
        };

        $scope.submit = function(){
           if (confirm("Are you sure")){
              if ($scope.checkboxList.length == 0){
                alert("Please select at least one row");

                 
              }else{
                console.log('aaaaaaaaaaaaaaa ');
                   // $scope.processing= true;
                    APIService.setData({ req_url: PrefixUrl + "/payout/submitForPay" ,data:{list:$scope.checkboxList}}).then(function (res) {
                   console.log('result')
                   console.log(res);
                   $scope.payoutReport= res.data;
                      alert("Payout Given successfully");
                      location.reload();
                    },function(er){

                    })

              }
          }

        }


        $scope.submitForRemarksForMultiple = function(){
           if (confirm("Are you sure")){
            // console.log('rrrrrrrrrr  ',$scope.report)
              if (!$scope.report.remarks1 && !$scope.report.remarks || !$scope.checkboxList){
                alert("Please add remarks and select at least one row");

                 
              }else{
                console.log('aaaaaaaaaaaaaaa ');
                   // $scope.processing= true;
                    APIService.setData({ req_url: PrefixUrl + "/payout/submitForRemarksForMultiple" ,data:{list:$scope.checkboxList,remarks:$scope.report.remarks1}}).then(function (res) {
                   console.log('result')
                   console.log(res);
                   // $scope.payoutReport= res.data;
                      alert("Payout Updated successfully");
                      location.reload();
                    },function(er){

                    })

              }
          }

        }


        
        $scope.addRemarks = function(_id,remarks,user_id,payout_id){
          console.log('ffffffffff',remarks)
           if (confirm("Are you sure")){
              APIService.updateData({ req_url: PrefixUrl + "/payout/addRemarks" ,data:{_id:_id,remarks: remarks}}).then(function (res) {
             console.log('result')
             console.log(res);

                APIService.setData({ req_url: PrefixUrl + "/trancsaction/LastTransactionForPayout" ,data:{user_id:user_id,payout_id: payout_id,remarks:remarks}}).then(function (res) {

                },function(er){

                })


                location.reload();
              },function(er){

              })
          }

        }



    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

    }

});