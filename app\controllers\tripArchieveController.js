import Responder from '../../lib/expressResponder';
import TripArchieve from '../models/tripArchieve';
import Trip from '../models/trip';
import Vehicle from '../models/vehicle';
import User from '../models/user';
import UserMessage from '../models/userMessage';
import _ from "lodash";
const { sendNotification } = require('./helpers/push_notification');
var ObjectId = require('mongodb').ObjectId;
import mongoose from 'mongoose';
var moment = require('moment-timezone');

// import nodemailer from 'nodemailer';

// var freetrialperiod=moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
// console.log('freetrialperiod--- ',freetrialperiod)

// // var currentDate=moment(new Date()).format('YYYY-MM-DDTHH:mm:ss.SSSZ');
// // console.log('currentDatenew--- ',currentDate)

// freetrialperiod=moment(freetrialperiod).format('YYYY-MM-DDTHH:mm:ss.SSSZ');
// console.log('freetrialperiod1111--- ',freetrialperiod)

const cron = require('node-cron');

var timeNow = new Date().getTime();
var oneDayInPast = new Date(timeNow - 1000 * 60 * 60 * 24).toISOString().slice(0, 10); //one month before

console.log('dateeeeee', oneDayInPast)
// var task = cron.schedule('*/5 * * * * *', () => { //run after every five seconds
// var task = cron.schedule('0 1 * * *', () => { // run at 1 am every night 
//   // console.log('Printing this line every minute in the terminal');
//   // showAllVehicleByUsers();
//         // Trip.update({ status:"ACCEPTED",time:{"$lt": new Date(new Date().toISOString().slice(0, 10)) }},{$set:{status:"DONE"}},{multi: true}).
//         // Trip.update({ status:"ACCEPTED",time:{"$lt": oneDayInPast }},{$set:{status:"DONE"}},{multi: true}).
//         Trip.update({ $or:[{status:"ACCEPTED"}],time:{$lt:new Date(new Date().toISOString().slice(0, 10)) }},{$set:{status:"DONE"}},{multi: true}).
//          then((result) =>{
//           console.log('resultiiiiiiiiii',result)

//          });


// });

function distance(lat1lon1, lat2lon2) {
  var R = 6371; // Radius of the earth in km
  var dLat = deg2rad(lat2lon2.lat - lat1lon1.lat);  // deg2rad below
  var dLon = deg2rad(lat2lon2.lng - lat1lon1.lng);
  var a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1lon1.lat)) * Math.cos(deg2rad(lat2lon2.lat)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
    ;
  var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  var d = R * c; // Distance in km
  return d;
}

// var transporter = nodemailer.createTransport({
//   service: 'gmail',
//   auth: {
//     user: '<EMAIL>',
//     pass: 'RabbiBani'
//   }
// });


function deg2rad(deg) {
  return deg * (Math.PI / 180)
}

export default class TripController {

  static page(req, res) {
    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];


    Trip.find({}).sort([['time', -1]])
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      // .then((veh)=> {vehicle=veh; console.log("#############"); ar1 = _.uniq(_.map(trips,'booking_request')); console.log(ar1,'aaaa'); ar2 = _.uniq(_.map(trips,'user_id')); console.log(ar2,'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3,'aaaa');  return User.find({_id:{$in:ar3}})})
      .then((veh) => { vehicle = veh; console.log("------8------", _.map(trips, 'user_id')); return User.find({ _id: { $in: _.flatten(_.map(trips, 'user_id')) } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          console.log('aa' + user);
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at,
            is_trip_boost: trip.is_trip_boost
          };

          var owner = [];

          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address,
              buisness_name: us.buisness_name
            };

            console.log(userDe);
            // if(trp.booking_request==userDe._id){
            if (trp.booking_request) {

              owner.push(userDe);
              trp.ownerDetails = owner;
            }
            // console.log(trp.booking_request)
            // if(trp.booking_request.indexOf(userDe._id)>-1){
            if (trp.user_id.indexOf(userDe._id) > -1) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }

          })

          // _.each(vehicle,vehi=>{

          //   if(trp && vehi._id == trp.vehical_id){
          //     trp.vehicleDetails=vehi;
          //   }
          // }) 
          response.push(trp); console.log("------11-------", response);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }

  static showTripByVehicleId(req, res) {

    Trip.find({ vehical_id: ObjectId(req.body.vehicleid) })
      .then((trip) => Responder.success(res, trip))
      .catch((err) => Responder.operationFailed(res, err))
  }

  static getCounts(req, res) {
    var counts = {};
    console.log("dfjgkrte" + req.params.id);
    Trip.count({ $or: [{ user_id: req.params.id }, { booking_request: req.params.id }], status: 'ACCEPTED', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, trip_type: 'trip' }, function (err, count) {
      if (err) console.log("problem~~~~~~~~1" + err);
      counts.upcoming = count;
      console.log('there are %d upcoming trips', count);
      Trip.count({ user_id: req.params.id, status: 'ACTIVE', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, trip_type: 'trip' }, function (err, count) {
        if (err) console.log("problem~~~~~~~~2" + err)
        counts.pending = count;
        console.log('there are %d pending', count);
        // Trip.count({user_id:req.params.id,booking_request:{ "$nin": [ null, "" ] },status:'ACTIVE',time:{$gt:new Date().toISOString().slice(0, 10)}},function(err,count){
        Trip.count({ user_id: req.params.id, status: 'ACTIVE', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, booking_request: { "$nin": [null, ObjectId()] }, trip_type: 'trip' }, function (err, count) {
          if (err) console.log("problem~~~~~~~~3" + err)
          counts.book = count;
          console.log('there are %d booking_request', count);
          Trip.count({ $and: [{ booking_request: req.params.id }, { status: 'ACTIVE' }, { time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() } }], trip_type: 'trip' }, function (err, count) {
            if (err) console.log("problem~~~~~~~~4" + err)
            counts.sent = count;
            console.log('there are %d sent request', count);
            Trip.count({ status: 'CANCELLED', cancel_request_by: req.params.id, time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, trip_type: 'trip' }, function (err, count) {
              if (err) console.log("problem~~~~~~~~5" + err)
              counts.cancel_sent = count;
              console.log('there are %d sent cancel request', count);
              Trip.count({ status: 'CANCELLED', cancel_request_by: { $ne: req.params.id }, $or: [{ user_id: req.params.id }, { booking_request: req.params.id }], time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() } }, function (err, count) {
                if (err) console.log("problem~~~~~~~~5" + err)
                counts.cancel_rec = count;
                console.log('there are %d received cancel request', count);
                UserMessage.count({
                  $and: [
                    {
                      $or: [
                        { senderId: req.params.id },
                        { recieverId: req.params.id },
                      ]
                    },
                    { myId: { $ne: req.params.id } },
                    { seen: false }
                  ]
                }, function (err, count) {
                  if (err) console.log("message count~~~~~~~~5" + err)
                  counts.message = count;
                  console.log('there are %d received messages', count);
                  Trip.count({ user_id: req.params.id, status: 'ACTIVE', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, booking_request: { "$nin": [null, ObjectId()] }, trip_type: 'passenger' }, function (err, count) {
                    counts.passenger_book = count;
                    Trip.count({ $and: [{ booking_request: req.params.id }, { status: 'ACTIVE' }, { time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() } }], trip_type: 'passenger' }, function (err, count) {
                      counts.passenger_sent = count;
                      Trip.count({ $or: [{ user_id: req.params.id }, { booking_request: req.params.id }], status: 'ACCEPTED', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, trip_type: 'passenger' }, function (err, count) {
                        counts.passenger_upcoming = count;
                        Trip.count({ user_id: req.params.id, status: 'ACTIVE', time: { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() }, trip_type: 'passenger' }, function (err, count) {
                          counts.passenger_pending = count;
                          Responder.success(res, counts);
                        });
                      });
                    });
                  });
                });
              });
            });
          });
        });
      });
    });
  }

  static bookRequestUpcoming(req, res) {

    // Trip.find({$or:[{user_id:req.params.id},{booking_request:req.params.id}],status:'ACCEPTED',time:{$gt:new Date().toISOString().slice(0, 10)}}).sort([['time', 1]])

    Trip.aggregate([
      {
        $match: {
          $or: [
            // 'user_id': req.params.id,

            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },

          ],
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'status': 'ACCEPTED',
          'trip_type': 'trip',



        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'ownerDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        "$sort": {
          "time": 1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }

  static bookRequestUpcomingPassenger(req, res) {

    // Trip.find({$or:[{user_id:req.params.id},{booking_request:req.params.id}],status:'ACCEPTED',time:{$gt:new Date().toISOString().slice(0, 10)}}).sort([['time', 1]])

    Trip.aggregate([
      {
        $match: {
          $or: [
            // 'user_id': req.params.id,

            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },

          ],
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'status': 'ACCEPTED',
          'trip_type': 'passenger',



        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'ownerDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        "$sort": {
          "time": 1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }

  static bookRequset(req, res) {

    // Trip.find( { $and: [{booking_request:req.params.id},{status:'ACTIVE'}]})
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'user_id': ObjectId(req.params.id),
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'booking_request': { "$nin": [null, ""] },
          'trip_type': 'trip',

          // 'booking_request':ObjectId(req.params.id)

          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }

  static bookRequsetPassenger(req, res) {

    // Trip.find( { $and: [{booking_request:req.params.id},{status:'ACTIVE'}]})
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'user_id': ObjectId(req.params.id),
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'booking_request': { "$nin": [null, ""] },
          'trip_type': 'passenger',

          // 'booking_request':ObjectId(req.params.id)

          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }

  static show(req, res) {
    var trips = [];
    var user = [];
    var vehicle = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    console.log('dddddddddddd' + new Date().toISOString().slice(0, 10));
    console.log('dddddddddddd' + new Date().toISOString());


    // user_id:req.params.id,status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}
    console.log()
    Trip.aggregate([
      {
        $match: {
          // $and:[
          'user_id': ObjectId(req.params.id),
          'status': 'ACTIVE',
          // 'time':{$gte:new Date(Date.now())}
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip',

          // ]
        }

      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'vehicletypes',
          localField: 'category',
          foreignField: '_id',
          as: 'vehicleType'
        }
      }, {
        '$sort': {
          'time': 1
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

    // return pending trips
    // Trip.find({user_id:req.params.id,status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}})
    //   .then((tr)=> {trips=tr;  return Vehicle.find({_id:{$in:_.map(tr,'vehical_id')}})})
    //   .then((veh)=> {vehicle=veh; console.log("------8------",_.map(trips,'user_id')); return User.find({_id:{$in:_.flatten( _.map(trips,'user_id'))}})})

    //   // .then((veh)=> {vehicle=veh; console.log("#############"); ar1 = _.uniq(_.map(trips,'booking_request')); console.log(ar1,'aaaa'); ar2 = _.uniq(_.map(trips,'user_id')); console.log(ar2,'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3,'aaaa');  return User.find({_id:{$in:ar3}})})
    //   .then((usr) => {user=usr; var response=[]; 
    //     _.each(trips,trip=>{
    //       var trp={_id:trip._id,
    //         vehical_id:trip.vehical_id,
    //         user_id:trip.user_id,
    //         time:trip.time,
    //         reg_no:trip.reg_no,
    //         origin:trip.origin,
    //         destination:trip.destination,
    //         address:trip.address,
    //         status:trip.status,
    //         address:trip.address,
    //         accepted_user_id:trip.accepted_user_id,
    //         booking_request:trip.booking_request,
    //         created_at:trip.created_at,
    //         is_trip_boost:trip.is_trip_boost
    //       };

    //       var owner=[];
    //       console.log(user);
    //       var bookUser=[];  _.each(user,us=>{
    //         var userDe={_id:us._id,
    //           phone_number:us.phone_number,
    //           name:us.name,
    //           pincode:us.pincode,
    //           email:us.email,
    //           profile_image_url:us.profile_image_url,
    //           address:us.address,
    //           buisness_name:us.buisness_name

    //         };

    //         console.log(trp.booking_request)
    //           if(trp.booking_request==userDe._id){

    //             bookUser.push(userDe);
    //             trp.userDetails=bookUser;

    //           }
    //           if(trp.user_id==userDe._id){
    //             owner.push(userDe);
    //             trp.ownerDetails=owner;              
    //           }



    //       }) 

    //       _.each(vehicle,vehi=>{
    //         console.log('vehicleid---')
    //         console.log(trp.vehical_id)

    //         if(trp && vehi._id == trp.vehical_id){
    //           trp.vehicleDetails=vehi;
    //         }
    //       }) 
    //       response.push(trp);
    //     }) ;Responder.success(res,response)

    //     })
    //   .catch((err)=>Responder.operationFailed(res,err))

    // .then((tr)=> {trips=tr; return Vehicle.find({_id:{$in:_.map(tr,'vehical_id')}})})
    // .then((tr)=>{var response=[];_.each(trips,trip =>{
    //   var trp={_id:trip._id,
    //     vehical_id:trip.vehical_id,
    //     user_id:trip.user_id,
    //     time:trip.time,
    //     reg_no:trip.reg_no,
    //     origin:trip.origin,
    //     destination:trip.destination,
    //     address:trip.address,
    //     status:trip.status,
    //     address:trip.address,
    //     accepted_user_id:trip.accepted_user_id,
    //     booking_request:trip.booking_request,
    //     created_at:trip.created_at,
    //     is_trip_boost:trip.is_trip_boost
    //   };

    //   _.each(tr,vehi=>{

    //     if(trp && vehi._id == trp.vehical_id){

    //       trp.vehicleDetails=vehi;


    //     }
    //   }) 
    //   response.push(trp);
    // });Responder.success(res,response) })
    // .catch((err)=>Responder.operationFailed(res,err))

  }



  static showPassenger(req, res) {
    var trips = [];
    var user = [];
    var vehicle = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    console.log('dddddddddddd' + new Date().toISOString().slice(0, 10));
    console.log('time@@@1', moment.utc().startOf('day').toISOString());
    console.log('time@@@2', moment.utc().format('YYYY-MM-DDTHH:mm:ss.SSS'));
    console.log('time@@@3', moment.utc().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'));
    console.log('time@@@4', moment().format('YYYY-MM-DDTHH:mm:ss.SSS'));
    console.log('time@@@5', moment().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'));
    console.log('time@@@6', moment(moment().startOf('day')).toISOString());
    console.log('time@@@7', moment.utc().toDate());
    console.log('time@@@8', moment.utc().add(5.5, 'hours').startOf('day').toDate());
    console.log('time@@@9', moment(moment.utc().add(5.5, 'hours').startOf('day')).toDate());
    // console.log('time@@@8',moment(moment.).toDate());

    // user_id:req.params.id,status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}
    console.log()
    Trip.aggregate([
      {
        $match: {
          // $and:[
          'user_id': ObjectId(req.params.id),
          'status': 'ACTIVE',
          // 'time':{$gte:new Date(Date.now())}
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          // 'time':{$gte:moment.utc().startOf('day').toDate()},
          'trip_type': 'passenger',

          // ]
        }

      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'vehicletypes',
          localField: 'category',
          foreignField: '_id',
          as: 'vehicleType'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }


  static search(req, res) {


    let searchQuery = {};
    if (req.body.from_date) {
      searchQuery.from_date = moment(req.body.from_date).add(5.5, 'hours').toDate();

      // searchQuery.from_date = new Date(req.body.from_date);
    }
    if (req.body.to_date) {
      searchQuery.to_date = moment(req.body.to_date).add(5.5, 'hours').toDate();

      // searchQuery.to_date = new Date(req.body.to_date);
    }

    // if(req.body.time){
    //  searchQuery.time=req.body.time;
    // }


    console.log(req.body.ac);
    console.log("!!!!!!!!!!!!!", searchQuery);
    var trips = [];
    var user = [];


    var pageNo = req.body.currentPage;
    console.log('pageNo--' + pageNo)
    var size = 3;
    console.log('size--' + size)

    Trip.aggregate([
      {
        $geoNear: {
          near: { type: "Point", coordinates: [req.body.origin_location.lng, req.body.origin_location.lat] },
          spherical: true,
          distanceField: "calcDistance",
          maxDistance: 20000

        }
      },
      {
        $match: {
          time: { $gt: searchQuery.from_date, $lte: searchQuery.to_date },
          status: "ACTIVE",
          trip_type: 'trip',
          user_id: { $ne: ObjectId(req.body.user_id) }
        }

      },

      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicle'
        }
      },

      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $skip: size * (pageNo - 1)
      },
      {
        $limit: size
      }

    ])

      // Trip.find({time:{$gte:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",is_trip_boost:false})
      //  Trip.find({time:{$gt:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",trip_type:'trip' ,   user_id: { $ne: ObjectId(req.body.user_id) }     })
      //  .then((tr)=> {
      //    trips=tr;

      //   // return Vehicle.find({_id:{$in:_.map(trips,'vehical_id')}})
      //      return(Vehicle.aggregate([ 
      //            {
      //                  $match: {
      //                    }

      //            },

      //              { $lookup:
      //               {
      //                 from: 'users',
      //                 localField: 'user_id',
      //                 foreignField: '_id',
      //                 as: 'userDetails'
      //               }
      //             },
      //            { $lookup:
      //               {
      //                 from: 'vehiclemakers',
      //                 localField: 'vehical_make',
      //                 foreignField: '_id',
      //                 as: 'vehicleMaker'
      //               }
      //             },
      //              { $lookup:
      //               {
      //                 from: 'vehiclemodels',
      //                 localField: 'vehical_model',
      //                 foreignField: '_id',
      //                 as: 'vehicleModel'
      //               }
      //             }


      //                  ])
      //      )
      // })
      .then((tr) => {
        var response = []; _.each(tr, trip => {
          //console.log('tr')

          console.log('get trip list')

          console.log(trip)

          var originUnder20 = false;
          var destinationUnder20 = false;
          _.each(trip.addressLatLng, addresses => {
            console.log('addresses--', addresses.lng);
            var addressArray = [];
            addressArray['lat'] = addresses.lat;
            addressArray['lng'] = addresses.lng;
            console.log(distance(req.body.origin_location, addressArray) + addresses.name);
            if (distance(req.body.origin_location, addressArray) <= 20) {
              originUnder20 = true;
            }
            if (distance(req.body.destination_location, addressArray) <= 20) {
              destinationUnder20 = true;
            }
            if (distance(trip.origin_location, req.body.origin_location) > distance(trip.origin_location, req.body.destination_location)) {
              console.log(distance(trip.origin_location, req.body.origin_location) + "left");
              console.log(distance(trip.origin_location, req.body.destination_location) + "right");
              console.log("I AM FALSE");
              originUnder20 = false;
              destinationUnder20 = false;
            }

          });

          console.log("distance--------11aaaassss" + originUnder20);
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            address: trip.address,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at,
            originAroundDistance: distance(req.body.origin_location, trip.origin_location),
            destinationAroundDistance: distance(req.body.destination_location, trip.destination_location),
            trip_by_phone_no: trip.trip_by_phone_no,
            is_trip_boost: trip.is_trip_boost,
            vehicleMaker: trip.vehicleMaker,
            vehicleModel: trip.vehicleModel,
            userDetails: trip.userDetails,
            vehicleDetails: trip.vehicle

          };

          // console.log(trip);
          //trp.vehicleDetails = trip.vehicle;  

          if ((trp.originAroundDistance < 20 || originUnder20) && (trp.destinationAroundDistance < 20 || destinationUnder20)) {
            //    _.each(tr,vehi=>{
            //     console.log('veh')
            //     // console.log(vehi)
            //   // if(trp && vehi._id == trp.vehical_id){
            //   if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
            //     console.log('trueee-----')
            //     trp.vehicleDetails=vehi;
            //   }else{
            //      // trp.vehicleDetails=vehi;
            //     console.log('false')
            //   }

            // }) 

            console.log('check trp..', trp);
            console.log(">>");
            // console.log(req.body.ac);
            console.log(">>" + req.body.number_of_seats + '-----' + trp.vehicleDetails[0].no_of_seats);
            // req.body.ac

            if (req.body.number_of_seats <= trp.vehicleDetails[0].no_of_seats) {

              if (req.body.ac == "true" && req.body.carrier == "true") {
                console.log(trp.vehicleDetails[0].ac);
                if (trp.vehicleDetails[0].ac && trp.vehicleDetails[0].carrier) {
                  console.log("$$  " + '1111');
                  response.push(trp);
                }
                else {
                  console.log('ac and carrier both required');
                }

              }
              else if (req.body.ac == "true" && req.body.carrier == "false") {
                console.log("ac check  " + '', trp.vehicleDetails[0].ac);

                if (trp.vehicleDetails[0].ac) {
                  console.log("$$  " + '22222');

                  response.push(trp);
                }
                else {
                  console.log('yes ac but not found and no carrier requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "true") {
                if (trp.vehicleDetails[0].carrier) {
                  console.log("$$  " + '3333333');

                  response.push(trp);
                }
                else {
                  console.log('yes carrier but not found and no ac requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "false") {
                // console.log(trp.vehicleDetails.ac);
                // if(trp.vehicleDetails.ac == "false" && trp.vehicleDetails.carrier == "false"){
                console.log("$$  " + '44444');
                response.push(trp);
                // }
                // else{
                // console.log('no ac and no carrier not found');
                // }

              }
              else {
                response.push(trp);
                console.log('no  carrier req  and no ac requied');

              }
            }
            // console.log(trp.vehicleDetails.ac);
            // if(!trp.vehicleDetails.ac){
            //   console.log("$$");  
            //   response.push(trp);
            // }

            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);

            // }

          } else if (!trp.destinationAroundDistance && (trp.originAroundDistance < 20 || originUnder20)) {
            //    _.each(tr,vehi=>{
            //     console.log('vehi1')
            //     // console.log(vehi)
            //   if(trp && vehi._id == trp.vehical_id){
            //     trp.vehicleDetails=vehi;
            //   }
            // })
            // req.body.ac
            //  trp.vehicleDetails = tr.vehicle;
            if (req.body.ac == "true") {
              // console.log(trp.vehicleDetails.ac);
              if (trp.vehicleDetails[0].ac) {
                console.log("$$");
                response.push(trp);
              }
            } else {
              // console.log(trp.vehicleDetails.ac);
              if (!trp.vehicleDetails[0].ac) {
                console.log("$$");
                response.push(trp);
              }
            }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          } else if (!trp.destinationAroundDistance && !trp.originAroundDistance) {
            // _.each(tr,vehi=>{
            //        if(trp && vehi._id == trp.vehical_id){
            //          trp.vehicleDetails=vehi;
            //        }
            //      }) 
            //trp.vehicleDetails = tr.vehicle;

            // req.body.ac
            if (req.body.ac == "true") {
              console.log(trp.vehicleDetails.ac);
              if (trp.vehicleDetails.ac) {
                console.log("$$");
                response.push(trp);
              }
            } else {
              console.log(trp.vehicleDetails.ac);
              if (!trp.vehicleDetails.ac) {
                console.log("$$");
                response.push(trp);
              }
            }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          }
        });

        Responder.success(res, _.sortBy(response, [function (o) { return o.originAroundDistance; }]))
      })
      .catch((err) => Responder.operationFailed(res, err))


  }



  static searchLocal(req, res) {
    let searchQuery = {};
    if (req.body.from_date) {
      // searchQuery.from_date = moment(req.body.from_date).add(5.5,'hours').toDate();
      searchQuery.from_date = moment.utc(req.body.from_date).startOf('day').toDate();
      //searchQuery.from_date = from_date.toDate();

      // searchQuery.from_date = new Date(req.body.from_date);
    }
    if (req.body.to_date) {
      // searchQuery.to_date = moment(req.body.to_date).add(5.5,'hours').toDate();
      searchQuery.to_date = moment.utc(req.body.to_date).endOf('day').toDate();
      //moment(new Date(req.body.to_date)).format('YYYY-MM-DDTHH:mm:ss.SSSZ');
      // searchQuery.to_date =new Date(req.body.to_date);
    }

    // if(req.body.time){
    //  searchQuery.time=req.body.time;
    // }
    console.log('@@@@@@@@@ 0', moment(req.body.to_date).toDate())
    console.log('@@@@@@@@@ 1', moment(req.body.from_date).startOf('day').toDate())
    console.log('@@@@@@@@@ 2', moment(req.body.to_date).endOf('day').toDate())
    console.log('@@@@@@@@@ 3', moment(req.body.from_date).add(5.5, 'hours').toDate())
    console.log('@@@@@@@@@ 4', moment.utc(req.body.to_date).endOf('day').toDate())
    console.log('@@@@@@@@@ 5', moment.utc(req.body.from_date).add(5.5, 'hours').toDate())


    console.log(req.body.ac);
    console.log("!!!!!!!!!!!!!", searchQuery);
    var trips = [];
    var user = [];


    var pageNo = req.body.currentPage;
    console.log('pageNo--' + pageNo)
    var size = 3;
    console.log('size--' + size)


    Trip.aggregate([
      {
        $geoNear: {
          near: { type: "Point", coordinates: [req.body.origin_location.lng, req.body.origin_location.lat] },
          spherical: true,
          distanceField: "calcDistance",
          maxDistance: 20000

        }
      },
      {
        $match: {
          time: { $gt: searchQuery.from_date, $lte: searchQuery.to_date },
          status: "ACTIVE",
          trip_type: 'trip',
          user_id: { $ne: ObjectId(req.body.user_id) }
        }

      },

      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicle'
        }
      },

      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $skip: size * (pageNo - 1)
      },
      {
        $limit: size
      }


    ])

      // Trip.find({time:{$gte:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",is_trip_boost:false})
      //  Trip.find({time:{$gt:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",trip_type:'trip' ,   user_id: { $ne: ObjectId(req.body.user_id) }     })
      //  .then((tr)=> {
      //    trips=tr;

      //   // return Vehicle.find({_id:{$in:_.map(trips,'vehical_id')}})
      //      return(Vehicle.aggregate([ 
      //            {
      //                  $match: {
      //                    }

      //            },

      //              { $lookup:
      //               {
      //                 from: 'users',
      //                 localField: 'user_id',
      //                 foreignField: '_id',
      //                 as: 'userDetails'
      //               }
      //             },
      //            { $lookup:
      //               {
      //                 from: 'vehiclemakers',
      //                 localField: 'vehical_make',
      //                 foreignField: '_id',
      //                 as: 'vehicleMaker'
      //               }
      //             },
      //              { $lookup:
      //               {
      //                 from: 'vehiclemodels',
      //                 localField: 'vehical_model',
      //                 foreignField: '_id',
      //                 as: 'vehicleModel'
      //               }
      //             }


      //                  ])
      //      )
      // })
      .then((tr) => {
        var response = []; _.each(tr, trip => {
          //console.log('tr')

          console.log('get trip list')

          console.log(trip)

          var originUnder20 = false;
          var destinationUnder20 = false;
          _.each(trip.addressLatLng, addresses => {
            console.log('addresses--', addresses.lng);
            var addressArray = [];
            addressArray['lat'] = addresses.lat;
            addressArray['lng'] = addresses.lng;
            console.log(distance(req.body.origin_location, addressArray) + addresses.name);
            if (distance(req.body.origin_location, addressArray) <= 20) {
              originUnder20 = true;
            }
            if (distance(req.body.destination_location, addressArray) <= 20) {
              destinationUnder20 = true;
            }
            if (distance(trip.origin_location, req.body.origin_location) > distance(trip.origin_location, req.body.destination_location)) {
              console.log(distance(trip.origin_location, req.body.origin_location) + "left");
              console.log(distance(trip.origin_location, req.body.destination_location) + "right");
              console.log("I AM FALSE");
              originUnder20 = false;
              destinationUnder20 = false;
            }

          });

          console.log("distance--------11aaaassss" + originUnder20);
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            address: trip.address,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at,
            originAroundDistance: distance(req.body.origin_location, trip.origin_location),
            destinationAroundDistance: distance(req.body.destination_location, trip.destination_location),
            trip_by_phone_no: trip.trip_by_phone_no,
            is_trip_boost: trip.is_trip_boost,
            vehicleMaker: trip.vehicleMaker,
            vehicleModel: trip.vehicleModel,
            userDetails: trip.userDetails,
            vehicleDetails: trip.vehicle

          };

          // console.log(trip);
          //trp.vehicleDetails = trip.vehicle;  

          if ((trp.originAroundDistance < 20 || originUnder20) && (trp.destinationAroundDistance < 20 || destinationUnder20)) {
            //    _.each(tr,vehi=>{
            //     console.log('veh')
            //     // console.log(vehi)
            //   // if(trp && vehi._id == trp.vehical_id){
            //   if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
            //     console.log('trueee-----')
            //     trp.vehicleDetails=vehi;
            //   }else{
            //      // trp.vehicleDetails=vehi;
            //     console.log('false')
            //   }

            // }) 

            console.log('check trp..', trp);
            console.log(">>");
            // console.log(req.body.ac);
            console.log(">>" + req.body.number_of_seats + '-----' + trp.vehicleDetails[0].no_of_seats);
            // req.body.ac

            if (req.body.number_of_seats <= trp.vehicleDetails[0].no_of_seats) {

              if (req.body.ac == "true" && req.body.carrier == "true") {
                console.log(trp.vehicleDetails[0].ac);
                if (trp.vehicleDetails[0].ac && trp.vehicleDetails[0].carrier) {
                  console.log("$$  " + '1111');
                  response.push(trp);
                }
                else {
                  console.log('ac and carrier both required');
                }

              }
              else if (req.body.ac == "true" && req.body.carrier == "false") {
                console.log("ac check  " + '', trp.vehicleDetails[0].ac);

                if (trp.vehicleDetails[0].ac) {
                  console.log("$$  " + '22222');

                  response.push(trp);
                }
                else {
                  console.log('yes ac but not found and no carrier requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "true") {
                if (trp.vehicleDetails[0].carrier) {
                  console.log("$$  " + '3333333');

                  response.push(trp);
                }
                else {
                  console.log('yes carrier but not found and no ac requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "false") {
                // console.log(trp.vehicleDetails.ac);
                // if(trp.vehicleDetails.ac == "false" && trp.vehicleDetails.carrier == "false"){
                console.log("$$  " + '44444');
                response.push(trp);
                // }
                // else{
                // console.log('no ac and no carrier not found');
                // }

              }
              else {
                response.push(trp);
                console.log('no  carrier req  and no ac requied');

              }
            }
            // console.log(trp.vehicleDetails.ac);
            // if(!trp.vehicleDetails.ac){
            //   console.log("$$");  
            //   response.push(trp);
            // }

            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);

            // }

          } else if (!trp.destinationAroundDistance && (trp.originAroundDistance < 20 || originUnder20)) {
            //    _.each(tr,vehi=>{
            //     console.log('vehi1')
            //     // console.log(vehi)
            //   if(trp && vehi._id == trp.vehical_id){
            //     trp.vehicleDetails=vehi;
            //   }
            // })
            // req.body.ac
            //  trp.vehicleDetails = tr.vehicle;
            if (req.body.ac == "true") {
              // console.log(trp.vehicleDetails.ac);
              if (trp.vehicleDetails[0].ac) {
                console.log("$$");
                response.push(trp);
              }
            } else {
              // console.log(trp.vehicleDetails.ac);
              if (!trp.vehicleDetails[0].ac) {
                console.log("$$");
                response.push(trp);
              }
            }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          } else if (!trp.destinationAroundDistance && !trp.originAroundDistance) {
            // _.each(tr,vehi=>{
            //        if(trp && vehi._id == trp.vehical_id){
            //          trp.vehicleDetails=vehi;
            //        }
            //      }) 
            //trp.vehicleDetails = tr.vehicle;

            // req.body.ac
            if (req.body.ac == "true") {
              console.log(trp.vehicleDetails.ac);
              if (trp.vehicleDetails.ac) {
                console.log("$$");
                response.push(trp);
              }
            } else {
              console.log(trp.vehicleDetails.ac);
              if (!trp.vehicleDetails.ac) {
                console.log("$$");
                response.push(trp);
              }
            }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          }
        });

        Responder.success(res, _.sortBy(response, [function (o) { return o.originAroundDistance; }]))
      })
      .catch((err) => Responder.operationFailed(res, err))


  }

  static searchPassenger(req, res) {
    let searchQuery = {};
    if (req.body.from_date) {
      // searchQuery.from_date = new Date(req.body.from_date);
      // searchQuery.from_date = moment(req.body.from_date).add(5.5,'hours').toDate();
      searchQuery.from_date = moment.utc(req.body.from_date).startOf('day').toDate();



    }
    if (req.body.to_date) {
      // searchQuery.to_date = new Date(req.body.to_date);
      // searchQuery.to_date = moment(req.body.to_date).add(5.5,'hours').toDate();
      searchQuery.to_date = moment.utc(req.body.to_date).endOf('day').toDate();

    }

    // if(req.body.time){
    //  searchQuery.time=req.body.time;
    // }


    console.log(req.body.ac);
    console.log("!!!!!!!!!!!!!", searchQuery);
    var trips = [];
    var user = [];
    // Trip.find({time:{$gte:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",is_trip_boost:false})


    //Paginations params
    var pageNo = req.body.currentPage;
    console.log('pageNo--' + pageNo)
    var size = 3;
    console.log('size--' + size)


    Trip.aggregate([
      {
        $geoNear: {
          near: { type: "Point", coordinates: [req.body.origin_location.lng, req.body.origin_location.lat] },
          spherical: true,
          distanceField: "calcDistance",
          maxDistance: 20000

        }
      },
      {
        $match: {
          time: { $gt: searchQuery.from_date, $lte: searchQuery.to_date },
          status: "ACTIVE",
          trip_type: 'passenger',
          user_id: { $ne: ObjectId(req.body.user_id) }
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $skip: size * (pageNo - 1)
      },
      {
        $limit: size
      }
    ])
      // .then((tr) =>
      //       {
      //         trips=tr;
      //       }
      //       )
      // .then((tr)=>{
      //            console.log('trrrrr ',tr)
      //          })


      //  Trip.find({time:{$gt:searchQuery.from_date,$lte:searchQuery.to_date},status:"ACTIVE",trip_type:'passenger' ,   user_id: { $ne: ObjectId(req.body.user_id) }     })
      //  .then((tr)=> {
      //    console.log('!!!#@@',tr);
      //    trips=tr;

      //   // return Vehicle.find({_id:{$in:_.map(trips,'vehical_id')}})
      //      return(User.aggregate([ 
      //            {
      //                  $match: {
      //                    "_id":tr.
      //                    }

      //            },

      //              { $lookup:
      //               {
      //                 from: 'users',
      //                 localField: 'user_id',
      //                 foreignField: '_id',
      //                 as: 'userDetails'
      //               }
      //             }



      //                  ])
      //      )
      // })
      .then((tr) => {
        var response = []; _.each(tr, trip => {
          console.log('tr')
          console.log(trip)

          var originUnder20 = false;
          var destinationUnder20 = false;
          _.each(trip.addressLatLng, addresses => {
            console.log(addresses.lng);
            var addressArray = [];
            addressArray['lat'] = addresses.lat;
            addressArray['lng'] = addresses.lng;
            console.log(distance(req.body.origin_location, addressArray) + addresses.name);
            if (distance(req.body.origin_location, addressArray) <= 20) {
              originUnder20 = true;
            }
            if (distance(req.body.destination_location, addressArray) <= 20) {
              destinationUnder20 = true;
            }
            if (distance(trip.origin_location, req.body.origin_location) > distance(trip.orign_location, req.body.destination_location)) {
              console.log(distance(trip.origin_location, req.body.origin_location) + "left");
              console.log(distance(trip.origin_location, req.body.destination_location) + "right");
              console.log("I AM FALSE");
              originUnder20 = false;
              destinationUnder20 = false;

            }
            if (distance(trip.origin_location, req.body.origin_location) && (!req.body.destination_location)) {
              originUnder20 = true;
              console.log('orign_location-- ')

            }
            console.log('address Check-- ')

          });

          console.log("distance--------11aaaassss" + originUnder20);
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            address: trip.address,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at,
            ac: trip.ac,
            carrier: trip.carrier,
            number_of_passengers: trip.number_of_passengers,
            preferred_vehicle: trip.preferred_vehicle,
            originAroundDistance: distance(req.body.origin_location, trip.origin_location),
            destinationAroundDistance: distance(req.body.destination_location, trip.destination_location),
            trip_by_phone_no: trip.trip_by_phone_no,
            is_trip_boost: trip.is_trip_boost,
            userDetails: trip.userDetails


          };

          console.log('trips!@!!', trp.originAroundDistance);


          if ((trp.originAroundDistance < 20 || originUnder20) && (trp.destinationAroundDistance < 20 || destinationUnder20)) {
            _.each(tr, vehi => {
              console.log('veh')
              console.log(vehi)
              // if(trp && vehi._id == trp.vehical_id){
              // if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
              //   console.log('trueee-----')
              //   trp.vehicleDetails=vehi;
              // }else{
              //    // trp.vehicleDetails=vehi;
              //   console.log('false')
              // }

            })
            // console.log(trp);
            console.log(">>");
            // console.log(req.body.ac);
            console.log(">>");
            // req.body.ac

            // if(req.body.ac=="true" && req.body.carrier =="true" ){
            //   console.log(trp.ac);
            //   if(trp.ac && trp.carrier){
            //     console.log("$$  "+'1111');  
            //     response.push(trp);
            //   }
            //   else{
            //     console.log('ac and carrier both required');
            //   }

            // }
            // else if(req.body.ac=="true" && req.body.carrier =="false")
            // {
            //   if(trp.ac){
            //     console.log("$$  "+'22222');  

            //     response.push(trp);
            //   }
            //   else{
            //     console.log('yes ac but not found and no carrier requied');
            //   }

            // }
            // else if(req.body.ac=="false" && req.body.carrier =="true")
            // {
            //   if(trp.carrier){
            //     console.log("$$  "+'3333333');  

            //     response.push(trp);
            //   }
            //   else{
            //     console.log('yes carrier but not found and no ac requied');
            //   }

            // }
            // else if(req.body.ac=="false" && req.body.carrier =="false" ){
            //   // console.log(trp.vehicleDetails.ac);
            //   // if(trp.vehicleDetails.ac == "false" && trp.vehicleDetails.carrier == "false"){
            //     console.log("$$  "+'44444');  
            //     response.push(trp);
            //   // }
            //   // else{
            //     // console.log('no ac and no carrier not found');
            //   // }

            // }
            // else{
            //    response.push(trp);
            response.push(trp);

            //    console.log('no  carrier req  and no ac requied');

            // }

            //   console.log(trp.vehicleDetails.ac);
            //   if(!trp.vehicleDetails.ac){
            //     console.log("$$");  
            //     response.push(trp);
            //   }

            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);

            // }

          } else if ((trp.originAroundDistance < 20 || originUnder20) && (req.body.destination == '')) {
            _.each(tr, vehi => {
              console.log('vehi1')
              // console.log(vehi)
              // if(trp && vehi._id == trp.vehical_id){
              //   trp.vehicleDetails=vehi;
              // }
            })
            // req.body.ac
            // if(req.body.ac=="true"){
            //   // console.log(trp.vehicleDetails.ac);
            //   if(trp.ac){
            //     console.log("$$");  
            //     response.push(trp);
            //   }
            // }else{
            //   // console.log(trp.vehicleDetails.ac);
            //   if(!trp.ac){
            //     console.log("$$");  
            response.push(trp);
            //   }
            // }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          } else if (!trp.destinationAroundDistance && !trp.originAroundDistance) {
            _.each(tr, vehi => {
              // if(trp && vehi._id == trp.vehical_id){
              //   trp.vehicleDetails=vehi;
              // }
            })
            // req.body.ac
            // if(req.body.ac=="true"){
            //   // console.log(trp.vehicleDetails.ac);
            //   if(trp.ac){
            //     console.log("$$");  
            //     response.push(trp);
            //   }
            // }else{
            //   // console.log(trp.vehicleDetails.ac);
            //   if(!trp.ac){
            //     console.log("$$");  
            response.push(trp);
            //   }
            // }
            // if(typeof(req.body.ac)=='undefined'){
            //   response.push(trp);
            // }


          }
        });

        Responder.success(res, _.sortBy(response, [function (o) { return o.originAroundDistance; }]))
      })
      .catch((err) => Responder.operationFailed(res, err))
  }

  static create(req, res) {
    console.log({ create_archice: req.body});
    req.body.time = moment(req.body.time).subtract(5.5, 'hours').toDate();
    console.log('req.body.time@@@@  ', req.body.time);
    req.body.created_at = moment(new Date()).toDate();
    req.body.booking_request_sent = moment(new Date()).toDate();
    req.body.booking_request_accept = moment(new Date()).toDate();
    req.body.cancel_request_accept = moment(new Date()).toDate();
    req.body.cancel_request_sent = moment(new Date()).toDate();
    req.body.canceled_at = moment(new Date()).toDate();

    if (req.body.trip_type == 'passenger') {
      Trip.create(req.body)
        .then((trip) => {
          // Responder.success(res,trip) 
          // .catch((err)=>Responder.operationFailed(res,err))
          // Responder.success(res,trip)

          User.aggregate([
            {
              $geoNear: {
                near: { type: "Point", coordinates: [req.body.origin_location.lng, req.body.origin_location.lat] },
                spherical: true,
                distanceField: "calcDistance",
                maxDistance: 50000

              }
            },
            {
              $match: {
                notification: true,
              }
            }
          ])
            .then((users) => {
              console.log('trip response--- ', users);
              var fcm_tocken = [];
              users.forEach(function (user, k) {
                // fcm_tocken.push(user.fcm_registration_token);

              const data = {
                title: 'New Passenger', // REQUIRED for Android
                topic: 'New Passenger', // REQUIRED for iOS (apn and gcm)                
                body: req.body.number_of_passengers + ' Passengers from ' + req.body.origin + ' to ' + req.body.destination,
                custom: {
                  sender: 'Triva',
                },                  
                priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn
                sound: 'jb',
                android_channel_id: 'test_channel',                
              };

            sendNotification([user.fcm_registration_token],data, user.notification_sound);
                      
            });
            Responder.success(res, 'success')
            })
            .catch((err) => Responder.operationFailed(res, 'catch222' + err))
        })
      // Responder.success(res,result) 

    } else {
      // req.body.time = moment.utc(req.body.time).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      // req.body.time = moment(req.body.time).subtract(5.5, 'hours').toDate();

      console.log('trip_time@@@1', moment.utc(req.body.time).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'))
      console.log('trip_time@@@2', moment(req.body.time).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'));
      console.log('trip_time@@@3', moment(req.body.time).subtract(5.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSS'));
      console.log('trip_time@@@4', moment.utc(req.body.time).subtract(5.5, 'hours').format('YYYY-MM-DDTHH:mm:ss.SSS'));
      console.log('trip_time@@@5', moment(req.body.time).format('YYYY-MM-DDTHH:mm:ss.SSS'));
      console.log('trip_time@@@6', moment(req.body.time));
      console.log('trip_time7', moment.utc(req.body.time).toDate());
      console.log('trip_time8', moment(req.body.time).subtract(5.5, 'hours').toDate());
      console.log('trip_time9', moment(req.body.time).toDate());
      // console.log('trip_time@@@',req.body)
      // console.log('trip_time@@@',req.body)

      Trip.create(req.body)
        .then((trip) => {
          Responder.success(res, trip)
        }
        )
        .catch((err) => Responder.operationFailed(res, err))
    }
  }

  static updateForBookingReq(req, res) {
    console.log(req.params)
    req.body.booking_request_sent = moment(new Date()).add(5.5, 'hours');
    Trip.findOneAndUpdate({ _id: req.params.id }, { 'booking_request': req.body.booking_request, 'booking_request_sent': req.body.booking_request_sent })
      .then((val) => {
        Trip.find({ _id: req.params.id }, function (err, docs) {
          console.log('docs1111111111111111111111111');
          console.log(docs);


          // var userArr={};
          // userArr.id_list[0]= req.params.id;
          // userArr.id_list[1]= req.body.booking_request;
          User.find({ _id: ObjectId(req.body.user_id) }, function (err, docs1) {
            console.log('docs1' + docs1)
            if (docs1) {
              var body_text = "received";
              var trip_type;
              if (req.body.trip_type == "trip") {
                trip_type = 'Trip';
              } else {
                trip_type = 'Passenger';

              }

              const data = {
                title: trip_type + ' Booking Request Received', // REQUIRED for Android
                topic: trip_type + ' Booking Request Received', // REQUIRED for iOS (apn and gcm)
                /* The topic of the notification. When using token-based authentication, specify the bundle ID of the app. 
                 * When using certificate-based authentication, the topic is usually your app's bundle ID.
                 * More details can be found under https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/sending_notification_requests_to_apns
                 */
                body: 'Trip from ' + docs[0].origin + ' to ' + docs[0].destination + ' on ' + docs[0].time + ' is ' + body_text,
                custom: {
                  sender: 'Triva',
                },          
                priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn
                sound: 'jb',
                android_channel_id: 'test_channel', // Custom sound file without extension       
              };
              sendNotification([docs1[0].fcm_registration_token],data);              
            }
          })

          User.find({ _id: docs[0].booking_request }, function (err, docs1) {
            if (docs1[0].fcm_registration_token != "") {
              var body_text = "sent";
              const data = {
                title: trip_type + ' Booking Request Sent', // REQUIRED for Android
                topic: trip_type + ' Booking Request Sent', // REQUIRED for iOS (apn and gcm)
                /* The topic of the notification. When using token-based authentication, specify the bundle ID of the app. 
                 * When using certificate-based authentication, the topic is usually your app's bundle ID.
                 * More details can be found under https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/sending_notification_requests_to_apns
                 */
                body: 'Trip from ' + docs[0].origin + ' to ' + docs[0].destination + ' on ' + docs[0].time + ' is ' + body_text,
                custom: {
                  sender: 'Triva',
                },  
                priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn
                sound: 'jb', 
                android_channel_id: 'test_channel',// Custom sound file without extension              
            };

              sendNotification([docs1[0].fcm_registration_token],data);              
            }            
          });

        })

      }
        // Responder.success(res,val)
      )
      .catch((err) => Responder.operationFailed(res, err))
  }

  static boostTrip(req, res) {    
    Trip.findOneAndUpdate({ _id: req.params.id }, { 'is_trip_boost': true })
      .then((val) => { console.log("Sdfdsdf11"); Responder.success(res, val); })
      .catch((err) => { console.log("Sdfdsdf"); Responder.operationFailed(res, err) })
  }


  static update(req, res) {
    
    var body_text;
    var body_text_rear;
    var body_text_start;
    var send_notification = 0;
    var notification_title;



    if (req.body.button_flag == "ACCEPTBOOKING") {
      console.log('ACCEPTBOOKING');
      send_notification = 1;
      if (req.body.trip_type == "trip") {
        notification_title = 'Trip Booking Request Accepted';
        body_text_rear = ''
        body_text_start = 'Trip Booking Request for';
      } else {
        notification_title = 'Passenger Booking Request Accepted';
        body_text_rear = ''
        body_text_start = 'Passenger Booking Request for';
      }

    }

    if (req.body.button_flag == "CANCELETRIP") {
      console.log('CANCELETRIP');
      send_notification = 1;
      if (req.body.trip_type == "trip") {
        notification_title = 'Trip Cancellation Request';
        body_text_rear = ''
        body_text_start = 'Trip Cancellation Request for';
      } else {
        notification_title = 'Passenger Cancellation Request';
        body_text_rear = ''
        body_text_start = 'Passenger Cancellation Request for';
      }

    }

    if (req.body.button_flag == "CANCELETRIPACCEPT") {
      console.log('CANCELETRIPACCEPT');
      send_notification = 1;
      if (req.body.trip_type == "trip") {
        notification_title = 'Trip Cancellation Request Accepted';
        body_text_rear = 'accepted'
        body_text_start = 'Trip Cancellation Request for';

      } else {
        notification_title = 'Passenger Cancellation Request Accepted';
        body_text_rear = 'accepted'
        body_text_start = 'Passenger Cancellation Request for';
      }

    }

    if (req.body.button_flag == "CANCELETRIPREJECT") {
      console.log('CANCELETRIPREJECT');
      send_notification = 1;
      if (req.body.trip_type == "trip") {
        notification_title = 'Trip Cancellation Request Rejected';
        body_text_rear = 'rejected'
        body_text_start = 'Trip Cancellation Request Rejected for';
      } else {
        notification_title = 'Passenger Cancellation Request Rejected';
        body_text_rear = 'rejected'
        body_text_start = 'Passenger Cancellation Request Rejected for';
      }


    }

    if (req.body.button_flag == "BOOKINGREQUESTCANCEL") {
      console.log('BOOKINGREQUESTCANCEL');
      send_notification = 1;
      if (req.body.trip_type == "trip") {
        notification_title = 'Trip Booking Request Withdrawn';
        body_text_rear = 'withdrawn'
        body_text_start = 'Trip Booking Request for';
      } else {
        notification_title = 'Passenger Booking Request Withdrawn';
        body_text_rear = 'withdrawn'
        body_text_start = 'Passenger Booking Request for';
      }


    }









    if (req.body.temp === undefined) {
      if (req.body.status == 'ACCEPTED') {
        // send_notification = 1;
        // notification_title= 'Trip Accepted';
        // body_text = 'accepted.';

      }
      if (req.body.cancelled == true) {
        // send_notification = 1;
        // notification_title= 'Trip Rejected';
        // body_text = 'rejected by ';

      }

      if (req.body.cancelled == undefined) {
        // send_notification = 1;
        // notification_title= 'Trip Cancel Request sent';
        // if (req.body.cancel_request_by == req.body.trip.userDetails[0]._id) {
        // body_text = 'Cancel Request sent by ';
        // }
        // if (req.body.cancel_request_by != req.body.trip.userDetails[0]._id) {
        // body_text = 'Cancel Request sent by ';
        // } 

      }
      // body_text = 'accepted.';

    } else {
      console.log('bbbbbbbbbbbbbbb' + req.body.status)
      if (req.body.status == 'ACTIVE') {
        delete req.body.temp._id;

        req.body.temp.cancel_request_accept = moment(new Date()).toDate();
        // body_text = 'cancelled.';
        send_notification = 1;
        req.body.temp.status = 'AFTER_CANCEL';
        notification_title = 'Trip Cancellation Request Accepted';
        body_text_rear = 'Cancellation Request Accepted'
        body_text_start = '';

        if (req.body.trip_type == "trip") {
          console.log("cancelled status", req.body.temp);
          Trip.create(req.body.temp)
            .then((trip) => console.log("OK--------------"))
            .catch((err) => console.log('errr_trip create', err))
        } else if (req.body.trip_type == "passenger") {

        }


      } else {
        delete req.body.temp._id;
        // body_text = 'cancelled.';
        // send_notification = 1;
        // req.body.temp.status='AFTER_CANCEL';
        // notification_title= 'Trip Cancelled';

        console.log("AFTER_CANCEL status");
        // Trip.create(req.body.temp)
        //  .then((trip)=>console.log("OK"))
        //  .catch((err)=> console.log(err))
      }
    }

    // get trip info
    if (send_notification == 1) {
      Trip.find({ _id: req.params.id }, function (err, docs) {        
        console.log(docs);
        // send push notification here
        const data = {
          title: notification_title, // REQUIRED for Android
          topic: 'topic', // REQUIRED for iOS (apn and gcm)        
          body: body_text_start + ' Trip from ' + docs[0].origin + ' to ' + docs[0].destination + ' on ' + new Date(docs[0].time).toDateString() + ' ' + body_text_rear,
          custom: {
            sender: 'Triva',
          },                 
          sound: 'jb',
          priority: 'high', // gcm, apn. 'high' or 'normal' for gcm, translates to 10 or 5 for apn
          sound: 'jb',
          android_channel_id: 'test_channel', // Custom sound file without extension  
        };

        // get user details to send notification fcm_registration_token
        User.find({ _id: docs[0].user_id }, function (err, docs1) {
          if (docs1[0].fcm_registration_token != "") {
            // You can use it in node callback style
            sendNotification([docs1[0].fcm_registration_token],data);          
          }          
        });

        User.find({ _id: docs[0].booking_request }, function (err, docs1) {
          if (docs1[0].fcm_registration_token != "") {
            sendNotification([docs1[0].fcm_registration_token],data);           
          }          
        });


      });

      console.log('000000')
    }

    console.log('11111' + req.body)
    console.log(req.body)
    console.log('check')
    //db.trips.findOneAndUpdate({_id:"5cbb238854699c3ed97759e5"},{'booking_request':''})

    //,{$set:req.body}

    if (req.params.booking === "" || req.params.booking == null) {
      console.log('booking id blank   ' + req.body.cancelled)
      if (req.body.cancelled == true) {

        console.log(' cancel true');

        Trip.findOneAndUpdate({ _id: req.params.id }, { $unset: { 'booking_request': 1 } })
          .then((val) => {
            console.log('vallll')
            console.log(val)
            Responder.success(res, val);
          })
          .catch((err) => Responder.operationFailed(res, err))

        Trip.findOneAndUpdate({ _id: req.params.id }, { $set: req.body })
          .then((val) => {
            Responder.success(res, val);
          })
          .catch((err) => Responder.operationFailed(res, err))

        console.log('2222')
      }
      else {
        console.log(' cancel false');
        Trip.findOneAndUpdate({ _id: req.params.id }, { $set: req.body })
          .then((val) => {
            Responder.success(res, val);
          })
          .catch((err) => Responder.operationFailed(res, err))
      }
    }

    else {
      console.log(' booking id not blank ');
      Trip.findOneAndUpdate({ _id: req.params.id }, { $set: req.body })
        .then((val) => {
          Responder.success(res, val);
        })
        .catch((err) => Responder.operationFailed(res, err))
    }

  }

  static remove(req, res) {

    Trip.remove({ _id: req.params.id })
      .then((product) => Responder.success(res, product))
      .catch((err) => Responder.operationFailed(res, err))

  }

  static showSt(req, res) {

    // Trip.find({status:'CANCELLED',cancel_request_by:{$ne:req.params.id},$or:[{user_id:req.params.id},{booking_request:req.params.id}]})

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          // ]
          'status': 'CANCELLED',
          'trip_type': 'trip',

          'cancel_request_by': { $ne: ObjectId(req.params.id) },
          // 'time':{$gte:new Date(new Date(new Date().toISOString().slice(0, 10)))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },

          $or: [{
            'user_id': ObjectId(req.params.id)
          },
          {
            'booking_request': ObjectId(req.params.id)
          }],

        }

        //                     $match: {
        //                       // $and:[
        //                         // 'user_id': req.params.id,
        //                         // ]
        //                         $or:[
        //                           {'booking_id':ObjectId(req.params.id)},
        //                           // {'booking_request':ObjectId(req.params.id)}                       
        //                            {'user_id':ObjectId(req.params.id)}

        //                         ],
        // 'cancel_request_by':ObjectId({$ne:req.params.id}),
        //                         'status': 'CANCELLED',

        //                       }


      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },



    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }



  static showStPassenger(req, res) {

    // Trip.find({status:'CANCELLED',cancel_request_by:{$ne:req.params.id},$or:[{user_id:req.params.id},{booking_request:req.params.id}]})

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          // ]
          'status': 'CANCELLED',
          'trip_type': 'passenger',

          'cancel_request_by': { $ne: ObjectId(req.params.id) },
          // 'time':{$gte:new Date(new Date(new Date().toISOString().slice(0, 10)))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          $or: [{
            'user_id': ObjectId(req.params.id)
          },
          {
            'booking_request': ObjectId(req.params.id)
          }],

        }

        //                     $match: {
        //                       // $and:[
        //                         // 'user_id': req.params.id,
        //                         // ]
        //                         $or:[
        //                           {'booking_id':ObjectId(req.params.id)},
        //                           // {'booking_request':ObjectId(req.params.id)}                       
        //                            {'user_id':ObjectId(req.params.id)}

        //                         ],
        // 'cancel_request_by':ObjectId({$ne:req.params.id}),
        //                         'status': 'CANCELLED',

        //                       }


      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },



    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }


  static cancelRequestsForUser(req, res) {

    // Trip.find({status:'CANCELLED',cancel_request_by:{$ne:req.params.id},$or:[{user_id:req.params.id},{booking_request:req.params.id}]})

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          // ]
          $or: [
            // {'cancel_request_by':{$ne:ObjectId(req.params.id)}},
            // {'booking_request':ObjectId(req.params.id)}                       
            { 'user_id': ObjectId(req.params.id) },

          ],
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'status': 'CANCELLED',
          'trip_type': 'trip'

        }

        //                     $match: {
        //                       // $and:[
        //                         // 'user_id': req.params.id,
        //                         // ]
        //                         $or:[
        //                           {'booking_id':ObjectId(req.params.id)},
        //                           // {'booking_request':ObjectId(req.params.id)}                       
        //                            {'user_id':ObjectId(req.params.id)}

        //                         ],
        // 'cancel_request_by':ObjectId({$ne:req.params.id}),
        //                         'status': 'CANCELLED',

        //                       }


      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },



    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }

  static cancelRequest(req, res) {
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          // ]
          // $and:[
          // {'user_id':ObjectId(req.params.id)},
          // $or:[
          // {'accepted_user_id':ObjectId(req.params.id)}, 
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'cancel_request_by': ObjectId(req.params.id),
          // ],
          // ],
          'status': 'CANCELLED',
          'trip_type': 'trip'

        }


      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }



  static cancelRequestPassenger(req, res) {
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          // ]
          // $and:[
          // {'user_id':ObjectId(req.params.id)},
          // $or:[
          // {'accepted_user_id':ObjectId(req.params.id)}, 
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'cancel_request_by': ObjectId(req.params.id),
          // ],
          // ],
          'status': 'CANCELLED',
          'trip_type': 'passenger'

        }


      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }



  static sendRequest(req, res) {
    // Trip.find( { $and: [{booking_request:req.params.id},{status:'ACTIVE'}]})
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'booking_request': ObjectId(req.params.id),
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }

  static sendRequestPassenger(req, res) {
    // Trip.find( { $and: [{booking_request:req.params.id},{status:'ACTIVE'}]})
    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'booking_request': ObjectId(req.params.id),
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }



  static getTripHistory(req, res) {

    // Trip.find({$or:[{user_id:req.params.id},{booking_request:req.params.id}]})
    // var timeNow = new Date().getTime();
    // var timeNowMonthsBeforeDate =timeNow.slice(0, 10);

    var twoMonthsBeforeTime = new Date(timeNow - 1000 * 60 * 60 * 168).toISOString();
    var twoMonthsBeforeDate = twoMonthsBeforeTime.slice(0, 10);
    // console.log('sixMonthsBeforeDate--- ',sixMonthsBeforeDate)
    Trip.aggregate([
      {

        $match: {
          $and: [
            {
              "time": {
                "$lte": new Date(),
                "$gte": new Date(twoMonthsBeforeDate)
              }

            },
            {
              'trip_type': 'trip'
            },
            {
              $or: [
                // 'user_id': req.params.id,
                { 'user_id': ObjectId(req.params.id) },
                { 'booking_request': ObjectId(req.params.id) },
              ],
            }, {
              $or: [
                // {$and:[
                //   {'status':'DONE'},
                //   {"time": { "$lt": new Date(new Date(new Date().toISOString().slice(0, 10))) , "$gte": new Date(sixMonthsBeforeDate)  }}, 

                //   ]},
                { 'status': 'DONE' },
                { 'status': 'AFTER_CANCEL' },
                // 'status':'CANCELLED'
              ]
            }
          ],

        }


        // $match: {
        //   $or:[
        //     // 'user_id': req.params.id,
        //     {'user_id': ObjectId(req.params.id)},
        //     {'booking_request':ObjectId(req.params.id)},
        //     ],
        //     $and:[
        //       {'time':{$lt:new Date().toISOString().slice(0, 10)} },
        //       {
        //         $or:[
        //         {'status':'ACCEPTED'},
        //         {'status':'AFTER_CANCEL'},
        //         // 'status':'CANCELLED'
        //         ]
        //       }
        //     ]
        //   }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'vehicletypes',
          localField: 'category',
          foreignField: '_id',
          as: 'vehicleType'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }



  static getTripHistoryPassenger(req, res) {

    // Trip.find({$or:[{user_id:req.params.id},{booking_request:req.params.id}]})
    // var timeNow = new Date().getTime();
    // var timeNowMonthsBeforeDate =timeNow.slice(0, 10);

    var twoMonthsBeforeTime = new Date(timeNow - 1000 * 60 * 60 * 168).toISOString();
    var twoMonthsBeforeDate = twoMonthsBeforeTime.slice(0, 10);
    // console.log('sixMonthsBeforeDate--- ',sixMonthsBeforeDate)
    Trip.aggregate([
      {

        $match: {
          $and: [
            {
              "time": {
                "$lte": new Date(),
                "$gte": new Date(twoMonthsBeforeDate)
              }

            },
            {
              'trip_type': 'passenger'
            },
            {
              $or: [
                // 'user_id': req.params.id,
                { 'user_id': ObjectId(req.params.id) },
                { 'booking_request': ObjectId(req.params.id) },
              ],
            }, {
              $or: [
                // {$and:[
                //   {'status':'DONE'},
                //   {"time": { "$lt": new Date(new Date(new Date().toISOString().slice(0, 10))) , "$gte": new Date(sixMonthsBeforeDate)  }}, 

                //   ]},
                { 'status': 'DONE' },
                { 'status': 'AFTER_CANCEL' },
                // 'status':'CANCELLED'
              ]
            }
          ],

        }


        // $match: {
        //   $or:[
        //     // 'user_id': req.params.id,
        //     {'user_id': ObjectId(req.params.id)},
        //     {'booking_request':ObjectId(req.params.id)},
        //     ],
        //     $and:[
        //       {'time':{$lt:new Date().toISOString().slice(0, 10)} },
        //       {
        //         $or:[
        //         {'status':'ACCEPTED'},
        //         {'status':'AFTER_CANCEL'},
        //         // 'status':'CANCELLED'
        //         ]
        //       }
        //     ]
        //   }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }



  static searchForBoostTrip(req, res) {
    let searchQuery = {};
    console.log(req.body.ac);
    if (req.body.from_date) {
      searchQuery.from_date = new Date(req.body.from_date);
    }
    if (req.body.to_date) {
      searchQuery.to_date = new Date(req.body.to_date);
    }
    // if(req.body.time){
    //  searchQuery.time=req.body.time;
    // }

    console.log(searchQuery);
    var trips = [];
    var user = [];
    // Trip.find({time:{$gte:req.body.from_date},status:"ACTIVE",is_trip_boost:true,ac:req.body.ac}).limit(19)
    Trip.find({ time: { $gt: req.body.from_date }, status: "ACTIVE", is_trip_boost: true })
      .then((tr) => {
        trips = tr; console.log("-------", trips)

        return Vehicle.find({ _id: { $in: _.map(trips, 'vehical_id') } })
      })
      .then((tr) => {
        var response = []; _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            is_trip_boost: trip.is_trip_boost,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            address: trip.address,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at,
            originAroundDistance: distance(req.body.origin_location, trip.origin_location),
            destinationAroundDistance: distance(req.body.destination_location, trip.destination_location)
          };

          if (trp.originAroundDistance < 20 && trp.destinationAroundDistance < 20) {
            _.each(tr, vehi => {
              // if(trp && vehi._id == trp.vehical_id){
              if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
                trp.vehicleDetails = vehi;
                console.log('vehicle.check')
              }
              else {
                trp.vehicleDetails = vehi;
                console.log('else case check')
              }
            })
            // response.push(trp);


            if (req.body.number_of_seats <= trp.vehicleDetails.no_of_seats) {

              if (req.body.ac == "true" && req.body.carrier == "true") {
                console.log(trp.vehicleDetails.ac);
                if (trp.vehicleDetails.ac && trp.vehicleDetails.carrier) {
                  console.log("$$b  " + '1111');
                  response.push(trp);
                }
                else {
                  console.log('ac and carrier both required');
                }

              }
              else if (req.body.ac == "true" && req.body.carrier == "false") {
                if (trp.vehicleDetails.ac) {
                  console.log("$$b  " + '22222');

                  response.push(trp);
                }
                else {
                  console.log('yes ac but not found and no carrier requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "true") {
                if (trp.vehicleDetails.carrier) {
                  console.log("$$b  " + '3333333');

                  response.push(trp);
                }
                else {
                  console.log('yes carrier but not found and no ac requied');
                }

              }
              else if (req.body.ac == "false" && req.body.carrier == "false") {
                // console.log(trp.vehicleDetails.ac);
                // if(trp.vehicleDetails.ac == "false" && trp.vehicleDetails.carrier == "false"){
                console.log("$$b  " + '44444');
                response.push(trp);
                // }
                // else{
                // console.log('no ac and no carrier not found');
                // }

              }
              else {
                response.push(trp);
                console.log('no  carrier req  and no ac requied');

              }
            }
          } else if (!trp.destinationAroundDistance && trp.originAroundDistance < 20) {
            _.each(tr, vehi => {
              // if(trp && vehi._id == trp.vehical_id){
              if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
                trp.vehicleDetails = vehi;
                console.log('else vehicle.check')
              }
              else {
                console.log('else case check other')
              }

            })
            response.push(trp);
          } else if (!trp.destinationAroundDistance && !trp.originAroundDistance) {
            _.each(tr, vehi => {
              // if(trp && vehi._id == trp.vehical_id){
              if (trp && JSON.stringify(vehi._id) === JSON.stringify(trp.vehical_id)) {
                trp.vehicleDetails = vehi;
              }
            })
            response.push(trp);
          }
        });

        Responder.success(res, _.sortBy(response, [function (o) { return o.originAroundDistance; }]))
      })
      .catch((err) => Responder.operationFailed(res, err))

  }



  static upcomingTrips(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)


    Trip.aggregate([
      {

        $match: {
          status: 'ACCEPTED',
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'trip_type': 'trip'

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }


  static upcomingTripsPassenger(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)


    Trip.aggregate([
      {

        $match: {
          status: 'ACCEPTED',
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'trip_type': 'passenger'

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }


  static pendingTrips(req, res) {

    // status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

  }


  static pendingTripsPassenger(req, res) {

    // status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

  }


  static bookingRequests(req, res) {

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'booking_request': { $ne: null },
          'trip_type': 'trip'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

  }

  static bookingRequestsPassenger(req, res) {

    Trip.aggregate([
      {
        $match: {
          // $and:[
          // 'user_id': req.params.id,
          'status': 'ACTIVE',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'booking_request': { $ne: null },
          'trip_type': 'passenger'
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'tripUserDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

  }



  static cancelTrips(req, res) {




    Trip.aggregate([
      {

        $match: {
          status: 'CANCELLED',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }


  static cancelTripsPassenger(req, res) {




    Trip.aggregate([
      {

        $match: {
          status: 'CANCELLED',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }



  static boostedTrips(req, res) {


    Trip.aggregate([
      {

        $match: {
          'is_trip_boost': true,
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip'

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }


  static boostedTripsPassenger(req, res) {


    Trip.aggregate([
      {

        $match: {
          'is_trip_boost': true,
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},                         
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger'

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }

  static tripsHistory(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)


    Trip.aggregate([
      {

        $match: {
          'trip_type': 'trip',
          $or: [

            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
          ]

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }


  static tripsHistoryPassenger(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)


    Trip.aggregate([
      {

        $match: {
          'trip_type': 'passenger',
          $or: [

            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
          ]

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }



  static upcomingTripsForVehicle(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)
    console.log(req.body)



    Trip.aggregate([
      {
        $match: {


          vehical_id: ObjectId(req.body.vehicleid),

          status: 'ACCEPTED',
          // {time:{$gte:new Date().toISOString().slice(0, 10)}}
          time: { '$gte': new Date(new Date().toISOString().slice(0, 10)) },
          'trip_type': 'trip',


        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))





  }

  static pendingTripsForVehicle(req, res) {

    console.log(req.body)

    Trip.aggregate([
      {
        $match: {
          status: 'ACTIVE',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          vehical_id: ObjectId(req.body.vehicleid),
          'trip_type': 'trip',

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }



  static cancelTripsForVehicle(req, res) {

    console.log(req.body)


    Trip.aggregate([
      {
        $match: {

          status: 'CANCELLED',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          vehical_id: ObjectId(req.body.vehicleid),
          'trip_type': 'trip',


        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

  }


  static boostedTripsForVehicle(req, res) {

    console.log(req.body)



    Trip.aggregate([
      {
        $match: {
          is_trip_boost: true,
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          vehical_id: ObjectId(req.body.vehicleid),
          'trip_type': 'trip',

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))





  }



  static bookingRequestsForVehicle(req, res) {

    Trip.aggregate([
      {

        $match: {
          status: 'ACTIVE',
          time: { $gt: new Date().toISOString().slice(0, 10) },
          vehical_id: req.body.vehicleid,
          'trip_type': 'trip',

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))




  }


  static tripHistoryForVehicle(req, res) {

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)
    console.log(req.body)

    console.log(req.body)
    Trip.aggregate([
      {

        $match: {

          vehical_id: ObjectId(req.body.vehicleid),
          'trip_type': 'trip',


          $or: [
            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
          ]

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }


  static upcomingTripsForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $or: [{
          status: 'ACCEPTED',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'trip'

      }
      // {$or: [{status:'ACCEPTED',time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static upcomingTripsForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $or: [{
          status: 'ACCEPTED',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'passenger'

      }
      // {$or: [{status:'ACCEPTED',time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static pendingTripForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $and: [{
          status: 'ACTIVE',
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'trip'

      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static pendingTripForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $and: [{
          status: 'ACTIVE',
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'passenger'
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }

  // 
  static cancelTripForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $and: [{
          status: 'CANCELLED',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'trip'

      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static cancelTripForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips

    Trip.find(
      {
        $and: [{
          status: 'CANCELLED',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'passenger'
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static boostedTripForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          is_trip_boost: true,
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'trip'

      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static boostedTripForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          is_trip_boost: true,
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'passenger'
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static tripHistoryForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'trip'

      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static tripHistoryForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          booking_request: { "$nin": [null, ""] },
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') }
        }],
        trip_type: 'passenger'
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static bookingRequestForDate(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          // booking_request:{ "$nin": [ null, "" ] },
          status: 'ACTIVE',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') },
          trip_type: 'trip'

        }]
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }


  static bookingRequestForDatePassenger(req, res) {

    // var pageNo= req.body.currentPage + 1;
    // console.log('pageNo--'+pageNo)
    // var size = req.body.page_limit;
    // console.log('size--'+size)

    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];

    Trip.find(
      {
        $and: [{
          // booking_request:{ "$nin": [ null, "" ] },
          status: 'ACTIVE',
          "time": { $gte: new Date(req.body.startDate + 'T00:00:00Z'), $lte: new Date(req.body.endDate + 'T23:59:59Z') },
          trip_type: 'passenger'
        }]
      }
      // {$and: [{status:'ACTIVE',booking_request:{ "$nin": [ null, "" ] },time:{$gte:req.body.startDate},time:{$lte:req.body.endDate}}]}
    )
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static tripsHistoryByUserId(req, res) {


    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)



    Trip.aggregate([
      {

        $match: {
          // booking_request:{ "$nin": [ null, "" ] },
          // user_id: ObjectId(req.body.userId),          

          $and: [
            { 'trip_type': 'trip' },
            {
              $or: [
                // 'user_id': req.params.id,
                { 'user_id': ObjectId(req.body.userId) },
                { 'booking_request': ObjectId(req.body.userId) },
              ]
            },
            {
              $or: [
                { 'status': 'DONE' },
                { 'status': 'AFTER_CANCEL' },
                // 'status':'CANCELLED'
              ]
            }
          ]

          // $or:[
          //   {'status':'DONE'},
          //   {'status':'AFTER_CANCEL'},
          //   ]

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }


  static tripsHistoryByUserIdPassenger(req, res) {


    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)



    Trip.aggregate([
      {

        $match: {
          // booking_request:{ "$nin": [ null, "" ] },
          // user_id: ObjectId(req.body.userId),          

          $and: [
            { 'trip_type': 'passenger' },
            {
              $or: [
                // 'user_id': req.params.id,
                { 'user_id': ObjectId(req.body.userId) },
                { 'booking_request': ObjectId(req.body.userId) },
              ]
            },
            {
              $or: [
                { 'status': 'DONE' },
                { 'status': 'AFTER_CANCEL' },
                // 'status':'CANCELLED'
              ]
            }
          ]


          // $or:[
          //   {'status':'DONE'},
          //   {'status':'AFTER_CANCEL'},
          //   ]

        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'booking_request',
          foreignField: '_id',
          as: 'bookingRequest'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }




  static upcomingTripsByUserId(req, res) {
    console.log('ussssssssss' + req.body.userId)


    Trip.aggregate([
      {

        $match: {
          $or: [
            { user_id: ObjectId(req.body.userId) },
            { booking_request: ObjectId(req.body.userId) }
          ],
          status: 'ACCEPTED',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip',


        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }


  static upcomingTripsByUserIdPassenger(req, res) {
    console.log('ussssssssss' + req.body.userId)


    Trip.aggregate([
      {

        $match: {
          $or: [
            { user_id: ObjectId(req.body.userId) },
            { booking_request: ObjectId(req.body.userId) }
          ],
          status: 'ACCEPTED',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger',


        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))



  }


  static pendingTripsByUserId(req, res) {
    console.log('ussssssssss' + req.body.userId)


    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips
    Trip.find({ user_id: req.body.userId, booking_request: { "$nin": [null, ""] }, status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) }, trip_type: 'trip' })
      // Trip.find({user_id:req.params.id,status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}})
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }

  static pendingTripsByUserIdPassenger(req, res) {
    console.log('ussssssssss' + req.body.userId)


    var user = [];
    var vehicle = [];
    var trips = [];
    var ar1 = [];
    var ar2 = [];
    var ar3 = [];
    // get upcoming trips
    Trip.find({ user_id: req.body.userId, booking_request: { "$nin": [null, ""] }, status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) }, trip_type: 'passenger' })
      // Trip.find({user_id:req.params.id,status:'ACTIVE',time:{$gte:new Date().toISOString().slice(0, 10)}})
      .then((tr) => { trips = tr; return Vehicle.find({ _id: { $in: _.map(tr, 'vehical_id') } }) })
      .then((veh) => { vehicle = veh; console.log("#############"); ar1 = _.uniq(_.map(trips, 'booking_request')); console.log(ar1, 'aaaa'); ar2 = _.uniq(_.map(trips, 'user_id')); console.log(ar2, 'aaaa'); ar3 = ar1.concat(ar2); console.log(ar3, 'aaaa'); return User.find({ _id: { $in: ar3 } }) })
      .then((usr) => {
        user = usr; var response = [];
        _.each(trips, trip => {
          var trp = {
            _id: trip._id,
            vehical_id: trip.vehical_id,
            user_id: trip.user_id,
            time: trip.time,
            reg_no: trip.reg_no,
            origin: trip.origin,
            destination: trip.destination,
            address: trip.address,
            status: trip.status,
            accepted_user_id: trip.accepted_user_id,
            booking_request: trip.booking_request,
            created_at: trip.created_at
          };

          var owner = [];
          console.log(user);
          var bookUser = []; _.each(user, us => {
            var userDe = {
              _id: us._id,
              phone_number: us.phone_number,
              name: us.name,
              pincode: us.pincode,
              email: us.email,
              profile_image_url: us.profile_image_url,
              address: us.address
            };

            console.log(trp.booking_request)
            if (trp.booking_request == userDe._id) {

              bookUser.push(userDe);
              trp.userDetails = bookUser;

            }
            if (trp.user_id == userDe._id) {
              owner.push(userDe);
              trp.ownerDetails = owner;
            }



          })

          _.each(vehicle, vehi => {

            if (trp && vehi._id == trp.vehical_id) {
              trp.vehicleDetails = vehi;
            }
          })
          response.push(trp);
        }); Responder.success(res, response)

      })
      .catch((err) => Responder.operationFailed(res, err))



  }



  static boostedTripsForUser(req, res) {

    Trip.aggregate([
      {

        $match: {
          is_trip_boost: true,
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          user_id: ObjectId(req.body.userId),
          'trip_type': 'trip',

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }



  static boostedTripsForUserPassenger(req, res) {

    Trip.aggregate([
      {

        $match: {
          is_trip_boost: true,
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          user_id: ObjectId(req.body.userId),
          'trip_type': 'passenger',

        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      }, {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      },
      {
        "$sort": {
          "created_at": -1,
        }
      },


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }


  static filterTripsHistory(req, res) {
    console.log('filterTripsHistory' + req.body.origin);

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)

    if ((req.body.startDate == null && req.body.startDate == null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('111111111');

      Trip.aggregate([
        {
          $match: {
            'trip_type': 'trip',

            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],

            $or: [
              { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
              { destination: { $regex: "^" + req.body.destination, $options: 'i' } }

            ]


          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('22222222222222');

      Trip.aggregate([
        {
          $match: {

            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],


            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }


              },
              {
                'trip_type': 'trip'
              },
              {
                $or: [
                  { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
                  { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
                ]
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate == null && req.body.startDate == null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('3333333333');

      User.aggregate([
        {
          $match: {
            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } },

            ]

          },

        }

      ])

        .then((makers) => {
          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                user_id: { $in: object },
                'trip_type': 'trip',


              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ]).skip(size * (pageNo - 1)).limit(size)
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('4444');

      User.aggregate([
        {
          $match: {



            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } }

            ]




          },

        }

      ])

        .then((makers) => {
          console.log('ffffffffffffffff', makers)

          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                $or: [

                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },

                ],


                $and: [
                  {
                    "time": {
                      "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                      "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                    }


                  },
                  { user_id: { $in: object } },
                  {
                    'trip_type': 'trip'
                  }
                ]

              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ]).skip(size * (pageNo - 1)).limit(size)
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }


    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin == null && req.body.destination == null && req.body.phone_number == null && req.body.buisness_name == null)) {
      console.log('55555555555');

      Trip.aggregate([
        {
          $match: {



            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }


              },
              {
                'trip_type': 'trip'
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }



    // Trip.find({$or: [{ origin : {$regex : "^" + req.body.origin, $options: 'i'}},{ destination : {$regex : "^" + req.body.destination, $options: 'i'}}]}) 
    //   .then((user) =>{ console.log(user); Responder.success(res, user)})
    //   .catch((err) => Responder.operationFailed(res, err))
  }


  static filterTripsHistoryPassenger(req, res) {
    console.log('filterTripsHistory' + req.body.origin);

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)

    if ((req.body.startDate == null && req.body.startDate == null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('111111111');

      Trip.aggregate([
        {
          $match: {
            'trip_type': 'passenger',

            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],

            $or: [
              { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
              { destination: { $regex: "^" + req.body.destination, $options: 'i' } }

            ]


          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('22222222222222');

      Trip.aggregate([
        {
          $match: {

            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],


            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }


              },
              {
                'trip_type': 'passenger'
              },
              {
                $or: [
                  { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
                  { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
                ]
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate == null && req.body.startDate == null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('3333333333');

      User.aggregate([
        {
          $match: {
            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } },

            ]

          },

        }

      ])

        .then((makers) => {
          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                user_id: { $in: object },
                'trip_type': 'passenger',


              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ]).skip(size * (pageNo - 1)).limit(size)
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('4444');

      User.aggregate([
        {
          $match: {



            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } }

            ]




          },

        }

      ])

        .then((makers) => {
          console.log('ffffffffffffffff', makers)

          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                $or: [

                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },

                ],


                $and: [
                  {
                    "time": {
                      "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                      "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                    }


                  },
                  { user_id: { $in: object } },
                  {
                    'trip_type': 'passenger'
                  }
                ]

              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ]).skip(size * (pageNo - 1)).limit(size)
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }


    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin == null && req.body.destination == null && req.body.phone_number == null && req.body.buisness_name == null)) {
      console.log('55555555555');

      Trip.aggregate([
        {
          $match: {



            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }


              },
              {
                'trip_type': 'passenger'
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ]).skip(size * (pageNo - 1)).limit(size)
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }



    // Trip.find({$or: [{ origin : {$regex : "^" + req.body.origin, $options: 'i'}},{ destination : {$regex : "^" + req.body.destination, $options: 'i'}}]}) 
    //   .then((user) =>{ console.log(user); Responder.success(res, user)})
    //   .catch((err) => Responder.operationFailed(res, err))
  }



  static filterPendingTrip(req, res) {
    console.log('filterPendingTrip' + req.body.origin);

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)

    Trip.aggregate([
      {
        $match: {
          // $and : [

          $or: [
            { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
            { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
          ]
          ,
          status: 'ACTIVE',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'trip',


          // time:{$gt:new Date().toISOString().slice(0, 10)}
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }


    ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

    // Trip.find(
    //     {
    //         $and : [
    //             {$or: [{ origin : {$regex : "^" + req.body.origin, $options: 'i'}},{ destination : {$regex : "^" + req.body.destination, $options: 'i'}}]},
    //             {status:'ACTIVE',time:{$gt:new Date().toISOString().slice(0, 10)}} 

    //         ]
    //     }
    // )  
    // .then((user) =>{ console.log(user); Responder.success(res, user)})
    // .catch((err) => Responder.operationFailed(res, err))
  }



  static filterPendingTripPassenger(req, res) {
    console.log('filterPendingTrip' + req.body.origin);

    var pageNo = req.body.currentPage + 1;
    console.log('pageNo--' + pageNo)
    var size = req.body.page_limit;
    console.log('size--' + size)

    Trip.aggregate([
      {
        $match: {
          // $and : [

          $or: [
            { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
            { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
          ]
          ,
          status: 'ACTIVE',
          // 'time':{$gte:new Date(new Date().toISOString().slice(0, 10))},
          'time': { $gte: moment.utc().add(5.5, 'hours').startOf('day').toDate() },
          'trip_type': 'passenger',


          // time:{$gt:new Date().toISOString().slice(0, 10)}
          // ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }


    ]).skip(size * (pageNo - 1)).limit(size)
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))

    // Trip.find(
    //     {
    //         $and : [
    //             {$or: [{ origin : {$regex : "^" + req.body.origin, $options: 'i'}},{ destination : {$regex : "^" + req.body.destination, $options: 'i'}}]},
    //             {status:'ACTIVE',time:{$gt:new Date().toISOString().slice(0, 10)}} 

    //         ]
    //     }
    // )  
    // .then((user) =>{ console.log(user); Responder.success(res, user)})
    // .catch((err) => Responder.operationFailed(res, err))
  }


  static filterTripsUpcoming(req, res) {
    console.log('filterTripsUpcoming' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { status: 'ACCEPTED', time: { $gt: new Date().toISOString().slice(0, 10) } }

        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }



  static filterTripsUpcomingPassenger(req, res) {
    console.log('filterTripsUpcomingPassenger' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { status: 'ACCEPTED', time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'passenger' }

        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }

  static filterCancelTrip(req, res) {
    console.log('filterCancelTrip' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { status: 'CANCELLED', time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'trip' }


        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }


  static filterCancelTripPassenger(req, res) {
    console.log('filterCancelTrip' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { status: 'CANCELLED', time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'passenger' }


        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }




  static filterBoostedTrip(req, res) {
    console.log('filterBoostedTrip' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { is_trip_boost: true, time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'trip' }
        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }

  static filterBoostedTripPassenger(req, res) {
    console.log('filterBoostedTripPassenger' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { is_trip_boost: true, time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'passenger' }

        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }



  static filterBookingRequest(req, res) {
    console.log('filterBookingRequest' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { booking_request: { "$nin": [null, ""] }, status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'trip' }

        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }

  static filterBookingRequestPassenger(req, res) {
    console.log('filterBookingRequest' + req.body.origin);

    Trip.find(
      {
        $and: [
          { $or: [{ origin: { $regex: "^" + req.body.origin, $options: 'i' } }, { destination: { $regex: "^" + req.body.destination, $options: 'i' } }] },
          { booking_request: { "$nin": [null, ""] }, status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) } },
          { trip_type: 'passenger' }

        ]
      }
    )
      .then((user) => { console.log(user); Responder.success(res, user) })
      .catch((err) => Responder.operationFailed(res, err))
  }

  static filterBoostedTripForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({ is_trip_boost: true, time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }

  static filterTripsHistoryForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({}) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }




  static filterBookingRequestForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({ booking_request: { "$nin": [null, ""] }, status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }


  static upcomingTripForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({ status: 'ACCEPTED', time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }


  static upcomingTripForUserPassenger(req, res) {

    var users = [];
    User.find({
      $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }],
      trip_type: 'passenger'

    })
      .then((usr) => { users = usr; return Trip.find({ status: 'ACCEPTED', time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }



  static pendingTripForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({ status: 'ACTIVE', time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }


  static cancelTripForUser(req, res) {

    var users = [];
    User.find({ $or: [{ buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } }] })
      .then((usr) => { users = usr; return Trip.find({ status: 'CANCELLED', time: { $gt: new Date().toISOString().slice(0, 10) } }) })
      .then((veh) => {
        // console.log("VEH====", veh);
        var response = []; _.each(users, us => {
          var trp = {
            _id: us._id,
            name: us.name,
            phone_number: us.phone_number,
            email: us.email,
            buisness_name: us.buisness_name,
            address: us.address,
            district: us.district,
            rating: us.rating,
            state: us.state,
            report: us.report,
            wallet_balance: us.wallet_balance,
            created_at: us.created_at,
            is_subscribed: us.referal_code,
            is_subscribed: us.is_subscribed,
            referred_by_code: us.referred_by_code,
            fcm_registration_token: us.fcm_registration_token,
            suspend: us.suspend
          };
          trp.tripDetails = [];
          _.each(veh, vehi => {
            // console.log("user====", vehi)
            // console.log("user====", trp._id)
            // console.log("VEH====", vehi.user_id)
            if (trp._id == vehi.user_id) {
              trp.tripDetails.push(vehi);
            }


          })
          response.push(trp);
        }); Responder.success(res, response)
      })
      .catch((err) => Responder.operationFailed(res, err))
    // User.find({$or: [{ buisness_name : {$regex : "^" + req.body.buisness_name, $options: 'i'}}]})
    // .then((product)=>Responder.success(res,product))
    // .catch((err)=>Responder.operationFailed(res,err))
  }



  static searchTripHistoryForDate(req, res) {
    console.log('req-----------trip');
    console.log(req.body);

    Trip.aggregate([
      {
        $match: {
          // $and:[
          "time": { "$gte": new Date(req.body.from_date), "$lte": new Date(req.body.to_date), "$ne": new Date(new Date().toISOString().slice(0, 10)) },
          'trip_type': 'trip',

          // ],

          $or: [
            // 'user_id': req.params.id,
            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },
          ],
          $or: [
            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
            // 'status':'CANCELLED'
          ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


    // let query = Transaction.find({$and:[{user_id:req.body.user_id} ,{"transaction_date": { "$gte": new Date(req.body.from_date) , "$lte": new Date(req.body.to_date)  } },{ transaction_reason: 'Referral Commission' } ,{transaction_date: { $gte: req.body.from_date } }]
    //     }).sort({ 'transaction_date': -1 });

    // let promise = query.exec();

    //     promise.then((trc)=>Responder.success(res,trc))
    //     .catch((err)=>Responder.operationFailed(res,err))
  }




  static searchTripHistoryForDatePassenger(req, res) {
    console.log('req-----------trip');
    console.log(req.body);

    Trip.aggregate([
      {
        $match: {
          // $and:[
          "time": { "$gte": new Date(req.body.from_date), "$lte": new Date(req.body.to_date), "$ne": new Date(new Date().toISOString().slice(0, 10)) },
          'trip_type': 'passenger',

          // ],

          $or: [
            // 'user_id': req.params.id,
            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },
          ],
          $or: [
            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
            // 'status':'CANCELLED'
          ]
        }

      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      }, {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'cancel_request_by',
          foreignField: '_id',
          as: 'cancelRequestBy'
        }
      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'accepted_user_id',
          foreignField: '_id',
          as: 'acceptedUser'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


    // let query = Transaction.find({$and:[{user_id:req.body.user_id} ,{"transaction_date": { "$gte": new Date(req.body.from_date) , "$lte": new Date(req.body.to_date)  } },{ transaction_reason: 'Referral Commission' } ,{transaction_date: { $gte: req.body.from_date } }]
    //     }).sort({ 'transaction_date': -1 });

    // let promise = query.exec();

    //     promise.then((trc)=>Responder.success(res,trc))
    //     .catch((err)=>Responder.operationFailed(res,err))
  }




  static getTripById(req, res) {

    Trip.aggregate([
      {
        $match: {
          '_id': ObjectId(req.params.id),
          'trip_type': 'trip',

        }

      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'vehicletypes',
          localField: 'category',
          foreignField: '_id',
          as: 'vehicleType'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }


  static getTripByIdPassenger(req, res) {

    Trip.aggregate([
      {
        $match: {
          '_id': ObjectId(req.params.id),
          'trip_type': 'passenger',

        }

      },
      {
        $lookup:
        {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehicles',
          localField: 'vehical_id',
          foreignField: '_id',
          as: 'vehicleDetails'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemakers',
          localField: 'vehical_make',
          foreignField: '_id',
          as: 'vehicleMaker'
        }
      },
      {
        $lookup:
        {
          from: 'vehiclemodels',
          localField: 'vehical_model',
          foreignField: '_id',
          as: 'vehicleModel'
        }
      },
      {
        $lookup:
        {
          from: 'vehicletypes',
          localField: 'category',
          foreignField: '_id',
          as: 'vehicleType'
        }
      }


    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }




  static filterTripsDetailsHistory(req, res) {
    console.log('filterTripsDetailsHistory' + req.body.origin);



    if ((req.body.startDate == null && req.body.startDate == null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('111111111');

      Trip.aggregate([
        {
          $match: {
            $and: [
              { vehical_id: ObjectId(req.body.vehical_id) },
              { 'trip_type': 'trip' },

              {
                $or: [
                  { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
                  { destination: { $regex: "^" + req.body.destination, $options: 'i' } }

                ]
              },
              {
                $or: [
                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },
                ]
              }
            ]


          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('22222222222222');

      Trip.aggregate([
        {
          $match: {

            vehical_id: ObjectId(req.body.vehical_id),
            'trip_type': 'trip',
            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],


            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }
              },
              {
                $or: [
                  { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
                  { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
                ]
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate == null && req.body.startDate == null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('3333333333');

      User.aggregate([
        {
          $match: {
            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } },

            ]

          },

        }

      ])

        .then((makers) => {
          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                $or: [
                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },
                ],
                vehical_id: ObjectId(req.body.vehical_id),
                user_id: { $in: object },
                'trip_type': 'trip',

              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ])
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('4444');

      User.aggregate([
        {
          $match: {



            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } }

            ]




          },

        }

      ])

        .then((makers) => {
          console.log('ffffffffffffffff', makers)

          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {

                vehical_id: ObjectId(req.body.vehical_id),
                'trip_type': 'trip',

                $or: [

                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },

                ],


                $and: [
                  {
                    "time": {
                      "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                      "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                    }
                  },
                  { user_id: { $in: object } }
                ]

              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }
          ])
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }


    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin == null && req.body.destination == null && req.body.phone_number == null && req.body.buisness_name == null)) {
      console.log('55555555555');

      Trip.aggregate([
        {
          $match: {



            $and: [
              {
                $or: [
                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },
                ]
              },
              {
                vehical_id: ObjectId(req.body.vehical_id),
                'trip_type': 'trip',
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }



    // Trip.find({$or: [{ origin : {$regex : "^" + req.body.origin, $options: 'i'}},{ destination : {$regex : "^" + req.body.destination, $options: 'i'}}]}) 
    //   .then((user) =>{ console.log(user); Responder.success(res, user)})
    //   .catch((err) => Responder.operationFailed(res, err))
  }



  static filterTripsHistoryCount(req, res) {
    if ((req.body.startDate == null && req.body.startDate == null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('111111111');

      Trip.aggregate([
        {
          $match: {
            'trip_type': 'trip',
            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],

            $or: [
              { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
              { destination: { $regex: "^" + req.body.destination, $options: 'i' } }

            ]


          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }, {
          $group: {
            _id: {

            },
            myCount: { $sum: 1 },
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin != null || req.body.destination != null)) {
      console.log('22222222222222');

      Trip.aggregate([
        {
          $match: {

            $or: [

              { 'status': 'DONE' },
              { 'status': 'AFTER_CANCEL' },

            ],


            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }
              },
              {
                'trip_type': 'trip',

              },
              {
                $or: [
                  { origin: { $regex: "^" + req.body.origin, $options: 'i' } },
                  { destination: { $regex: "^" + req.body.destination, $options: 'i' } }
                ]
              }
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }, {
          $group: {
            _id: {

            },
            myCount: { $sum: 1 },
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate == null && req.body.startDate == null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('3333333333');

      User.aggregate([
        {
          $match: {
            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } },

            ]

          },

        }

      ])

        .then((makers) => {
          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                user_id: { $in: object },
                'trip_type': 'trip',


              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }, {
              $group: {
                _id: {

                },
                myCount: { $sum: 1 },
              }
            }
          ])
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }

    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.buisness_name != null || req.body.phone_number != null)) {
      console.log('4444');

      User.aggregate([
        {
          $match: {



            $or: [
              { buisness_name: { $regex: "^" + req.body.buisness_name, $options: 'i' } },
              { phone_number: { $regex: "^" + req.body.phone_number, $options: 'i' } }

            ]




          },

        }

      ])

        .then((makers) => {
          console.log('ffffffffffffffff', makers)

          var object = [];
          makers.forEach(function (maker, k) {
            console.log('ffffffffffffffff', maker.name)
            object.push(ObjectId(maker._id))
          });


          console.log('ooooooo', object)


          Trip.aggregate([
            {
              $match: {
                $or: [

                  { 'status': 'DONE' },
                  { 'status': 'AFTER_CANCEL' },

                ],


                $and: [
                  {
                    "time": {
                      "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                      "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                    }
                  },
                  { user_id: { $in: object } },
                  {
                    'trip_type': 'trip',

                  }
                ]

              }
            },
            {
              $lookup:
              {
                from: 'vehicles',
                localField: 'vehical_id',
                foreignField: '_id',
                as: 'vehicleDetails'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'user_id',
                foreignField: '_id',
                as: 'userDetails'
              }
            }, {
              $lookup:
              {
                from: 'vehiclemakers',
                localField: 'vehical_make',
                foreignField: '_id',
                as: 'vehicleMaker'
              }
            },
            {
              $lookup:
              {
                from: 'vehiclemodels',
                localField: 'vehical_model',
                foreignField: '_id',
                as: 'vehicleModel'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'cancel_request_by',
                foreignField: '_id',
                as: 'cancelRequestBy'
              }
            },
            {
              $lookup:
              {
                from: 'users',
                localField: 'accepted_user_id',
                foreignField: '_id',
                as: 'acceptedUser'
              }
            }, {
              $group: {
                _id: {

                },
                myCount: { $sum: 1 },
              }
            }
          ])
            .then((product) => {
              console.log('lengthhhhhh' + product.length)
              // if (product.length > 0) {
              Responder.success(res, product)
              // }
            }
            )
            .catch((err) => Responder.operationFailed(res, err))

          // Responder.success(res,object)

          // console.log('object',object); 
        }
          // Responder.success(res,trc)
        )
        .catch((err) => Responder.operationFailed(res, err))

    }


    else if ((req.body.startDate != null && req.body.startDate != null) && (req.body.origin == null && req.body.destination == null && req.body.phone_number == null && req.body.buisness_name == null)) {
      console.log('55555555555');

      Trip.aggregate([
        {
          $match: {



            $and: [
              {
                "time": {
                  "$gte": new Date(req.body.startDate + 'T00:00:00Z'),
                  "$lte": new Date(req.body.endDate + 'T23:59:59Z')
                }
              },
              {
                'trip_type': 'trip'

              },
            ]




          }
        },
        {
          $lookup:
          {
            from: 'vehicles',
            localField: 'vehical_id',
            foreignField: '_id',
            as: 'vehicleDetails'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'user_id',
            foreignField: '_id',
            as: 'userDetails'
          }
        }, {
          $lookup:
          {
            from: 'vehiclemakers',
            localField: 'vehical_make',
            foreignField: '_id',
            as: 'vehicleMaker'
          }
        },
        {
          $lookup:
          {
            from: 'vehiclemodels',
            localField: 'vehical_model',
            foreignField: '_id',
            as: 'vehicleModel'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'cancel_request_by',
            foreignField: '_id',
            as: 'cancelRequestBy'
          }
        },
        {
          $lookup:
          {
            from: 'users',
            localField: 'accepted_user_id',
            foreignField: '_id',
            as: 'acceptedUser'
          }
        }, {
          $group: {
            _id: {

            },
            myCount: { $sum: 1 },
          }
        }


      ])
        .then((trc) => Responder.success(res, trc))
        .catch((err) => Responder.operationFailed(res, err))

    }


  }


  static totalTripHistory(req, res) {
    Trip.aggregate([
      {

        $match: {

          'trip_type': 'trip',

          $or: [
            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },
          ],
          $or: [
            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
          ]
        }
      },
      {
        $group: {
          _id: {

          },
          myCount: { $sum: 1 },
        }
      }

    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))


  }



  static totalTripHistoryPassenger(req, res) {
    Trip.aggregate([
      {

        $match: {

          'trip_type': 'passenger',

          $or: [
            { 'user_id': ObjectId(req.params.id) },
            { 'booking_request': ObjectId(req.params.id) },
          ],
          $or: [
            { 'status': 'DONE' },
            { 'status': 'AFTER_CANCEL' },
          ]
        }
      },
      {
        $group: {
          _id: {

          },
          myCount: { $sum: 1 },
        }
      }

    ])
      .then((trc) => Responder.success(res, trc))
      .catch((err) => Responder.operationFailed(res, err))
  }
}
