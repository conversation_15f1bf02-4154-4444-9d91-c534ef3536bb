angular.module('referralUsers.controllers', [])

    .controller('referralUsersCtrl', function ($scope,APIService, $state,$stateParams) {

    		 $scope.addBalance =false;
     $scope.add={};
     $scope.filter={};
     $scope.adminData=JSON.parse(localStorage.getItem("UserDeatails"));
     $scope.userDetails = [];
    $scope.completed_orders = [];
    $scope.rejected_orders = [];
    $scope.orderList = [];
    var startDate;
    var endDate;
    $scope.title;
    $scope.emailTitle;
    $scope.state;
    $scope.emailBody;
    $scope.businessProfile;
    $scope.currentPage = 0;
    $scope.userDetailsLength;
    $scope.pageLimit;
    $scope.cityList;
    $scope.selectedCity;
    $scope.selectedCityForNotification;
    $scope.phone_number;
    $scope.district;
    $scope.userEmail;
    $scope.buisness_name;
    $scope.userName;
    $scope.userState;
    $scope.filterSearch =false;
    
    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
    };


    
  


    APIService.setData({
        req_url: PrefixUrl + '/user/getCityList'  
    }).then(function(resp) {
      $scope.cityList= resp.data;
    },function(resp) {
      // This block execute in case of error.
       // $scope.logout = function() {
      localStorage.removeItem("UserDeatails");
      localStorage.removeItem("token");
      $state.go('login');
      console.log('error')
    });

    var userData=localStorage.getItem('UserDeatails');
    var parsedUser= JSON.parse(userData);
    // console.log(parsedUser.user_details)
    if (parsedUser == null || parsedUser.user_details.role != 'admin') {
      localStorage.removeItem("UserDeatails");
      localStorage.removeItem("token");
      $state.go('login');
    }


    // console.log('ppppppp'+$scope.settings.currentPage)


    // $scope.$watch('checkStatus', function (value) {
    //   console.log('checkStatus'+value)

    // });

    $scope.$watch('settings.pageLimit', function (pageLimit) {
      console.log('pageLimits'+pageLimit)
      $scope.pageLimit= pageLimit;
      if($scope.filterSearch){
        APIService.setData({
          req_url: PrefixUrl + '/user/filterReferralUsers' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate,phone_number: $scope.phone_number, district: $scope.district, email: $scope.userEmail, buisness_name:$scope.buisness_name, userName:$scope.userName, state:$scope.userState } 
        }).then(function(resp) {
          console.log("====respPagination======",resp);
          $scope.userDetails=resp.data
        // $scope.userDetailsLength= $scope.userDetails.length;
           },function(resp) {
              // This block execute in case of error.
        });
      }else{
        APIService.setData({
            req_url: PrefixUrl + '/user/showAllVehicleByReferralUsersPagination/all' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
        }).then(function(resp) {
          console.log("====respPagination======",resp);
          $scope.userDetails=resp.data
        // $scope.userDetailsLength= $scope.userDetails.length;
           },function(resp) {
              // This block execute in case of error.
        });
      }
    }
    );


    $scope.$watch('settings.currentPage', function (value) {
      console.log('currentPage'+$scope.settings.currentPage)  
      if($scope.filterSearch){
        console.log('userDetailslll='+$scope.userDetails.length)
        APIService.setData({
          req_url: PrefixUrl + '/user/filterReferralUsers' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate,phone_number: $scope.phone_number, district: $scope.district, email: $scope.userEmail, buisness_name:$scope.buisness_name, userName:$scope.userName, state:$scope.userState } 
          }).then(function(resp) {
            console.log("====respPagination======",resp);
            $scope.userDetails=resp.data
          // $scope.userDetailsLength= $scope.userDetails.length;
             },function(resp) {
                // This block execute in case of error.
          });
      }else{
        APIService.setData({
              req_url: PrefixUrl + '/user/showAllVehicleByReferralUsersPagination/all' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
          }).then(function(resp) {
            console.log("====respPagination======",resp);
            $scope.userDetails=resp.data
          // $scope.userDetailsLength= $scope.userDetails.length;
             },function(resp) {
                // This block execute in case of error.
        });
      }
    }
    );

    if($stateParams.data){
         console.log('ddddddddddddddd')
         console.log(JSON.parse($stateParams.data));
         $scope.businessProfile= JSON.parse($stateParams.data);
        }

    if($stateParams.data){
     console.log('ddddddddddddddd111')
     console.log(JSON.parse($stateParams.data))
     var trancsaction= JSON.parse($stateParams.data);
     // console.log(JSON.parse($stateParams.data));
       APIService.getData({
            req_url: PrefixUrl + '/user/'+trancsaction.user_id
        }).then(function(resp) {
        $scope.businessProfile= resp.data;

           },function(resp) {
              // This block execute in case of error.
        });
     // $scope.businessProfile= JSON.parse($stateParams.data);
    }

   
    $scope.filterUsers = function() {
      
      $scope.filterSearch =true;

      if ($scope.startDate) {
        $scope.startDate= $scope.startDate;
      }else{
        $scope.startDate= null;
      }


      if ($scope.endDate) {
        $scope.endDate= $scope.endDate;
      }else{
        $scope.endDate= null;
      }
      if ($scope.phone_number) {
        $scope.phone_number= $scope.phone_number;
      }else{
        $scope.phone_number= null;
      }

      if ($scope.district) {
        var district= $scope.district;
      }else{
        $scope.district= null;
      }

      if ($scope.email) {
        $scope.email= $scope.email;
      }else{
        $scope.email= null;
      }

      if ($scope.buisness_name) {
        $scope.buisness_name= $scope.buisness_name;
      }else{
        $scope.buisness_name= null;
      }

      if ($scope.userName) {
        $scope.userName= $scope.userName;
      }else{
        $scope.userName= null;
      }

      if ($scope.userEmail) {
        $scope.userEmail= $scope.userEmail;
      }else{
        $scope.userEmail= null;
      }

      
      if ($scope.userState) {
        $scope.userState= $scope.userState;
      }else{
        $scope.userState= null;
      }

       


       APIService.setData({
          req_url: PrefixUrl + '/user/filterReferralUsers' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate,phone_number: $scope.phone_number, district: $scope.district, email: $scope.userEmail, buisness_name:$scope.buisness_name, userName:$scope.userName, state:$scope.userState } 
      }).then(function(resp) {
        $scope.userDetails= resp.data;
        $scope.getReferralUsersCount();
      },function(resp) {
       
      });
    }


    $scope.suspendUser = function(id) {
        APIService.updateData({
            req_url: PrefixUrl + '/user/update/'+ id,data:{suspend:true}
        }).then(function(resp) {
          // console.log("====resp======",resp);
          alert('Account suspended successfully')
          $state.go('app.UserDetails');

          console.log('reeeeeeeeee'+resp.suspend)
           },function(resp) {
              // This block execute in case of error.
        });
    }; 

    $scope.activateUser = function(id) {
        APIService.updateData({
            req_url: PrefixUrl + '/user/update/'+ id,data:{suspend:false}
        }).then(function(resp) {
          // console.log("====resp======",resp);
          alert('Account Activated successfully')
          $state.go('app.UserDetails');

          
          console.log('reeeeeeeeee'+resp.suspend)
           },function(resp) {
              // This block execute in case of error.
        });
    }; 


    $scope.doIfChecked = function(value) {
      console.log('value'+value)
      if (value == false) {
        
      }
    }; 


    // $scope.subscribedUsers = function(value) {
    //   console.log('subscribedUsers'+value)

    // }; 



    // $scope.value = function(value){
    // console.log('vvvv'+value)
    // }

    $scope.addUser = function(value,user_id){
      if (!angular.isArray($scope.checkboxList)){
        $scope.checkboxList = [];
      }
      if (-1 === $scope.checkboxList.indexOf(value)){
        var obj={};
        obj.reg_id= value;
        obj.user_id= user_id;
        $scope.checkboxList.push(obj);
        // $scope.checkboxList.push(value);
        console.log($scope.checkboxList)
      }
    }
    $scope.remove = function(value){
      if (!angular.isArray($scope.checkboxList)) {
        return;
      }
      var index = $scope.checkboxList.indexOf(value);
      if (-1 !== index) {
        $scope.checkboxList.splice(index, 1);
        console.log($scope.checkboxList)

      }
    }

    $scope.addMail = function(value,name,user_id){
      if (!angular.isArray($scope.checkboxListMail)){
        $scope.checkboxListMail = [];
      }
      if (-1 === $scope.checkboxListMail.indexOf(value)){
        var obj={};
        obj.name= name;
        obj.email= value;
        obj.user_id= user_id;
        $scope.checkboxListMail.push(obj);
        console.log($scope.checkboxListMail)
      }
    }

    $scope.addMessageUser = function(value,user_id){
      if (!angular.isArray($scope.checkboxListaddMessageUser)){
        $scope.checkboxListaddMessageUser = [];
      }
      if (-1 === $scope.checkboxListaddMessageUser.indexOf(value)){
        var obj={};
        obj.phone_number= value;
        obj.user_id= user_id;
        $scope.checkboxListaddMessageUser.push(obj);
        // $scope.checkboxListaddMessageUser.push(value);
        console.log($scope.checkboxListaddMessageUser)
      }
    }
     

    $scope.$watch('startDate', function (value) {
      try {
       startDate = new Date(value).toISOString().slice(0, 10);
       console.log('startDate'+startDate);

      } catch(e) {}
   
      if (!startDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



    $scope.$watch('endDate', function (value) {
      try {
       endDate = new Date(value).toISOString().slice(0, 10);
       console.log('enddate'+endDate);

      } catch(e) {}
   
      if (!endDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });

    $scope.getUsersForSelectedDate = function(startDate,enddate) {
      var obj= {};
      obj.startDate = startDate; 
      obj.enddate = enddate; 
        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersForSelectedDate' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
        $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
    };


    // $scope.getAllUsers = function() {
    //     APIService.getData({
    //         req_url: PrefixUrl + '/user/usersvehicle/all'
    //     }).then(function(resp) {
    //       console.log("====resp11111111111======",resp);
    //     // $scope.userDetails=resp.data
    //     $scope.userDetailsLength= resp.data.length;
    //        },function(resp) {
    //           // This block execute in case of error.
    //     });
    // };  

    $scope.getReferralUsersCountForFirstTime = function() {
        APIService.setData({
            req_url: PrefixUrl + '/user/getReferralUsersCountForFirstTime/all'
        }).then(function(resp) {
          console.log("====resp11111111111======",resp);
        // $scope.userDetails=resp.data
        $scope.userDetailsLength= resp.data;
           },function(resp) {
              // This block execute in case of error.
        });
    }; 


    $scope.getReferralUsersCount = function() {

      $scope.filterSearch =true;

      if ($scope.startDate) {
        $scope.startDate= $scope.startDate;
      }else{
        $scope.startDate= null;
      }


      if ($scope.endDate) {
        $scope.endDate= $scope.endDate;
      }else{
        $scope.endDate= null;
      }
      if ($scope.phone_number) {
        $scope.phone_number= $scope.phone_number;
      }else{
        $scope.phone_number= null;
      }

      if ($scope.district) {
        var district= $scope.district;
      }else{
        $scope.district= null;
      }

      if ($scope.email) {
        $scope.email= $scope.email;
      }else{
        $scope.email= null;
      }

      if ($scope.buisness_name) {
        $scope.buisness_name= $scope.buisness_name;
      }else{
        $scope.buisness_name= null;
      }

      if ($scope.userName) {
        $scope.userName= $scope.userName;
      }else{
        $scope.userName= null;
      }

      if ($scope.userEmail) {
        $scope.userEmail= $scope.userEmail;
      }else{
        $scope.userEmail= null;
      }

      
      if ($scope.userState) {
        $scope.userState= $scope.userState;
      }else{
        $scope.userState= null;
      }

       

        APIService.setData({
            req_url: PrefixUrl + '/user/referralUsersCount/all' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate,phone_number: $scope.phone_number, district: $scope.district, email: $scope.userEmail, buisness_name:$scope.buisness_name, userName:$scope.userName, state:$scope.userState } 
        }).then(function(resp) {
          console.log("====resp11111111111======",resp);
        // $scope.userDetails=resp.data
        $scope.userDetailsLength= resp.data[0].myCount;
           },function(resp) {
              // This block execute in case of error.
        });
    };  



    $scope.showAddBal = function(id) {
        $scope.add.id =id;
        $scope.addBalance =true;
    }; 
    
    // $scope.getReferralUsersCount();
    $scope.getReferralUsersCountForFirstTime();

    $scope.forCarDetails=function(vehi){
        console.log(vehi.vehicleDetails);
        $state.go('app.vehicleDetails',{data:JSON.stringify(vehi.vehicleDetails )});
    }


    $scope.addbalance = function(){

        APIService.updateData({
            req_url: PrefixUrl + '/user/refercode/'+ $scope.add.id,data:$scope.add
        }).then(function(resp) {
          // console.log("====resp======",resp);
          alert($scope.add.paytmMoney+"Rs. is add in user wallet");
          $scope.addBalance =false;
          $scope.postTransaction();
          $scope.getReferralUsersCount();
        
           },function(resp) {
              // This block execute in case of error.
        });
        
   
    }

    $scope.delete = function (id) {
      if (confirm("Are you sure?")) {
        console.log("sdfghggfdghgdgfh1111gf");
        APIService.removeData({
            req_url: PrefixUrl + '/user/:id',
            data: {id: id}
        }).then(function(resp) {
            $scope.getReferralUsersCount();
            $uibModalInstance.close(resp.data);
           },function(resp) {
            // This block execute in case of error.
        });
      }
    }

    $scope.postTransaction = function(){

       
       
        $scope.add.transaction_date=new Date();
        $scope.add.transaction_reason="Byadmin";
        $scope.add.transaction_type=$scope.transaction_group;
        $scope.add.due_to_user_name="Byadmin";
        $scope.add.user_id=$scope.add.id ;
        $scope.add.transaction_id=Number(String(Math.random()).slice(2)) + (Date.now() + Math.round( Date.now())).toString(36);
        $scope.add.amount=parseFloat(this.add.paytmMoney).toFixed(2);
        APIService.setData({
            req_url: PrefixUrl + '/trancsaction',data:$scope.add
        }).then(function(resp) {
          console.log("====resp======",resp.data.user_id);
            APIService.setData({
                req_url: PrefixUrl + '/trancsaction/getTransactionBalance/',
                data: {user_id: resp.data.user_id,transaction_id:resp.data.transaction_id}
            }).then(function(resp2) {
              console.log('resp2')
              console.log(resp2.data[0].balance)
                APIService.setData({
                  req_url: PrefixUrl + '/trancsaction/updateTransaction/',
                  data: {balance: parseFloat(resp2.data[0].balance).toFixed(2),transaction_id:resp.data.transaction_id}
                }).then(function(resp3) {
                  console.log('response------ ')
                  console.log(resp3)
                  console.log(resp2)
                    APIService.updateData({
                    req_url: PrefixUrl + '/user/update/'+resp3.data.user_id ,
                     data:{wallet_balance: parseFloat(resp2.data[0].balance).toFixed(2)}
                    }).then(function(resp4) {


                      
                   },function(resp) {

                    // This block execute in case of error.

                    });


               },function(resp) {

                // This block execute in case of error.

                });

                

               },function(resp) {
                // This block execute in case of error.
            });
          
          $scope.add={};
           },function(resp) {
              // This block execute in case of error.
              console.log('errrrrrrr');
              console.log(resp);
        });
        
   
    }


    $scope.sendNotification = function(){
      console.log('nnnnnnnnnn'+$scope.title)

      var reg_ids_obj= [];

      $scope.checkboxList.forEach(function (user, k) {                
        reg_ids_obj.push(user.reg_id);
      });

      var obj= {};

      // obj.id_list =$scope.checkboxList;
      obj.id_list =reg_ids_obj;
      obj.title =$scope.title;
        APIService.setData({
            req_url: PrefixUrl + '/user/sendPushNotification' , data: obj 
        }).then(function(resp) {
                  $scope.checkboxList.forEach(function (pushUser, k) {                
                    var pushObj= {};
                    pushObj.user_id= pushUser.user_id;
                    pushObj.type= 2;
                    pushObj.status= 1;
                    pushObj.title= $scope.title;
                    pushObj.send_by= 0;

                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: pushObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });

          alert("Notification sent successfully");

          // console.log("====resp======",resp);
        // $scope.userDetails=resp.data
        // if (resp.data[0].success) {
        //   alert("Notification sent successfully");

        // }
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }


    $scope.getUsersByState = function(){
      console.log('ss'+$scope.state)
      var obj= {};
      var obj1= [];
      obj1.push({'title':$scope.title});

      // obj.id_list =$scope.checkboxList;
      obj.state =$scope.state;
        APIService.setData({
            req_url: PrefixUrl + '/user/getStateUsersFcmToken' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
            resp.data.forEach(function (user, k) {                
                
                obj1.push({'id_list':user.fcm_registration_token});
                console.log('ddddddddddddd')
                console.log(obj1)
             
            });

           APIService.setData({

              req_url: PrefixUrl + '/user/sendPushNotificationToState' , data: obj1 

            }).then(function(resp21) {

                  resp.data.forEach(function (pushUser, k) {                
                    var pushObj= {};
                    pushObj.user_id= pushUser._id;
                    pushObj.type= 2;
                    pushObj.status= 1;
                    pushObj.title= $scope.title;
                    pushObj.send_by= 0;

                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: pushObj 

                    }).then(function(resp) {
                      alert('Notification sent')

                       },function(resp) {

                    });
                  });

               },function(resp) {

            });

        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }



    $scope.getUsersByCityForNotification = function(){
      console.log('ss'+$scope.selectedCityForNotification)
      var obj= {};
      var obj1= [];
      obj1.push({'title':$scope.title});

      // obj.id_list =$scope.checkboxList;
      obj.city =$scope.selectedCityForNotification;
        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersByCityForNotification' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
            resp.data.forEach(function (user, k) {                
                
                obj1.push({'id_list':user.fcm_registration_token});
                console.log('ddddddddddddd')
                console.log(obj1)
             
            });

           APIService.setData({

              req_url: PrefixUrl + '/user/sendPushNotificationToCity' , data: obj1 

            }).then(function(resp111) {

                resp.data.forEach(function (pushUser, k) {                
                    var pushObj= {};
                    pushObj.user_id= pushUser._id;
                    pushObj.type= 2;
                    pushObj.status= 1;
                    pushObj.title= $scope.title;
                    pushObj.send_by= 0;
                    
                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: pushObj 

                    }).then(function(resp) {
                      alert('Notification sent')

                       },function(resp) {

                    });
                  });

               },function(resp) {

            });

        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }






    $scope.sendEmailToSelectedUsers = function(){
      console.log('sendEmailToSelectedUsers'+$scope.emailTitle)
      console.log('emailBody'+$scope.emailBody)
      
      var obj= {};
      obj.email_list =$scope.checkboxListMail;
      obj.title =$scope.emailTitle;
      obj.emailBody =$scope.emailBody;

        APIService.setData({
            req_url: PrefixUrl + '/user/sendEmailToSelectedUsers' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
        if (resp.data.message=='success') {

            // obj.email_list
            obj.email_list.forEach(function (email, k) {                
              var emailObj= {};
              emailObj.user_id= email.user_id;
              emailObj.type= 0;
              emailObj.status= 1;
              emailObj.send_by= 0;
              emailObj.title= $scope.emailTitle;
              emailObj.body= $scope.emailBody;
              APIService.setData({

                req_url: PrefixUrl + '/notificationLog/create' , data: emailObj 

              }).then(function(resp) {
                // alert('Mail sent')

                 },function(resp) {

              });
            });

              
          alert("Email sent successfully");

        }
           },function(resp) {
              // This block execute in case of error.
        });
   
    }



    $scope.getUsersByStateForEmail = function(){
      console.log('ss'+$scope.state)
      var obj= {};
      var obj2= {};
      var obj1= [];
      
      obj1.push({'title':$scope.title});

      // obj.id_list =$scope.checkboxList;
      obj.state =$scope.state;
        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersByStateForEmail' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
            // resp.data.forEach(function (user, k) {                
                


            //     obj1.push({'id_list':user.email});
            //     console.log('email')
            //     console.log(obj1)
             
            // });

            var email_obj= [];
            console.log('ffffffffffffff')
            console.log(resp.data)
            resp.data.forEach(function (user, k) {                
                // email_obj.push(user.email);
                var obj= {};
          
                obj.name= user.name;
                obj.email= user.email;
                obj.user_id= user._id;
                email_obj.push(obj);
               
                               
            });

          obj2.email_list = email_obj;
          obj2.title =$scope.emailTitle;
          obj2.emailBody =$scope.emailBody;


           APIService.setData({

              req_url: PrefixUrl + '/user/sendEmailToState' , data: obj2 

            }).then(function(resp) {
                  obj2.email_list.forEach(function (email, k) {                
                    var emailObj= {};
                    emailObj.user_id= email.user_id;
                    emailObj.type= 0;
                    emailObj.status= 1;
                    emailObj.send_by= 0;
                    emailObj.title= $scope.emailTitle;
                    emailObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: emailObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });
              alert('Mail sent')

               },function(resp) {

            });

        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }



    
    $scope.getUsersByCityForEmail = function(){
      console.log('selectedCity'+$scope.selectedCity)

      var obj= {};
      var obj2= {};
      var obj1= [];
      
      obj1.push({'title':$scope.title});

      // obj.id_list =$scope.checkboxList;
      obj.city =$scope.selectedCity;
        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersByCityForEmail' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
            // resp.data.forEach(function (user, k) {                
                


            //     obj1.push({'id_list':user.email});
            //     console.log('email')
            //     console.log(obj1)
             
            // });

            var email_obj= [];
            console.log('ffffffffffffff')
            console.log(resp.data)
            resp.data.forEach(function (user, k) {                
                // email_obj.push(user.email);
                var obj= {};
          
                obj.name= user.name;
                obj.email= user.email;
                obj.user_id= user._id;
                email_obj.push(obj);
               
                               
            });

          obj2.email_list = email_obj;
          obj2.title =$scope.emailTitle;
          obj2.emailBody =$scope.emailBody;


           APIService.setData({

              req_url: PrefixUrl + '/user/sendEmailToState' , data: obj2 

            }).then(function(resp) {
                obj2.email_list.forEach(function (email, k) {                
                    var emailObj= {};
                    emailObj.user_id= email.user_id;
                    emailObj.type= 0;
                    emailObj.status= 1;
                    emailObj.send_by= 0;
                    emailObj.title= $scope.emailTitle;
                    emailObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: emailObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });
              alert('Mail sent')

               },function(resp) {

            });

        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }

    $scope.sendMessageToSelectedUsers = function(){
      console.log('nnnnnnnnnn'+$scope.title)
      var phone_numbers= [];
           
            $scope.checkboxListaddMessageUser.forEach(function (data, k) {                
              // console.log(data)
              phone_numbers.push(data.phone_number);
            });


      var obj= {};
      // obj.phone_number_list =$scope.checkboxListaddMessageUser;
      obj.phone_number_list =phone_numbers;
      obj.title =$scope.title;
        APIService.setData({
            req_url: PrefixUrl + '/user/sendMessageToSelectedUsers' , data: obj 
        }).then(function(resp) {
                $scope.checkboxListaddMessageUser.forEach(function (mobile, k) {                
                    var mobileObj= {};
                    mobileObj.user_id= mobile.user_id;
                    mobileObj.type= 1;
                    mobileObj.status= 1;
                    mobileObj.send_by= 0;
                    mobileObj.title= $scope.title;
                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: mobileObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });
            alert("Message sent")

          // console.log("====resp======",resp);
        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }


    $scope.getUsersByStateForMessage = function(){
      console.log('ss'+$scope.state)
      var obj= {};
      var obj1= [];
      obj1.push({'title':$scope.title});

      // obj.id_list =$scope.checkboxList;
      obj.state =$scope.state;
        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersByStateForMessage' , data: obj 
        }).then(function(resp) {
          // console.log("====resp======",resp);
            resp.data.forEach(function (user, k) {                
                
                obj1.push({'id_list':user.phone_number});
                console.log('phone_number')
                console.log(obj1)
             
            });

           APIService.setData({

              req_url: PrefixUrl + '/user/sendMessageToState' , data: obj1 

            }).then(function(resp12) {


                  resp.data.forEach(function (mobile, k) {                
                    var mobileObj= {};
                    mobileObj.user_id= mobile._id;
                    mobileObj.type= 1;
                    mobileObj.status= 1;
                    mobileObj.send_by= 0;
                    mobileObj.title= $scope.title;
                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: mobileObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });

                  alert("Message sent")

               },function(resp) {

            });

        // $scope.userDetails=resp.data
        console.log('resp')
        console.log(resp)
           },function(resp) {
              // This block execute in case of error.
        });
   
    }



    $scope.sendNotificationToAllUsers = function() {
        APIService.setData({
            req_url: PrefixUrl + '/user/getAllUsersOnce'
        }).then(function(resp) {
        // $scope.userDetails=resp.data
        var obj= {};
        var fcm_token_obj= [];
        var phone_number_obj= [];
        var email_obj= [];
        resp.data.forEach(function (user, k) {                
            fcm_token_obj.push(user.fcm_registration_token);
            phone_number_obj.push(user.phone_number);
            email_obj.push(user.email);
           
                           
        });

        console.log('objeeee')
        console.log(fcm_token_obj)
        console.log(email_obj)
        console.log(phone_number_obj)


            console.log('nnnnnnnnnn'+$scope.title)
            var obj= {};
            obj.id_list =fcm_token_obj;
            obj.title =$scope.title;
              APIService.setData({
                  req_url: PrefixUrl + '/user/sendPushNotification' , data: obj 
              }).then(function(resp121) {
                // console.log("====resp======",resp);
              // $scope.userDetails=resp.data
                resp.data.forEach(function (pushUser, k) {                
                    var pushObj= {};
                    pushObj.user_id= pushUser._id;
                    pushObj.type= 2;
                    pushObj.status= 1;
                    pushObj.title= $scope.title;
                    pushObj.send_by= 0;

                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: pushObj 

                    }).then(function(resp) {
                      // alert('Notification sent')

                       },function(resp) {

                    });
                  });
              alert("Notification sent");
              console.log('resp')
              console.log(resp)
                 },function(resp) {
                    // This block execute in case of error.
              });


   

           },function(resp) {
              // This block execute in case of error.
        });
    }; 


    $scope.sendMessageToAllUsers = function() {
          
        APIService.setData({
            req_url: PrefixUrl + '/user/getAllUsersOnce'
        }).then(function(resp) {
        var phone_number_obj= [];
        resp.data.forEach(function (user, k) {                
            phone_number_obj.push(user.phone_number);
           
                           
        });

      var obj= {};
      obj.phone_number_list =phone_number_obj
      obj.title =$scope.title;
        APIService.setData({
            req_url: PrefixUrl + '/user/sendMessageToSelectedUsers' , data: obj 
        }).then(function(resp1) {

                resp.data.forEach(function (mobile, k) {                
                    var mobileObj= {};
                    mobileObj.user_id= mobile._id;
                    mobileObj.type= 1;
                    mobileObj.status= 1;
                    mobileObj.send_by= 0;

                    mobileObj.title= $scope.title;
                    // mobileObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: mobileObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });
          alert("Message sent")

          // console.log("====resp======",resp);
        // $scope.userDetails=resp.data
        console.log('respmmmmm')
        // console.log(resp.data.message)
           },function(resp) {
            // if (resp.data.message=='success') {
            //   alert("sent successfully");

            // }

              // This block execute in case of error.
        });
      });
    }; 


    $scope.sendMailToAllUsers = function() {
        APIService.setData({
            req_url: PrefixUrl + '/user/getAllUsersOnce'
        }).then(function(resp) { 
          console.log('resp[[[')
          console.log(resp)
          // var obj= {};
          var obj1= {};
          var email_obj= [];
          resp.data.forEach(function (user, k) {                
              // email_obj.push(user.email);
              var obj= {};
        
              obj.name= user.name;
              obj.email= user.email;
              obj.user_id= user._id;
              email_obj.push(obj);
             
                             
          });
          console.log('llllll')
          console.log(email_obj)
          // var obj= {};
          obj1.email_list = email_obj;
          obj1.title =$scope.emailTitle;
          obj1.emailBody =$scope.emailBody;

            APIService.setData({
                req_url: PrefixUrl + '/user/sendEmailToSelectedUsers' , data: obj1 
            }).then(function(resp) {
              // console.log("====resp======",resp);
            // $scope.userDetails=resp.data
            console.log('resp')
            console.log(resp)
            if (resp.data.message=='success') {
                  obj1.email_list.forEach(function (email, k) {                
                    var emailObj= {};
                    emailObj.user_id= email.user_id;
                    emailObj.type= 0;
                    emailObj.status= 1;
                    emailObj.send_by= 0;
                    emailObj.title= $scope.emailTitle;
                    emailObj.body= $scope.emailBody;
                    APIService.setData({

                      req_url: PrefixUrl + '/notificationLog/create' , data: emailObj 

                    }).then(function(resp) {
                      // alert('Mail sent')

                       },function(resp) {

                    });
                  });
              alert("Email sent successfully");

            }
               },function(resp) {
                  // This block execute in case of error.
            });

          },function(resp) {
              // This block execute in case of error.
        });
    }; 

    // $scope.BusinessProfile = function(){

    // }

    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

      // var obj= {};
      // obj.startDate = startDate; 
      // obj.enddate = enddate; 
      //   APIService.setData({
      //       req_url: PrefixUrl + '/user/getUsersForSelectedDate' , data: obj 
      //   }).then(function(resp) {
      //     // console.log("====resp======",resp);
      //   $scope.userDetails=resp.data
      //   console.log('resp')
      //   console.log(resp)
      //      },function(resp) {
      //         // This block execute in case of error.
      //   });
    };

     $scope.alltrips = function(prod){
            $state.go("app.tripDetails",{data:JSON.stringify(prod)})
        console.log(prod._id);
    }


    $scope.updateUser = function(user){
            $state.go("app.updateProfile",{data:JSON.stringify(user)})
    }
     $scope.reloadPage = function(){
         location.reload();
        
    } 


    });