angular.module('offers.controllers', [])

    .controller('offersCtrl', function ($scope,APIService, $state,$stateParams,$http,$base64) {

        $scope.offerObj={};
      
        $scope.getOfferCategory= function(){
	        console.log('offerObj -- ',$scope.offerObj)
	      	APIService.setData({
	          req_url: PrefixUrl + '/offerCategory/getOfferCategory' ,data:{} 
	      	}).then(function(resp) {
	            $scope.offerCategories=resp.data
	             },function(resp) {
	                // This block execute in case of error.
	         });

	     }

      $scope.getOfferCategory();



      $scope.uploadFile = function(files) {

      	var imageData=$base64.encode(files[0]);
      	console.log('base64--------- ',imageData);
		// var s3 = S3.new
      	console.log('s3--------- ',s3);
		
// https://www.cheynewallace.com/uploading-to-s3-with-angularjs/
      	$scope.file=files;
      			$scope.creds = {
					  bucket: 'triva-in',
					  access_key: '********************',
					  secret_key: '4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t'
					}
					// console.log('s33333333 ',new AWS.S3())
					// $scope.upload = function() {
					  // Configure The S3 Object 
					  // var AWS = require('aws-sdk');
					  AWS.config.update({ accessKeyId: $scope.creds.access_key, secretAccessKey: $scope.creds.secret_key });
					  // AWS.config.region = 'us-east-1';
					  var bucket = new AWS.S3({ params: { Bucket: $scope.creds.bucket } });

					  if($scope.file) {
					    var params = { Key: $scope.file.name, ContentType: $scope.file.type, Body: $scope.file, ServerSideEncryption: 'AES256' };

					    bucket.putObject(params, function(err, data) {
					      if(err) {
					        // There Was An Error With Your S3 Config
					        alert(err.message);
					        return false;
					      }
					      else {
					        // Success!
					        alert('Upload Done');
					      }
					    })
					    .on('httpUploadProgress',function(progress) {
					          // Log Progress Information
					          console.log(Math.round(progress.loaded / progress.total * 100) + '% done');
					        });
					  }
					  else {
					    // No File Selected
					    alert('No File Selected');
					  }
					// }

		    // var fd = new FormData();
		    // //Take the first selected file
		    // fd.append("file", files[0]);
		    // console.log('uploadFile -- ',fd)
		    //  var blob = new Blob([files], { type : "image/png"});
      // 		fd.append("file", blob);
		    // // $http.post(PrefixUrl + '/offer/uploadOfferImage', fd, {
		    // $http.post(PrefixUrl + '/utility/images', imageData, {
		    //     withCredentials: true,
		    //     headers: {'Content-Type': undefined },
		    //     transformRequest: angular.identity
		    // })

		};

      $scope.addOffer= function(files){
        console.log('offerObj -- ',$scope.offerObj)
        console.log('offerObj -- ',files)



           // var fd = new FormData();
           // fd.append('file', files);

           // $http.post(PrefixUrl + '/offer/uploadOfferImage', fd, {
           //    transformRequest: angular.identity,
           //    headers: {'Content-Type': undefined}
           // })

           // .success(function(){
           // })

           // .error(function(){
           // });
   	// 	$scope.file= files[0];
    //      var fd = new FormData();
	   //  //Take the first selected file
	   //  fd.append("file", files[0]);
	   //  var imageData= files;
    //     console.log('imageData -- ',fd)

	   //   var config = {
		  //   headers: { 'Content-type': 'multipart/form-data' }
		  // };

    //     var promise = $http.post(PrefixUrl + '/offer/uploadOfferImage',fd,config);
	   //  promise.then(function(response){
	   //    // $scope.result="Success "+response.status;
	   //  }).catch(function(errorResponse) {
	   //    // $scope.result="Error "+errorRespone.status;
	   //  });

	   	// APIService.setData({
     //      req_url: PrefixUrl + '/offer/uploadOfferImage' ,data:{'fd':fd} 
     //  	}).then(function(resp) {
     //        // $scope.userDetails=resp.datas
     //         },function(resp) {
     //            // This block execute in case of error.
     //     });

	    // $http.post('/images', fd, {
	    //     withCredentials: true,
	    //     headers: {'Content-Type': undefined },
	    //     transformRequest: angular.identity
	    // }).success().error();


      //     var f = document.getElementById('file').files[0],
		    //  r = new FileReader();

		    // r.onloadend = function(e) {
		    //   var data = e.target.result;
		    //   console.log('binary data ',data)
		    //   //send your binary data via $http or $resource or do anything else with it
		    
		    //   APIService.setData({
	     //      req_url: PrefixUrl + '/utility/images' ,data:data 
	     //  	}).then(function(resp) {
	     //        $scope.userDetails=resp.data
	     //         },function(resp) {
	     //            // This block execute in case of error.
	     //     });
		    // }

		    // r.readAsBinaryString(f);
   		





      	APIService.setData({
          req_url: PrefixUrl + '/offer/create' ,data:$scope.offerObj 
      	}).then(function(resp) {
			$scope.userDetails=resp.data
			$scope.getOfferCategory();
			alert('Offer Submitted successfully')
				location.reload(); 
             },function(resp) {
				 
				// alert('Offer Submitted successfully')
				// location.reload(); 
                // This block execute in case of error.
         });

      }





  //     	$scope.uploadFile = function(files) {
  //     		console.log('files-- ',files)
		//     var fd = new FormData();
		//     //Take the first selected file
		//     fd.append("file", files[0]);


        	

		//     // $http.post(uploadUrl, fd, {
		//     //     withCredentials: true,
		//     //     headers: {'Content-Type': undefined },
		//     //     transformRequest: angular.identity
		//     // }).success( ...all right!... ).error( ..damn!... );

		// };

    });