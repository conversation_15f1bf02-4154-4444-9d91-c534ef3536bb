import Responder from '../../lib/expressResponder';
import SupportTicket from '../models/supportTicket';
import SupportTicketComment from '../models/supportTicketComment';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class SupportTicketCommentController {

  static page(req, res) {

     SupportTicketComment.aggregate([ 
              {
                $match: {
                          'support_id':ObjectId(req.params.id),
                        
                        }
                    
              },
               // { $lookup:
               //   {
               //     from: 'users',
               //     localField: 'user_id',
               //     as: 'SupportTicketComment',
               //   }
               // },
               
               // { $lookup:
               //   {
               //     from: 'users',
               //     localField: 'SupportTicketed_by',
               //     foreignField: '_id',
               //     as: 'SupportTicketedBy'
               //   }
               // }
               ,
               { 
                "$sort": { 
                    "time": -1,
                } 
            }, 

                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))



   
  }

    
  static totalUnread(req, res) {
       SupportTicketComment.aggregate([ 
        {
          $match: {
            // $or: [
            //   {
                "assign_user": ObjectId(req.params.id)
            //   },
            //   {
            //     "recieverId": ObjectId(req.params.id)
            //   }
            // ]
          }
        }
        // ,
        // {
        //   "$project": {
        //     assign_user: 1,
        //     category: 1,
        //     created_at: 1,
        //     description: 1,
        //     status: 1,
        //     title: 1,
        //     user_id: 1,
        //     seen:1,
        //     count:1,
        //     // fromsenderId: [
        //     //   "$user_id",
        //     //   // "$senderId"
        //     // ]
        //   }
        // },
        // {
        //   $unwind: "$fromsenderId"
        // },
        // {
        //   $sort: {
        //     "fromsenderId": 1
        //   }
        // },
        // {
        //   $group: {
        //     _id: "$_id",
        //     "fromsenderId": {
        //       $push: "$fromsenderId"
        //     },
        //     "recieverId": {
        //       "$first": "$recieverId"
        //     },
        //     "senderId": {
        //       "$first": "$senderId"
        //     },
        //     "message": {
        //       "$first": "$message"
        //     },
        //     "myId": {
        //       "$first": "$myId"
        //     },

        //     "createdAt": {
        //       "$first": "$createdAt"
        //     },

        //     "seen": {
        //       "$first": "$seen"
        //     }
        //   }
        // },
        // {
        //   "$sort": {
        //     "createdAt": -1
        //   }
        // },

        // {
        //   "$group": {
        //     "_id": "$fromsenderId",
        //     "recieverId": {
        //       "$first": "$recieverId"
        //     },
        //     "senderId": {
        //       "$first": "$senderId"
        //     },
        //     "message": {
        //       "$first": "$message"
        //     },
        //     "myId": {
        //       "$first": "$myId"
        //     },
        //     "createdAt": {
        //       "$first": "$createdAt"
        //     },
        //      "seen": {
        //       "$first": "$seen"
        //     },
        //     "count": { $sum: 1 } ,

        //   }
        // },
        // {
        //   "$group": 
        //     {
              
        //     // myId: {$ne:req.params.id},
        //     // seen:false,
        //    count: { $sum: 1 } ,
            
        //   }
        // },
    //      { $lookup:
    //        {
    //          from: 'users',
    //          localField: 'senderId',
    //          foreignField: '_id',
    //          as: 'senderDetials'
    //        }
    //      },
    //        { $lookup:
    //        {
    //          from: 'users',
    //          localField: 'recieverId',
    //          foreignField: '_id',
    //          as: 'recieveDetials'
    //        }
    //      }
    //        SupportTicketComment.aggregate([ 
    //           {
    //                 $match: {
    //                     seen:false,

    //                   }
                    
    //           },
    //           {
    //            $group: {
    //                 _id: {
                      
    //                 },
    //                 myCount: { $sum: 1 } ,
    //               }
    //           },
      ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
  }



  static count(req, res) {
  }

  // static show(req, res) {

  //   console.log(req.params.user_id)
  //   Vehicle.find({user_id: req.params.user_id})
  //   .then((veh)=>Responder.success(res,veh))
  //   .catch((err)=>Responder.operationFailed(res,err))
   
  // }
 
static getComments(req, res) {
 console.log("id...",req.params.id)
 SupportTicketComment.aggregate([ 
      {
            $match: {
                support_id: ObjectId(req.params.id)
                      
              }
            
      },
         { $lookup:
                 {
                   from: 'users',
                   localField: 'submitted_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               }
               

            ])

    // SupportTicketComment.find({support_id: ObjectId(req.params.id)})
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err)) 

  }

  // static getpage(req, res) {
  //      console.log(req.params)
  //      SupportTicketComment.find({user_id: req.params.id})
  //      .then((rep)=>Responder.success(res,rep))
  //      .catch((err)=>Responder.operationFailed(res,err))
      
  // }
static create(req, res) {
   
    //re-open ticket on each reply
    SupportTicket.findOneAndUpdate({ _id: req.body.support_id }, {status:true},{new:true}).then((d)=>{
      console.log(" Ticket re-opened ", d)
    });

    SupportTicketComment.create(req.body)
       .then((rep)=>Responder.success(res,rep))
       .catch((err)=>Responder.operationFailed(res,err))

    
      
  }
 
}

