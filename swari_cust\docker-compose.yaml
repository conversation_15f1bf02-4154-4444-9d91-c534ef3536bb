version: '3'
services:
  app:
    build: .
    network_mode: "host"
    environment:
      - SMS_GURU_HOST=www.textguru.in
      - SMS_GURU_SEND_TEXT_URL=/api/v22.0/?
      - SMS_GURU_USERNAME=navpreet.au
      - SMS_GURU_PASSWORD=Nav!@txt9
      - SMS_GURU_TEMPLATEID_REGISTER=1707169831386859678
     # - MONGO_URI=mongodb://host.docker.internal:27017/triva  # Use special Docker DNS
      - MONGO_URI=mongodb://localhost:27017/triva
      - PORT=3004
      - JWT_SECRET=jo1i23hjiey78diuasiubni
      - TZ=Asia/Calcutta
      - MQTT_BROKER_URL=ws://*************:8083/mqtt
   
   
