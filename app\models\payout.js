import mongoose from 'mongoose';
const Schema = mongoose.Schema;
var ObjectId = mongoose.Schema.Types.ObjectId;
const timeZone = require('mongoose-timezone');


const PayoutSchema = new Schema({
  user_id:ObjectId,
  amount:Number,
  payout_id:Number,  
  status:{type:Boolean,default:false},
  created_at:Date,
  updated_at:Date,
  remarks:String
});
 PayoutSchema.plugin(timeZone, { paths: ['created_at','updated_at'] });

export default mongoose.model('Payout', PayoutSchema);
