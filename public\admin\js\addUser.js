angular.module('addUser.controllers', [])

     .controller('addUserCtrl', function($scope, $state,APIService,$stateParams,$http) {

        $scope.userCount;
        
      
        $scope.getTotalSMSBalance = function(){
            // $http.get("http://bulksms.genx-infotech.com/rest/services/sendSMS/getClientRouteBalance?AUTH_KEY=7f1547ae1f47dc1bb0eb7478e2745b71").then(function (data, status, headers, config) { 
            //     $scope.smsBalance= data.data[0].routeBalance;
            // },function (data, status, headers, config) { 
            // })


             APIService.setData({
                req_url: PrefixUrl + '/user/getTotalSMSBalance/' 
            }).then(function(data) {
         //    var smsData= JSON.parse(data.data.body);
         //    console.log('smsData  ?? ',smsData[0].balance)
         //
         //       $scope.smsBalance= smsData[0].balance;
         console.log('smsData  ?? ', data.data.balance);
        $scope.smsBalance = data.data.balance;

            });

        }

        $scope.series = ['Normal User', 'Referal User'];
        
        $scope.usersByMonthOptions = {
             scales: {
               xAxes: [{
                 stacked: true
                }],
               yAxes: [{
                 stacked: true
                }]
            },
            legend: {
              display: true,
              position: 'bottom'
            }
         }

        $scope.getTotalSMSBalance();

        $scope.getDashboardDataForSelectedMonth = function(startDate,enddate) {
            

            $scope.getPayoutCreatedReportForMonth(startDate,enddate);
            // $scope.getBoostTripGstTotalForSelectedMonth(startDate,enddate);
            $scope.getDashboardUserCountForMonth(startDate,enddate);
            // $scope.getDashboardTotalReferralIncomeForMonth(startDate,enddate);
            // $scope.getDashboardTotalBoostedIncomeForMonth(startDate,enddate);
            // $scope.getDashboardUsersCountForSelectedMonth(startDate,enddate);
            $scope.getDashboardAllUsersCountForSelectedMonth(startDate,enddate);
            // $scope.getDashboardWithdrawTotalForSelectedMonth(startDate,enddate);
            // $scope.getDashboardGstTotalForSelectedMonth(startDate,enddate);
            // $scope.getDashboardSubscriptionTotalForSelectedMonth(startDate,enddate);
            $scope.getDashboardUsersForSelectedMonth(startDate,enddate);
            $scope.getDashboardReferralIncomeForSelectedMonth(startDate,enddate);
            $scope.getDashboardSubscriptionFeesForSelectedMonth(startDate,enddate);
            $scope.getDashboardBoostIncomeForSelectedMonth(startDate,enddate);
            $scope.getDashboardTotalGstForSelectedMonth(startDate,enddate);
            $scope.getDashboardTripBoostGstForSelectedMonth(startDate,enddate);


         
        };






        $scope.getPayoutCreatedReport = function(){
            APIService.getData({ req_url: PrefixUrl + "/payout/getPayoutTotal"}).then(function (res) {
           console.log('result')
           console.log(res)
           // $scope.payoutReport= res.data;

            // $scope.totalPayout= 0;
            // $scope.payoutReport.forEach(function (payout, k) {
            // console.log(payout)                
                $scope.totalPayout= res.data[0].total;
              // });
            
            },function(er){

            })

        }

        $scope.getPayoutCreatedReport();
        



        $scope.getUserCount = function(){
            APIService.setData({
                req_url: PrefixUrl + '/user/userCountAllCount/'
            }).then(function(respAllCount) {
                $scope.respAllCount= respAllCount.data;
            });


            APIService.setData({
                        req_url: PrefixUrl + '/user/userCount/'
                }).then(function(resp) {
                $scope.userCount= resp.data;
                console.log('1111111  ---')
                $scope.getSubscriptionTotalData();
            })

        }

        $scope.getUserCount();


        $scope.getSubscriptionTotalData = function(){
            APIService.setData({
                req_url: PrefixUrl + '/user/getSubscriptionTotal/'
            }).then(function(resp11) {
            // $scope.userData1= [resp4.data[0].total];
                
                if(resp11.data.length > 0){
                    console.log('subscriptionTotal--121 ',resp11.data[0].total)
                $scope.subscriptionTotal= [resp11.data[0].total];
                $scope.subscriptionData= resp11.data[0].total;
                $scope.gstTotal= resp11.data[0].total_gst;
                $scope.totalTransaction = resp11.data[0].count;
        
                }
                else{
                    $scope.subscriptionTotal= [0];
                $scope.subscriptionData= 0;
                $scope.gstTotal= 0;
                $scope.totalTransaction = 0;

                }
                console.log('1111112222222  ---')

                console.log('gsttotal------ ',$scope.gstTotal);
                $scope.subscriptionL= ["Total Subscription Amount"];
                $scope.subscriptionLColors = [  "#2683C6"] ;
                $scope.getBoostedTotalData();                
               },function(resp) {
                  // This block execute in case of error.
            });
        }



        $scope.getBoostedTotalData = function(){
            APIService.setData({
                        req_url: PrefixUrl + '/user/totalBoostedIncome/'
                }).then(function(resp3) {

                    if (resp3.data.length) {
                        $scope.totalBoostedIncome= resp3.data[0].total;
                        $scope.tripBoostGst= resp3.data[0].total_gst;
                        $scope.userCount= $scope.respAllCount.normalUser + $scope.respAllCount.referalUser;
                        
        //                $scope.totalReferralIncome= resp1.data[0].total;
                        $scope.userData= [$scope.userCount,$scope.totalReferralIncome,resp3.data[0].total];
                    }else{
                        $scope.totalBoostedIncome= 0;
                        $scope.tripBoostGst= 0;

                    }
                   
                    $scope.usersLabels= ["Total Number of Users","Total Referral Income","Total Boosted Income"];
                    $scope.usersColors = [  "#84ACB6", "#80D1ED", "#ED9AA1"] ;
                    console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
                    console.log('11111133333  ---')
                    $scope.getReferralTotalData();
            })
        }


        $scope.getReferralTotalData = function(){
            APIService.setData({
                        req_url: PrefixUrl + '/user/totalReferralIncome/'
                }).then(function(resp1) {
    //                $scope.totalReferralIncome= resp1.data[0].total;

                     if (resp1.data.length) {
                        $scope.totalReferralIncome= resp1.data[0].total;
                    }else{
                        $scope.totalReferralIncome= 0;
                    }
                    console.log('1111114444  ---')
                    $scope.userData= [$scope.userCount,$scope.totalReferralIncome,$scope.totalBoostedIncome];

                    $scope.getGstData();
            })
        }


 


  
   





        // APIService.setData({
        //     req_url: PrefixUrl + '/user/userCount/'
        // }).then(function(resp1) {
        //     $scope.userCount= resp.data;
        //         APIService.setData({
        //             req_url: PrefixUrl + '/user/totalReferralIncome/'
        //         }).then(function(resp1) {
        //                 // APIService.setData({
        //                 //     req_url: PrefixUrl + '/user/totalGst/'
        //                 // }).then(function(resp2) {
        //                     console.log(resp)
        //                         APIService.setData({
        //                             req_url: PrefixUrl + '/user/totalBoostedIncome/'
        //                         }).then(function(resp3) {
        //                             if (resp3.data.length) {
        //                                 $scope.totalBoostedIncome= resp3.data[0].total;
        //                                 $scope.tripBoostGst= resp3.data[0].total_gst;
        //                                 $scope.totalReferralIncome= resp1.data[0].total;
        //                                 $scope.userData= [$scope.userCount,resp1.data[0].total,resp3.data[0].total];
        //                             }else{
        //                                 $scope.totalBoostedIncome= 0;
        //                                 $scope.tripBoostGst= 0;

        //                             }
        //                             if (resp1.data.length) {
        //                                 $scope.totalReferralIncome= resp1.data[0].total;
        //                             }else{
        //                                 $scope.totalReferralIncome= 0;
        //                             }
        //                             $scope.usersLabels= ["Total Number of Users","Total Referral Income","Total Boosted Income"];
        //                             console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
        //                             $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;
        //                             $scope.getGstData();
        //                             // $scope.getDashboardGstTotalForSelectedMonth();

        //                             console.log('getdata-------')

        //                             // $scope.totalGst= [resp.data[0].total];
        //                             // $scope.usersLabels2= ["Total Gst"];
        //                             // $scope.gst= resp.data[0].total;
        //                             // $scope.usersData = [$scope.userCount];
        //                             console.log(resp)
        //                            },function(resp) {
        //                               // This block execute in case of error.
        //                         });
        //                 //    },function(resp) {
        //                 //       // This block execute in case of error.
        //                 // });
        //             console.log(resp)
        //            },function(resp) {
        //               // This block execute in case of error.
        //         });

        //     console.log($scope.userCount)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });


        APIService.setData({
            req_url: PrefixUrl + '/user/getWithdrawTotal/'
        }).then(function(resp4) {
        $scope.userData1= [resp4.data[0].total];
        $scope.withdrawTotal= resp4.data[0].total;
        $scope.usersLabels1= ["Total Withdraw Amount"];
         $scope.usersColors1 = [   "#80D1ED"] ;
            
           },function(resp) {
              // This block execute in case of error.
               localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        });

        

        APIService.setData({
            req_url: PrefixUrl + '/user/getWithdrawRequestTotal/'
        }).then(function(resp) {
             $scope.withdrawRequestTotal= resp.data[0].total;
            
           },function(resp) {

        });




       var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }





        $scope.getGstData = function(){

                // APIService.setData({
                //     req_url: PrefixUrl + '/user/getBoostTripGstTotal/'
                // }).then(function(resp5) {
                //     // APIService.setData({
                    //     req_url: PrefixUrl + '/user/totalGst/'
                    // }).then(function(resp2) {

                        console.log('trip boost gst-- ',$scope.tripBoostGst);
                        $scope.boostTripGstTotalData= [$scope.gstTotal,$scope.tripBoostGst,$scope.gstTotal + $scope.tripBoostGst];
                        // if (resp2.data.length > 0) {
                        //     $scope.gstTotal= resp2.data[0].total;
                        // }else{
                        //     $scope.gstTotal= 0;
                        // }
                        // if (resp5.data.length > 0) {
                        //     $scope.tripBoostGst= resp5.data[0].total;
                        // }else{
                        //     $scope.tripBoostGst= 0;
                        // }
  
                         $scope.totalCr = $scope.totalBoostedIncome+$scope.subscriptionData;
                          console.log('totalCr-- ',$scope.totalBoostedIncome+ '  ' +$scope.subscriptionData)
        
                        $scope.tripGstL1= ["Gst","Trip Gst Total","Total Gst"];
                        $scope.tripGstL1Colors = [  "#F7931E", "#80D1ED", "#3494BA"] ;
                        console.log($scope.tripBoostGst+$scope.tripBoostGst)
                        $scope.totalDr= $scope.tripBoostGst+ $scope.gstTotal +$scope.totalReferralIncome;
                        // console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
                        $scope.grandGst= $scope.tripBoostGst+$scope.tripBoostGst;

                          console.log('totalDr-- ',$scope.tripBoostGst+ '  '+$scope.totalReferralIncome)

                    // },function(resp) {
                    //   // This block execute in case of error.
                    // }); 
                //    },function(resp) {
                //       // This block execute in case of error.
                // });

        }


        APIService.setData({
            req_url: PrefixUrl + '/user/getUsersByMonth/'
        }).then(function(resp) {
                console.log('rrrrrrrrrr')
                console.log(resp.data[0])
                var userObj= [];
                var userObjMonths= [];
                // resp.data[0]
                var month_get =['Jan','Feb','Mar','Aprl','May','June','July','Aug','Sep','Oct','Nov','Dec'];

                if(resp.data.length > 0)
                 {   
                    var yearValue= resp.data[0]._id.year;
              // $scope.series = ['Series A', 'Series B'];
                   var userObj1= [];
                // var j=1;
                //  for (var i = 1; i<resp.data[0]._id.month;i++) {

                //       //userObj.push(yearValue +'/'+ month_get[j]);
                //       //userObj1.push(0);
                //       j++;
                //  }

                
                 var monthData= 12-(resp.data[0]._id.month); 
                 console.log('monthData-- ',monthData)
                 for (var i = 0; i <=resp.data.length;i++) {
                    if(resp.data[i])
                     {   
                        console.log('month compare -- ',resp.data[i]._id.month+'   iii  '+i)
                         
                    console.log('respData--- ',resp.data[i]._id.month)
                     // if(resp.data[i]._id.month == i)
                     // {
                      userObj[resp.data[i]._id.month]=(resp.data[i]._id.year+'/'+ month_get[resp.data[i]._id.month]);
                      userObj1[resp.data[i]._id.month]=(resp.data[i].myCount);

                  //      userObj.push(resp.data[i]._id.year+'/'+ month_get[j]);
                    //   userObj1.push(resp.data[i].myCount);

                     //    // userObjMonths.push(user._id.month);
                     // }
                       j++;

                  }
                  // else{
                  //      //  userObj.push(yearValue+'/'+ month_get[j]);
                  //     // userObj1.push(0);
                  //      j++;
                  // }
                 }


                 for(var j=1;j<=12;j++)
                 {
                  if(!userObj[j])
                   {
                    userObj[j]=(yearValue +'/'+ month_get[j]);
                      userObj1[j]=0;

                   } 
                 }
                 $scope.labels = userObj;
                 $scope.colors = ["#8CC63F"];
                // $scope.data=userObj1;
                 $scope.usersByMonthOptions = userObj1;
                 console.log('labels --  ',userObj)
                 console.log('count --  ',userObj1)
                 console.log('colors --  ',$scope.colors)
             }


              APIService.setData({
                    req_url: PrefixUrl + '/user/getAffiliateUsersByMonth/'
                }).then(function(resp) {
                        console.log('rrrrrrrrrr')
                        console.log(resp.data[0])
                        var userObj= [];
                        var userObjMonths= [];
                        // resp.data[0]
                        var month_get =['','Jan','Feb','Mar','Aprl','May','June','July','Aug','Sep','Oct','Nov','Dec'];

                        if(resp.data.length > 0)
                         {   
                            var yearValue= resp.data[0]._id.year;
                      // $scope.series = ['Series A', 'Series B'];
                           var userObj2= [];
                        var j=1;
                         //for (var i = 1; i<resp.data[0]._id.month;i++) {

                             // userObj.push(yearValue +'/'+ month_get[j]);
                              //userObj2.push(0);
                           //   j++;
                         //}

                        
                        // var monthData= 12-(resp.data[0]._id.month); 
                         console.log('monthData-- ',monthData)
                         for (var i = 0; i <=resp.data.length;i++) {
                            if(resp.data[i])
                             {   
                                console.log('month compare -- ',resp.data[i]._id.month+'   iii  '+i)
                                 
                            console.log('respData--- ',resp.data[i]._id.month)
                             // if(resp.data[i]._id.month == i)
                             // {
                                  userObj[resp.data[i]._id.month]=(resp.data[i]._id.year+'/'+ month_get[resp.data[i]._id.month]);
                                userObj2[resp.data[i]._id.month]=(resp.data[i].myCount);

  //                              userObj.push(resp.data[i]._id.year+'/'+ month_get[j]);
                             //  userObj2.push(resp.data[i].myCount);

                             //    // userObjMonths.push(user._id.month);
                             // }
                               //j++;

                          }
                          // else{
                          //        userObj.push(yearValue+'/'+ month_get[j]);
                          //      userObj2.push(0);
                          //      j++;
                          // }
                         }
                         for(var j=1;j<=12;j++)
                           {
                            if(!userObj[j])
                             {
                              userObj[j]=(yearValue +'/'+ month_get[j]);
                                userObj2[j]=0;

                             } 
                           }

                         $scope.labels = userObj;
                         $scope.colors = ["#8CC63F"];
                        $scope.data=[userObj1, userObj2];
                         console.log('labels --  ',userObj)
                         console.log('count --  ',userObj1)
                         console.log('count --  ',userObj2)
                         console.log('colors --  ',$scope.colors)
                     }
         })


            //     resp.data.forEach(function (user, k) {     
            //         if (user._id.month == 1) {
            //             userObj.push(user._id.year+'/'+ 'Jan');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 2) {
            //             userObj.push(user._id.year+'/'+ 'Feb');
            //             userObjMonths.push(user._id.month);
            //         }           
            //         if (user._id.month == 3) {
            //             userObj.push(user._id.year+'/'+ 'Mar');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 4) {
            //             userObj.push(user._id.year+'/'+ 'Aprl');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 5) {
            //             userObj.push(user._id.year+'/'+ 'May');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 6) {
            //             userObj.push(user._id.year+'/'+ 'June');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 7) {
            //             userObj.push(user._id.year+'/'+ 'July');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 8) {
            //             userObj.push(user._id.year+'/'+ 'Aug');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 9) {
            //             userObj.push(user._id.year+'/'+ 'Sep');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 10) {
            //             userObj.push(user._id.year+'/'+ 'Oct');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 11) {
            //             userObj.push(user._id.year+'/'+ 'Nov');
            //             userObjMonths.push(user._id.month);
            //         }
            //         if (user._id.month == 12) {
            //             userObj.push(user._id.year+'/'+ 'Dec');
            //             userObjMonths.push(user._id.month);
            //         }
            //     });

            // $scope.labels = userObj;
            //    $scope.colors = ["#8CC63F"];
            //   // $scope.series = ['Series A', 'Series B'];
            //     var userObj1= [];
            //     resp.data.forEach(function (user, k) {     
            //          userObj1.push(user.myCount);
            //     });
              // $scope.data= userObj1 ;


                // start get affiliate users by month
                // APIService.setData({
                //     req_url: PrefixUrl + '/user/getAffiliateUsersByMonth/'
                // }).then(function(resp) {
                //         console.log('rrrrrrrrrr')
                //         console.log(resp.data)
                //         var userObjAff= [];
                //         resp.data.forEach(function (user, k) {     
                //             if (user._id.month == 1) {
                //                 userObjAff.push(user._id.year+'/'+ 'Jan');

                //             }
                //             if (user._id.month == 2) {
                //                 userObjAff.push(user._id.year+'/'+ 'Feb');

                //             }           
                //             if (user._id.month == 3) {
                //                 userObjAff.push(user._id.year+'/'+ 'Mar');

                //             }
                //             if (user._id.month == 4) {
                //                 userObjAff.push(user._id.year+'/'+ 'Aprl');

                //             }
                //             if (user._id.month == 5) {
                //                 userObjAff.push(user._id.year+'/'+ 'May');

                //             }
                //             if (user._id.month == 6) {
                //                 userObjAff.push(user._id.year+'/'+ 'June');

                //             }
                //             if (user._id.month == 7) {
                //                 userObjAff.push(user._id.year+'/'+ 'July');

                //             }
                //             if (user._id.month == 8) {
                //                 userObjAff.push(user._id.year+'/'+ 'Aug');

                //             }
                //             if (user._id.month == 9) {
                //                 userObjAff.push(user._id.year+'/'+ 'Sep');

                //             }
                //             if (user._id.month == 10) {
                //                 userObjAff.push(user._id.year+'/'+ 'Oct');

                //             }
                //             if (user._id.month == 11) {
                //                 userObjAff.push(user._id.year+'/'+ 'Nov');

                //             }
                //             if (user._id.month == 12) {
                //                 userObjAff.push(user._id.year+'/'+ 'Dec');

                //             }
                //         });

                //     $scope.labels = userObjAff;
                //        $scope.colors = ["#8CC63F"];
                //       // $scope.series = ['Series A', 'Series B'];
                //         var userObjAff1= [];
                //         console.log('$scope.userObjMonths  ',userObjMonths)
                //         resp.data.forEach(function (user, k) {     
                //              if (userObjMonths[k] == user._id.month) {
                //                 userObjAff1.push(user.myCount);
                //              }else{
                //                 userObjAff1.push(0);
                //              }
                //         });
                //       $scope.data= [userObj1, userObjAff1] ;

                //       console.log('userObj1   ?',userObjAff1)
                //    },function(resp) {
                //       // This block execute in case of error.
                // });
                // end get affiliate users by month

           },function(resp) {
              // This block execute in case of error.
        });



        APIService.setData({
            req_url: PrefixUrl + '/user/getReferralIncomeByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels1 = userObj;
                $scope.colors1 = ["#3494BA"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj1= [];
                resp.data.forEach(function (user, k) {     
                     userObj1.push(user.total);
                });
              $scope.referralIncomedata= userObj1 ;
           },function(resp) {
              // This block execute in case of error.
        });


        
        
        APIService.setData({
            req_url: PrefixUrl + '/user/getWithdrawAmountByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels1 = userObj;
                // $scope.series = ['Series A', 'Series B'];
                var userObj1= [];
                resp.data.forEach(function (user, k) {     
                     userObj1.push(user.total);
                });
              // $scope.referralIncomedata= userObj1 ;
           },function(resp) {
              // This block execute in case of error.
        });



        APIService.setData({
            req_url: PrefixUrl + '/user/getSubscriptionByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels2 = userObj;
                 $scope.colors2 = ["#80D1ED"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj2= [];
                resp.data.forEach(function (user, k) {     
                     userObj2.push(user.total);
                });
              $scope.subscriptionChartData= userObj2 ;
           },function(resp) {
              // This block execute in case of error.
        });




        APIService.setData({
            req_url: PrefixUrl + '/user/getBoostIncomeByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels3 = userObj;
                 $scope.colors3 = ["#FBB03B"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj3= [];
                resp.data.forEach(function (user, k) {     
                     userObj3.push(user.total);
                });
              $scope.boostFeesChartData= userObj3 ;
           },function(resp) {
              // This block execute in case of error.
        });



        APIService.setData({
            req_url: PrefixUrl + '/user/getTotalGstByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels4 = userObj;
                  $scope.colors4 = ["#ED9AA1"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj4= [];
                resp.data.forEach(function (user, k) {     
                     userObj4.push(user.total);
                });
              $scope.gstByMonthChartData= userObj4 ;
           },function(resp) {
              // This block execute in case of error.
        });




        APIService.setData({
            req_url: PrefixUrl + '/user/getTotalTripBoostGstByMonth/'
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels5 = userObj;
                  $scope.colors5 = ["#84ACB6"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj4= [];
                resp.data.forEach(function (user, k) {     
                     userObj4.push(user.total);
                });
              $scope.totalTripBoostChartData= userObj4 ;
           },function(resp) {
              // This block execute in case of error.
        });


         $scope.getPayoutCreatedReportForMonth = function(startDate,enddate){

            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate;
            APIService.setData({
                req_url: PrefixUrl + '/payout/payoutCreatedReportForMonth', data: obj
                }).then(function(resp7) {
                    console.log('resp7---- ',resp7);
                    if (resp7.data.length > 0) { 
                        $scope.totalPayout = resp7.data[0].total;
                    }else{
                        $scope.totalPayout = 0;
                    }
                    // if (referralData.data.length > 0) {
                    //     $scope.totalReferralIncome= referralData.data[0].total;
                    // }else{
                    //     $scope.totalReferralIncome= 0;
                    // }
            },function(resp) {
                  // This block execute in case of error.
            });
        }


        $scope.getDashboardUserCountForMonth = function(startDate,enddate){

            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate;
            APIService.setData({
                req_url: PrefixUrl + '/user/userCountForMonth/', data: obj
                }).then(function(usersData) {
                // $scope.userData= [$scope.userCount,resp1.data[0].total,resp3.data[0].total];
                if (usersData.data > 0) {
                    $scope.userCount= usersData.data;
                }else{
                    $scope.userCount= 0;
                }
                $scope.getDashboardSubscriptionTotalForSelectedMonth(startDate,enddate);

            },function(resp) {
                  // This block execute in case of error.
            });
        }


        $scope.getDashboardSubscriptionTotalForSelectedMonth = function(startDate,enddate){

       
            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate; 


            APIService.setData({
                req_url: PrefixUrl + '/user/getSubscriptionTotalForSelectedMonth/', data: obj
                }).then(function(resp11) {
                    console.log('kkkkkkkkkkkkkkkkkkkkk')
                    console.log(resp11)
                
                if (resp11.data.length > 0) {
                    $scope.subscriptionTotal= [resp11.data[0].total];
                    $scope.subscriptionData= resp11.data[0].total;
                    $scope.gstTotal= resp11.data[0].total_gst;
                    $scope.totalTransaction = resp11.data[0].count;
                    $scope.subscriptionL= ["Total Subscription Amount"];
                }else{
                    $scope.subscriptionTotal= [0];
                    $scope.subscriptionData= 0;
                    $scope.gstTotal= 0;
                    $scope.totalTransaction = 0;

                    $scope.subscriptionL= ["Total Subscription Amount"];
                }

                // $scope.getDashboardTotalBoostedIncomeForMonth(startDate,enddate);
                $scope.getDashboardTotalReferralIncomeForMonth(startDate,enddate);

                
               },function(resp) {
                  // This block execute in case of error.
            });

        }


        $scope.getDashboardTotalBoostedIncomeForMonth = function(startDate,enddate){

            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate;
            APIService.setData({
                req_url: PrefixUrl + '/user/totalBoostedIncomeForMonth/', data: obj
                }).then(function(boostData) {
                    console.log('boostData -- ',boostData)
                    console.log('$scope.respAllCount -- ',$scope.respAllCount)
                    console.log('$scope.totalReferralIncome -- ',$scope.totalReferralIncome)
                    if (boostData.data.length > 0) {
                        $scope.totalBoostedIncome= boostData.data[0].total;

                    }else{
                        $scope.totalBoostedIncome= 0;
                    }


                        $scope.userCount= $scope.respAllCount.normalUser + $scope.respAllCount.referalUser;
                        $scope.userData= [$scope.userCount,$scope.totalReferralIncome,$scope.totalBoostedIncome];
                        $scope.boostTripGstTotalData= [$scope.gstTotal,$scope.tripBoostGst,$scope.gstTotal + $scope.tripBoostGst];
                        
                        $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
                        $scope.totalCr = $scope.totalBoostedIncome+$scope.subscriptionData;
                        $scope.getBoostTripGstTotalForSelectedMonth(startDate,enddate);
                        $scope.getDashboardWithdrawTotalForSelectedMonth(startDate,enddate);

                    // $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;
                
                // $scope.getDashboardTotalReferralIncomeForMonth(startDate,enddate);

            },function(resp) {
                  // This block execute in case of error.
            });
        }


        $scope.getDashboardTotalReferralIncomeForMonth = function(startDate,enddate){

            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate;
            APIService.setData({
                req_url: PrefixUrl + '/user/totalReferralIncomeForMonth/', data: obj
                }).then(function(referralData) {
                    if (referralData.data.length > 0) {
                        $scope.totalReferralIncome= referralData.data[0].total;
                    }else{
                        $scope.totalReferralIncome= 0;
                    }

               
                // $scope.getDashboardWithdrawTotalForSelectedMonth(startDate,enddate);
                $scope.getDashboardTotalBoostedIncomeForMonth(startDate,enddate);


            },function(resp) {
                  // This block execute in case of error.
            });
        }


        $scope.getDashboardWithdrawTotalForSelectedMonth = function(startDate,enddate){

                var obj= {};
                obj.startDate = startDate; 
                obj.enddate = enddate; 
                APIService.setData({
                    req_url: PrefixUrl + '/user/getWithdrawTotalForSelectedMonth/', data: obj
                }).then(function(resp4) {
                        APIService.setData({
                            req_url: PrefixUrl + '/user/getWithdrawRequestTotalForSelectedMonth/', data: obj
                        }).then(function(resp) {
                            console.log('getWithdrawRequestTotalForSelectedMonth--',resp.data);
                                if (resp.data.length > 0) {
                                    $scope.withdrawRequestTotal= resp.data[0].total;
                                }else{
                                    $scope.withdrawRequestTotal= 0;
                                }
                
                            if (resp4.data.length > 0) {
                                $scope.userData1= [resp4.data[0].total];
                                $scope.withdrawTotal= resp4.data[0].total;
                                $scope.usersLabels1= ["Total Withdraw Amount"];
                            }else{
                                $scope.userData1= [0];
                                $scope.withdrawTotal= 0;
                                $scope.usersLabels1= ["Total Withdraw Amount"];
                            }     
                            
                            $scope.getBoostTripGstTotalForSelectedMonth(startDate,enddate);

                        },function(resp) {

                        });       
                   },function(resp) {
                      // This block execute in case of error.
                       localStorage.removeItem("UserDeatails");
                        localStorage.removeItem("token");
                        $state.go('login');
                });
            }


            $scope.getBoostTripGstTotalForSelectedMonth = function(startDate,enddate){

                var obj= {};
                obj.startDate = startDate; 
                obj.enddate = enddate; 

               APIService.setData({
                    req_url: PrefixUrl + '/user/getBoostTripGstTotalForSelectedMonth/', data: obj
                }).then(function(resp5) {
                    if (resp5.data.length) { 
                        $scope.tripBoostGst= resp5.data[0].total;
                    }else{
                        $scope.tripBoostGst= 0;
                    }
                    
                    $scope.boostTripGstTotalData= [$scope.gstTotal,$scope.tripBoostGst,$scope.gstTotal + $scope.tripBoostGst];

                    $scope.totalGstForSelectedMonth(startDate,enddate);

                })
            }


            $scope.totalGstForSelectedMonth = function(startDate,enddate){

                var obj= {};
                obj.startDate = startDate; 
                obj.enddate = enddate; 

               APIService.setData({
                    req_url: PrefixUrl + '/user/totalGstForSelectedMonth/', data: obj
                }).then(function(resp6) {
                    if (resp6.data.length > 0) { 
                        // $scope.gstTotal= resp6.data[0].total;
                    }else{
                        // $scope.gstTotal= 0;
                    }
                        $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
                        $scope.totalCr = $scope.totalBoostedIncome+$scope.subscriptionData;

                    console.log('totalDr---@@@',$scope.totalDr)
                    console.log('totalCr---@@@!!!',$scope.totalCr)



                })
            }


      



        
        // $scope.getDashboardUsersCountForSelectedMonth = function(startDate,enddate){

        //     var obj= {};
        //     obj.startDate = startDate; 
        //     obj.enddate = enddate; 

        //     APIService.setData({
        //         req_url: PrefixUrl + '/user/userCountForMonth/', data: obj
        //     }).then(function(resp) {
        //         $scope.userCount= resp.data;
        //             APIService.setData({
        //                 req_url: PrefixUrl + '/user/totalReferralIncomeForMonth/', data: obj
        //             }).then(function(resp1) {
        //                     // APIService.setData({
        //                     //     req_url: PrefixUrl + '/user/totalGst/'
        //                     // }).then(function(resp2) {
        //                         console.log(resp)
        //                             APIService.setData({
        //                                 req_url: PrefixUrl + '/user/totalBoostedIncomeForMonth/', data: obj
        //                             }).then(function(resp3) {
        //                                 console.log('vvvvvvvvvvvvvv'+resp3.data.length+  '{  }'  +resp1.data.length  + '{  }'  +  $scope.userCount);
        //                                 if (resp3.data.length > 0 && resp1.data.length > 0 && $scope.userCount > 0) {

        //                                     $scope.totalBoostedIncome= resp3.data[0].total;
        //                                     $scope.totalReferralIncome= resp1.data[0].total;
        //                                     $scope.userData= [$scope.userCount,resp1.data[0].total,resp3.data[0].total];
        //                                     $scope.usersLabels= ["Total Number of Users","Total Referral Income","Total Boosted Income"];
        //                                     $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;
        //                                     // $scope.getGstData();
        //                                     $scope.getDashboardGstTotalForSelectedMonth();


        //                                 }else if (resp3.data.length == 0 && resp1.data.length == 0 && $scope.userCount > 0) {
        //                                     console.log('ccccccc')
        //                                     $scope.userData= [$scope.userCount,0,0];
        //                                     $scope.usersLabels= ["Total Number of Users","Total Referral Income","Total Boosted Income"];
        //                                     $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;
        //                                     // $scope.getGstData();
        //                                     $scope.getDashboardGstTotalForSelectedMonth();
                                            
        //                                 }
        //                                 else if (resp3.data.length == 0 && resp1.data.length == 0 && $scope.userCount == 0) {
        //                                     console.log('else if');
        //                                     $scope.totalBoostedIncome= 0;
        //                                     $scope.userCount= 0;
        //                                     $scope.totalReferralIncome= 0;
        //                                     $scope.totalCr= 0;
        //                                     // $scope.totalDr= 0;
        //                                     $scope.userData= [0,0,0]
        //                                 }

        //                                 else if (resp3.data.length == 0 && resp1.data.length > 0 && $scope.userCount > 0) {
        //                                     console.log('11111111----');
        //                                     $scope.totalBoostedIncome= 0;
        //                                     $scope.userCount= $scope.userCount;
        //                                     $scope.totalReferralIncome= resp1.data[0].total;
        //                                     $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;
        //                                     // $scope.totalDr= 0;
        //                                     $scope.userData= [$scope.userCount,resp1.data[0].total,0];
        //                                 }

        //                                 else if (resp3.data.length > 0 && resp1.data.length == 0 && $scope.userCount == 0) {
        //                                     console.log('totalBoostedIncome',resp3.data[0]);
        //                                     $scope.totalBoostedIncome= resp3.data[0].total;
        //                                     $scope.userData= [0,0,$scope.totalBoostedIncome];
        //                                     $scope.usersLabels= ["Total Number of Users","Total Referral Income","Total Boosted Income"];
        //                                     $scope.userCount= 0;
        //                                     $scope.totalReferralIncome= 0;
        //                                     $scope.totalCr= 0;
        //                                     // $scope.totalDr= 0;
        //                                     // $scope.userData= [0,0,0]
        //                                 }
        //                                 else{
        //                                     console.log('else'+resp.data.length)

        //                                 }

        //                                     $scope.totalCr= $scope.totalBoostedIncome+$scope.subscriptionData;


        //                                },function(resp) {
        //                                   // This block execute in case of error.
        //                             });
        //                     //    },function(resp) {
        //                     //       // This block execute in case of error.
        //                     // });
        //                 console.log(resp)
        //                },function(resp) {
        //                   // This block execute in case of error.
        //             });

        //         console.log($scope.userCount)
        //        },function(resp) {
        //           // This block execute in case of error.
        //     });
        // }




        
        $scope.getDashboardAllUsersCountForSelectedMonth = function(startDate,enddate){

            var obj= {};
            obj.startDate = startDate; 
            obj.enddate = enddate; 

            APIService.setData({
                req_url: PrefixUrl + '/user/userAllCountForMonth/', data: obj
            }).then(function(resp) {
                $scope.respAllCount= resp.data;
                 

                console.log($scope.userCount)
               },function(resp) {
                  // This block execute in case of error.
            });
        }



    // $scope.getDashboardGstTotalForSelectedMonth = function(startDate,enddate){

    //         var obj= {};
    //         obj.startDate = startDate; 
    //         obj.enddate = enddate; 

    //        APIService.setData({
    //                 req_url: PrefixUrl + '/user/getBoostTripGstTotalForSelectedMonth/', data: obj
    //             }).then(function(resp5) {
    //                 APIService.setData({
    //                     req_url: PrefixUrl + '/user/totalGstForSelectedMonth/', data: obj
    //                 }).then(function(resp2) {
    //                     console.log('getDashboardGstTotalForSelectedMonth',resp5);
    //                     console.log('totalGstForSelectedMonth',resp2);
    //                     console.log('length111111',resp5.data.length + '  ' +resp2.data.length );

    //                     if (resp5.data.length > 0 && resp2.data.length > 0) {
    //                         console.log('gst 000');                            
    //                         $scope.boostTripGstTotalData= [resp5.data[0].total,resp2.data[0].total,resp5.data[0].total + resp2.data[0].total];
    //                         $scope.tripBoostGst= resp5.data[0].total;
    //                         $scope.gstTotal= resp2.data[0].total;
    //                         $scope.tripGstL1= ["Trip Gst Total","Gst","Total Gst"];
    //                         console.log($scope.tripBoostGst+$scope.gstTotal)
    //                         $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
    //                         // console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
    //                         $scope.grandGst= $scope.tripBoostGst+$scope.gstTotal;
    //                     }

    //                      else if (resp5.data.length > 0 && resp2.data.length == 0) {
    //                         console.log('gst 111');                            
    //                         $scope.boostTripGstTotalData= [resp5.data[0].total,0,resp5.data[0].total + 0];
    //                         $scope.tripBoostGst= resp5.data[0].total;
    //                         $scope.gstTotal= 0;
    //                         $scope.tripGstL1= ["Trip Gst Total","Gst","Total Gst"];
    //                         console.log($scope.tripBoostGst+$scope.gstTotal)
    //                         $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
    //                         // console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
    //                         $scope.grandGst= $scope.tripBoostGst+$scope.gstTotal;
    //                     }

    //                     else if (resp5.data.length == 0 && resp2.data.length > 0) {
    //                         console.log('gst 222');                            
    //                         $scope.boostTripGstTotalData= [0,resp2.data[0].total,resp2.data[0].total];
    //                         $scope.tripBoostGst= 0;
    //                         $scope.gstTotal= resp2.data[0].total;
    //                         $scope.tripGstL1= ["Trip Gst Total","Gst","Total Gst"];
    //                         console.log($scope.tripBoostGst+$scope.gstTotal)
    //                         $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
    //                         // console.log($scope.totalBoostedIncome+$scope.totalReferralIncome+$scope.subscriptionData)
    //                         $scope.grandGst= $scope.tripBoostGst+$scope.gstTotal;
    //                     }
    //                     else if (resp5.data.length == 0 && resp2.data.length == 0) {
    //                         console.log('gst 3333');                            
    //                         $scope.tripBoostGst= 0;
                            
    //                     }

    //                     else{
    //                         console.log('gst else');                            

    //                         // $scope.boostTripGstTotalData= [0];
    //                         // $scope.tripBoostGst= 0;
    //                         // $scope.gstTotal= 0;
    //                         // $scope.tripGstL1= ["Trip Gst Total","Gst","Total Gst"];
    //                         // $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
    //                         // $scope.grandGst= $scope.tripBoostGst+$scope.gstTotal;
    //                     }
    //                         $scope.totalDr= $scope.tripBoostGst+$scope.gstTotal+$scope.totalReferralIncome;
                        
    //                 },function(resp) {
    //                   // This block execute in case of error.
    //                 }); 
    //                },function(resp) {
    //                   // This block execute in case of error.
    //             });
        
    // }




    $scope.getDashboardUsersForSelectedMonth = function(startDate,enddate){

        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 

        
       APIService.setData({
                req_url: PrefixUrl + '/user/getUsersByMonthForSelectedMonth/', data: obj
            }).then(function(resp) {
                    console.log('rrrrrrrrrr')
                    console.log(resp.data[0])
                    var userObj= [];
                    var userObjMonths= [];
                    // resp.data[0]
                    var month_get =['','Jan','Feb','Mar','Aprl','May','June','July','Aug','Sep','Oct','Nov','Dec'];

                    if(resp.data.length > 0)
                     {   
                        var yearValue= resp.data[0]._id.year;
                  // $scope.series = ['Series A', 'Series B'];
                       var userObj1= [];
                    // var j=1;
                    //  for (var i = 1; i<resp.data[0]._id.month;i++) {

                    //       //userObj.push(yearValue +'/'+ month_get[j]);
                    //       //userObj1.push(0);
                    //       j++;
                    //  }

                    
                     var monthData= 12-(resp.data[0]._id.month); 
                     console.log('monthData-- ',monthData)
                     for (var i = 0; i <=resp.data.length;i++) {
                        if(resp.data[i])
                         {   
                            console.log('month compare -- ',resp.data[i]._id.month+'   iii  '+i)
                             
                        console.log('respData--- ',resp.data[i]._id.month)
                         // if(resp.data[i]._id.month == i)
                         // {
                          userObj[resp.data[i]._id.month]=(resp.data[i]._id.year+'/'+ month_get[resp.data[i]._id.month]);
                          userObj1[resp.data[i]._id.month]=(resp.data[i].myCount);

                      //      userObj.push(resp.data[i]._id.year+'/'+ month_get[j]);
                        //   userObj1.push(resp.data[i].myCount);

                         //    // userObjMonths.push(user._id.month);
                         // }
                           j++;

                      }
                      // else{
                      //      //  userObj.push(yearValue+'/'+ month_get[j]);
                      //     // userObj1.push(0);
                      //      j++;
                      // }
                     }


                     for(var j=1;j<=12;j++)
                     {
                      if(!userObj[j])
                       {
                        userObj[j]=(yearValue +'/'+ month_get[j]);
                          userObj1[j]=0;

                       } 
                     }
                     $scope.labels = userObj;
                     $scope.colors = ["#8CC63F"];
                    // $scope.data=userObj1;
                     console.log('labels --  ',userObj)
                     console.log('count --  ',userObj1)
                     console.log('colors --  ',$scope.colors)
                 }


                  APIService.setData({
                        req_url: PrefixUrl + '/user/getReferalUsersByMonthForSelectedMonth/', data: obj
                    }).then(function(resp) {
                            console.log('rrrrrrrrrr')
                            console.log(resp.data[0])
                            var userObj= [];
                            var userObjMonths= [];
                            // resp.data[0]
                            var month_get =['','Jan','Feb','Mar','Aprl','May','June','July','Aug','Sep','Oct','Nov','Dec'];

                            if(resp.data.length > 0)
                             {   
                                var yearValue= resp.data[0]._id.year;
                          // $scope.series = ['Series A', 'Series B'];
                               var userObj2= [];
                            var j=1;
                             //for (var i = 1; i<resp.data[0]._id.month;i++) {

                                 // userObj.push(yearValue +'/'+ month_get[j]);
                                  //userObj2.push(0);
                               //   j++;
                             //}

                            
                            // var monthData= 12-(resp.data[0]._id.month); 
                             console.log('monthData-- ',monthData)
                             for (var i = 0; i <=resp.data.length;i++) {
                                if(resp.data[i])
                                 {   
                                    console.log('month compare -- ',resp.data[i]._id.month+'   iii  '+i)
                                     
                                console.log('respData--- ',resp.data[i]._id.month)
                                 // if(resp.data[i]._id.month == i)
                                 // {
                                      userObj[resp.data[i]._id.month]=(resp.data[i]._id.year+'/'+ month_get[resp.data[i]._id.month]);
                                    userObj2[resp.data[i]._id.month]=(resp.data[i].myCount);

      //                              userObj.push(resp.data[i]._id.year+'/'+ month_get[j]);
                                 //  userObj2.push(resp.data[i].myCount);

                                 //    // userObjMonths.push(user._id.month);
                                 // }
                                   //j++;

                              }
                              // else{
                              //        userObj.push(yearValue+'/'+ month_get[j]);
                              //      userObj2.push(0);
                              //      j++;
                              // }
                             }
                             for(var j=1;j<=12;j++)
                               {
                                if(!userObj[j])
                                 {
                                  userObj[j]=(yearValue +'/'+ month_get[j]);
                                    userObj2[j]=0;

                                 } 
                               }

                             $scope.labels = userObj;
                             $scope.colors = ["#8CC63F"];
                            $scope.data=[userObj1, userObj2];
                             console.log('labels --  ',userObj)
                             console.log('count --  ',userObj1)
                             console.log('count --  ',userObj2)
                             console.log('colors --  ',$scope.colors)
                         }
             })


               

               },function(resp) {
                  // This block execute in case of error.
            });
    }
        
    //     APIService.setData({
    //         req_url: PrefixUrl + '/user/getUsersByMonthForSelectedMonth/', data: obj
    //     }).then(function(resp) {
    //             if (resp.data.length > 0) {

    //                 console.log('rrrrrrrrrr')
    //                 console.log(resp.data)
    //                 var userObj= [];
    //                 resp.data.forEach(function (user, k) {     
    //                     if (user._id.month == 1) {
    //                         userObj.push(user._id.year+'/'+ 'Jan');

    //                     }
    //                     if (user._id.month == 2) {
    //                         userObj.push(user._id.year+'/'+ 'Feb');

    //                     }           
    //                     if (user._id.month == 3) {
    //                         userObj.push(user._id.year+'/'+ 'Mar');

    //                     }
    //                     if (user._id.month == 4) {
    //                         userObj.push(user._id.year+'/'+ 'Aprl');

    //                     }
    //                     if (user._id.month == 5) {
    //                         userObj.push(user._id.year+'/'+ 'May');

    //                     }
    //                     if (user._id.month == 6) {
    //                         userObj.push(user._id.year+'/'+ 'June');

    //                     }
    //                     if (user._id.month == 7) {
    //                         userObj.push(user._id.year+'/'+ 'July');

    //                     }
    //                     if (user._id.month == 8) {
    //                         userObj.push(user._id.year+'/'+ 'Aug');

    //                     }
    //                     if (user._id.month == 9) {
    //                         userObj.push(user._id.year+'/'+ 'Sep');

    //                     }
    //                     if (user._id.month == 10) {
    //                         userObj.push(user._id.year+'/'+ 'Oct');

    //                     }
    //                     if (user._id.month == 11) {
    //                         userObj.push(user._id.year+'/'+ 'Nov');

    //                     }
    //                     if (user._id.month == 12) {
    //                         userObj.push(user._id.year+'/'+ 'Dec');

    //                     }
    //                 });

    //             $scope.labels = userObj;
    //              $scope.colors = ["#8CC63F"];
    //               // $scope.series = ['Series A', 'Series B'];
    //                 var userObj1= [];
    //                 $scope.userCount= 0;
    //                 resp.data.forEach(function (user, k) {     
    //                      userObj1.push(user.myCount);
    //                     $scope.userCount= $scope.userCount + user.myCount;

    //                 });
    //               // $scope.data= userObj1 ;

    //               // start get referral users by month
    //                     APIService.setData({
    //                         req_url: PrefixUrl + '/user/getReferalUsersByMonthForSelectedMonth/', data: obj
    //                     }).then(function(resp) {
    //                             if (resp.data.length > 0) {

    //                                 console.log('rrrrrrrrrr')
    //                                 console.log(resp.data)
    //                                 var userObjRef= [];
    //                                 resp.data.forEach(function (user, k) {     
    //                                     if (user._id.month == 1) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Jan');

    //                                     }
    //                                     if (user._id.month == 2) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Feb');

    //                                     }           
    //                                     if (user._id.month == 3) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Mar');

    //                                     }
    //                                     if (user._id.month == 4) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Aprl');

    //                                     }
    //                                     if (user._id.month == 5) {
    //                                         userObjRef.push(user._id.year+'/'+ 'May');

    //                                     }
    //                                     if (user._id.month == 6) {
    //                                         userObjRef.push(user._id.year+'/'+ 'June');

    //                                     }
    //                                     if (user._id.month == 7) {
    //                                         userObjRef.push(user._id.year+'/'+ 'July');

    //                                     }
    //                                     if (user._id.month == 8) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Aug');

    //                                     }
    //                                     if (user._id.month == 9) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Sep');

    //                                     }
    //                                     if (user._id.month == 10) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Oct');

    //                                     }
    //                                     if (user._id.month == 11) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Nov');

    //                                     }
    //                                     if (user._id.month == 12) {
    //                                         userObjRef.push(user._id.year+'/'+ 'Dec');

    //                                     }
    //                                 });

    //                             $scope.labels = userObjRef;
    //                              $scope.colors = ["#8CC63F"];
    //                               // $scope.series = ['Series A', 'Series B'];
    //                                 var userObjRef1= [];
    //                                 $scope.userCount= 0;
    //                                 resp.data.forEach(function (user, k) {     
    //                                      userObjRef1.push(user.myCount);
    //                                     $scope.userCount= $scope.userCount + user.myCount;

    //                                 });
    //                               $scope.data= [userObj1, userObjRef1] ;
    //                               console.log('aaaaa',$scope.userCount);
    //                         }else{
    //                             $scope.labels = 'No data found';
    //                             // myCount: 0
    //                             // _id: {}
    //                             // month: 
    //                             // year: 
    //                             $scope.data= 0 ;

    //                         }
    //                        },function(resp) {
    //                           // This block execute in case of error.
    //                     });
    //               // end get users by month for date

    //               console.log('aaaaa',$scope.userCount);
    //         }else{
    //             $scope.labels = 'No data found';
    //             // myCount: 0
    //             // _id: {}
    //             // month: 
    //             // year: 
    //             $scope.data= 0 ;

    //         }
    //        },function(resp) {
    //           // This block execute in case of error.
    //     });

    // }


    $scope.getDashboardReferralIncomeForSelectedMonth = function(startDate,enddate){

        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 
      APIService.setData({
            req_url: PrefixUrl + '/user/getReferralIncomeByMonthForSelectedMonth/', data: obj
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels1 = userObj;
                // $scope.series = ['Series A', 'Series B'];
                var userObj1= [];
                resp.data.forEach(function (user, k) {     
                     userObj1.push(user.total);
                });
              $scope.referralIncomedata= userObj1 ;
              $scope.colors2 = ["#80D1ED"];
           },function(resp) {
              // This block execute in case of error.
        });
    }


    $scope.getDashboardSubscriptionFeesForSelectedMonth = function(startDate,enddate){


        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 

        APIService.setData({
            req_url: PrefixUrl + '/user/getSubscriptionByMonthForSelectedMonth/', data: obj
        }).then(function(resp) {
            if (resp.data.length > 0) {

                var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels2 = userObj;
                 $scope.colors2 = ["#80D1ED"]; 
                // $scope.series = ['Series A', 'Series B'];
                var userObj2= [];
                resp.data.forEach(function (user, k) {     
                     userObj2.push(user.total);
                });
              $scope.subscriptionChartData= userObj2 ;
          }
          else{
                $scope.labels2 = 'No data found';
                $scope.subscriptionChartData= 0 ;

            }
           },function(resp) {
              // This block execute in case of error.
        });
    }



    $scope.getDashboardBoostIncomeForSelectedMonth = function(startDate,enddate){


        

        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 

        APIService.setData({
            req_url: PrefixUrl + '/user/getBoostIncomeByMonthForSelectedMonth/', data: obj
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels3 = userObj;
                // $scope.series = ['Series A', 'Series B'];
                var userObj3= [];
                resp.data.forEach(function (user, k) {     
                     userObj3.push(user.total);
                });
              $scope.boostFeesChartData= userObj3 ;
           },function(resp) {
              // This block execute in case of error.
        });

    }




    $scope.getDashboardTotalGstForSelectedMonth = function(startDate,enddate){


        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 
        APIService.setData({
            req_url: PrefixUrl + '/user/getTotalGstByMonthForSelectedMonth/', data: obj
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels4 = userObj;
                 $scope.colors4 = ["#ED9AA1"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj4= [];
                resp.data.forEach(function (user, k) {     
                     userObj4.push(user.total);
                });
              $scope.gstByMonthChartData= userObj4 ;
           },function(resp) {
              // This block execute in case of error.
        });
    }



    $scope.getDashboardTripBoostGstForSelectedMonth = function(startDate,enddate){


        var obj= {};
        obj.startDate = startDate; 
        obj.enddate = enddate; 


        APIService.setData({
            req_url: PrefixUrl + '/user/getTotalTripBoostGstByMonthForSelectedMonth/', data: obj
        }).then(function(resp) {
            

              var userObj= [];
                resp.data.forEach(function (user, k) {     
                    if (user._id.month == 1) {
                        userObj.push(user._id.year+'/'+ 'Jan');

                    }
                    if (user._id.month == 2) {
                        userObj.push(user._id.year+'/'+ 'Feb');

                    }           
                    if (user._id.month == 3) {
                        userObj.push(user._id.year+'/'+ 'Mar');

                    }
                    if (user._id.month == 4) {
                        userObj.push(user._id.year+'/'+ 'Aprl');

                    }
                    if (user._id.month == 5) {
                        userObj.push(user._id.year+'/'+ 'May');

                    }
                    if (user._id.month == 6) {
                        userObj.push(user._id.year+'/'+ 'June');

                    }
                    if (user._id.month == 7) {
                        userObj.push(user._id.year+'/'+ 'July');

                    }
                    if (user._id.month == 8) {
                        userObj.push(user._id.year+'/'+ 'Aug');

                    }
                    if (user._id.month == 9) {
                        userObj.push(user._id.year+'/'+ 'Sep');

                    }
                    if (user._id.month == 10) {
                        userObj.push(user._id.year+'/'+ 'Oct');

                    }
                    if (user._id.month == 11) {
                        userObj.push(user._id.year+'/'+ 'Nov');

                    }
                    if (user._id.month == 12) {
                        userObj.push(user._id.year+'/'+ 'Dec');

                    }
                });

                $scope.labels5 = userObj;
                  $scope.colors5 = ["#84ACB6"];
                // $scope.series = ['Series A', 'Series B'];
                var userObj4= [];
                resp.data.forEach(function (user, k) {     
                     userObj4.push(user.total);
                });
              $scope.totalTripBoostChartData= userObj4 ;
           },function(resp) {
              // This block execute in case of error.
        });

    }


        // APIService.setData({
        //     req_url: PrefixUrl + '/user/totalReferralIncome/'
        // }).then(function(resp) {
        //     $scope.totalReferralIncome= [resp.data[0].total];
        //     $scope.usersLabels1= ["Total Referral Income"];
        //     $scope.referralIncome= resp.data[0].total;
        //     // $scope.usersData = [$scope.userCount];
        //     console.log(resp)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });


        // APIService.setData({
        //     req_url: PrefixUrl + '/user/totalGst/'
        // }).then(function(resp) {
        //     $scope.totalGst= [resp.data[0].total];
        //     $scope.usersLabels2= ["Total Gst"];
        //     $scope.gst= resp.data[0].total;
        //     // $scope.usersData = [$scope.userCount];
        //     console.log(resp)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });



        // APIService.setData({
        //     req_url: PrefixUrl + '/user/totalBoostedIncome/'
        // }).then(function(resp) {
        //     // $scope.totalGst= [resp.data[0].total];
        //     // $scope.usersLabels2= ["Total Gst"];
        //     // $scope.gst= resp.data[0].total;
        //     // $scope.usersData = [$scope.userCount];
        //     console.log(resp)
        //    },function(resp) {
        //       // This block execute in case of error.
        // });
        

        




    // $scope.userdata = {};
    // $scope.userdata.affiliated_user = true;
    // $scope.jsonData= {};

    // $scope.registerUser = function(){
    //     if( $scope.userdata.name &&  $scope.userdata.password){
    //         APIService.getData({ req_url: PrefixUrl + "/user/checkMobile/"+$scope.userdata.phone_number})
    //         .then(function (res1) {
    //             console.log(res1)
    //             if (res1.data.message) {
    //                 console.log('mobile verified')
    //                  APIService.getData({ req_url: PrefixUrl + "/user/checkEmail/"+$scope.userdata.email})
    //                 .then(function (res2) {
    //                     if (res1.data.message) {
    //                         console.log('email verified')
    //                       APIService.setData({ req_url: PrefixUrl + "/user/otp",data:$scope.userdata})
    //                         .then(function (res4) {
    //                             if (res4.data.success) {
    //                                 console.log("otp"+res4.data.message)

    //                                 $scope.jsonData.otp= res4.data.message;
    //                                 $scope.jsonData.mobile= $scope.userdata;
    //                                 $state.go("app.otp",{data:JSON.stringify($scope.jsonData)});
                                    
    //                             }
                            
    //                         },function(er){
             
    //                         })
    //                     }
                        
    //                 },function(er){
             
    //                 })
    //             }
    //             // $scope.userDetails=res.data;
    //             // $scope.userdata ={};
    //             // alert("User is registered Successfully.")
    //             // localStorage.setItem('UserDeatails', JSON.stringify(res.data));
    //             // $state.go("app.UserDetails");
            
    //         },function(er){
             
    //         })
    //     }else{
    //         alert("Please fill all the fields.")
    //     }
     

    //  // if( $scope.userdata.name &&  $scope.userdata.password){
    //  //        APIService.setData({ req_url: PrefixUrl + "/user",data:$scope.userdata}).then(function (res) {
    //  //            console.log(res)
    
    //  //            $scope.userDetails=res.data;
    //  //            $scope.userdata ={};
    //  //            alert("User is registered Successfully.")
    //  //            localStorage.setItem('UserDeatails', JSON.stringify(res.data));
    //  //            $state.go("app.UserDetails");
            
    //  //        },function(er){
             
    //  //        })
    //  //    }else{
    //  //        alert("Please fill all the fields.")
    //  //    }

    // }

 })