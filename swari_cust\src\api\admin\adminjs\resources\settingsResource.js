/**
 * Settings Resource
 * Defines the AdminJS resource for the Settings model
 * PRD Reference: Section 9 - Settings and Configuration Module
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Setting from '../../../../models/Setting.js';
import logger from '../../../../utils/logger.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import custom components
// import { componentLoader } from '../adminConfig.js';
const componentLoader = new ComponentLoader();

// Register custom components
const bidAmountEditor = componentLoader.add('BidAmountEditor', join(__dirname,'../components/bid-amount-editor'));
const settingsChart = componentLoader.add('SettingsChart', join(__dirname,'../components/settings-chart'));
const settingsHistory = componentLoader.add('SettingsHistory', join(__dirname,'../components/settings-history'));


// const componentLoader = new AdminJS.ComponentLoader();

// Register json Viewer component
const jsonViewerComponent = componentLoader.add(
  'jsonViewer', 
 join(__dirname, '../components/json-viewer') // Path to your React component
);

// Register bid Amounts Show component
const bidAmountsShowComponent = componentLoader.add(
  'bidAmountsShow', 
  join(__dirname, '../components/bid-amounts-show') // Path to your component
);
// Register bid Amounts Edit component
const bidAmountsEditComponent = componentLoader.add(
  'bidAmountsEdit', 
  join(__dirname, '../components/bid-amounts-edit') // Path to your React component
);

// Register date Range Filter component
const dateRangeFilterComponent = componentLoader.add(
  'dateRangeFilter', 
  join(__dirname, '../components/date-range-filter') // Path to your component
);








/**
 * Settings resource configuration for AdminJS
 */
const settingsResource = {
  resource: Setting,
  options: {
    id: 'settings',
    navigation: {
      name: 'Configuration',
      icon: 'Settings',
    },
    actions: {
      new: {
        before: async (request) => {
          // Set the updatedBy field to the current admin
          if (request.currentAdmin && request.currentAdmin._id) {
            request.payload = {
              ...request.payload,
              updatedBy: request.currentAdmin._id,
            };
          }
          return request;
        },
        after: async (response) => {
          // Log the creation of a new setting
          if (response.record && response.record.params) {
            const settingData = response.record.params;
            logger.info('Setting created', {
              settingId: settingData._id,
              name: settingData.name,
              category: settingData.category,
              adminId: settingData.updatedBy,
              action: 'create',
            });
          }
          return response;
        },
      },
      edit: {
        before: async (request, context) => {
          // Get the current record before update for logging changes
          const { record } = context;
          if (record) {
            const currentValue = record.params.value;
            // Store the current value as previousValue for change tracking
            request.payload = {
              ...request.payload,
              previousValue: currentValue,
              updatedBy: request.currentAdmin._id,
              updatedAt: new Date(),
            };
          }
          return request;
        },
        after: async (response) => {
          // Log the update with before/after values
          if (response.record && response.record.params) {
            const settingData = response.record.params;
            logger.info('Setting updated', {
              settingId: settingData._id,
              name: settingData.name,
              category: settingData.category,
              previousValue: settingData.previousValue,
              newValue: settingData.value,
              adminId: settingData.updatedBy,
              action: 'update',
            });
          }
          return response;
        },
      },
      // Custom action for adjusting bid amounts by fare type
      adjustBidAmounts: {
        actionType: 'record',
        component: bidAmountEditor,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          try {
            // Update bid amounts in the database
            await Setting.findByIdAndUpdate(record.params._id, {
              bidAmounts: request.payload.bidAmounts,
              updatedBy: currentAdmin._id,
              updatedAt: new Date(),
            });
            
            // Log the bid amount adjustment
            logger.info('Bid amounts adjusted', {
              settingId: record.params._id,
              adminId: currentAdmin._id,
              fareTypes: request.payload.bidAmounts.map(b => b.fareType),
              action: 'adjust_bid_amounts',
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Bid amounts successfully adjusted',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error adjusting bid amounts', {
              settingId: record.params._id,
              adminId: currentAdmin._id,
              error: error.message,
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: `Error: ${error.message}`,
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view settings history
      viewHistory: {
        actionType: 'record',
        component: settingsHistory,
        handler: async (request, response, context) => {
          return {
            record: context.record.toJSON(context.currentAdmin),
          };
        },
      },
      // Custom action to view impact charts
      viewImpactCharts: {
        actionType: 'record',
        component: settingsChart,
        handler: async (request, response, context) => {
          return {
            record: context.record.toJSON(context.currentAdmin),
          };
        },
      },
    },
    properties: {
      _id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
      },
      name: {
        isTitle: true,
        position: 1,
      },
      description: {
        type: 'textarea',
        position: 2,
      },
      category: {
        availableValues: [
          { value: 'wallet', label: 'Wallet' },
          { value: 'cancellation', label: 'Cancellation' },
          { value: 'bidding', label: 'Bidding' },
          { value: 'system', label: 'System' },
          { value: 'notification', label: 'Notification' },
        ],
        position: 3,
      },
      value: {
        position: 4,
        isVisible: { list: true, filter: false, show: true, edit: true },
        components: {
          show: jsonViewerComponent,
        },
      },
      previousValue: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        components: {
          show: jsonViewerComponent,
        },
      },
      bidAmounts: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        components: {
          show: bidAmountsShowComponent,
          edit: bidAmountsEditComponent,
        },
      },
      isActive: {
        position: 5,
        isVisible: { list: true, filter: true, show: true, edit: true },
      },
      updatedBy: {
        position: 6,
        isVisible: { list: true, filter: true, show: true, edit: false },
        reference: 'Admin',
      },
      createdAt: {
        position: 7,
        isVisible: { list: true, filter: true, show: true, edit: false },
        components: {
          filter: dateRangeFilterComponent,
        },
      },
      updatedAt: {
        position: 8,
        isVisible: { list: true, filter: true, show: true, edit: false },
        components: {
          filter: dateRangeFilterComponent,
        },
      },
    },
    sort: {
      sortBy: 'updatedAt',
      direction: 'desc',
    },
    filterProperties: ['name', 'category', 'isActive', 'updatedBy', 'createdAt', 'updatedAt'],
    listProperties: ['name', 'category', 'value', 'isActive', 'updatedBy', 'updatedAt'],
    showProperties: ['name', 'description', 'category', 'value', 'previousValue', 'bidAmounts', 'isActive', 'updatedBy', 'createdAt', 'updatedAt'],
    editProperties: ['name', 'description', 'category', 'value', 'bidAmounts', 'isActive'],
  },
};

export default settingsResource;