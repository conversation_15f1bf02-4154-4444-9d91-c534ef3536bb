angular.module('viewPayoutTransactions.controllers', [])

    .controller('viewPayoutTransactionsCtrl', function ($scope, APIService, $state,$rootScope,$stateParams) {


      var obj= JSON.parse($stateParams.data);
      var payout_id= obj.payout_id; 
      var user_id= obj.user_id; 
        // console.log($scope.payout_id)
          APIService.setData({ req_url: PrefixUrl + "/trancsaction/forPayout_id/",data:{ payout_id:payout_id,user_id:user_id}}).then(function (res) {
           console.log('result')
           console.log(res)
           $scope.transactionDetails= res.data;
            
            },function(er){

          })


    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});

    }


});