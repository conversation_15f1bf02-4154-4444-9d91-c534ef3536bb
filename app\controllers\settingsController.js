import Responder from '../../lib/expressResponder';
import Setting from '../models/settings';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class SettingsController {


	
	static getSetting(req, res) {
		console.log('req.params------con')
		Setting.findOne({ type: 'app_settings' })
		  .then((user) =>{ console.log(user); Responder.success(res, user)})
		  .catch((err) => Responder.operationFailed(res, err))
	}



	static createType(req, res) {
	    Setting.count({type:'app_settings'})
	    .then((count)=>
	    {
	    		console.log('count--- Setting',count)
	    	if (count > 0) {
	    		Responder.success(res,true);

	    	}else{
	    		 Setting.create({type:'app_settings'})
	     		.then((trip)=>Responder.success(res,trip))
	     		.catch((err)=>Responder.operationFailed(res,err))		
	    	}
	    })
	    .catch((err)=>Responder.operationFailed(res,err))
	    
	}


  static update(req, res) {
    console.log('req.body-- ',req.body)
    // if (req.body.location) {
    //   req.body.location.type= 'Point';
    // }

    // delete req.body._id;
    // if (!req.params.id)
      // return Responder.operationFailed(res, { message: 'User Id Is Required.' })
    Setting.findOneAndUpdate({ type:'app_settings' }, { $set: req.body })
    .then((data) =>{ return Setting.find({type:'app_settings' })})  
    .then((val) => Responder.success(res, val))
    .catch((err) => Responder.operationFailed(res, err))
    
  }

}
