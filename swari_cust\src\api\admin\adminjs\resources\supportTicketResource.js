/**
 * Support Ticket Resource for AdminJS
 * Defines the resource configuration for support tickets
 * PRD Reference: Sections 4.4, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import SupportTicket from '../../../../models/SupportTicket.js';
import Customer from '../../../../models/Customer.js';
import Driver from '../../../../models/Driver.js';
import Trip from '../../../../models/Trip.js';
import logger from '../../../../utils/logger.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register resolution Time Show component
const resolutionTimeShowComponent = componentLoader.add(
  'resolutionTimeShow', 
  join(__dirname, '../components/resolution-time-show.jsx') // Path to your React component
);

// Register ticket Responses Show component
const ticketResponsesShowComponent = componentLoader.add(
  'ticketResponsesShow', 
  join(__dirname, '../components/ticket-responses-show.jsx') // Path to your component
);
// Register related Trip Show component
const relatedTripShowComponent = componentLoader.add(
  'relatedTripShow', 
  join(__dirname, '../components/related-trip-show.jsx') // Path to your React component
);
// Register add Ticket Response component
const addTicketResponseComponent = componentLoader.add(
  'addTicketResponse', 
  join(__dirname, '../components/add-ticket-response.jsx') // Path to your component
);
// Register ticketStats component
const ticketStatsComponent = componentLoader.add(
  'ticketStats', 
  join(__dirname, '../components/ticket-stats.jsx') // Path to your React component
);







/**
 * Format user information for display
 * @param {Object} userId - User ID
 * @param {String} type - User type (customer, driver)
 * @returns {String} - Formatted user information
 */
const formatUserInfo = async (userId, type) => {
  try {
    if (!userId) return 'N/A';
    
    let user;
    if (type === 'customer') {
      user = await Customer.findById(userId).lean();
    } else if (type === 'driver') {
      user = await Driver.findById(userId).lean();
    } else {
      return 'Unknown';
    }
    
    if (!user) return 'User not found';
    
    return `${user.name || 'Unknown'} (${type})`;
  } catch (error) {
    logger.error('Error formatting user info', { error: error.message });
    return 'Error';
  }
};

/**
 * Support Ticket resource configuration
 */
const supportTicketResource = {
  resource: SupportTicket,
  options: {
    id: 'support-tickets',
    navigation: {
      name: 'Support',
      icon: 'HelpCircle',
    },
    listProperties: [
      '_id', 
      'subject', 
      'status', 
      'priority', 
      'category',
      'created_at'
    ],
    showProperties: [
      '_id', 
      'subject', 
      'description', 
      'status', 
      'priority', 
      'category',
      'related_trip_id',
      'responses',
      'resolution_time',
      'created_at',
      'updated_at'
    ],
    editProperties: [
      'status', 
      'priority', 
      'category'
    ],
    filterProperties: [
      'status', 
      'priority', 
      'category', 
      'created_at'
    ],
    properties: {
      _id: {
        isTitle: true,
        position: 1,
      },
      subject: {
        position: 2,
        isTitle: true,
      },
      description: {
        type: 'textarea',
        position: 3,
      },
      status: {
        availableValues: [
          { value: 'open', label: 'Open' },
          { value: 'in_progress', label: 'In Progress' },
          { value: 'resolved', label: 'Resolved' },
          { value: 'closed', label: 'Closed' },
        ],
        position: 4,
      },
      priority: {
        availableValues: [
          { value: 'low', label: 'Low' },
          { value: 'medium', label: 'Medium' },
          { value: 'high', label: 'High' },
          { value: 'urgent', label: 'Urgent' },
        ],
        position: 5,
      },
      category: {
        availableValues: [
          { value: 'account', label: 'Account' },
          { value: 'payment', label: 'Payment' },
          { value: 'trip', label: 'Trip' },
          { value: 'technical', label: 'Technical' },
          { value: 'other', label: 'Other' },
        ],
        position: 6,
      },
      created_at: {
        type: 'datetime',
        position: 7,
      },
      updated_at: {
        type: 'datetime',
        position: 8,
      },
      resolution_time: {
        type: 'number',
        position: 9,
        components: {
          show: resolutionTimeShowComponent,
        },
      },
      responses: {
        type: 'mixed',
        position: 10,
        components: {
          show: ticketResponsesShowComponent,
        },
      },
      related_trip_id: {
        position: 11,
        components: {
          show: relatedTripShowComponent,
        },
      },
    },
    actions: {
      new: {
        isAccessible: false, // Only create through API
      },
      delete: {
        isAccessible: false, // Tickets should not be deleted
      },
      // Custom action to add a response to a ticket
      addResponse: {
        actionType: 'record',
        component: addTicketResponseComponent,
        handler: async (request, response, context) => {
          try {
            const { record, currentAdmin } = context;
            const { content } = request.payload;
            
            if (!content) {
              return {
                record: record.toJSON(context.currentAdmin),
                notice: {
                  message: 'Response content is required',
                  type: 'error',
                },
              };
            }
            
            const ticketId = record.params._id;
            const ticket = await SupportTicket.findById(ticketId);
            
            if (!ticket) {
              return {
                record: record.toJSON(context.currentAdmin),
                notice: {
                  message: 'Ticket not found',
                  type: 'error',
                },
              };
            }
            
            // Add admin response
            ticket.responses.push({
              responder_id: currentAdmin._id,
              responder_type: 'admin',
              content,
              created_at: new Date()
            });
            
            // Update status if needed
            if (request.payload.updateStatus && request.payload.newStatus) {
              ticket.status = request.payload.newStatus;
            }
            
            // Update timestamp
            ticket.updated_at = new Date();
            
            // Save the updated ticket
            await ticket.save();
            
            logger.info(`Admin response added to ticket: ${ticketId}`);
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Response added successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error adding response to ticket', { error: error.message });
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: `Error: ${error.message}`,
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view ticket statistics
      ticketStats: {
        actionType: 'resource',
        component: ticketStatsComponent,
        handler: async (request, response, context) => {
          try {
            // Get counts by status
            const statusCounts = await SupportTicket.aggregate([
              { $group: { _id: '$status', count: { $sum: 1 } } }
            ]);
            
            // Get counts by priority
            const priorityCounts = await SupportTicket.aggregate([
              { $group: { _id: '$priority', count: { $sum: 1 } } }
            ]);
            
            // Get counts by category
            const categoryCounts = await SupportTicket.aggregate([
              { $group: { _id: '$category', count: { $sum: 1 } } }
            ]);
            
            // Get average resolution time
            const resolutionTimeStats = await SupportTicket.aggregate([
              { $match: { resolution_time: { $ne: null } } },
              { $group: { 
                  _id: null, 
                  averageTime: { $avg: '$resolution_time' },
                  minTime: { $min: '$resolution_time' },
                  maxTime: { $max: '$resolution_time' }
                } 
              }
            ]);
            
            // Format the results
            const formattedStatusCounts = statusCounts.reduce((acc, curr) => {
              acc[curr._id] = curr.count;
              return acc;
            }, {});
            
            const formattedPriorityCounts = priorityCounts.reduce((acc, curr) => {
              acc[curr._id] = curr.count;
              return acc;
            }, {});
            
            const formattedCategoryCounts = categoryCounts.reduce((acc, curr) => {
              acc[curr._id] = curr.count;
              return acc;
            }, {});
            
            const resolutionTime = resolutionTimeStats.length > 0 ? {
              average: Math.round(resolutionTimeStats[0].averageTime),
              min: resolutionTimeStats[0].minTime,
              max: resolutionTimeStats[0].maxTime
            } : { average: 0, min: 0, max: 0 };
            
            return {
              statusCounts: formattedStatusCounts,
              priorityCounts: formattedPriorityCounts,
              categoryCounts: formattedCategoryCounts,
              resolutionTime
            };
          } catch (error) {
            logger.error('Error retrieving ticket statistics', { error: error.message });
            return { error: error.message };
          }
        },
      },
    },
  },
};

export default supportTicketResource;