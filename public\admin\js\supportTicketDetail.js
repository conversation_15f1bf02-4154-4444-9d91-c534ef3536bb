angular.module('supportTicketDetail.controllers', [])

    .controller('supportTicketDetailCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';
     $scope.userDetails = [];
     $scope.title;
     $scope.category;
     $scope.description;
     $scope.message= null;
      $scope.zoomO= true;
      $scope.zoomI= false;


    var userData=localStorage.getItem('UserDeatails');
    var parsedUser= JSON.parse(userData);
    $scope.userData= parsedUser.user_details;
    console.log('user',userData)

     // if($stateParams.data){
     //     console.log('data')
     //     console.log(JSON.parse($stateParams.data));
     //     $scope.ticket= JSON.parse($stateParams.data);
     //    }


        $scope.getTicketById= function(){
          APIService.getData({
              req_url: PrefixUrl + '/SupportTicket/getTicketById/'+$scope.ticket_id
          }).then(function(resp) {
              $scope.ticket= resp.data;    
             },function(resp) {
                // This block execute in case of error.
          });

        }



      if($stateParams.data){
       console.log('data')
       console.log(JSON.parse($stateParams.data));
       var ticket= JSON.parse($stateParams.data);
       $scope.ticket_id= ticket._id;
       console.log('ttttttttttt',$scope.ticket_id)
       // $scope.getTicketById();

          APIService.getData({
            req_url: PrefixUrl + '/SupportTicket/getTicketById/'+$scope.ticket_id
          }).then(function(resp) {
              $scope.ticket= resp.data[0];    
             },function(resp) {
                // This block execute in case of error.
          });
        
      }

     $scope.getSupportTicket = function(){
      // console.log('totalData',getSupportTicket())
        APIService.getData({
            req_url: PrefixUrl + '/SupportTicketComment/getComments/'+$scope.ticket_id
        }).then(function(resp) {
          console.log('totalData',resp.data)
          $scope.supportComments= resp.data;
          $scope.message= null;
          if ($scope.ticket.reply_status == 2) {            
            $scope.updateReplyStatus(4);
          }
          
           },function(resp) {
              // This block execute in case of error.
        });
      }
    $scope.getSupportTicket()


    

      $scope.postComment = function(){
        if ($scope.message == null) {
          alert('Message is required')
        }else{
          // console.log('totalData',getSupportTicket())
            APIService.setData({
                req_url: PrefixUrl + '/SupportTicketComment/SupportTicketComment/',data:{'comment': $scope.message,'submitted_id': $scope.userData._id,'support_id':$scope.ticket._id,created_at: new Date()}
            }).then(function(resp) {
              console.log('totalData',resp.data)
              // $scope.supportComments= resp.data;
              $scope.getSupportTicket()
              $scope.updateReplyStatus(1);
              $scope.updateReplyDate();

               },function(resp) {
                  // This block execute in case of error.
            });
        }
      }


      $scope.updateReplyStatus = function(reply_status){
        console.log('reply_status--- ',reply_status)
      
          // console.log('totalData',getSupportTicket())
            APIService.updateData({
                req_url: PrefixUrl + '/SupportTicket/updateForReplyStatus/'+$scope.ticket_id,data:{reply_status:reply_status}
            }).then(function(resp) {
              console.log('totalData',resp.data)
              // $scope.supportComments= resp.data;

               },function(resp) {
                  // This block execute in case of error.
            });
      }



      $scope.updateReplyDate = function(reply_status){
        console.log('reply_status--- ',reply_status)
      
          // console.log('totalData',getSupportTicket())
            APIService.updateData({
                req_url: PrefixUrl + '/SupportTicket/updateForReplyDate/'+$scope.ticket_id,data:{reply_date:new Date()}
            }).then(function(resp) {
              console.log('totalData',resp.data)
              // $scope.supportComments= resp.data;
              // $scope.getTicketById();

               },function(resp) {
                  // This block execute in case of error.
            });
      }


      $scope.zoomOut = function(){
        $scope.zoomO= false;
        $scope.zoomI= true;
      }

      $scope.zoomIn = function(){
        $scope.zoomI= false;
        $scope.zoomO= true;
      }
      
      


  })



