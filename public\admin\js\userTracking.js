angular.module('userTracking.controllers', [])

.controller('userTrackingCtrl', function($scope, $state, APIService) {
    $scope.page = 'main';
    $scope.filters = {
        start_date: '',
        end_date: '',
        phone_number: '',
        status_type: ''
    };
    $scope.stats = {};
    $scope.users = [];
    
    $scope.getTrackingData = function() {
        APIService.getData({
            req_url: PrefixUrl + '/tracking/metrics',
            data: $scope.filters
        }).then(function(resp) {
            $scope.users = resp.data.users;
            $scope.stats = resp.data.stats;
        }, function(resp) {
            // This block executes in case of error
        });
    };

    $scope.viewTimeline = function(phoneNumber) {
        APIService.getData({
            req_url: PrefixUrl + '/tracking/timeline/' + phoneNumber
        }).then(function(resp) {
            $scope.timelineData = resp.data;
        }, function(resp) {
            // This block executes in case of error
        });
    };

    // Initialize data on controller load
    $scope.getTrackingData();
});
