angular.module('referralEarning.controllers', [])

    .controller('ReferralIncomeCtrl', function ($scope,APIService, $state,$stateParams) {
      $scope.page = 'main';
      $scope.usersList;
      $scope.maxAmount;
      $scope.minAmount;
      $scope.referred_by_code;
      var startDate;
      var endDate;

      $scope.pageLimit;


      $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
      };
      $scope.filterSearchForUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      //filter for referral code written by shisham 
      $scope.filterSearchForReferralCode= false;

  




    $scope.$watch('startDate', function (value) {
      try {
       startDate = new Date(value).toISOString().slice(0, 10);
       console.log('startDate'+startDate);

      } catch(e) {}
   
      if (!startDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



    $scope.$watch('endDate', function (value) {
      try {
       endDate = new Date(value).toISOString().slice(0, 10);
       console.log('enddate'+endDate);

      } catch(e) {}
   
      if (!endDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



   	// APIService.setData({
    //     req_url: PrefixUrl + '/trancsaction/getAllReferralTransactions'  
    // }).then(function(resp) {
    // 	console.log('getAllReferralTransactions')
    // 	console.log(resp)
    //   $scope.referralEarningList= resp.data;
    //   $scope.countItems();

    // },function(resp) {
    //   localStorage.removeItem("UserDeatails");
    //   localStorage.removeItem("token");
    //   $state.go('login');
    // });


    $scope.$watch('settings.currentPage', function (currentPage) {
        console.log('currentPage'+$scope.settings.currentPage)  
        $scope.pageLimit= $scope.settings.pageLimit;

      if($scope.filterSearchForDateAllUser){
          console.log('111111112222222222')
          $scope.filterSearchForUser= false;
          $scope.filterSearchForDateAllUser= true;
          $scope.filterSearchForPhone= false;
          $scope.filterSearchForDateSelectedUser= false;
          $scope.filterSearchFivePercentAllUser= false;
          $scope.filterSearchFivePercentSelectedUser= false;
          $scope.filterSearchTwentyPercentAllUser= false;
          $scope.filterSearchTwentyPercentSelectedUser= false;
          $scope.filterSearchForReferralCode= false;




          APIService.setData({
            req_url: PrefixUrl + '/trancsaction/getFilterByDateForAllUsers', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:startDate,endDate:endDate}  
          }).then(function(resp) {
            console.log('getFilterByDateForAllUsers')
            console.log(resp)
            $scope.referralEarningList= resp.data;
            $scope.countItems();

          });
        
        }
        else if ($scope.filterSearchForUser) {
          $scope.getTransactionsForUser($scope.userName);
        }
        else if ($scope.filterSearchForPhone) {
          $scope.getTransactionsForPhoneNumber($scope.phone_number);
        }
        else if ($scope.filterSearchForDateSelectedUser) {
          console.log('startDate== 111 '+$scope.startDate + '  endDate' + $scope.endDate)
          $scope.getUserTransactionsForDate($scope.userName,$scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchFivePercentAllUser) {
          $scope.getFilterByFivePerForAllUsers($scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchFivePercentSelectedUser) {
          $scope.getFilterByFivePerForSelectedUser($scope.userName,$scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchTwentyPercentAllUser) {
          $scope.getFilterByTwentyPerForAllUsers($scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchTwentyPercentSelectedUser) {
          $scope.getFilterByTwentyPerForSelectedUser($scope.userName);
        }
         //filter for referral code written by shisham 
         else if ($scope.filterSearchForReferralCode) {
          $scope.getTransactionsForReferralCode($scope.referred_by_code);
        }
        else{ 

         
          APIService.setData({
                req_url: PrefixUrl + '/trancsaction/getAllReferralTransactions',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.referralEarningList=resp.data
              $scope.countItems();

            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
        } 
        }    
      
      );


 //pagination code written by shisham 
     $scope.$watch('settings.pageLimit', function (pageLimit) {
       console.log('pageLimits'+pageLimit)
      $scope.pageLimit= pageLimit;

      if($scope.filterSearchForDateAllUser){
          console.log('111111112222222222')
          $scope.filterSearchForUser= false;
          $scope.filterSearchForDateAllUser= true;
          $scope.filterSearchForPhone= false;
          $scope.filterSearchForDateSelectedUser= false;
          $scope.filterSearchFivePercentAllUser= false;
          $scope.filterSearchFivePercentSelectedUser= false;
          $scope.filterSearchTwentyPercentAllUser= false;
          $scope.filterSearchTwentyPercentSelectedUser= false;
          $scope.filterSearchForReferralCode= false;



          APIService.setData({
            req_url: PrefixUrl + '/trancsaction/getFilterByDateForAllUsers', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:startDate,endDate:endDate}  
          }).then(function(resp) {
            console.log('getFilterByDateForAllUsers')
            console.log(resp)
            $scope.referralEarningList= resp.data;
            $scope.countItems();

          });
        
        }
        else if ($scope.filterSearchForUser) {
          $scope.getTransactionsForUser($scope.userName);
        }
        else if ($scope.filterSearchForPhone) {
          $scope.getTransactionsForPhoneNumber($scope.phone_number);
        }
        else if ($scope.filterSearchForDateSelectedUser) {
          $scope.getUserTransactionsForDate($scope.userName,$scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchFivePercentAllUser) {
          $scope.getFilterByFivePerForAllUsers($scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchFivePercentSelectedUser) {
          $scope.getFilterByFivePerForSelectedUser($scope.userName,$scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchTwentyPercentAllUser) {
          $scope.getFilterByTwentyPerForAllUsers($scope.startDate,$scope.endDate);
        }
        else if ($scope.filterSearchTwentyPercentSelectedUser) {
          $scope.getFilterByTwentyPerForSelectedUser($scope.userName);
        }
        //filter for referral code written by shisham 
         else if ($scope.filterSearchForReferralCode) {
          $scope.getTransactionsForReferralCode($scope.referred_by_code);
        }
        else{ 

         
          APIService.setData({
                req_url: PrefixUrl + '/trancsaction/getAllReferralTransactions',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.referralEarningList=resp.data
              $scope.countItems();

            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
        } 
        }    
      
      );





    var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }
    


    APIService.setData({
        req_url: PrefixUrl + '/trancsaction/countReferralTransactions'  
    }).then(function(resp) {
      $scope.totalTransactions= resp.data;

    },function(resp) {
    });


    // APIService.getData({
    //     req_url: PrefixUrl + '/user/allusers/admin'  
    // }).then(function(resp) {
    //   console.log('getusers')
    //   console.log(resp)
    //   $scope.usersList= resp.data;
    // });



    $scope.getUserTransactionsForDate = function(userName,startDate,endDate) {
        console.log('startDate===== '+startDate+ 'endDate == '+endDate)

      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= true;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;


      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getUserTransactionsForDate', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,userName:userName,startDate:startDate,endDate:endDate}  
      }).then(function(resp) {
        console.log('getUserTransactionsForDate')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(startDate,endDate,$scope.filter.referred_by_name,$scope.filter.referred_by_code,null,null);

          // $scope.countItems();
        }
      });

    }; 

    $scope.getTransactionsForUser = function(userName) {
      console.log('userttttttttttttttttttt',userName)
      $scope.filterSearchForUser= true;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;



      $scope.userName= userName;
      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getTransactionsForUser', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,userName:userName}  
      }).then(function(resp) {
        console.log('getTransactionsForUser')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{   
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          // $scope.countItems();
          $scope.countFilterByDateForAllUsers(null,null,$scope.filter.referred_by_name,null,null,null);
        }

      });

    };    

    $scope.findUserBusiness = function(user) {
     
     console.log('ddddddddddddddddddddddddddddddddddd')
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
  	};


    $scope.getFilterByDateForAllUsers = function(startDate,endDate) {
     
      $scope.filterSearchForDateAllUser =true;
     
      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getFilterByDateForAllUsers', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:startDate,endDate:endDate}  
      }).then(function(resp) {
        console.log('getFilterByDateForAllUsers')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(startDate,endDate,null,null,null,null);
          // $scope.countItems();
        }

      });

    }; 

    $scope.getFilterByFivePerForAllUsers = function(startDate,endDate) {
     
      $scope.filterSearchFivePercentAllUser= true;
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;

      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getFilterByFivePerForAllUsers', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:startDate,endDate:endDate}  
      }).then(function(resp) {
        console.log('getFilterByFivePerForAllUsers')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(null,null,null,null,null,5);

          // $scope.countItems();
        }
      });

    };


    $scope.getFilterByTwentyPerForAllUsers = function(startDate,endDate) {
      $scope.filterSearchTwentyPercentAllUser= true;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;

      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getFilterByTwentyPerForAllUsers', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:startDate,endDate:endDate}  
      }).then(function(resp) {
        console.log('getFilterByTwentyPerForAllUsers')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(null,null,null,null,null,20);

          // $scope.countItems();
        }

      });

    };



    $scope.getTransactionsForPhoneNumber = function(phone_number) {
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= true;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;

      $scope.phone_number= phone_number;
      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getTransactionsForPhoneNumber', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,phone_number:phone_number}  
      }).then(function(resp) {
        console.log('getTransactionsForPhoneNumber')
        console.log(resp)
          // $scope.countFilterByDateForAllUsers(null,null,null,phone_number);
          console.log('getTransactionsForPhoneNumber  - ---',resp.data.length)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{          
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(null,null,null,null,phone_number,null);

        }
        
        // $scope.countItems();

      });

    };



    $scope.getFilterByFivePerForSelectedUser = function(userName,startDate,endDate) {
      $scope.userName= userName;
      $scope.filterSearchFivePercentSelectedUser= true;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= false;

      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getFilterByFivePerForSelectedUser', data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,userName:userName,startDate:startDate,endDate:endDate}  
      }).then(function(resp) {
        console.log('getFilterByFivePerForSelectedUser')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(null,null,$scope.filter.referred_by_name,$scope.filter.referred_by_code,null,5);

          // $scope.countItems();
        }

      });

    };
    
    $scope.getFilterByTwentyPerForSelectedUser = function(userName) {
      $scope.filterSearchTwentyPercentSelectedUser= true;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchForReferralCode= false;

      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getFilterByTwentyPerForSelectedUser', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,userName:userName}  
      }).then(function(resp) {
        console.log('getFilterByTwentyPerForSelectedUser')
        console.log(resp)
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data[0].myCount;
          $scope.countFilterByDateForAllUsers(null,null,$scope.filter.referred_by_name,$scope.filter.referred_by_code,null,20);

          // $scope.countItems();
        }

      });

    };
    $scope.getTransactionsForReferralCode = function(referred_by_code) {
      $scope.filterSearchForUser= false;
      $scope.filterSearchForDateAllUser= false;
      $scope.filterSearchForPhone= false;
      $scope.filterSearchForDateSelectedUser= false;
      $scope.filterSearchFivePercentAllUser= false;
      $scope.filterSearchFivePercentSelectedUser= false;
      $scope.filterSearchTwentyPercentAllUser= false;
      $scope.filterSearchTwentyPercentSelectedUser= false;
      $scope.filterSearchForReferralCode= true;

      $scope.referred_by_code= referred_by_code;
      APIService.setData({
        req_url: PrefixUrl + '/trancsaction/getTransactionsForReferralCode', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,referred_by_code:$scope.filter.referred_by_code}  
      }).then(function(resp) {
        console.log('getTransactionsForReferralCode')
        console.log(resp)
          // $scope.countFilterByDateForAllUsers(null,null,null,phone_number);
          
        if (resp.data.length == 0) {
          $scope.referralEarningList= 0;
          $scope.totalTransactions= 0;
        }else{          
          $scope.referralEarningList= resp.data;
          // $scope.totalTransactions= resp.data.length;
          $scope.countFilterByDateForAllUsers(null,null,null,$scope.filter.referred_by_code,null,null);

        }
        
        // $scope.countItems();

      });

    };

    
    $scope.countFilterByDateForAllUsers= function(startDate,endDate,referred_by_name,referred_by_code,phone_number,percent){
        APIService.setData({
            req_url: PrefixUrl + '/trancsaction/countFilterByDateForAllUsers',data:{startDate:startDate,endDate:endDate,referred_by_name: referred_by_name,referred_by_code:referred_by_code,phone_number,percent:percent}  
        }).then(function(resp) {
          $scope.totalTransactions= resp.data;

        },function(resp) {
        });
    }

    $scope.countItems= function(){
      console.log('count---')
      
      // $scope.totalTransactions= 0;
        // $scope.referralEarningList.forEach(function (item, k) {                
        //   console.log('ittttttttttt',item);
        //   $scope.totalTransactions= $scope.totalTransactions+ item.myCount
        // });

        // console.log('ccccccccccccc',$scope.totalTransactions);
    
    }
      

});
