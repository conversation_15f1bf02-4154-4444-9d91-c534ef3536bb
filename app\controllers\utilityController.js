import Responder from '../../lib/expressResponder';
import { MulterService } from '../service';
import User from '../models/user';
const AWS = require('aws-sdk');
import { PutObjectCommand, GetObjectCommand, GetObjectAclCommand,S3Client } from '@aws-sdk/client-s3';
const fs = require('fs');
const path = require('path');
import mongoose from 'mongoose';
var ObjectId = mongoose.Types.ObjectId;

const baseSpaces = "https://swaritest.sgp1.digitaloceanspaces.com";
// Step 2: The s3Client function validates your request and directs it to your Space's specified endpoint using the AWS SDK.
const s3Client = new S3Client({
    endpoint: "https://sgp1.digitaloceanspaces.com", // Find your endpoint in the control panel, under Settings. Prepend "https://".
    forcePathStyle: false,
    region: "sgp1", // Must be "us-east-1" when creating new Spaces. Otherwise, use the region in your endpoint (e.g. nyc3).
    credentials: {
      accessKeyId: "DO00DDZBQ27VM24JGC8W", // Access key pair. You can create access key pairs using the control panel or API.
      secretAccessKey: "zF2lMVNnNkZYL9M6pfdcyqZPe3Hw5wSgzMc3yCFx96E" // Secret access key defined through an environment variable.
    }
});

export default class UtilityController {

	static uploadImage(req, res) {
		console.log('aaaaaaaaaaaaa');
		// console.log(req);
		MulterService.uploadImage(req, res)
			.then(file => {
				console.log(file);

				console.log('fileeeeeeeeeeeeee   ');
				console.log(file);
				console.log(file.destination);
				console.log(file.filename);
				console.log('fileeeeeeeeeeeeee   ');


				const sharp = require('sharp');
				const fs = require('fs');

				sharp(file.path)
					.resize({ width: 500 })
					.toFile(file.destination + '200' + file.filename, function (err) {
						// output.jpg is a 200 pixels wide and 200 pixels high image
						// containing a scaled and cropped version of input.jpg
					});

				// 	sharp(file.path)
				// .resize(400, 400)
				// .toFile(file.destination+'400'+file.filename, function(err) {
				//   // output.jpg is a 200 pixels wide and 200 pixels high image
				//   // containing a scaled and cropped version of input.jpg
				// });

				var imagesArr = {};
				imagesArr.file = file;
				imagesArr.file200 = '200' + file.filename;
				console.log('upload file--- ', imagesArr.file.path)
				// imagesArr.file400= '400'+file.filename;
				// imagesArr.push(file.destination+'ouput200.jpg');
				// imagesArr.push(file);




				//configuring the AWS environment
				AWS.config.update({
					accessKeyId: "********************",
					secretAccessKey: "4p3NvQoqp6H2zkvb9POUJk8nTXRx46KLcLA5914t",
				});

				var s3 = new AWS.S3();
				var filePath = file.path;

				console.log('s3  -- ', s3)

				//configuring parameters
				var uploadParams = {
					Bucket: 'triva-in',
					Body: fs.createReadStream(file.path),
					Key: "qrs/" + file.filename
				};

				var params = {
					Bucket: 'triva-in',
					// Body : fs.createReadStream(file.path),
					Key: "qrs/" + file.filename
				};


				// console.log('Bucket locaction -- ',s3.getPublicUrl('triva-in', "folder/file-1574407048917.jpg", ["https://triva-in.s3.amazonaws.com/"]));
				s3.upload(uploadParams, function (err, data) {
					//handle error
					if (err) {
						console.log("Error", err);
					}

					//success
					if (data) {
						console.log("Uploaded in:", data);

						require("fs").unlinkSync('./public/uploads/' + imagesArr.file200);
						require("fs").unlinkSync(imagesArr.file.path);

						s3.getObject(params, function (errtxt, file) {
							if (errtxt) {
								console.Log("lireFic", "ERR " + errtxt);
							} else {
								console.log('lecture OK')
								// var imageTest={};
								// imageTest.image =  encode(file.Body);
								// imageTest.response =  data;
								// // console.log('imageTest   ',imageTest);

								Responder.success(res, data.Location)

							}
						});
						function encode(data) {
							var btoa = require('btoa');

							var str = data.reduce(function (a, b) { return a + String.fromCharCode(b) }, '');
							return btoa(str).replace(/.{76}(?=.)/g, '$&\n');
						}
					}
				});

			}
			)
			.catch(error => Responder.operationFailed(res, error));
	}

	static uploadImageSpaces(req, res) {		
  		var folder_name = req.headers.folder;
		MulterService.uploadImage(req, res)
			.then(file => {
				console.log(file);
				
				console.log(file);
				console.log(file.destination);
				console.log(file.filename);

				const sharp = require('sharp');
				const fs = require('fs');

				sharp(file.path)
					.resize({ width: 500 })
					.toFile(file.destination + '200' + file.filename, function (err) {
						// output.jpg is a 200 pixels wide and 200 pixels high image
						// containing a scaled and cropped version of input.jpg
					});				

				var imagesArr = {};
				imagesArr.file = file;
				imagesArr.file200 = '200' + file.filename;
				console.log('upload file--- ', imagesArr.file.path)
				var filePath = file.path;
				// console.log('s3  -- ', s3)

				//configuring parameters
				var uploadParams = {
					Bucket: 'swaritest',
					ACL: "public-read",
					Body: fs.createReadStream(file.path),
					Key: folder_name +"/" + file.filename
				};			
				  
				let defaultFolder = folder_name;

				//if(req.folder) {
				//	defaultFolder = req.folder;
				//}else{
					defaultFolder = folder_name
				//}
				var params = {
					Bucket: 'swaritest',
					// Body : fs.createReadStream(file.path),
					Key: defaultFolder +"/"+ file.filename
				};

				s3Client.send(new PutObjectCommand(uploadParams)).then( (e)=> {				
					return Responder.success(res, baseSpaces+"/"+params.Key)						
				});

					// return null;

				}).catch((err)=>{
					console.error(" err ",err)
					return null;
				});
	}			
	
	static useUploadFiles(req, res) {		
		var folder_name = "user-docs";
		var user = req.user;
	  MulterService.uploadImage(req, res)
		  .then(file => {
			  console.log(file);
			  
			  console.log(file);
			  console.log(file.destination);
			  console.log(file.filename);

			  const sharp = require('sharp');
			  const fs = require('fs');

			  sharp(file.path)
				  .resize({ width: 500 })
				  .toFile(file.destination + '200' + file.filename, function (err) {
					  // output.jpg is a 200 pixels wide and 200 pixels high image
					  // containing a scaled and cropped version of input.jpg
				  });				

			  var imagesArr = {};
			  imagesArr.file = file;
			  imagesArr.file200 = '200' + file.filename;
			  console.log('upload file--- ', imagesArr.file.path)
			  var filePath = file.path;
			  // console.log('s3  -- ', s3)

			  folder_name = folder_name +"/" +user.phone_number+"/";
			  //configuring parameters
			  var uploadParams = {
				  Bucket: 'swaritest',
				//   ACL: "public-read",
				  Body: fs.createReadStream(file.path),
				  Key: folder_name + file.filename
			  };			
				
			  let defaultFolder = folder_name;

			  //if(req.folder) {
			  //	defaultFolder = req.folder;
			  //}else{
				//   defaultFolder = folder_name
			  //}
			//   var params = {
			// 	  Bucket: 'swaritest',
			// 	  // Body : fs.createReadStream(file.path),
			// 	  Key: defaultFolder +"/"+ file.filename
			//   };
			 
			  s3Client.send(new PutObjectCommand(uploadParams)).then( (e)=> {		
					User.findOneAndUpdate({
						_id: user._id
					}, {
						$push: {
							'documents': {
								_id: new ObjectId(),
								url: baseSpaces+"/"+uploadParams.Key,
								type: req.body.type// doc type
							} 

						}
					}).
					exec((result) => {
					console.log('user doc uploaded ',result)
					// callback(null);

					console.log("updated 1");
					});

					console.log(" =========  ",baseSpaces+"/"+uploadParams.Key)
				  return Responder.success(res, baseSpaces+"/"+uploadParams.Key)						
			  });

				  // return null;

			  }).catch((err)=>{
				  console.error(" err ",err)
				  return null;
			  });
  	}
	
	static	deleteDoc(req, res) {
		var user = req.user;

		User.updateOne(
			{ "_id": user._id },
			{ $pull: { "documents": { "_id": ObjectId(req.params.id) } } }
		  ).exec((result) => {
			console.log('Doc deleted ',req.params.id, result)
			// callback(null);

			console.log("updated 1");
		});
		
	  return Responder.success(res, "Document deleted successfully")						
	}
}
