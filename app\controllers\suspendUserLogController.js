import Responder from '../../lib/expressResponder';
import SuspendUserLog from '../models/suspendUserLog';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class SuspendUserLogController {


  static create(req, res) {
    console.log(req.body);
    SuspendUserLog.create(req.body)
     .then((trnc)=>{
      Responder.success(res,trnc)
      console.log('trccccc')
      // console.log(trnc)
     });
  }


  static show(req, res) {
    // console.log('res.params',req.params)

       SuspendUserLog.aggregate([ 
              {
                    $match: {
                      
                    user_id:req.params.id


                      }
                    
              },
              {
                '$sort':{created_at:-1}
              }
  	 
      ])
     .then((trnc)=>{
      Responder.success(res,trnc)
      console.log('trccccc')
      // console.log(trnc)
     });

  }

}
