/**
 * Wallet Service
 * Handles wallet operations and transaction logging
 * PRD Reference: Sections 4.8, 10.3
 */

import mongoose from 'mongoose';
import Customer from '../models/Customer.js';
import Driver from '../models/Driver.js';
import Transaction from '../models/Transaction.js';
import logger from '../utils/logger.js';

/**
 * Get wallet balance for a user
 * @param {string} userId - User ID
 * @param {string} userRole - User role (customer or driver)
 * @returns {Promise<number>} - Wallet balance
 */
export const getWalletBalanceService = async (userId, userRole) => {
  try {
    let user;
    
    if (userRole === 'customer') {
      user = await Customer.findById(userId);
    } else if (userRole === 'driver') {
      user = await Driver.findById(userId);
    } else {
      throw new Error('Invalid user role');
    }

    if (!user) {
      throw new Error('User not found');
    }

    return user.wallet_balance;
  } catch (error) {
    logger.error('Error getting wallet balance', { error: error.message, userId, userRole });
    throw error;
  }
};

/**
 * Get transaction history for a user
 * @param {string} userId - User ID
 * @param {string} userRole - User role (customer or driver)
 * @param {Object} filters - Optional filters (date range, transaction type)
 * @returns {Promise<Array>} - Array of transactions
 */
export const getTransactionHistoryService = async (userId, userRole, filters = {}) => {
  try {
    const userType = userRole === 'customer' ? 'customer' : 'driver';
    
    // Build query
    const query = {
      user_id: userId,
      user_type: userType
    };

    // Add date range filter if provided
    if (filters.from || filters.to) {
      query.created_at = {};
      
      if (filters.from) {
        query.created_at.$gte = new Date(filters.from);
      }
      
      if (filters.to) {
        // Set time to end of day
        const toDate = new Date(filters.to);
        toDate.setHours(23, 59, 59, 999);
        query.created_at.$lte = toDate;
      }
    }

    // Add transaction type filter if provided
    if (filters.transaction_type) {
      query.transaction_type = filters.transaction_type;
    }

    const transactions = await Transaction.find(query)
      .sort({ created_at: -1 })
      .limit(filters.limit || 50);

    return transactions;
  } catch (error) {
    logger.error('Error getting transaction history', { error: error.message, userId, userRole });
    throw error;
  }
};

/**
 * Create a transaction log
 * @param {string} userId - User ID
 * @param {string} userRole - User role (customer or driver)
 * @param {string} transactionType - Type of transaction
 * @param {number} amount - Transaction amount
 * @param {Object} options - Additional options (reference_id, reference_type, notes)
 * @returns {Promise<Object>} - Created transaction
 */
export const createTransaction = async (userId, userRole, transactionType, amount, options = {}) => {
  // Check if running in test environment
  const isTestEnvironment = process.env.NODE_ENV === 'test';
  
  // If in test environment or MongoDB is not configured for transactions, use a simplified approach
  if (isTestEnvironment || !mongoose.connection.db.serverConfig.isConnected()) {
    try {
      // Get user model based on role
      const UserModel = userRole === 'customer' ? Customer : Driver;
      const userType = userRole === 'customer' ? 'customer' : 'driver';
      
      // Get user with current balance
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Calculate new balance
      const balanceBefore = user.wallet_balance;
      const balanceAfter = balanceBefore + amount;

      // Update user's wallet balance
      await UserModel.findByIdAndUpdate(
        userId,
        { wallet_balance: balanceAfter }
      );

      // Create transaction log
      const transaction = new Transaction({
        user_id: userId,
        user_type: userType,
        transaction_type: transactionType,
        amount,
        reference_id: options.reference_id || null,
        reference_type: options.reference_type || null,
        notes: options.notes || '',
        balance_before: balanceBefore,
        balance_after: balanceAfter
      });

      await transaction.save();

      logger.info('Transaction created successfully (test mode)', {
        userId,
        userRole,
        transactionType,
        amount,
        balanceBefore,
        balanceAfter
      });

      return transaction;
    } catch (error) {
      logger.error('Error creating transaction (test mode)', {
        error: error.message,
        userId,
        userRole,
        transactionType,
        amount
      });

      throw error;
    }
  }
  
  // Regular production code with transactions
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Get user model based on role
    const UserModel = userRole === 'customer' ? Customer : Driver;
    const userType = userRole === 'customer' ? 'customer' : 'driver';
    
    // Get user with current balance
    const user = await UserModel.findById(userId).session(session);
    if (!user) {
      throw new Error('User not found');
    }

    // Calculate new balance
    const balanceBefore = user.wallet_balance;
    const balanceAfter = balanceBefore + amount;

    // Update user's wallet balance
    await UserModel.findByIdAndUpdate(
      userId,
      { wallet_balance: balanceAfter },
      { session }
    );

    // Create transaction log
    const transaction = new Transaction({
      user_id: userId,
      user_type: userType,
      transaction_type: transactionType,
      amount,
      reference_id: options.reference_id || null,
      reference_type: options.reference_type || null,
      notes: options.notes || '',
      balance_before: balanceBefore,
      balance_after: balanceAfter
    });

    await transaction.save({ session });

    // Commit transaction
    await session.commitTransaction();
    session.endSession();

    logger.info('Transaction created successfully', {
      userId,
      userRole,
      transactionType,
      amount,
      balanceBefore,
      balanceAfter
    });

    return transaction;
  } catch (error) {
    // Abort transaction on error
    await session.abortTransaction();
    session.endSession();

    logger.error('Error creating transaction', {
      error: error.message,
      userId,
      userRole,
      transactionType,
      amount
    });

    throw error;
  }
};

/**
 * Check if user has sufficient balance for a transaction
 * @param {string} userId - User ID
 * @param {string} userRole - User role (customer or driver)
 * @param {number} requiredAmount - Required amount
 * @returns {Promise<boolean>} - True if sufficient balance, false otherwise
 */
export const hasSufficientBalance = async (userId, userRole, requiredAmount) => {
  try {
    const balance = await getWalletBalance(userId, userRole);
    return balance >= requiredAmount;
  } catch (error) {
    logger.error('Error checking sufficient balance', {
      error: error.message,
      userId,
      userRole,
      requiredAmount
    });
    throw error;
  }
};





/**
 * Get current wallet balance for a user by calculating from transaction history
 * @param {string} userId - User ID to calculate balance for
 * @returns {Promise<number>} - Current balance calculated from transactions
 */
export const getCurrentBalance = async (userId) => {
  try {
    // Convert userId to ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);
    // Get all transactions for the user
    const transactions = await Transaction.getTransactionsByUserId(userObjectId);

    // logger.info(`Transactions retrieved successfully. Is transaction an array? ${Array.isArray(transactions)}`);
    // transactions.forEach(transaction => {
    //   logger.info('Transaction details:', {
    //     id: transaction._id,
    //     type: transaction.transaction_type,
    //     amount: transaction.amount,
    //     created_at: transaction.created_at
    //   });
    // });
    
    // Calculate balance by summing CR transactions and subtracting DR transactions
    const balance = transactions.reduce((acc, transaction) => {
      logger.info('Balance calculation:', {
        currentBalance: acc,
        transactionAmount: transaction.amount,
        transactionType: transaction.transaction_type,
        transactionId: transaction.transaction_id,
      });
    
      if (transaction.transaction_type === 'CR') {
        return acc + transaction.amount;
      } else if (transaction.transaction_type === 'DR') {
        return acc - transaction.amount;
      }
    
      return acc;
    }, 0);
    
    return balance;
  } catch (error) {
    throw new Error(`Error calculating current balance: ${error.message}`);
  }
};

