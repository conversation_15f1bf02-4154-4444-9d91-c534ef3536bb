import Responder from '../../lib/expressResponder';
import ExtendDayLog from '../models/extendDayLog';
import User from '../models/user';
import _ from "lodash";
var ObjectId= require('mongodb').ObjectId;
import mongoose from 'mongoose';

export default class ExtendDayLogController {

	static create(req, res) {
    	ExtendDayLog.create(req.body)
       .then((rateR)=>Responder.success(res,rateR))
       .catch((err)=>Responder.operationFailed(res,err))
  	}

	static showData(req, res) {

		ExtendDayLog.aggregate([ 
          {
                $match: {
                  }
                
          },

          { $lookup:
                 {
                   from: 'users',
                   localField: 'user_id',
                   foreignField: '_id',
                   as: 'userDetails'
                 }
               },
               { 
                "$sort": { 
                    "created_at": -1,
                } 
            },
          


                ])
    	.then((trc)=>Responder.success(res,trc))
    	.catch((err)=>Responder.operationFailed(res,err))
   

  	}
}
