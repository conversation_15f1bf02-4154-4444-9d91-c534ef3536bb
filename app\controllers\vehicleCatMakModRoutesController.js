import Responder from '../../lib/expressResponder';
import Vehicle from '../models/vehicle';
import User from '../models/user';
import _ from "lodash";
import VehicleCatMakMod from '../models/VehicleCatMakMod';

export default class vehicleCatMakModRoutesController {

 


  static count(req, res) {
  }

  static show(req, res) {

    console.log(req.params.user_id)
     VehicleCatMakMod.aggregate([ 
              {
                    $match: {
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehiclemakers',
                   localField: 'vehicleMaker_id',
                   foreignField: '_id',
                   as: 'vehicleMaker'
                 }
               },
                { $lookup:
                 {
                   from: 'vehiclemodels',
                   localField: 'vehicleModels_id',
                   foreignField: '_id',
                   as: 'vehicleModel'
                 }
               },
               { $lookup:
                 {
                   from: 'vehicletypes',
                   localField: 'vehicleType_id',
                   foreignField: '_id',
                   as: 'vehicleType'
                 }
               }


                    ])
    .then((veh)=>Responder.success(res,veh))
    .catch((err)=>Responder.operationFailed(res,err))
   
  }
 
  static create(req, res) {
   
    VehicleCatMakMod.create(req.body)
    .then((vehicle)=>Responder.success(res,vehicle))
    .catch((err)=>Responder.operationFailed(res,err))

  }

  static update(req, res) {


    VehicleCatMakMod.findOneAndUpdate({_id:req.params.id},{$set:req.body})
     .then((val)=>Responder.success(res,val))
     .catch((err)=>Responder.operationFailed(res,err))
  }

  static remove(req, res) {
    Vehicle.remove({_id:req.params.id})
    .then((product)=>Responder.success(res,product))
    .catch((err)=>Responder.operationFailed(res,err))
  }


  static getVehicleCategories(req, res) {
    
    VehicleCatMakMod.aggregate([ 
          {
                $match: {
                 
                  }
                
          },
          { $lookup:
            {
              from: 'vehicleModels',
              localField: 'model_id',
              foreignField: '_id',
              as: 'vehicleModels'
            }
            }


                ])
      .then((trc)=>Responder.success(res,trc))
      .catch((err)=>Responder.operationFailed(res,err))

  }
}
