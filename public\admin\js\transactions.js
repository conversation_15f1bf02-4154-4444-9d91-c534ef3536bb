angular.module('transactions.controllers', [])

    .controller('transactionsCtrl', function ($scope,$state, APIService) {
      $scope.page = 'main';
      $scope.transactionDetails = [];
      $scope.dateF = {};
      $scope.pageLimit;
      $scope.transactionsLength;
      $scope.getDebitTrancsactionsData;
      $scope.debitTransaction=false;
      $scope.creditTransaction=false;
      $scope.allTransaction=true;
      var startDate;
      var endDate;
      $scope.filterSearch1= false;
      $scope.filterSearch2= false;



    // var userData=localStorage.getItem('UserDeatails');
    // var parsedUser= JSON.parse(userData);
    // console.log(parsedUser)
    // if (parsedUser.role != 'admin') {
    //   localStorage.removeItem("UserDeatails");
    //   localStorage.removeItem("token");
    //   $state.go('login');
    // }



    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 20,
      pageLimits: [2, 5, 10,20,100]
    };

    $scope.transaction_group= "All";
    
     $scope.exportAsExcel = function () {
        console.log('1111111111')
          var blob = new Blob([document.getElementById('exportable').innerHTML], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
          });
          saveAs(blob, new Date().toISOString().slice(0, 10)+"transaction.xls")
          
      };
    
    $scope.oncv =function(){
        
        $scope.dateF.transaction_date = new Date(($scope.dateF.transaction_date).toDateString());
        console.log( $scope.dateF)
    }
  

    $scope.getTranscationsDetails = function() {


        $scope.$watch('settings.pageLimit', function (pageLimit) {
          console.log('pageLimits'+pageLimit)
          $scope.pageLimit= pageLimit;
           
          if($scope.filterSearch2){
            APIService.setData({
              req_url: PrefixUrl + '/trancsaction/filterTransactionDetail' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate: $scope.startDate,endDate:$scope.endDate,due_to_user_name:$scope.due_to_user_name,transaction_id:$scope.transaction_id,amount:$scope.amount,transaction_reason:$scope.transaction_reason,phone_number:$scope.phone_number } 
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.transactionDetails=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
          }else if($scope.filterSearch1)  {
            APIService.setData({
              req_url: PrefixUrl + '/trancsaction/filterTransactions' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, type:$scope.transaction_group } 
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.transactionDetails=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
          }
       


        $scope.$watch('settings.currentPage', function (value) {
          console.log('currentPage'+$scope.settings.currentPage)  
            if($scope.filterSearch2){
            APIService.setData({
              req_url: PrefixUrl + '/trancsaction/filterTransactionDetail' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate: $scope.startDate,endDate:$scope.endDate,due_to_user_name:$scope.due_to_user_name,transaction_id:$scope.transaction_id,amount:$scope.amount,transaction_reason:$scope.transaction_reason,phone_number:$scope.phone_number } 
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.transactionDetails=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
          }else if($scope.filterSearch1){
            APIService.setData({
              req_url: PrefixUrl + '/trancsaction/filterTransactions' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, type:$scope.transaction_group } 
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.transactionDetails=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
          }else{
             APIService.setData({
              req_url: PrefixUrl + '/trancsaction/filterTransactions' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, type:$scope.transaction_group } 
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.transactionDetails=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
            });
          }
        }
        );

         }
        );

        // APIService.getData({ req_url: PrefixUrl + "/trancsaction/all/usrs/count"}).then(function (res) {
        //     console.log(res)

        //     $scope.transactionsLength=res.data;
        //     // $scope.transactionDetails=res.data;
           
        
        // },function(er){
        //         localStorage.removeItem("UserDeatails");
        //         localStorage.removeItem("token");
        //         $state.go('login');
        // })



        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }

    }

    $scope.getTranscationsDetails();


    $scope.checkPagination = function(id) {

      $scope.$watch('settings.pageLimit', function (pageLimit) {
        console.log('pageLimits'+pageLimit)
        $scope.pageLimit= pageLimit;
          if ($scope.debitTransaction) {
            APIService.setData({
                req_url: PrefixUrl + '/trancsaction/getDebitTrancsactions',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            }).then(function(resp) {
            $scope.transactionDetails= resp.data;

                },function(resp) {
                  // This block execute in case of error.
                  localStorage.removeItem("UserDeatails");
                  localStorage.removeItem("token");
                  $state.go('login');
            });
          }

          if ($scope.creditTransaction) {
            APIService.setData({
                req_url: PrefixUrl + '/trancsaction/getCreditTransactions',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            }).then(function(resp) {
            $scope.transactionDetails= resp.data;

                },function(resp) {
                  // This block execute in case of error.
                  localStorage.removeItem("UserDeatails");
                  localStorage.removeItem("token");
                  $state.go('login');
            });
          }

          
      }
      );


      $scope.$watch('settings.currentPage', function (value) {
        console.log('currentPage'+$scope.settings.currentPage)  
        // console.log('userDetailslll='+$scope.userDetails.length)
          if ($scope.PayToBanks) {
            APIService.setData({
                req_url: PrefixUrl + '/withdrawWallet/withdrawWalletGetDataForPaidToBank',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            }).then(function(resp) {
            $scope.withdrawWalletGetDataForPaidToBank= resp.data;
              console.log('withdrawWalletGetDataForPaidToBank')
              console.log($scope.withdrawWalletGetDataForPaidToBank)

                },function(resp) {
            });
          }

          
      }
      );
    }

    $scope.checkPagination();

    $scope.debitTransactions = function(){

      $scope.debitTransaction =true;
      $scope.creditTransaction =false;
      $scope.allTransaction =false;

        APIService.setData({
            req_url: PrefixUrl + '/trancsaction/getDebitTrancsactionsLength'
        }).then(function(resp) {
        $scope.transactionsLength= resp.data;

           },function(resp) {
              // This block execute in case of error.
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
        });

      $scope.checkPagination();

      
    };


    $scope.creditTransactions = function(){

      $scope.creditTransaction =true;
      $scope.debitTransaction =false;
      $scope.allTransaction =false;

        APIService.setData({
            req_url: PrefixUrl + '/trancsaction/getCreditTransactionsLength'
        }).then(function(resp) {
        $scope.transactionsLength= resp.data;

           },function(resp) {
              // This block execute in case of error.
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
        });

      $scope.checkPagination();

      
    };


    $scope.allTransactions = function(){

      $scope.allTransaction =true;
      $scope.creditTransaction =false;
      $scope.debitTransaction =false;

      $scope.getTranscationsDetails();

        // APIService.setData({
        //     req_url: PrefixUrl + '/trancsaction/getCreditTransactionsLength'
        // }).then(function(resp) {
        // $scope.transactionsLength= resp.data;

        //    },function(resp) {
        //       // This block execute in case of error.
        //       localStorage.removeItem("UserDeatails");
        //       localStorage.removeItem("token");
        //       $state.go('login');
        // });
      
    };



     $scope.$watch('startDate', function (value) {
      try {
       startDate = new Date(value).toISOString().slice(0, 10);
       console.log('startDate'+startDate);

      } catch(e) {}
   
      if (!startDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



    $scope.$watch('endDate', function (value) {
      try {
       endDate = new Date(value).toISOString().slice(0, 10);
       console.log('enddate'+endDate);

      } catch(e) {}
   
      if (!endDate) {
   
        $scope.error = "This is not a valid date";
      } else {
        $scope.error = false;
      }
    });



    $scope.filterTransaction = function(startDate,endDate) {
        
        $scope.filterSearch1= true;
        $scope.filterSearch2= false;


        if (startDate) {
            $scope.startDate= startDate;
        }else{
            $scope.startDate= null;
        }

        if (endDate) {
          $scope.endDate= endDate;
        }else{
            $scope.endDate= null;
        }



       

       APIService.setData({
          req_url: PrefixUrl + '/trancsaction/filterTransactions' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, type:$scope.transaction_group } 
      }).then(function(resp) {
        $scope.transactionDetails= resp.data;
        $scope.filtertrancsactionsCount();
      },function(resp) {
       
      });
    }



    $scope.trancsactionCountForFirstTime= function(){
        APIService.setData({ req_url: PrefixUrl + "/trancsaction/all/usrs/countPostForFirstTime",data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate: $scope.startDate,endDate:$scope.endDate,due_to_user_name:$scope.due_to_user_name,transaction_id:$scope.transaction_id,amount:$scope.amount,transaction_reason:$scope.transaction_reason,phone_number:$scope.phone_number }
      }).then(function (res) {
            console.log(res)

            $scope.transactionsLength=res.data;
            // $scope.transactionDetails=res.data;
           
        
        },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        })

    }


    $scope.trancsactionCount= function(){
        APIService.setData({ req_url: PrefixUrl + "/trancsaction/all/usrs/countPost",data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate: $scope.startDate,endDate:$scope.endDate,due_to_user_name:$scope.due_to_user_name,transaction_id:$scope.transaction_id,amount:$scope.amount,transaction_reason:$scope.transaction_reason,phone_number:$scope.phone_number }
      }).then(function (res) {
            console.log(res)

            $scope.transactionsLength=res.data[0].myCount;
            // $scope.transactionDetails=res.data;
           
        
        },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        })

    }


    $scope.filtertrancsactionsCount= function(){
        APIService.setData({ req_url: PrefixUrl + "/trancsaction/all/usrs/filtertrancsactionsCount",data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, type:$scope.transaction_group }
      }).then(function (res) {
            console.log(res)

            // $scope.transactionsLength=res.data[0].myCount;
          if (res.data.length == 0) {
            $scope.transactionsLength= 0;
          }else{
            $scope.transactionsLength= res.data[0].myCount;

          }
            // $scope.transactionDetails=res.data;
           
        
        },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
        })

    }


    

    $scope.filterTransactionDetail = function(startDate,endDate) {

        $scope.filterSearch1 =false;
        $scope.filterSearch2= true;



        if (startDate) {
            $scope.startDate= startDate;
        }else{
            $scope.startDate= null;
        }

        if (endDate) {
          $scope.endDate= endDate;
        }else{
            $scope.endDate= null;
        }

        if ($scope.due_to_user_name) {
            $scope.due_to_user_name= $scope.due_to_user_name;
        }else{
            $scope.due_to_user_name= null;
        }

        if ($scope.transaction_id) {
            $scope.transaction_id= $scope.transaction_id;
        }else{
            $scope.transaction_id= null;
        }

        if ($scope.amount) {
            $scope.amount= $scope.amount;
        }else{
            $scope.amount= null;
        }

        if ($scope.transaction_reason) {
            $scope.transaction_reason= $scope.transaction_reason;
        }else{
            $scope.transaction_reason= null;
        }

        if ($scope.phone_number) {
            $scope.phone_number= $scope.phone_number;
        }else{
            $scope.phone_number= null;
        }

        

       APIService.setData({
          req_url: PrefixUrl + '/trancsaction/filterTransactionDetail' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate: $scope.startDate,endDate:$scope.endDate,due_to_user_name:$scope.due_to_user_name,transaction_id:$scope.transaction_id,amount:$scope.amount,transaction_reason:$scope.transaction_reason,phone_number:$scope.phone_number } 
      }).then(function(resp) {
        $scope.transactionDetails= resp.data;
        $scope.transactionsLength= resp.data.length;
        $scope.trancsactionCount();


      },function(resp) {
       
      });
    }

    $scope.trancsactionCountForFirstTime();

    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }

})
       