const https = require('https');

function wpSendOtp(otpToken, phoneNumber) {
    console.log("WP Send OTP ", "91"+phoneNumber, otpToken);
    const data = JSON.stringify({
        apiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY2YjVhZjI5ZWJkNTE1MGI3MDgxMmIzNiIsIm5hbWUiOiJUcml2YSIsImFwcE5hbWUiOiJBaVNlbnN5IiwiY2xpZW50SWQiOiI2NmI1YWYyOGViZDUxNTBiNzA4MTJiMzEiLCJhY3RpdmVQbGFuIjoiQkFTSUNfTU9OVEhMWSIsImlhdCI6MTcyMzE4Mjg4OX0.9bE_Ai0pBxrEORwYMhD1uDUi21o4t_cGe9zoNV3uyEE",
        campaignName: "SignupOTP",
        destination: "91"+phoneNumber, //"919988094305",
        userName: "Triva",
        templateParams: [
            otpToken
        ],
        source: "new-landing-page form",
        media: {},
        buttons: [
            {
                type: "button",
                sub_type: "url",
                index: 0,
                parameters: [
                    {
                        type: "text",
                        text: otpToken
                    }
                ]
            }
        ],
        carouselCards: [],
        location: {},
        paramsFallbackValue: {
            "1": otpToken
        }
    });

    const options = {
        hostname: 'backend.aisensy.com',
        port: 443,
        path: '/campaign/t1/api/v2',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': data.length
        }
    };

    const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
            responseData += chunk;
        });

        res.on('end', () => {
            console.log('Response:', responseData);
        });
    });

    req.on('error', (error) => {
        console.error('Error:', error);
    });

    // Write data to request body
    req.write(data);
    req.end();
}

export default wpSendOtp;
