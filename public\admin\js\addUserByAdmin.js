angular.module('addUserByAdmin.controllers', [])

     .controller('addUserByAdminCtrl', function($scope, $state,APIService,$stateParams,$http) {


     // 	$scope.userdata = {};
	    // // $scope.userdata.user_added_by_admin = true;
	    // $scope.userdata.role = "subAdmin";
	   $scope.userdata = {};

	    $scope.jsonData= {};
	   	$scope.displayForm= true;


	    APIService.setData({
        req_url: PrefixUrl + '/user/getCityList'  
	    }).then(function(resp) {
	      $scope.cityList= resp.data;
	    },function(resp) {
	      // This block execute in case of error.
	       // $scope.logout = function() {
	      localStorage.removeItem("UserDeatails");
	      localStorage.removeItem("token");
	      $state.go('login');
	      console.log('error')
	    });

	    var userData=localStorage.getItem('UserDeatails');
	    var parsedUser= JSON.parse(userData);
	    // console.log(parsedUser.user_details)
	    if (parsedUser == null || parsedUser.user_details.role != 'admin') {
	      localStorage.removeItem("UserDeatails");
	      localStorage.removeItem("token");
	      $state.go('login');
	    }

	    $scope.registerUser = function(){
	    	$scope.displayForm= true;
	    	$scope.displayFilledForm= false;


	    	// console.log('roll')
	    	// console.log($scope.userRole)
		    // $scope.userdata.user_added_by_admin = true;
		    
		    $scope.userdata.role = $scope.userRole;
	    	$scope.userdata.district=  angular.uppercase($scope.userdata.district);
	        if ($scope.userRole == 'referalUser') {
		        if( $scope.userdata.name &&
		        	  $scope.userdata.password &&
		        	   $scope.userdata.phone_number &&
		        	   $scope.userdata.email &&
		        	   $scope.userdata.address &&
		        	   $scope.userdata.district &&
		        	   $scope.userdata.state 
		        	   // &&
		        	   // $scope.userdata.bankDetails.payeename &&
		        	   // $scope.userdata.bankDetails.bankacno &&
		        	   // $scope.userdata.bankDetails.bankifsc &&
		        	   // $scope.userdata.bankDetails.bankname 
		        	   ){


		     			$scope.showConfirm();
		     			// $scope.registerUserMethod();
		        }else{
		            alert("Please fill all the fields.")
		        }
		    }else if ($scope.userRole == 'subAdmin') {
		     			$scope.showConfirm();

		     			// $scope.registerUserMethod();
		    }
		    else{
		            alert("Please fill all the fields.")

		    }

    }
     $scope.searchDistrict= function(city){
      console.log('city',city);
      if (city.length > 2) {

           $http.get('https://contabo.triva.in/api?q='+city+'&limit=5&osm_tag=place&osm_tag=!place:county&osm_tag=!place:state&osm_tag=!place:postcode').then(function (data) { 
                
             $scope.cityList= data.data.features;
 					console.log('city---', data.data.features);

                  // alert("success",status); 
              },function (data) { 
                  // alert("error"); 
              });
       }
   }
 $scope.selectDistrict = function(city) {
      // $scope.cityList= city;
      
      $scope.city= city.geometry.coordinates;
      console.log('aaaaa', $scope.city);
      $scope.selectedCity= city;
      $scope.userdata.district =  city.properties.name;
       $scope.cityList= [];
      var obj={coordinates:[]};
      obj.coordinates= city.geometry.coordinates;
      // obj.type= city.geometry.type;
      $scope.userdata.location = obj;
      console.log('user data', $scope.userdata);

    }
    
	    $scope.showConfirm = function(){
	    	$scope.displayFilledForm= true;
	    	$scope.displayForm= false;

	  //   	$scope.	= $scope.userdata.name;
			// $scope.	= $scope.userdata.buisness_name;
			// $scope.	= $scope.userdata.password;
			// $scope.	= $scope.userdata.phone_number;
			// $scope.	= $scope.userdata.email;
			// $scope.	= $scope.userdata.address;
			// $scope.	= $scope.userdata.district;
			// $scope.	= $scope.userdata.state;
			// $scope.	= $scope.userdata.bankDetails.payeename;
			// $scope.	= $scope.userdata.bankDetails.bankacno;
			// $scope.	= $scope.userdata.bankDetails.bankifsc;
			// $scope.	= $scope.userdata.bankDetails.bankname;
		}

	    $scope.confirmUser = function(){
		    $scope.registerUserMethod();

		}

   $scope.getUserState = function(){
            console.log("hhh",$scope.getUserState)
            APIService.getData({ req_url: PrefixUrl + "/States/getStates"}).then(function (res) {
                console.log(res);
                 console.log("====re======",res);
                $scope.states= res.data;
                // $scope.getALLCat=res.data;
    
             
            
            },function(er){
               
            })
    
        }
        $scope.getUserState();

	    $scope.registerUserMethod = function(){

           APIService.getData({ req_url: PrefixUrl + "/user/checkMobile/"+$scope.userdata.phone_number})
	        .then(function (res1) {
	            console.log(res1)
	            if (res1.data.message) {
	                console.log('mobile verified')
	                 APIService.getData({ req_url: PrefixUrl + "/user/checkEmail/"+$scope.userdata.email})
	                .then(function (res2) {
	                    if (res1.data.message) {
	                        console.log('email verified')
	                        $scope.userdata.created_at= new Date();
	                        $scope.userdata.active_status= true;

			                 APIService.setData({ req_url: PrefixUrl + "/user/registerUserByAdmin/",data:$scope.userdata})
			                .then(function (res3) {
			                	console.log('reeeeeeeeeeeeeee',res3)
			                	alert("user is added Successfully")
			                	    if ($scope.userRole == 'referalUser') {
						              $state.go("app.referralUsers");
						            }else{
						              $state.go("app.UserDetails");
						            }
			                },function(er){

			                })



	              
	                      // APIService.setData({ req_url: PrefixUrl + "/user/otp",data:$scope.userdata})
	                      //   .then(function (res4) {
	                      //       if (res4.data.success) {
	                      //           console.log("otp"+res4.data.message)

	                      //           $scope.jsonData.otp= res4.data.message;
	                      //           $scope.jsonData.mobile= $scope.userdata;
	                      //           $state.go("app.otp",{data:JSON.stringify($scope.jsonData)});
	                                
	                      //       }
	                        
	                      //   },function(er){
	         
	                      //   })
	                    }
	                    
	                },function(er){
	         
	                })
	            }else{
                            alert("Phone Number Already Registered")
	    					$scope.displayForm= true;
	    					$scope.displayFilledForm= false;

                        }
	            // $scope.userDetails=res.data;
	            // $scope.userdata ={};
	            // alert("User is registered Successfully.")
	            // localStorage.setItem('UserDeatails', JSON.stringify(res.data));
	            // $state.go("app.UserDetails");
	        
	        },function(er){
	         
	        })
	    }




	    $scope.checkPhoneNumber= function(phone_number){
	    	console.log('ppppppppp',(''+phone_number).length)
	    	if ((''+phone_number).length < 10 || (''+phone_number).length > 10) {
	    		$scope.isValid= false;
	    	}else{
	    		$scope.isValid= true;

	    	}
	    }

	    $scope.checkPassword= function(password){
	    	console.log('aaa',(''+password).length)
	    	if ((''+password).length < 6) {
	    		$scope.passwordIsValid= false;
	    	}else{
	    		$scope.passwordIsValid= true;

	    	}
	    }
	        
     
 })
