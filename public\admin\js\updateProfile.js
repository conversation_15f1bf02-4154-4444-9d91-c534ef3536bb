angular.module('updateProfile.controllers', [])

     .controller('updateProfileCtrl', function($scope, $state,APIService,$stateParams,$http) {

     	 $scope.page = 'main';

        $scope.jsonData= {};

     $scope.usersDetails = {};
     $scope.userData = {};
     $scope.expire;
     $scope.cityList = [];
      $scope.searchDistrictFlag= true;

     $scope.userData1= JSON.parse($stateParams.data);
     console.log($scope.userData1);

    // $scope.userDetails = JSON.parse(localStorage.getItem("UserDeatails"));

    // if ($scope.userDetails.user_details) {
    // 	$scope.userData= $scope.userDetails.user_details; 
    // }else{
    // 	$scope.userData= $scope.userDetails; 

    // }
    // console.log(  $scope.userData)


        

        APIService.getData({
            req_url: PrefixUrl + '/user/'+$scope.userData1._id
        }).then(function(resp) {
        $scope.userData= resp.data;
            //district uppercase
            $scope.userData[0].district= angular.uppercase($scope.userData[0].district);
           },function(resp) {
              // This block execute in case of error.
        });


        $scope.updateUserTransaction = function (user) {
            APIService.setData({ req_url: PrefixUrl + "/trancsaction/updateUserTransaction/",data:$scope.userData[0]}).then(function (res) {
            })

        }
     $scope.getUserState = function(){
            console.log("hhh",$scope.getUserState)
            APIService.getData({ req_url: PrefixUrl + "/States/getStates"}).then(function (res) {
                console.log(res);
                 console.log("====re======",res);
                $scope.states= res.data;
                // $scope.getALLCat=res.data;
    
             
            
            },function(er){
               
            })
    
        }

$scope.getUserState();

        $scope.updateUser = function (user) {
            if (confirm("Are you sure?")) {
              
                  APIService.setData({ req_url: PrefixUrl + "/user/checkEmailForUser/", data:{email:$scope.userData[0].email,user_id:$scope.userData[0]._id}}).then(function (res) {
                    var local_data = res.data;
                    if(typeof local_data.name == 'undefined' && local_data.message){
                      
          	        	  APIService.updateData({ req_url: PrefixUrl + "/user/update/"+$scope.userData[0]._id,data:$scope.userData[0]}).then(function (res) {
          	                console.log('res.data')
          	                console.log(res.data)
          	                console.log('res.data')
                      		// $state.go("app.users")

                              $scope.updateUserTransaction();

                              if ($scope.userData[0].role == 'normalUser') {
                                  $state.go("app.UserDetails")

                              }else if ($scope.userData[0].role == 'referalUser') {
                                  $state.go("app.referralUsers")

                              }
                              // localStorage.setItem('UserDeatails', JSON.stringify(res.data));
                            	// location.reload(); 
          	    			
          	               
          	                alert("User is Updated Successfully.")
          	            
          	            },function(er){
          	             
          	            })
                  }else{
                      alert('Email already registered!')
                    }
                  },function(er){

                  })
        	}
        }


        $scope.searchDistrict= function(district){
          $scope.searchDistrictFlag= false;
       console.log('aaa',(''+district).length)
      // if (district.length > 3) {
        if ((''+district).length >3) {

          APIService.setData({ req_url: PrefixUrl + "/user/contaboSearch/", data:{district:$scope.userData[0].district}}).then(function (res) {
             var contaboData= JSON.parse(res.data.body);
             console.log('contaboserver ',contaboData)
             $scope.cityList= contaboData.features;

          },function(er){

          })


           // $http.get('http://vmi282694.contaboserver.net:2322/api?q='+$scope.userData[0].district+'&limit=5&osm_tag=place&osm_tag=!place:county&osm_tag=!place:state&osm_tag=!place:postcode').then(function (data) { 
                
           //   $scope.cityList= data.data.features;
           //   console.log('district---', data.data.features);

           //        // alert("success",status); 
           //    },function (data) { 
           //        // alert("error"); 
           //    });
       }
         }
       $scope.selectDistrict = function(district) {
            // $scope.cityList= city;
            $scope.searchDistrictFlag= true;
            $scope.district= district.geometry.coordinates;
            console.log('aaaaa', $scope.district);
            $scope.selectedCity= district;
            $scope.userData[0].district =  district.properties.name;
             $scope.cityList= [];
            var obj={coordinates:[]};
            obj.coordinates= city.geometry.coordinates;
            // obj.type= city.geometry.type;
            $scope.userdata[0].location = obj;
            console.log('user data', $scope.userdata);

          }


        $scope.updateExpiry = function (user) {
            if (confirm("Are you sure?")) {

              APIService.updateData({ req_url: PrefixUrl + "/user/updateUserExpiry/"+$scope.userData[0]._id,data:{'expire':$scope.expire,renewal_date:$scope.userData[0].renewal_date}}).then(function (res) {
                  console.log('res.data')
                  console.log(res.data)
                  console.log('res.data')   

                    APIService.setData({ req_url: PrefixUrl + "/extenddaylog/createLog/",data:{renewal_date:$scope.expire,state:null,user_id: $scope.userData[0]._id,created_at:new Date()}}).then(function (res) {
                        $scope.updatedata ={};
                        $scope.showUpdate= true;
                        alert("User is Updated Successfully.")
                        $state.go("app.UserDetails")

                    },function(er){

                    })
                  
              },function(er){
               
              })
          }
        }

});