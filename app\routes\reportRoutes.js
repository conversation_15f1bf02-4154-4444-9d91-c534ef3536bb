import express from 'express';
import ReportController from '../controllers/reportController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initreportRoutes = () => {
  const reportRoutes = express.Router();

  reportRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.pageNew);
  reportRoutes.get('/reportedBy/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.reportedBy);
  reportRoutes.get('/pageProcessed/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.pageProcessed);
  reportRoutes.get('/:user_id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON><PERSON><PERSON> ,  ReportController.show);
  reportRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON>and<PERSON> ,  ReportController.create);
  reportRoutes.put('/update',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.update);
  // reportRoutes.delete('/id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.remove);
  reportRoutes.post('/filterReports',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.filterReports);
  reportRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.updateReport);
  reportRoutes.delete('/removeReport/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, ReportController.removeReport);

  reportRoutes.put('/hideReport/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ReportController.hideReport);

  // reportRoutes.get('/', ReportController.page);
  // reportRoutes.get('/:user_id', ReportController.show);
  // reportRoutes.post('/', ReportController.create);
  // reportRoutes.put('/update', ReportController.update);
  // reportRoutes.delete('/id', ReportController.remove);

  return reportRoutes;
};

export default initreportRoutes;
