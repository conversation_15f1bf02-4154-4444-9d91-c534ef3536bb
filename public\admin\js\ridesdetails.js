angular.module('ridesdetails.controllers', [])

    .controller('RidesdetailsCtrl', function ($scope, $state, $stateParams, $uibModal, firebaseservices, ngDialog, NgMap, mapservices, firebaseservices) {
    //$scope.product = $stateParams.productObj;
    //if(!$scope.product) {
    //    $state.go('app.product');
    //}
    //$scope.myInterval = 3000;
    //$scope.active_slide = 0;
    $scope.task = JSON.parse($stateParams.data);
    console.log(  $scope.task)
    var myLat;
    var myLng;
    console.log($scope.task)
    // firebaseservices.getDataFromNodeValue('Users/' + $scope.task.UserAuthorInfo.OwnerId).then(function (res) {
    //     console.log(res)
    //     $scope.myDetails = res;
    // }, function (er) {

    // })
    mapservices.getLatLong().then(function (res) {
        myLat = res.coords.latitude;
        myLng = res.coords.longitude;
        console.log(myLat);console.log(myLng)
        $scope.distanceCalculation = mapservices.distanceBetweenTwoLatLong(myLat, myLng, $scope.task.Latitude, $scope.task.Longitude, 'mi');
        console.log($scope.task)

    }, function (er) { })
    

   // if ($scope.task) {
        //NgMap.getMap().then(function (map) {
        //    console.log(map.getCenter([22.719569, 75.857726]));
        //    console.log('markers', map.markers);
        //    console.log('shapes', map.shapes);
        //});
   // }
   // }
    
    $scope.evalueateTag = function (value) {
        //  console.log(value);
        console.log(value);
        if (!angular.isUndefined(value)) {

            return value.split(',');
        }
    }
    $scope.open = function (size, parentSelector, config) {
        var parentElem = parentSelector ?
            angular.element($document[0].querySelector('.modal-demo ' + parentSelector)) : undefined;
        $scope.modalInstance = $uibModal.open({
            animation: $scope.animationsEnabled,
            ariaLabelledBy: 'modal-title',
            ariaDescribedBy: 'modal-body',
            templateUrl: 'myModalContent.html',
            controller: 'ModalInstanceCtrl',
            controllerAs: '$ctrl',
            size: size,
            appendTo: parentElem,
            resolve: {
                items: function () {
                    return config;
                }
            }
        });


    }
    $scope.deleteTask = function (task) {
        $scope.open('', '', { title: { titleText: 'Alert', titleColor: 'Red' }, modalBody: { bodyText: 'Do You Want Delete?', bodyColor: 'Red' } })
        setTimeout(function () {

            $scope.modalInstance.result.then(function () {
                firebaseservices.removeData('Tasks/' + task.key).then(function (res) {
                    // $scope.Tasks.splice(index, 1);
                    firebaseservices.removeData('taskLocation/' + task.key).then(function (res) {

                    $state.go('app.product')
                    });
                })

            }, function () {
                // alert('cancel')
                //  $log.info('Modal dismissed at: ' + new Date());
            });
        }, 1000)

    }
    $scope.updateData = function (task) {
        $state.go('app.updateProduct', { data: JSON.stringify(task), page: 'app.ridesdetails'  });
    }
    $scope.deleteProduct = function (product) {

      var modalInstance = $uibModal.open({
          animation: $scope.animationsEnabled,
          templateUrl: 'partials/deleteConfirmation.html',
          controller: 'DeleteConfirmationCtrl',
          size: 'md',
          resolve: {
              product: function () {
                  return product;
              },
              url: function () {
                  return url_prifix + 'api/deleteProduct';
              }
          }
      });
      modalInstance.result.then(function (productList) {
          ngDialog.open({ template: 'partials/deletePopup.html', className: 'ngdialog-theme-default' });
          $state.go('app.product');
      }, function () {
        $log.info('Modal dismissed at: ' + new Date());
      });
    };
})

.controller('DeleteConfirmationCtrl', function ($scope, $rootScope, $uibModalInstance, APIService, product, url){
    $scope.delete = function () {
        APIService.removeData({
            req_url: url,
            data: product
        }).then(function(resp) {
            $uibModalInstance.close(resp.data);
            
           },function(resp) {
              // This block execute in case of error.
        });
    }
    $scope.cancel = function () {
        $uibModalInstance.dismiss('cancel');
    };
});