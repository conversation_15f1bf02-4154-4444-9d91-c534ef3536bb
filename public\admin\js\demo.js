// Code goes here

angular.module('ui.tinymce')
.controller('DemoCtrl',['$scope', '$sce', '$timeout', function($scope,$sce,$timeout) {
    var ctrl = this;
    $scope.tinyMceOptions = {
                        setup: function(editor) {
                            console.log('test1');
                            $timeout(function () {
                                console.log('test2');
                                editor.focus();
                            }, 200)
                        },
                        plugins: ["image", "imagetools"],
                        statusbar: false,
                        menubar: false,
                        resize: false,
                        toolbar: 'formatselect | bold italic underline | bullist numlist | undo redo | image'

                    };
    this.updateHtml = function() {
      ctrl.tinymceHtml = $sce.trustAsHtml(ctrl.tinymce);
    };
  }]);