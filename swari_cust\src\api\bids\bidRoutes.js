/**
 * Bid Routes
 * Defines API routes for bid submission, retrieval, and acceptance
 * PRD Reference: Sections 4.3, 10.2
 */

import{Router} from 'express';
import { check } from 'express-validator';
import { verifyToken, isDriver, isCustomer } from '../auth/authMiddleware.js';
import { submitBid, getBidsForTrip, acceptBid , rejectBid } from './bidController.js';

const router = Router();

/**
 * @route   POST /api/trips/:id/bids
 * @desc    Submit a bid for a trip
 * @access  Private (Driver only)
 */
router.post('/trips/:id/bids', [
  verifyToken,
  
  check('amount', 'Amount is required and must be a positive number').isFloat({ min: 0 }),
  check('notes').optional().trim()
], submitBid);

/**
 * @route   GET /api/trips/:id/bids
 * @desc    Get all bids for a trip
 * @access  Private (Customer, Driver)
 */
router.get('/trips/:id/bids', verifyToken, getBidsForTrip);

/**
 * @route   POST /api/bids/accept
 * @desc    Accept a bid
 * @access  Private (Customer only)
 */
router.post('/bids/accept', [
  verifyToken,
  isCustomer, // Add this middleware
  check('bidId', 'Bid ID is required').notEmpty()
], acceptBid);

/**
 * @route   POST /api/bids/reject
 * @desc    Reject a bid
 * @access  Private (Customer only)
 */
router.post('/bids/reject', [
  verifyToken,
  isCustomer,
  check('bidId', 'Bid ID is required').notEmpty()
], rejectBid);

export default router;