{"name": "angularfire", "description": "The officially supported AngularJS binding for Firebase", "version": "2.3.0", "authors": ["Firebase (https://firebase.google.com/)"], "homepage": "https://github.com/firebase/angularfire", "repository": {"type": "git", "url": "https://github.com/firebase/angularfire.git"}, "license": "MIT", "keywords": ["angular", "<PERSON><PERSON>s", "firebase", "realtime"], "main": "dist/angularfire.js", "ignore": ["**/*", "!dist/*.js", "!README.md", "!LICENSE"], "dependencies": {"angular": "^1.3.0", "firebase": "3.x.x"}, "_release": "2.3.0", "_resolution": {"type": "version", "tag": "v2.3.0", "commit": "8678f727648386019bc6fcaaadb7599ae064ca7b"}, "_source": "https://github.com/firebase/angularFire.git", "_target": "^2.3.0", "_originalSource": "angularfire", "_direct": true}