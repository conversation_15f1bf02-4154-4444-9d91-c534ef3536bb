angular.module('payToBankRemarks.controllers', [])

    .controller('payToBankRemarksCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.page = 'main';
    	$scope.adminRemarks;
    	var dataJson;
    	$scope.updatedata ={};

    	   APIService.setData({
		        req_url: PrefixUrl + '/user/getCityList'  
		    }).then(function(resp) {
		      $scope.cityList= resp.data;
		    },function(resp) {
		      // This block execute in case of error.
		       // $scope.logout = function() {
		      localStorage.removeItem("UserDeatails");
		      localStorage.removeItem("token");
		      $state.go('login');
		      console.log('error')
		    });


        if($stateParams.data){

            console.log(JSON.parse($stateParams.data))

            dataJson=JSON.parse($stateParams.data);

		  

        }


    	$scope.payMoneyToBank = function(id,walletData) {
    		    console.log('remarks')
		      console.log(dataJson)
		        APIService.updateData({
		            req_url: PrefixUrl + '/withdrawWallet/update/'+ dataJson[0].id,data:{status:'paid to bank',remarks:$scope.adminRemarks,remittDate:true}
		        }).then(function(resp) {
		          $scope.updatedata.user_id= dataJson[1].walletData.user_id;
		          $scope.updatedata.transaction_date=new Date();
		          $scope.updatedata.transaction_id=Number(String(Math.random()).slice(2)) + (Date.now() + Math.round( Date.now())).toString(36);
		          $scope.updatedata.transaction_reason= dataJson[1].walletData.reason;
		          $scope.updatedata.transaction_type=dataJson[1].walletData.transaction_type;
		          $scope.updatedata.amount= parseFloat(dataJson[1].walletData.amount).toFixed(2);
		          $scope.updatedata.transaction_reason=$scope.adminRemarks;

		              APIService.setData({

		                  req_url: PrefixUrl + '/trancsaction/',data:$scope.updatedata
		              }).then(function(resp1) {
		                  //update user wallet
		                    // APIService.updateData({

		                        // req_url: PrefixUrl + '/user/update/'+ dataJson[1].walletData.user_id,data:{wallet_balance: dataJson[1].walletData.userDetails.wallet_balance -  dataJson[1].walletData.amount}
		                    // }).then(function(resp) {
		                      // alert('money transfered to the bank')
		                      // location.reload(); 
		                      // console.log('reeeeeeeeeeeeeeeeeee')
		                      // console.log(resp.data)
		                      // $scope.email_id= resp.data.email;

		                        APIService.setData({
						            req_url: PrefixUrl + '/trancsaction/getTransactionBalance/',
						            data: {user_id: resp1.data.user_id,transaction_id:resp1.data.transaction_id}
						        }).then(function(resp2) {
						          console.log('resp2')
						          console.log(resp2.data[0].balance)
						            APIService.setData({
						              req_url: PrefixUrl + '/trancsaction/updateTransaction/',
						              data: {balance: parseFloat(resp2.data[0].balance).toFixed(2),transaction_id:resp1.data.transaction_id}
						            }).then(function(resp3) {
						            	console.log('rrrrrrrrrrrrrrrrrrrrrrrrrrrr')
						            	console.log(resp3)
		                    			
		                    			APIService.updateData({

						            	req_url: PrefixUrl + '/user/update/'+ dataJson[1].walletData.user_id,data:{wallet_balance: parseFloat(resp2.data[0].balance).toFixed(2)}
		                    			}).then(function(resp4) {
		                      				$scope.email_id= resp4.data.email;

        										$scope.senMail(resp4.data,resp2.data[0].balance,resp1.data.transaction_id);
		                    			
		                    				},function(resp) {
		                    			});

						           },function(resp) {

						            // This block execute in case of error.

						            });

						            

						           },function(resp) {
						            // This block execute in case of error.
						        });
		                      
		                       // },function(resp) {
		                    // });
		                alert('money transfered to the bank')
		          		$state.go('app.withdrawWallet');

		                // location.reload(); 
		                
		                 },function(resp) {
		              });
		          // $state.go('app.withdrawWallet');

		          
		           },function(resp) {
		        });
    	};




    $scope.senMail = function(user,balance,transaction_id) {
     
    	console.log('user------')
    	console.log(user)
    	// console.log(trancsaction.transaction_id)
          // var obj= {};
          // obj.email_id = $scope.email_id;
          // obj.transaction_id = trancsaction.transaction_id ;
          // obj.transaction_amount =trancsaction.amount;
          // obj.emailBody =$scope.emailBody;

            APIService.setData({
                req_url: PrefixUrl + '/withdrawWallet/sendEmailForWithdrawWallet/' , data: {user_id:user._id,amount:$scope.updatedata.amount,balance: balance,transaction_id:transaction_id,date: new Date(), account_no: (user.bankDetails.hiddenAccountNo).substring(user.bankDetails.hiddenAccountNo.length - 3, user.bankDetails.hiddenAccountNo.length)}
            }).then(function(resp) {
              // console.log("====resp======",resp);
            // $scope.userDetails=resp.data
            console.log('resp')
            console.log(resp)
            if (resp.data.message=='success') {
                  
              alert("Email sent successfully");

            }
               },function(resp) {
                  // This block execute in case of error.
            });


    };



})
