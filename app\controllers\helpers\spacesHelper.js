
import { PutO<PERSON>Command, GetObjectCommand, GetObjectAclCommand, S3Client } from '@aws-sdk/client-s3';
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
import mongoose from 'mongoose';
const url = require('url');
var ObjectId = mongoose.Types.ObjectId;

const baseSpaces = "https://swaritest.sgp1.digitaloceanspaces.com";
// Step 2: The s3Client function validates your request and directs it to your Space's specified endpoint using the AWS SDK.
const s3Client = new S3Client({
    endpoint: "https://sgp1.digitaloceanspaces.com", // Find your endpoint in the control panel, under Settings. Prepend "https://".
    forcePathStyle: false,
    region: "sgp1", // Must be "us-east-1" when creating new Spaces. Otherwise, use the region in your endpoint (e.g. nyc3).
    credentials: {
      accessKeyId: "DO00DDZBQ27VM24JGC8W", // Access key pair. You can create access key pairs using the control panel or API.
      secretAccessKey: "zF2lMVNnNkZYL9M6pfdcyqZPe3Hw5wSgzMc3yCFx96E" // Secret access key defined through an environment variable.
    }
});

const getFileSignedUrl = async function createSignedUrl(docs) {
    // const command = new GetObjectCommand({
    //     Bucket: 'swaritest',
    //     Key: objectKey
    // });

    

    try {

        let doc_links = []
        for(let dc of docs) {

            if(dc && dc.url) {                
                const parsedUrl = url.parse(dc.url);
                const pathname = parsedUrl.pathname;
                const [bucketName, ...objectKeyParts] = pathname.split('/').filter(part => part); // filter removes empty parts
                const objectKey = objectKeyParts.join('/');

                console.log({bucketName})
                console.log({objectKey})
                
                const command = new GetObjectCommand({
                    Bucket: 'swaritest',
                    Key: 'user-docs/'+objectKey, //'user-docs/9988776655/file-1718110224186.jpg'
                });
                
                const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
                console.log("Signed URL:", signedUrl);
                doc_links.push({
                    _id: dc._id,
                    url: signedUrl,
                    type: dc.type
                });
            }
            // return signedUrl;
        }

        console.log(" doc_links ",doc_links)
        return doc_links;
    } catch (error) {
        console.error("Error creating signed URL:", error);
    }
}

export default getFileSignedUrl;