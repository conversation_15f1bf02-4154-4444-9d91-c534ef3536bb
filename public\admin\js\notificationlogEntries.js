angular.module('notificationlogEntries.controllers', [])

    .controller('notificationlogEntriesCtrl', function ($scope,APIService, $state,$stateParams) {

    	$scope.pageLimit;
      $scope.filterByDate= false;;
      $scope.filterSearch= false;

    	$scope.settings = {
	      currentPage: 0,
	      offset: 0,
	      pageLimit: 10,
	      pageLimits: [2, 5, 10,20,100]
	    };


 

 



    

        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }



    $scope.getLogsForSelectedDate = function(startDate,endDate) {

      $scope.filterSearch =true;

      $scope.filterByDate= true;
      $scope.checkPagination(startDate,endDate);
      $scope.logEntriesFilterCount();




    };


    $scope.checkPagination = function(startDate,endDate) {
      console.log('$scope.filterByDate'+$scope.filterByDate)

          $scope.obj= {};

          if (startDate) {
              $scope.obj.startDate = startDate; 
          }else{
              $scope.obj.startDate = null; 

          }

          if (endDate) {
              $scope.obj.endDate = endDate; 
          }else{
              $scope.obj.endDate = null; 
          }

          if ($scope.email) {
              $scope.obj.email= $scope.email;
          }else{
              $scope.obj.email= null;
          }

          if ($scope.name) {
              $scope.obj.name= $scope.name;
          }else{
              $scope.obj.name= null;
          }

          if ($scope.phone_number) {
              $scope.obj.phone_number= $scope.phone_number;
          }else{
              $scope.obj.phone_number= null;
          }


          

       // if ($scope.filterByDate) {



      $scope.$watch('settings.pageLimit', function (pageLimit) {
          console.log('pageLimits'+pageLimit)
          $scope.pageLimit= pageLimit;
         

          if($scope.filterSearch){
            APIService.setData({
            req_url: PrefixUrl + '/notificationLog/getLogsForSelectedDate' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj:$scope.obj}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.logEntries=resp.data;
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
              console.log('error')
            });
          }else{
            APIService.setData({
            req_url: PrefixUrl + '/notificationLog/show' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj:$scope.obj}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.logEntries=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
              console.log('error')
            });
          }
        }
        );


        $scope.$watch('settings.currentPage', function (value) {
          console.log('currentPage'+$scope.settings.currentPage)  
          // var obj= {};
          // $scope.obj.startDate = startDate; 
          // $scope.obj.enddate = enddate; 
          if($scope.filterSearch){
            APIService.setData({
            req_url: PrefixUrl + '/notificationLog/getLogsForSelectedDate' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj:$scope.obj}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.logEntries=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
              console.log('error')
            });
          }else{
            APIService.setData({
            req_url: PrefixUrl + '/notificationLog/show' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj:$scope.obj}
            }).then(function(resp) {
              console.log("====respPagination======",resp);
              $scope.logEntries=resp.data
            // $scope.userDetailsLength= $scope.userDetails.length;
               },function(resp) {
                  // This block execute in case of error.
              console.log('error')
            });
          }
        }
        );

  // }
  // else{

  //     $scope.$watch('settings.pageLimit', function (pageLimit) {
  //       console.log('pageLimits'+pageLimit)
  //       $scope.pageLimit= pageLimit;
  //         if($scope.filterSearch){
  //           APIService.setData({
  //           req_url: PrefixUrl + '/notificationLog/getLogsForSelectedDate' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj}
  //           }).then(function(resp) {
  //             console.log("====respPagination======",resp);
  //             $scope.logEntries=resp.data
  //           // $scope.userDetailsLength= $scope.userDetails.length;
  //              },function(resp) {
  //                 // This block execute in case of error.
  //             console.log('error')
  //           });
  //         }else{
  //           APIService.setData({
  //           req_url: PrefixUrl + '/notificationLog/show' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj}
  //           }).then(function(resp) {
  //             console.log("====respPagination======",resp);
  //             $scope.logEntries=resp.data
  //           // $scope.userDetailsLength= $scope.userDetails.length;
  //              },function(resp) {
  //                 // This block execute in case of error.
  //             console.log('error')
  //           });
  //         }
  //     }
  //     );


  //     $scope.$watch('settings.currentPage', function (value) {
  //       console.log('currentPage'+$scope.settings.currentPage)  
  //       // console.log('userDetailslll='+$scope.userDetails.length)
  //         if($scope.filterSearch){
  //           APIService.setData({
  //           req_url: PrefixUrl + '/notificationLog/getLogsForSelectedDate' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj}
  //           }).then(function(resp) {
  //             console.log("====respPagination======",resp);
  //             $scope.logEntries=resp.data
  //           // $scope.userDetailsLength= $scope.userDetails.length;
  //              },function(resp) {
  //                 // This block execute in case of error.
  //             console.log('error')
  //           });
  //         }else{
  //           APIService.setData({
  //           req_url: PrefixUrl + '/notificationLog/show' , data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj}
  //           }).then(function(resp) {
  //             console.log("====respPagination======",resp);
  //             $scope.logEntries=resp.data
  //           // $scope.userDetailsLength= $scope.userDetails.length;
  //              },function(resp) {
  //                 // This block execute in case of error.
  //             console.log('error')
  //           });
  //         }
  //     }
  //     );

  // }

    };

    $scope.logEntriesFilterCountForFirstTime= function(){
      APIService.setData({
        req_url: PrefixUrl + '/notificationLog/logEntriesFilterCountForFirstTime'  
      }).then(function(resp) {
        $scope.logEntriesLength= resp.data;
        $scope.checkPagination();

      },function(resp) {
         localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
      });
    }

    $scope.logEntriesFilterCount= function(){
      APIService.setData({
        req_url: PrefixUrl + '/notificationLog/getCount', data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,obj:$scope.obj}  
      }).then(function(resp) {
        $scope.logEntriesLength= resp.data[0].myCount;
      // $scope.checkPagination();

      },function(resp) {
         localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
      });
    }

    $scope.logEntriesFilterCountForFirstTime();


    $scope.findUserBusiness = function(user) {
     
      console.log(user)
      $state.go('app.userBusinessProfile',{data:JSON.stringify(user)});
    }



    	// APIService.getData({
     //    req_url: PrefixUrl + '/notificationLog/'  
	    // }).then(function(resp) {
	    // 	$scope.logEntries= resp.data;
	    // },function(resp) {
	    //   localStorage.removeItem("UserDeatails");
	    //   localStorage.removeItem("token");
	    //   $state.go('login');
	    //   console.log('error')
	    // });

    });