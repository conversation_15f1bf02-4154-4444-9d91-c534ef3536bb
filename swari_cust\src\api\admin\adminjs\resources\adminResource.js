/**
 * Admin Resource Configuration
 * Defines the AdminJS resource for Admin model
 * PRD Reference: Sections 4.11, 10.2
 */

import AdminJS from 'adminjs';
import Admin from '../../../../models/Admin.js';
import Log from '../../../../models/Log.js';
import logger from '../../../../utils/logger.js';

/**
 * Admin resource configuration
 */
export default {
  resource: Admin,
  options: {
    navigation: {
      name: 'System',
      icon: 'Settings',
    },
    listProperties: ['username', 'email', 'role', 'last_login', 'login_attempts', 'account_locked'],
    filterProperties: ['username', 'email', 'role', 'last_login', 'account_locked'],
    editProperties: ['username', 'email', 'role', 'mfa_enabled', 'account_locked'],
    showProperties: ['username', 'email', 'role', 'mfa_enabled', 'last_login', 'login_attempts', 'account_locked'],
    actions: {
      // Log access action
      logAccess: {
        actionType: 'resource',
        handler: async (request, response, context) => {
          const { currentAdmin } = context;
          const { component } = request.payload;
          
          try {
            // Create a log entry
            const log = new Log({
              event_type: 'admin_action',
              user_id: currentAdmin._id,
              user_type: 'Admin',
              details: { action: 'access', component },
              ip_address: request.ip
            });
            
            await log.save();
            
            return { success: true };
          } catch (error) {
            logger.error('Error logging access', { error: error.message });
            return { success: false, error: error.message };
          }
        },
      },
    },
  },
};