angular.module('offerCategory.controllers', [])

    .controller('offerCategoryCtrl', function ($scope,APIService, $state,$stateParams) {

        $scope.offerObj={};
      

          $scope.getOfferCategory= function(){
	        console.log('offerObj -- ',$scope.offerObj)
	      	APIService.setData({
	          req_url: PrefixUrl + '/offerCategory/getOfferCategory' ,data:{} 
	      	}).then(function(resp) {
	            $scope.offerCategories=resp.data
	             },function(resp) {
	                // This block execute in case of error.
	         });

	      }

	      $scope.getOfferCategory();

	      $scope.addOffer= function(files){
	        console.log('offerObj -- ',$scope.offerObj)
	      	APIService.setData({
	          req_url: PrefixUrl + '/offerCategory/create' ,data:$scope.offerObj 
	      	}).then(function(resp) {
					$scope.offerObj={};
					alert('Offer Category added successfully')
				     location.reload(); 

	      		$scope.getOfferCategory();

	             },function(resp) {
	                // This block execute in case of error.
	         });

	      }


    });