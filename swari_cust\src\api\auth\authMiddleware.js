/**
 * Authentication Middleware
 * Handles JWT verification and role-based access control
 * PRD Reference: Sections 4.1, 10.2
 */

import jwt from 'jsonwebtoken';
import config from '../../config/config.js';
import logger from '../../utils/logger.js';
import { validationResult } from "express-validator";

/**
 * Middleware to verify JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const verifyToken = (req, res, next) => {
 
  // Get token from header

  // const token = req?.header && req.headers['x-auth-token'];
   // get token from header for authorization bearer token
   const token = req?.headers?.authorization?.startsWith('Bearer ') 
  ? req.headers.authorization.split(' ')[1] 
  : null;

  
  

  // Check if no token
  if (!token) {
    logger.warn('Authentication failed: No token provided');
    // console.trace("Trace:");
    return res.status(401).json({ message: 'No token, authorization denied' });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwtSecret);
    
    // Add user from payload to request object
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed: Invalid token', { error: error.message });
    res.status(401).json({ message: 'Token is not valid' });
  }
};
// const verifyAdmin = (allowedRoles) => {
//   return (req, res, next) => {
//     // Get token from header
//     const token = req?.header && req.header['x-auth-token'];

//     // Check if no token
//     if (!token) {
//       logger.warn('Authentication failed: No token provided');
//       return res.status(401).json({ message: 'No token, authorization denied' });
//     }

//     try {
//       // Verify token
//       const decoded = jwt.verify(token, config.jwtSecret);
      
//       // Add user from payload to request object
//       req.user = decoded;

//       // Check if the user's role is allowed
//       if (allowedRoles && !allowedRoles.includes(decoded.role)) {
//         logger.warn('Authorization failed: User role not authorized');
//         return res.status(403).json({ message: 'Access denied: Insufficient permissions' });
//       }

//       next();
//     } catch (error) {
//       logger.error('Authentication failed: Invalid token', { error: error.message });
//       res.status(401).json({ message: 'Token is not valid' });
//     }
//   };
// };

/**
 * Middleware to check if user has customer role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const isCustomer = (req, res, next) => {
  if (req.user && req.user.role === 'customer') {
    next();
  } else {
    logger.warn('Authorization failed: User not a customer', { userId: req.user?.id });
    res.status(403).json({ message: 'Access denied: Customer role required' });
  }
};

/**
 * Middleware to check if user has driver role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const isDriver = (req, res, next) => {
  if (req.user && req.user.role === 'driver') {
    next();
  } else {
    logger.warn('Authorization failed: User not a driver', { userId: req.user?.id });
    res.status(403).json({ message: 'Access denied: Driver role required' });
  }
};

/**
 * Middleware to check if user has admin role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    logger.warn('Authorization failed: User not an admin', { userId: req.user?.id });
    res.status(403).json({ message: 'Access denied: Admin role required' });
  }
};
/**
* Middleware to check if request are validated
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */

export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.error("Validation failed", {
      errors: errors.array(),
    });
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

export const getUserTrips = (req, res, next) => {
  next();
}

// export const getAllTransactions = (req, res, next) => {
//   next();
// }

// export const getAllTrips = (req, res, next) => {
//   next();
// }

// export const updateUserStatus = (req, res, next) => {
//   next();
// }

// export const getAllUsers = (req, res, next) => {
//   next();
// }

export const verifySwariNodeToken = (req, res, next) => {
  // get token from header for authorization bearer token
  const token = req?.headers?.authorization?.startsWith('Bearer ') 
    ? req.headers.authorization.split(' ')[1] 
    : null;

  // Check if no token
  if (!token) {
    logger.warn('Authentication failed: No token provided');
    return res.status(401).json({ message: 'No token, authorization denied' });
  }

  try {
    // Verify token using SwariNode secret
    const decoded = jwt.verify(token, config.jwtSwariNodeSecret);
    
    // Add user from payload to request object
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed: Invalid token', { error: error.message });
    res.status(401).json({ message: 'Token is not valid' });
  }
};


