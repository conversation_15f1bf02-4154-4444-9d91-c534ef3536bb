import express from "express";

const initUtilityRoutes = () => {
  const utilityRoutes = express.Router();

  utilityRoutes.get("/config", (req, res) => {
      const appConfig = {
        contabo: 'https://contabo.triva.in',
        youtube: {
          1: "https://www.youtube.com/watch?v=ExO4xms-ts0&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          2: "https://www.youtube.com/watch?v=lylALVmmHSM&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          3: "https://www.youtube.com/watch?v=DEOTq5sfYXE&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          4: "https://www.youtube.com/watch?v=ZGbZ_GjEUho&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          5: "https://www.youtube.com/watch?v=8eqfgepz9Ig&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          6: "https://www.youtube.com/watch?v=l7-Olxso7Wk&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
          7: "https://www.youtube.com/watch?v=OaKLdB44GXY&list=PL58OQEjqL-lAu3n8RJe5ciBEwTFPUV85k",
        }
      }
  
      return res.send(appConfig);    
  });

  return utilityRoutes;
};

export default initUtilityRoutes;
