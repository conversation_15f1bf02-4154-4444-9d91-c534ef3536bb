Trip.aggregate([ 
              {
                    $match: {
                      // $and:[
                        'user_id': req.params.id,
                        'status': 'ACTIVE',
                        'time':{$gte:new Date(new Date().toISOString().slice(0, 10))}
                        // ]
                      }
                    
              },
              { $lookup:
                 {
                   from: 'vehicles',
                   localField: 'vehical_id',
                   foreignField: '_id',
                   as: 'vehicleDetails'
                 }
               }


                    ])
    .then((trc)=>Responder.success(res,trc))
    .catch((err)=>Responder.operationFailed(res,err))
