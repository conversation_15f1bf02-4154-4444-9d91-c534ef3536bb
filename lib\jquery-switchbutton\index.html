<!DOCTYPE html>
<html>
  <head>
    <title>jQ<PERSON>y UI Switch <PERSON><PERSON> Demo - <PERSON></title>

    <link rel="stylesheet" href="jquery.switchButton.css">
    <link rel="stylesheet" href="main.css">
  </head>
  <body>
    <div class="wrapper">
      <a href="https://github.com/olance/jQuery-switchButton"><img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_white_ffffff.png" alt="Fork me on GitHub"></a>

      <h1>jQuery UI iPhone-like Switch Button Demo</h1>

      <p>
        This widget will replace the receiver element with an iPhone-style switch
        button with two states: "on" and "off". Labels of the states are customizable,
        as are their presence and position. The receiver element's "checked" attribute
        is updated according to the state of the switch, so that it can be used in
        a <code>&lt;form&gt;</code>, on a checkbox input for instance.
      </p>

      <p>
        This widget has been made to answer my specific needs, so there's room for
        improvement here and there, but it does the job for me as it is.
      </p>


      <h2>Basic usage</h2>

      <p>
        Using the following markup:
      </p>

      <pre>
&lt;input type="checkbox" value="1" checked&gt;</pre>

      <p>
        You can simply call <code>switchButton()</code> without any
        options on the checkbox element, and you'll get that:
      </p>

      <div class="demo" id="basic">
        <input type="checkbox" value="1" checked>
      </div>

      <p>
        The widget's elements are floated, so you must wrap the checkbox in its own
        <code>&lt;div&gt;</code> if you want to inline it with some
        text:
      </p>

      <pre>
It is
&lt;div class="switch-wrapper"&gt;
  &lt;input type="checkbox" value="1" checked&gt;
&lt;/div&gt;
!</pre>

      <p>
        Apply some styles to the wrapping <code>&lt;div&gt;</code> and
        everything will look perfect:
      </p>

      <pre>
.switch-wrapper {
  display: inline-block;
  position: relative;
  top: 3px;
}</pre>


      <div class="demo" id="basic2">
        It is
        <div class="switch-wrapper">
          <input type="checkbox" value="1" checked>
        </div>
        !
      </div>


      <h2>Playing with options</h2>

      <h3>Labels texts</h3>
      <p>
        The first thing you might want to do, is changing the labels texts. The
        <code>on_label</code> and <code>off_label</code>
        options allow you to do that:
      </p>

      <pre>
$("input[type=checkbox]").switchButton({
  on_label: 'yes',
  off_label: 'no'
});</pre>

      <div class="demo" id="labels">
        I want to break free:
        <div class="switch-wrapper">
          <input type="checkbox" value="1" checked>
        </div>
      </div>


      <h3>Default state</h3>
      <p>
        The widget will use the <code>checked</code> attribute of the
        receiver to determine the initial state of the switch.<br>
        However, you can force it to be on or off by specifying it in the options:
      </p>

      <pre>
// Force the checked property to false, whatever state it is in initially
$("input[type=checkbox]").switchButton({
  checked: false
});</pre>

      <div class="demo" id="default">
        <input type="checkbox" value="1" checked>
      </div>


      <h3>Other labels options</h3>
      <p>
        Maybe you'd like to display the button without any label at all:
      </p>

      <pre>
$("input[type=checkbox]").switchButton({
  show_labels: false
});</pre>

      <div class="demo" id="labels2-1">
        <input type="checkbox" value="1" checked>
      </div>

      <p>
        Or display them on one side only:
      </p>

      <pre>
$("input[type=checkbox]").switchButton({
  labels_placement: "right"
});</pre>

      <div class="demo" id="labels2-2">
        <input type="checkbox" value="1" checked>
      </div>

      <pre>
$("input[type=checkbox]").switchButton({
  labels_placement: "left"
});</pre>

      <div class="demo" id="labels2-3">
        <input type="checkbox" value="1" checked>
      </div>


      <h3>Slider options</h3>
      <p>
        You can customize the size of the slider and the sliding element:
      </p>

      <pre>
$("input[type=checkbox]").switchButton({
  width: 100,
  height: 40,
  button_width: 50
});</pre>

      <div class="slider demo" id="slider-1">
        <input type="checkbox" value="1" checked>
      </div>

      <pre>
$("input[type=checkbox]").switchButton({
  width: 100,
  height: 40,
  button_width: 70
});</pre>

      <div class="slider demo" id="slider-2">
        <input type="checkbox" value="1">
      </div>

      <p>
        Note that the <code>font-size</code> of the labels for both
        examples above have been changed in the CSS.
      </p>
    </div>

    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
    <script src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"></script>
    <script src="jquery.switchButton.js"></script>

    <script>
      $(function() {
        $('#basic.demo input').switchButton();

        $('#basic2.demo input').switchButton();

        $("#labels.demo input").switchButton({
          on_label: 'YES',
          off_label: 'NO'
        });

        $("#default.demo input").switchButton({
          checked: false
        });

        $("#labels2-1.demo input").switchButton({
          show_labels: false
        });

        $("#labels2-2.demo input").switchButton({
          labels_placement: "right"
        });

        $("#labels2-3.demo input").switchButton({
          labels_placement: "left"
        });

        $("#slider-1.demo input").switchButton({
          width: 100,
          height: 40,
          button_width: 50
        });

        $("#slider-2.demo input").switchButton({
          width: 100,
          height: 40,
          button_width: 70
        });
      })
    </script>

  </body>
</html>
