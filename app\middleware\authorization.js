var jwt = require('jsonwebtoken');
var config = require('../../config/default');

var authorization = function (req, res, next) {
    var token = req.headers['x-access-token'];    
    // console.log(token);
    var msg = {auth: false, message: 'No token provided.'};
    if (!token) res.status(500).send(msg);
    jwt.verify(token, config.jwtSecret, function (err, decoded) {
        var msg = {auth: false, message: 'Failed to authenticate token.'};
        if (err) res.status(401).send(msg);
        next();
    });
}

module.exports = authorization;