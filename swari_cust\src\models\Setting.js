/**
 * Setting Model
 * Defines the schema for system settings including wallet thresholds, cancellation limits, and bid amounts
 * PRD Reference: Section 9 - Settings and Configuration Module
 */

import mongoose from 'mongoose';
const Schema = mongoose.Schema;

// Define the schema for bid amounts by fare type
const bidAmountSchema = new Schema({
  fareType: {
    type: String,
    required: true,
    enum: ['sedan', 'suv', 'luxury', 'compact', 'minivan']
  },
  minAmount: {
    type: Number,
    required: true,
    min: 0
  },
  maxAmount: {
    type: Number,
    required: true,
    min: 0,
    validate: {
      validator: function(value) {
        return value >= this.minAmount;
      },
      message: 'Maximum amount must be greater than or equal to minimum amount'
    }
  },
  defaultAmount: {
    type: Number,
    required: true,
    min: 0,
    validate: {
      validator: function(value) {
        return value >= this.minAmount && value <= this.maxAmount;
      },
      message: 'Default amount must be between minimum and maximum amounts'
    }
  }
});

// Define the main settings schema
const settingSchema = new Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['wallet', 'cancellation', 'bidding', 'system', 'notification']
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  previousValue: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },
  bidAmounts: [bidAmountSchema],
  isActive: {
    type: Boolean,
    default: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Pre-save middleware to update the updatedAt field
settingSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create a model from the schema
const Setting = mongoose.model('Setting', settingSchema);

export default Setting;