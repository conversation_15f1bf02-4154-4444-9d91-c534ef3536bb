import express from 'express';
import NotificationLogController from '../controllers/notificationLogController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');
console.log('notificationLogController');

const initNotificationLogControllerRoutes = () => {
  const notificationLogRoutes = express.Router();
  console.log('notificationLogRoutes');

  notificationLogRoutes.post('/create',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  NotificationLogController.create);
  notificationLogRoutes.post('/show',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  NotificationLogController.show);
  notificationLogRoutes.post('/getCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHand<PERSON> ,  NotificationLogController.getCount);
  notificationLogRoutes.post('/getLogsForSelectedDate/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  NotificationLogController.getLogsForSelectedDate);
  notificationLogRoutes.post('/logEntriesFilterCountForFirstTime/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  NotificationLogController.logEntriesFilterCountForFirstTime);
 



  // withdrawWalletRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.create);
  // withdrawWalletRoutes.put('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  WithdrawWalletController.create);


  return notificationLogRoutes;
};

export default initNotificationLogControllerRoutes;
