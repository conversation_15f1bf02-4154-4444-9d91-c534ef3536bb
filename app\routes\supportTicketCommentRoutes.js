import express from 'express';
// import ReportCommentController from '../controllers/reportCommentController';
import SupportTicketCommentController from '../controllers/supportTicketCommentController';
var passport = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');


const initsupportTicketCommentRoutes = () => {
  const supportTicketCommentRoutes = express.Router();

  supportTicketCommentRoutes.get('/getComments/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketCommentController.getComments);
  supportTicketCommentRoutes.post('/SupportTicketComment/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler , SupportTicketCommentController.create);
  supportTicketCommentRoutes.get('/totalUnread/:id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  SupportTicketCommentController.totalUnread);

  // supportTicketRoutes.get('/title/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.title);
  // supportTicketRoutes.get('/description/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.description);
  // supportTicketRoutes.post('/SupportTicket/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  SupportTicketController.create);


  return supportTicketCommentRoutes;
};

export default initsupportTicketCommentRoutes;
