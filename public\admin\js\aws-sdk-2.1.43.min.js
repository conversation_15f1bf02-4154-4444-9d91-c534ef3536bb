// AWS SDK for JavaScript v2.1.43
// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// License at https://sdk.amazonaws.com/js/BUNDLE_LICENSE.txt
!function e(t,r,a){function o(i,s){if(!r[i]){if(!t[i]){var u="function"==typeof require&&require;if(!s&&u)return u(i,!0);if(n)return n(i,!0);throw new Error("Cannot find module '"+i+"'")}var c=r[i]={exports:{}};t[i][0].call(c.exports,function(e){var r=t[i][1][e];return o(r?r:e)},c,c.exports,e,t,r,a)}return r[i].exports}for(var n="function"==typeof require&&require,i=0;i<a.length;i++)o(a[i]);return o}({1:[function(e,t,r){var a=e("./core");a.apiLoader=function(e,t){return a.apiLoader.services[e][t]},a.apiLoader.services={},a.XML.Parser=e("./xml/browser_parser"),e("./http/xhr"),"undefined"!=typeof window&&(window.AWS=a),"undefined"!=typeof t&&(t.exports=a),a.apiLoader.services.cloudwatch={},a.CloudWatch=a.Service.defineService("cloudwatch",["2010-08-01"]),a.apiLoader.services.cloudwatch["2010-08-01"]={metadata:{apiVersion:"2010-08-01",endpointPrefix:"monitoring",serviceAbbreviation:"CloudWatch",serviceFullName:"Amazon CloudWatch",signatureVersion:"v4",xmlNamespace:"http://monitoring.amazonaws.com/doc/2010-08-01/",protocol:"query"},operations:{DeleteAlarms:{input:{type:"structure",required:["AlarmNames"],members:{AlarmNames:{shape:"S2"}}},http:{}},DescribeAlarmHistory:{input:{type:"structure",members:{AlarmName:{},HistoryItemType:{},StartDate:{type:"timestamp"},EndDate:{type:"timestamp"},MaxRecords:{type:"integer"},NextToken:{}}},output:{resultWrapper:"DescribeAlarmHistoryResult",type:"structure",members:{AlarmHistoryItems:{type:"list",member:{type:"structure",members:{AlarmName:{},Timestamp:{type:"timestamp"},HistoryItemType:{},HistorySummary:{},HistoryData:{}}}},NextToken:{}}},http:{}},DescribeAlarms:{input:{type:"structure",members:{AlarmNames:{shape:"S2"},AlarmNamePrefix:{},StateValue:{},ActionPrefix:{},MaxRecords:{type:"integer"},NextToken:{}}},output:{resultWrapper:"DescribeAlarmsResult",type:"structure",members:{MetricAlarms:{shape:"Sj"},NextToken:{}}},http:{}},DescribeAlarmsForMetric:{input:{type:"structure",required:["MetricName","Namespace"],members:{MetricName:{},Namespace:{},Statistic:{},Dimensions:{shape:"Sv"},Period:{type:"integer"},Unit:{}}},output:{resultWrapper:"DescribeAlarmsForMetricResult",type:"structure",members:{MetricAlarms:{shape:"Sj"}}},http:{}},DisableAlarmActions:{input:{type:"structure",required:["AlarmNames"],members:{AlarmNames:{shape:"S2"}}},http:{}},EnableAlarmActions:{input:{type:"structure",required:["AlarmNames"],members:{AlarmNames:{shape:"S2"}}},http:{}},GetMetricStatistics:{input:{type:"structure",required:["Namespace","MetricName","StartTime","EndTime","Period","Statistics"],members:{Namespace:{},MetricName:{},Dimensions:{shape:"Sv"},StartTime:{type:"timestamp"},EndTime:{type:"timestamp"},Period:{type:"integer"},Statistics:{type:"list",member:{}},Unit:{}}},output:{resultWrapper:"GetMetricStatisticsResult",type:"structure",members:{Label:{},Datapoints:{type:"list",member:{type:"structure",members:{Timestamp:{type:"timestamp"},SampleCount:{type:"double"},Average:{type:"double"},Sum:{type:"double"},Minimum:{type:"double"},Maximum:{type:"double"},Unit:{}},xmlOrder:["Timestamp","SampleCount","Average","Sum","Minimum","Maximum","Unit"]}}}},http:{}},ListMetrics:{input:{type:"structure",members:{Namespace:{},MetricName:{},Dimensions:{type:"list",member:{type:"structure",required:["Name"],members:{Name:{},Value:{}}}},NextToken:{}}},output:{xmlOrder:["Metrics","NextToken"],resultWrapper:"ListMetricsResult",type:"structure",members:{Metrics:{type:"list",member:{type:"structure",members:{Namespace:{},MetricName:{},Dimensions:{shape:"Sv"}},xmlOrder:["Namespace","MetricName","Dimensions"]}},NextToken:{}}},http:{}},PutMetricAlarm:{input:{type:"structure",required:["AlarmName","MetricName","Namespace","Statistic","Period","EvaluationPeriods","Threshold","ComparisonOperator"],members:{AlarmName:{},AlarmDescription:{},ActionsEnabled:{type:"boolean"},OKActions:{shape:"So"},AlarmActions:{shape:"So"},InsufficientDataActions:{shape:"So"},MetricName:{},Namespace:{},Statistic:{},Dimensions:{shape:"Sv"},Period:{type:"integer"},Unit:{},EvaluationPeriods:{type:"integer"},Threshold:{type:"double"},ComparisonOperator:{}}},http:{}},PutMetricData:{input:{type:"structure",required:["Namespace","MetricData"],members:{Namespace:{},MetricData:{type:"list",member:{type:"structure",required:["MetricName"],members:{MetricName:{},Dimensions:{shape:"Sv"},Timestamp:{type:"timestamp"},Value:{type:"double"},StatisticValues:{type:"structure",required:["SampleCount","Sum","Minimum","Maximum"],members:{SampleCount:{type:"double"},Sum:{type:"double"},Minimum:{type:"double"},Maximum:{type:"double"}}},Unit:{}}}}}},http:{}},SetAlarmState:{input:{type:"structure",required:["AlarmName","StateValue","StateReason"],members:{AlarmName:{},StateValue:{},StateReason:{},StateReasonData:{}}},http:{}}},shapes:{S2:{type:"list",member:{}},Sj:{type:"list",member:{type:"structure",members:{AlarmName:{},AlarmArn:{},AlarmDescription:{},AlarmConfigurationUpdatedTimestamp:{type:"timestamp"},ActionsEnabled:{type:"boolean"},OKActions:{shape:"So"},AlarmActions:{shape:"So"},InsufficientDataActions:{shape:"So"},StateValue:{},StateReason:{},StateReasonData:{},StateUpdatedTimestamp:{type:"timestamp"},MetricName:{},Namespace:{},Statistic:{},Dimensions:{shape:"Sv"},Period:{type:"integer"},Unit:{},EvaluationPeriods:{type:"integer"},Threshold:{type:"double"},ComparisonOperator:{}},xmlOrder:["AlarmName","AlarmArn","AlarmDescription","AlarmConfigurationUpdatedTimestamp","ActionsEnabled","OKActions","AlarmActions","InsufficientDataActions","StateValue","StateReason","StateReasonData","StateUpdatedTimestamp","MetricName","Namespace","Statistic","Dimensions","Period","Unit","EvaluationPeriods","Threshold","ComparisonOperator"]}},So:{type:"list",member:{}},Sv:{type:"list",member:{type:"structure",required:["Name","Value"],members:{Name:{},Value:{}},xmlOrder:["Name","Value"]}}},paginators:{DescribeAlarmHistory:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxRecords",result_key:"AlarmHistoryItems"},DescribeAlarms:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxRecords",result_key:"MetricAlarms"},DescribeAlarmsForMetric:{result_key:"MetricAlarms"},ListMetrics:{input_token:"NextToken",output_token:"NextToken",result_key:"Metrics"}}},a.apiLoader.services.cloudwatchlogs={},a.CloudWatchLogs=a.Service.defineService("cloudwatchlogs",["2014-03-28"]),a.apiLoader.services.cloudwatchlogs["2014-03-28"]={version:"2.0",metadata:{apiVersion:"2014-03-28",endpointPrefix:"logs",jsonVersion:"1.1",serviceFullName:"Amazon CloudWatch Logs",signatureVersion:"v4",targetPrefix:"Logs_20140328",protocol:"json"},operations:{CreateLogGroup:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{}}},http:{}},CreateLogStream:{input:{type:"structure",required:["logGroupName","logStreamName"],members:{logGroupName:{},logStreamName:{}}},http:{}},DeleteDestination:{input:{type:"structure",required:["destinationName"],members:{destinationName:{}}},http:{}},DeleteLogGroup:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{}}},http:{}},DeleteLogStream:{input:{type:"structure",required:["logGroupName","logStreamName"],members:{logGroupName:{},logStreamName:{}}},http:{}},DeleteMetricFilter:{input:{type:"structure",required:["logGroupName","filterName"],members:{logGroupName:{},filterName:{}}},http:{}},DeleteRetentionPolicy:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{}}},http:{}},DeleteSubscriptionFilter:{input:{type:"structure",required:["logGroupName","filterName"],members:{logGroupName:{},filterName:{}}},http:{}},DescribeDestinations:{input:{type:"structure",members:{DestinationNamePrefix:{},nextToken:{},limit:{type:"integer"}}},output:{type:"structure",members:{destinations:{type:"list",member:{shape:"Si"}},nextToken:{}}},http:{}},DescribeLogGroups:{input:{type:"structure",members:{logGroupNamePrefix:{},nextToken:{},limit:{type:"integer"}}},output:{type:"structure",members:{logGroups:{type:"list",member:{type:"structure",members:{logGroupName:{},creationTime:{type:"long"},retentionInDays:{type:"integer"},metricFilterCount:{type:"integer"},arn:{},storedBytes:{type:"long"}}}},nextToken:{}}},http:{}},DescribeLogStreams:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{},logStreamNamePrefix:{},orderBy:{},descending:{type:"boolean"},nextToken:{},limit:{type:"integer"}}},output:{type:"structure",members:{logStreams:{type:"list",member:{type:"structure",members:{logStreamName:{},creationTime:{type:"long"},firstEventTimestamp:{type:"long"},lastEventTimestamp:{type:"long"},lastIngestionTime:{type:"long"},uploadSequenceToken:{},arn:{},storedBytes:{type:"long"}}}},nextToken:{}}},http:{}},DescribeMetricFilters:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{},filterNamePrefix:{},nextToken:{},limit:{type:"integer"}}},output:{type:"structure",members:{metricFilters:{type:"list",member:{type:"structure",members:{filterName:{},filterPattern:{},metricTransformations:{shape:"S17"},creationTime:{type:"long"}}}},nextToken:{}}},http:{}},DescribeSubscriptionFilters:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{},filterNamePrefix:{},nextToken:{},limit:{type:"integer"}}},output:{type:"structure",members:{subscriptionFilters:{type:"list",member:{type:"structure",members:{filterName:{},logGroupName:{},filterPattern:{},destinationArn:{},roleArn:{},creationTime:{type:"long"}}}},nextToken:{}}},http:{}},FilterLogEvents:{input:{type:"structure",required:["logGroupName"],members:{logGroupName:{},logStreamNames:{type:"list",member:{}},startTime:{type:"long"},endTime:{type:"long"},filterPattern:{},nextToken:{},limit:{type:"integer"},interleaved:{type:"boolean"}}},output:{type:"structure",members:{events:{type:"list",member:{type:"structure",members:{logStreamName:{},timestamp:{type:"long"},message:{},ingestionTime:{type:"long"},eventId:{}}}},searchedLogStreams:{type:"list",member:{type:"structure",members:{logStreamName:{},searchedCompletely:{type:"boolean"}}}},nextToken:{}}},http:{}},GetLogEvents:{input:{type:"structure",required:["logGroupName","logStreamName"],members:{logGroupName:{},logStreamName:{},startTime:{type:"long"},endTime:{type:"long"},nextToken:{},limit:{type:"integer"},startFromHead:{type:"boolean"}}},output:{type:"structure",members:{events:{type:"list",member:{type:"structure",members:{timestamp:{type:"long"},message:{},ingestionTime:{type:"long"}}}},nextForwardToken:{},nextBackwardToken:{}}},http:{}},PutDestination:{input:{type:"structure",required:["destinationName","targetArn","roleArn"],members:{destinationName:{},targetArn:{},roleArn:{}}},output:{type:"structure",members:{destination:{shape:"Si"}}},http:{}},PutDestinationPolicy:{input:{type:"structure",required:["destinationName","accessPolicy"],members:{destinationName:{},accessPolicy:{}}},http:{}},PutLogEvents:{input:{type:"structure",required:["logGroupName","logStreamName","logEvents"],members:{logGroupName:{},logStreamName:{},logEvents:{type:"list",member:{type:"structure",required:["timestamp","message"],members:{timestamp:{type:"long"},message:{}}}},sequenceToken:{}}},output:{type:"structure",members:{nextSequenceToken:{},rejectedLogEventsInfo:{type:"structure",members:{tooNewLogEventStartIndex:{type:"integer"},tooOldLogEventEndIndex:{type:"integer"},expiredLogEventEndIndex:{type:"integer"}}}}},http:{}},PutMetricFilter:{input:{type:"structure",required:["logGroupName","filterName","filterPattern","metricTransformations"],members:{logGroupName:{},filterName:{},filterPattern:{},metricTransformations:{shape:"S17"}}},http:{}},PutRetentionPolicy:{input:{type:"structure",required:["logGroupName","retentionInDays"],members:{logGroupName:{},retentionInDays:{type:"integer"}}},http:{}},PutSubscriptionFilter:{input:{type:"structure",required:["logGroupName","filterName","filterPattern","destinationArn"],members:{logGroupName:{},filterName:{},filterPattern:{},destinationArn:{},roleArn:{}}},http:{}},TestMetricFilter:{input:{type:"structure",required:["filterPattern","logEventMessages"],members:{filterPattern:{},logEventMessages:{type:"list",member:{}}}},output:{type:"structure",members:{matches:{type:"list",member:{type:"structure",members:{eventNumber:{type:"long"},eventMessage:{},extractedValues:{type:"map",key:{},value:{}}}}}}},http:{}}},shapes:{Si:{type:"structure",members:{destinationName:{},targetArn:{},roleArn:{},accessPolicy:{},arn:{},creationTime:{type:"long"}}},S17:{type:"list",member:{type:"structure",required:["metricName","metricNamespace","metricValue"],members:{metricName:{},metricNamespace:{},metricValue:{}}}}},examples:{},paginators:{DescribeLogGroups:{input_token:"nextToken",output_token:"nextToken",limit_key:"limit",result_key:"logGroups"},DescribeLogStreams:{input_token:"nextToken",output_token:"nextToken",limit_key:"limit",result_key:"logStreams"},DescribeMetricFilters:{input_token:"nextToken",output_token:"nextToken",limit_key:"limit",result_key:"metricFilters"},GetLogEvents:{input_token:"nextToken",output_token:"nextForwardToken",limit_key:"limit",result_key:"events"}}},a.apiLoader.services.cognitoidentity={},a.CognitoIdentity=a.Service.defineService("cognitoidentity",["2014-06-30"]),e("./services/cognitoidentity"),a.apiLoader.services.cognitoidentity["2014-06-30"]={version:"2.0",metadata:{apiVersion:"2014-06-30",endpointPrefix:"cognito-identity",jsonVersion:"1.1",serviceFullName:"Amazon Cognito Identity",signatureVersion:"v4",targetPrefix:"AWSCognitoIdentityService",protocol:"json"},operations:{CreateIdentityPool:{input:{type:"structure",required:["IdentityPoolName","AllowUnauthenticatedIdentities"],members:{IdentityPoolName:{},AllowUnauthenticatedIdentities:{type:"boolean"},SupportedLoginProviders:{shape:"S4"},DeveloperProviderName:{},OpenIdConnectProviderARNs:{shape:"S8"}}},output:{shape:"Sa"},http:{}},DeleteIdentities:{input:{type:"structure",required:["IdentityIdsToDelete"],members:{IdentityIdsToDelete:{type:"list",member:{}}}},output:{type:"structure",members:{UnprocessedIdentityIds:{type:"list",member:{type:"structure",members:{IdentityId:{},ErrorCode:{}}}}}},http:{}},DeleteIdentityPool:{input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{}}},http:{}},DescribeIdentity:{input:{type:"structure",required:["IdentityId"],members:{IdentityId:{}}},output:{shape:"Sl"},http:{}},DescribeIdentityPool:{input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{}}},output:{shape:"Sa"},http:{}},GetCredentialsForIdentity:{input:{type:"structure",required:["IdentityId"],members:{IdentityId:{},Logins:{shape:"Sq"}}},output:{type:"structure",members:{IdentityId:{},Credentials:{type:"structure",members:{AccessKeyId:{},SecretKey:{},SessionToken:{},Expiration:{type:"timestamp"}}}}},http:{}},GetId:{input:{type:"structure",required:["IdentityPoolId"],members:{AccountId:{},IdentityPoolId:{},Logins:{shape:"Sq"}}},output:{type:"structure",members:{IdentityId:{}}},http:{}},GetIdentityPoolRoles:{input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{}}},output:{type:"structure",members:{IdentityPoolId:{},Roles:{shape:"S12"}}},http:{}},GetOpenIdToken:{input:{type:"structure",required:["IdentityId"],members:{IdentityId:{},Logins:{shape:"Sq"}}},output:{type:"structure",members:{IdentityId:{},Token:{}}},http:{}},GetOpenIdTokenForDeveloperIdentity:{input:{type:"structure",required:["IdentityPoolId","Logins"],members:{IdentityPoolId:{},IdentityId:{},Logins:{shape:"Sq"},TokenDuration:{type:"long"}}},output:{type:"structure",members:{IdentityId:{},Token:{}}},http:{}},ListIdentities:{input:{type:"structure",required:["IdentityPoolId","MaxResults"],members:{IdentityPoolId:{},MaxResults:{type:"integer"},NextToken:{},HideDisabled:{type:"boolean"}}},output:{type:"structure",members:{IdentityPoolId:{},Identities:{type:"list",member:{shape:"Sl"}},NextToken:{}}},http:{}},ListIdentityPools:{input:{type:"structure",required:["MaxResults"],members:{MaxResults:{type:"integer"},NextToken:{}}},output:{type:"structure",members:{IdentityPools:{type:"list",member:{type:"structure",members:{IdentityPoolId:{},IdentityPoolName:{}}}},NextToken:{}}},http:{}},LookupDeveloperIdentity:{input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{},IdentityId:{},DeveloperUserIdentifier:{},MaxResults:{type:"integer"},NextToken:{}}},output:{type:"structure",members:{IdentityId:{},DeveloperUserIdentifierList:{type:"list",member:{}},NextToken:{}}},http:{}},MergeDeveloperIdentities:{input:{type:"structure",required:["SourceUserIdentifier","DestinationUserIdentifier","DeveloperProviderName","IdentityPoolId"],members:{SourceUserIdentifier:{},DestinationUserIdentifier:{},DeveloperProviderName:{},IdentityPoolId:{}}},output:{type:"structure",members:{IdentityId:{}}},http:{}},SetIdentityPoolRoles:{input:{type:"structure",required:["IdentityPoolId","Roles"],members:{IdentityPoolId:{},Roles:{shape:"S12"}}},http:{}},UnlinkDeveloperIdentity:{input:{type:"structure",required:["IdentityId","IdentityPoolId","DeveloperProviderName","DeveloperUserIdentifier"],members:{IdentityId:{},IdentityPoolId:{},DeveloperProviderName:{},DeveloperUserIdentifier:{}}},http:{}},UnlinkIdentity:{input:{type:"structure",required:["IdentityId","Logins","LoginsToRemove"],members:{IdentityId:{},Logins:{shape:"Sq"},LoginsToRemove:{shape:"Sm"}}},http:{}},UpdateIdentityPool:{input:{shape:"Sa"},output:{shape:"Sa"},http:{}}},shapes:{S4:{type:"map",key:{},value:{}},S8:{type:"list",member:{}},Sa:{type:"structure",required:["IdentityPoolId","IdentityPoolName","AllowUnauthenticatedIdentities"],members:{IdentityPoolId:{},IdentityPoolName:{},AllowUnauthenticatedIdentities:{type:"boolean"},SupportedLoginProviders:{shape:"S4"},DeveloperProviderName:{},OpenIdConnectProviderARNs:{shape:"S8"}}},Sl:{type:"structure",members:{IdentityId:{},Logins:{shape:"Sm"},CreationDate:{type:"timestamp"},LastModifiedDate:{type:"timestamp"}}},Sm:{type:"list",member:{}},Sq:{type:"map",key:{},value:{}},S12:{type:"map",key:{},value:{}}}},a.apiLoader.services.cognitosync={},a.CognitoSync=a.Service.defineService("cognitosync",["2014-06-30"]),a.apiLoader.services.cognitosync["2014-06-30"]={version:"2.0",metadata:{apiVersion:"2014-06-30",endpointPrefix:"cognito-sync",jsonVersion:"1.1",serviceFullName:"Amazon Cognito Sync",signatureVersion:"v4",protocol:"rest-json"},operations:{BulkPublish:{http:{requestUri:"/identitypools/{IdentityPoolId}/bulkpublish",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"}}},output:{type:"structure",members:{IdentityPoolId:{}}}},DeleteDataset:{http:{method:"DELETE",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"}}},output:{type:"structure",members:{Dataset:{shape:"S8"}}}},DescribeDataset:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"}}},output:{type:"structure",members:{Dataset:{shape:"S8"}}}},DescribeIdentityPoolUsage:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"}}},output:{type:"structure",members:{IdentityPoolUsage:{shape:"Sg"}}}},DescribeIdentityUsage:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"}}},output:{type:"structure",members:{IdentityUsage:{type:"structure",members:{IdentityId:{},IdentityPoolId:{},LastModifiedDate:{type:"timestamp"},DatasetCount:{type:"integer"},DataStorage:{type:"long"}}}}}},GetBulkPublishDetails:{http:{requestUri:"/identitypools/{IdentityPoolId}/getBulkPublishDetails",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"}}},output:{type:"structure",members:{IdentityPoolId:{},BulkPublishStartTime:{type:"timestamp"},BulkPublishCompleteTime:{type:"timestamp"},BulkPublishStatus:{},FailureMessage:{}}}},GetCognitoEvents:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/events",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"}}},output:{type:"structure",members:{Events:{shape:"Sq"}}}},GetIdentityPoolConfiguration:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/configuration",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"}}},output:{type:"structure",members:{IdentityPoolId:{},PushSync:{shape:"Sv"},CognitoStreams:{shape:"Sz"}}}},ListDatasets:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets",responseCode:200},input:{type:"structure",required:["IdentityId","IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},NextToken:{location:"querystring",locationName:"nextToken"},MaxResults:{location:"querystring",locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{Datasets:{type:"list",member:{shape:"S8"}},Count:{type:"integer"},NextToken:{}}}},ListIdentityPoolUsage:{http:{method:"GET",requestUri:"/identitypools",responseCode:200},input:{type:"structure",members:{NextToken:{location:"querystring",locationName:"nextToken"},MaxResults:{location:"querystring",locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{IdentityPoolUsages:{type:"list",member:{shape:"Sg"}},MaxResults:{type:"integer"},Count:{type:"integer"},NextToken:{}}}},ListRecords:{http:{method:"GET",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}/records",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"},LastSyncCount:{location:"querystring",locationName:"lastSyncCount",type:"long"},NextToken:{location:"querystring",locationName:"nextToken"},MaxResults:{location:"querystring",locationName:"maxResults",type:"integer"},SyncSessionToken:{location:"querystring",locationName:"syncSessionToken"}}},output:{type:"structure",members:{Records:{shape:"S1c"},NextToken:{},Count:{type:"integer"},DatasetSyncCount:{type:"long"},LastModifiedBy:{},MergedDatasetNames:{type:"list",member:{}},DatasetExists:{type:"boolean"},DatasetDeletedAfterRequestedSyncCount:{type:"boolean"},SyncSessionToken:{}}}},RegisterDevice:{http:{requestUri:"/identitypools/{IdentityPoolId}/identity/{IdentityId}/device",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","Platform","Token"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},Platform:{},Token:{}}},output:{type:"structure",members:{DeviceId:{}}}},SetCognitoEvents:{http:{requestUri:"/identitypools/{IdentityPoolId}/events",responseCode:200},input:{type:"structure",required:["IdentityPoolId","Events"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},Events:{shape:"Sq"}}}},SetIdentityPoolConfiguration:{http:{requestUri:"/identitypools/{IdentityPoolId}/configuration",responseCode:200},input:{type:"structure",required:["IdentityPoolId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},PushSync:{shape:"Sv"},CognitoStreams:{shape:"Sz"}}},output:{type:"structure",members:{IdentityPoolId:{},PushSync:{shape:"Sv"},CognitoStreams:{shape:"Sz"}}}},SubscribeToDataset:{http:{requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}/subscriptions/{DeviceId}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName","DeviceId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"},DeviceId:{location:"uri",locationName:"DeviceId"}}},output:{type:"structure",members:{}}},UnsubscribeFromDataset:{http:{method:"DELETE",requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}/subscriptions/{DeviceId}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName","DeviceId"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"},DeviceId:{location:"uri",locationName:"DeviceId"}}},output:{type:"structure",members:{}}},UpdateRecords:{http:{requestUri:"/identitypools/{IdentityPoolId}/identities/{IdentityId}/datasets/{DatasetName}",responseCode:200},input:{type:"structure",required:["IdentityPoolId","IdentityId","DatasetName","SyncSessionToken"],members:{IdentityPoolId:{location:"uri",locationName:"IdentityPoolId"},IdentityId:{location:"uri",locationName:"IdentityId"},DatasetName:{location:"uri",locationName:"DatasetName"},DeviceId:{},RecordPatches:{type:"list",member:{type:"structure",required:["Op","Key","SyncCount"],members:{Op:{},Key:{},Value:{},SyncCount:{type:"long"},DeviceLastModifiedDate:{type:"timestamp"}}}},SyncSessionToken:{},ClientContext:{location:"header",locationName:"x-amz-Client-Context"}}},output:{type:"structure",members:{Records:{shape:"S1c"}}}}},shapes:{S8:{type:"structure",members:{IdentityId:{},DatasetName:{},CreationDate:{type:"timestamp"},LastModifiedDate:{type:"timestamp"},LastModifiedBy:{},DataStorage:{type:"long"},NumRecords:{type:"long"}}},Sg:{type:"structure",members:{IdentityPoolId:{},SyncSessionsCount:{type:"long"},DataStorage:{type:"long"},LastModifiedDate:{type:"timestamp"}}},Sq:{type:"map",key:{},value:{}},Sv:{type:"structure",members:{ApplicationArns:{type:"list",member:{}},RoleArn:{}}},Sz:{type:"structure",members:{StreamName:{},RoleArn:{},StreamingStatus:{}}},S1c:{type:"list",member:{type:"structure",members:{Key:{},Value:{},SyncCount:{type:"long"},LastModifiedDate:{type:"timestamp"},LastModifiedBy:{},DeviceLastModifiedDate:{type:"timestamp"}}}}}},a.apiLoader.services.devicefarm={},a.DeviceFarm=a.Service.defineService("devicefarm",["2015-06-23"]),a.apiLoader.services.devicefarm["2015-06-23"]={version:"2.0",metadata:{apiVersion:"2015-06-23",endpointPrefix:"devicefarm",jsonVersion:"1.1",serviceFullName:"AWS Device Farm",signatureVersion:"v4",targetPrefix:"DeviceFarm_20150623",protocol:"json"},operations:{CreateDevicePool:{input:{type:"structure",required:["projectArn","name","rules"],members:{projectArn:{},name:{},description:{},rules:{shape:"S5"}}},output:{type:"structure",members:{devicePool:{shape:"Sb"}}},http:{}},CreateProject:{input:{type:"structure",required:["name"],members:{name:{}}},output:{type:"structure",members:{project:{shape:"Sf"}}},http:{}},CreateUpload:{input:{type:"structure",required:["projectArn","name","type"],members:{projectArn:{},name:{},type:{},contentType:{}}},output:{type:"structure",members:{upload:{shape:"Sl"}}},http:{}},GetAccountSettings:{input:{type:"structure",members:{}},output:{type:"structure",members:{accountSettings:{type:"structure",members:{awsAccountNumber:{},unmeteredDevices:{type:"map",key:{},value:{type:"integer"}}}}}},http:{}},GetDevice:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{device:{shape:"Sy"}}},http:{}},GetDevicePool:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{devicePool:{shape:"Sb"}}},http:{}},GetDevicePoolCompatibility:{input:{type:"structure",required:["devicePoolArn","appArn"],members:{devicePoolArn:{},appArn:{},testType:{}}},output:{type:"structure",members:{compatibleDevices:{shape:"S19"},incompatibleDevices:{shape:"S19"}}},http:{}},GetJob:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{job:{shape:"S1g"}}},http:{}},GetProject:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{project:{shape:"Sf"}}},http:{}},GetRun:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{run:{shape:"S1o"}}},http:{}},GetSuite:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{suite:{shape:"S1s"}}},http:{}},GetTest:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{test:{shape:"S1v"}}},http:{}},GetUpload:{input:{type:"structure",required:["arn"],members:{arn:{}}},output:{type:"structure",members:{upload:{shape:"Sl"}}},http:{}},ListArtifacts:{input:{type:"structure",required:["arn","type"],members:{arn:{},type:{},nextToken:{}}},output:{type:"structure",members:{artifacts:{type:"list",member:{type:"structure",members:{arn:{},name:{},type:{},extension:{},url:{}}}},nextToken:{}}},http:{}},ListDevicePools:{input:{type:"structure",required:["arn"],members:{arn:{},type:{},nextToken:{}}},output:{type:"structure",members:{devicePools:{type:"list",member:{shape:"Sb"}},nextToken:{}}},http:{}},ListDevices:{input:{type:"structure",members:{arn:{},nextToken:{}}},output:{type:"structure",members:{devices:{type:"list",member:{shape:"Sy"}},nextToken:{}}},http:{}},ListJobs:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{jobs:{type:"list",member:{shape:"S1g"}},nextToken:{}}},http:{}},ListProjects:{input:{type:"structure",members:{arn:{},nextToken:{}}},output:{type:"structure",members:{projects:{type:"list",member:{shape:"Sf"}},nextToken:{}}},http:{}},ListRuns:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{runs:{type:"list",member:{shape:"S1o"}},nextToken:{}}},http:{}},ListSamples:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{samples:{type:"list",member:{type:"structure",members:{arn:{},type:{},url:{}}}},nextToken:{}}},http:{}},ListSuites:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{suites:{type:"list",member:{shape:"S1s"}},nextToken:{}}},http:{}},ListTests:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{tests:{type:"list",member:{shape:"S1v"}},nextToken:{}}},http:{}},ListUniqueProblems:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{uniqueProblems:{type:"map",key:{},value:{type:"list",member:{type:"structure",members:{message:{},problems:{type:"list",member:{type:"structure",members:{run:{shape:"S32"},job:{shape:"S32"},suite:{shape:"S32"},test:{shape:"S32"},device:{shape:"Sy"},result:{},message:{}}}}}}}},nextToken:{}}},http:{}},ListUploads:{input:{type:"structure",required:["arn"],members:{arn:{},nextToken:{}}},output:{type:"structure",members:{uploads:{type:"list",member:{shape:"Sl"}},nextToken:{}}},http:{}},ScheduleRun:{input:{type:"structure",required:["projectArn","appArn","devicePoolArn","test"],members:{projectArn:{},appArn:{},devicePoolArn:{},name:{},test:{type:"structure",required:["type"],
members:{type:{},testPackageArn:{},filter:{},parameters:{type:"map",key:{},value:{}}}},configuration:{type:"structure",members:{extraDataPackageArn:{},networkProfileArn:{},locale:{},location:{type:"structure",required:["latitude","longitude"],members:{latitude:{type:"double"},longitude:{type:"double"}}},radios:{type:"structure",members:{wifi:{type:"boolean"},bluetooth:{type:"boolean"},nfc:{type:"boolean"},gps:{type:"boolean"}}},auxiliaryApps:{type:"list",member:{}},billingMethod:{}}}}},output:{type:"structure",members:{run:{shape:"S1o"}}},http:{}}},shapes:{S5:{type:"list",member:{type:"structure",members:{attribute:{},operator:{},value:{}}}},Sb:{type:"structure",members:{arn:{},name:{},description:{},type:{},rules:{shape:"S5"}}},Sf:{type:"structure",members:{arn:{},name:{},created:{type:"timestamp"}}},Sl:{type:"structure",members:{arn:{},name:{},created:{type:"timestamp"},type:{},status:{},url:{},metadata:{},contentType:{},message:{}}},Sy:{type:"structure",members:{arn:{},name:{},manufacturer:{},model:{},formFactor:{},platform:{},os:{},cpu:{type:"structure",members:{frequency:{},architecture:{},clock:{type:"double"}}},resolution:{type:"structure",members:{width:{type:"integer"},height:{type:"integer"}}},heapSize:{type:"long"},memory:{type:"long"},image:{},carrier:{},radio:{}}},S19:{type:"list",member:{type:"structure",members:{device:{shape:"Sy"},compatible:{type:"boolean"},incompatibilityMessages:{type:"list",member:{type:"structure",members:{message:{},type:{}}}}}}},S1g:{type:"structure",members:{arn:{},name:{},type:{},created:{type:"timestamp"},status:{},result:{},started:{type:"timestamp"},stopped:{type:"timestamp"},counters:{shape:"S1j"},message:{},device:{shape:"Sy"}}},S1j:{type:"structure",members:{total:{type:"integer"},passed:{type:"integer"},failed:{type:"integer"},warned:{type:"integer"},errored:{type:"integer"},stopped:{type:"integer"},skipped:{type:"integer"}}},S1o:{type:"structure",members:{arn:{},name:{},type:{},platform:{},created:{type:"timestamp"},status:{},result:{},started:{type:"timestamp"},stopped:{type:"timestamp"},counters:{shape:"S1j"},message:{},totalJobs:{type:"integer"},completedJobs:{type:"integer"},billingMethod:{}}},S1s:{type:"structure",members:{arn:{},name:{},type:{},created:{type:"timestamp"},status:{},result:{},started:{type:"timestamp"},stopped:{type:"timestamp"},counters:{shape:"S1j"},message:{}}},S1v:{type:"structure",members:{arn:{},name:{},type:{},created:{type:"timestamp"},status:{},result:{},started:{type:"timestamp"},stopped:{type:"timestamp"},counters:{shape:"S1j"},message:{}}},S32:{type:"structure",members:{arn:{},name:{}}}},examples:{},paginators:{ListArtifacts:{input_token:"nextToken",output_token:"nextToken",result_key:"artifacts"},ListDevicePools:{input_token:"nextToken",output_token:"nextToken",result_key:"devicePools"},ListDevices:{input_token:"nextToken",output_token:"nextToken",result_key:"devices"},ListJobs:{input_token:"nextToken",output_token:"nextToken",result_key:"jobs"},ListProjects:{input_token:"nextToken",output_token:"nextToken",result_key:"projects"},ListRuns:{input_token:"nextToken",output_token:"nextToken",result_key:"runs"},ListSamples:{input_token:"nextToken",output_token:"nextToken",result_key:"samples"},ListSuites:{input_token:"nextToken",output_token:"nextToken",result_key:"suites"},ListTests:{input_token:"nextToken",output_token:"nextToken",result_key:"tests"},ListUniqueProblems:{input_token:"nextToken",output_token:"nextToken",result_key:"uniqueProblems"},ListUploads:{input_token:"nextToken",output_token:"nextToken",result_key:"uploads"}}},a.apiLoader.services.dynamodb={},a.DynamoDB=a.Service.defineService("dynamodb",["2011-12-05","2012-08-10"]),e("./services/dynamodb"),a.apiLoader.services.dynamodb["2012-08-10"]={version:"2.0",metadata:{apiVersion:"2012-08-10",endpointPrefix:"dynamodb",jsonVersion:"1.0",serviceAbbreviation:"DynamoDB",serviceFullName:"Amazon DynamoDB",signatureVersion:"v4",targetPrefix:"DynamoDB_20120810",protocol:"json"},operations:{BatchGetItem:{input:{type:"structure",required:["RequestItems"],members:{RequestItems:{shape:"S2"},ReturnConsumedCapacity:{}}},output:{type:"structure",members:{Responses:{type:"map",key:{},value:{shape:"Sr"}},UnprocessedKeys:{shape:"S2"},ConsumedCapacity:{shape:"St"}}},http:{}},BatchWriteItem:{input:{type:"structure",required:["RequestItems"],members:{RequestItems:{shape:"S10"},ReturnConsumedCapacity:{},ReturnItemCollectionMetrics:{}}},output:{type:"structure",members:{UnprocessedItems:{shape:"S10"},ItemCollectionMetrics:{type:"map",key:{},value:{type:"list",member:{shape:"S1a"}}},ConsumedCapacity:{shape:"St"}}},http:{}},CreateTable:{input:{type:"structure",required:["AttributeDefinitions","TableName","KeySchema","ProvisionedThroughput"],members:{AttributeDefinitions:{shape:"S1f"},TableName:{},KeySchema:{shape:"S1j"},LocalSecondaryIndexes:{type:"list",member:{type:"structure",required:["IndexName","KeySchema","Projection"],members:{IndexName:{},KeySchema:{shape:"S1j"},Projection:{shape:"S1o"}}}},GlobalSecondaryIndexes:{type:"list",member:{type:"structure",required:["IndexName","KeySchema","Projection","ProvisionedThroughput"],members:{IndexName:{},KeySchema:{shape:"S1j"},Projection:{shape:"S1o"},ProvisionedThroughput:{shape:"S1u"}}}},ProvisionedThroughput:{shape:"S1u"},StreamSpecification:{shape:"S1w"}}},output:{type:"structure",members:{TableDescription:{shape:"S20"}}},http:{}},DeleteItem:{input:{type:"structure",required:["TableName","Key"],members:{TableName:{},Key:{shape:"S6"},Expected:{shape:"S2e"},ConditionalOperator:{},ReturnValues:{},ReturnConsumedCapacity:{},ReturnItemCollectionMetrics:{},ConditionExpression:{},ExpressionAttributeNames:{shape:"Sm"},ExpressionAttributeValues:{shape:"S2m"}}},output:{type:"structure",members:{Attributes:{shape:"Ss"},ConsumedCapacity:{shape:"Su"},ItemCollectionMetrics:{shape:"S1a"}}},http:{}},DeleteTable:{input:{type:"structure",required:["TableName"],members:{TableName:{}}},output:{type:"structure",members:{TableDescription:{shape:"S20"}}},http:{}},DescribeTable:{input:{type:"structure",required:["TableName"],members:{TableName:{}}},output:{type:"structure",members:{Table:{shape:"S20"}}},http:{}},GetItem:{input:{type:"structure",required:["TableName","Key"],members:{TableName:{},Key:{shape:"S6"},AttributesToGet:{shape:"Sj"},ConsistentRead:{type:"boolean"},ReturnConsumedCapacity:{},ProjectionExpression:{},ExpressionAttributeNames:{shape:"Sm"}}},output:{type:"structure",members:{Item:{shape:"Ss"},ConsumedCapacity:{shape:"Su"}}},http:{}},ListTables:{input:{type:"structure",members:{ExclusiveStartTableName:{},Limit:{type:"integer"}}},output:{type:"structure",members:{TableNames:{type:"list",member:{}},LastEvaluatedTableName:{}}},http:{}},PutItem:{input:{type:"structure",required:["TableName","Item"],members:{TableName:{},Item:{shape:"S14"},Expected:{shape:"S2e"},ReturnValues:{},ReturnConsumedCapacity:{},ReturnItemCollectionMetrics:{},ConditionalOperator:{},ConditionExpression:{},ExpressionAttributeNames:{shape:"Sm"},ExpressionAttributeValues:{shape:"S2m"}}},output:{type:"structure",members:{Attributes:{shape:"Ss"},ConsumedCapacity:{shape:"Su"},ItemCollectionMetrics:{shape:"S1a"}}},http:{}},Query:{input:{type:"structure",required:["TableName"],members:{TableName:{},IndexName:{},Select:{},AttributesToGet:{shape:"Sj"},Limit:{type:"integer"},ConsistentRead:{type:"boolean"},KeyConditions:{type:"map",key:{},value:{shape:"S35"}},QueryFilter:{shape:"S36"},ConditionalOperator:{},ScanIndexForward:{type:"boolean"},ExclusiveStartKey:{shape:"S6"},ReturnConsumedCapacity:{},ProjectionExpression:{},FilterExpression:{},KeyConditionExpression:{},ExpressionAttributeNames:{shape:"Sm"},ExpressionAttributeValues:{shape:"S2m"}}},output:{type:"structure",members:{Items:{shape:"Sr"},Count:{type:"integer"},ScannedCount:{type:"integer"},LastEvaluatedKey:{shape:"S6"},ConsumedCapacity:{shape:"Su"}}},http:{}},Scan:{input:{type:"structure",required:["TableName"],members:{TableName:{},IndexName:{},AttributesToGet:{shape:"Sj"},Limit:{type:"integer"},Select:{},ScanFilter:{shape:"S36"},ConditionalOperator:{},ExclusiveStartKey:{shape:"S6"},ReturnConsumedCapacity:{},TotalSegments:{type:"integer"},Segment:{type:"integer"},ProjectionExpression:{},FilterExpression:{},ExpressionAttributeNames:{shape:"Sm"},ExpressionAttributeValues:{shape:"S2m"},ConsistentRead:{type:"boolean"}}},output:{type:"structure",members:{Items:{shape:"Sr"},Count:{type:"integer"},ScannedCount:{type:"integer"},LastEvaluatedKey:{shape:"S6"},ConsumedCapacity:{shape:"Su"}}},http:{}},UpdateItem:{input:{type:"structure",required:["TableName","Key"],members:{TableName:{},Key:{shape:"S6"},AttributeUpdates:{type:"map",key:{},value:{type:"structure",members:{Value:{shape:"S8"},Action:{}}}},Expected:{shape:"S2e"},ConditionalOperator:{},ReturnValues:{},ReturnConsumedCapacity:{},ReturnItemCollectionMetrics:{},UpdateExpression:{},ConditionExpression:{},ExpressionAttributeNames:{shape:"Sm"},ExpressionAttributeValues:{shape:"S2m"}}},output:{type:"structure",members:{Attributes:{shape:"Ss"},ConsumedCapacity:{shape:"Su"},ItemCollectionMetrics:{shape:"S1a"}}},http:{}},UpdateTable:{input:{type:"structure",required:["TableName"],members:{AttributeDefinitions:{shape:"S1f"},TableName:{},ProvisionedThroughput:{shape:"S1u"},GlobalSecondaryIndexUpdates:{type:"list",member:{type:"structure",members:{Update:{type:"structure",required:["IndexName","ProvisionedThroughput"],members:{IndexName:{},ProvisionedThroughput:{shape:"S1u"}}},Create:{type:"structure",required:["IndexName","KeySchema","Projection","ProvisionedThroughput"],members:{IndexName:{},KeySchema:{shape:"S1j"},Projection:{shape:"S1o"},ProvisionedThroughput:{shape:"S1u"}}},Delete:{type:"structure",required:["IndexName"],members:{IndexName:{}}}}}},StreamSpecification:{shape:"S1w"}}},output:{type:"structure",members:{TableDescription:{shape:"S20"}}},http:{}}},shapes:{S2:{type:"map",key:{},value:{type:"structure",required:["Keys"],members:{Keys:{type:"list",member:{shape:"S6"}},AttributesToGet:{shape:"Sj"},ConsistentRead:{type:"boolean"},ProjectionExpression:{},ExpressionAttributeNames:{shape:"Sm"}}}},S6:{type:"map",key:{},value:{shape:"S8"}},S8:{type:"structure",members:{S:{},N:{},B:{type:"blob"},SS:{type:"list",member:{}},NS:{type:"list",member:{}},BS:{type:"list",member:{type:"blob"}},M:{type:"map",key:{},value:{shape:"S8"}},L:{type:"list",member:{shape:"S8"}},NULL:{type:"boolean"},BOOL:{type:"boolean"}}},Sj:{type:"list",member:{}},Sm:{type:"map",key:{},value:{}},Sr:{type:"list",member:{shape:"Ss"}},Ss:{type:"map",key:{},value:{shape:"S8"}},St:{type:"list",member:{shape:"Su"}},Su:{type:"structure",members:{TableName:{},CapacityUnits:{type:"double"},Table:{shape:"Sw"},LocalSecondaryIndexes:{shape:"Sx"},GlobalSecondaryIndexes:{shape:"Sx"}}},Sw:{type:"structure",members:{CapacityUnits:{type:"double"}}},Sx:{type:"map",key:{},value:{shape:"Sw"}},S10:{type:"map",key:{},value:{type:"list",member:{type:"structure",members:{PutRequest:{type:"structure",required:["Item"],members:{Item:{shape:"S14"}}},DeleteRequest:{type:"structure",required:["Key"],members:{Key:{shape:"S6"}}}}}}},S14:{type:"map",key:{},value:{shape:"S8"}},S1a:{type:"structure",members:{ItemCollectionKey:{type:"map",key:{},value:{shape:"S8"}},SizeEstimateRangeGB:{type:"list",member:{type:"double"}}}},S1f:{type:"list",member:{type:"structure",required:["AttributeName","AttributeType"],members:{AttributeName:{},AttributeType:{}}}},S1j:{type:"list",member:{type:"structure",required:["AttributeName","KeyType"],members:{AttributeName:{},KeyType:{}}}},S1o:{type:"structure",members:{ProjectionType:{},NonKeyAttributes:{type:"list",member:{}}}},S1u:{type:"structure",required:["ReadCapacityUnits","WriteCapacityUnits"],members:{ReadCapacityUnits:{type:"long"},WriteCapacityUnits:{type:"long"}}},S1w:{type:"structure",members:{StreamEnabled:{type:"boolean"},StreamViewType:{}}},S20:{type:"structure",members:{AttributeDefinitions:{shape:"S1f"},TableName:{},KeySchema:{shape:"S1j"},TableStatus:{},CreationDateTime:{type:"timestamp"},ProvisionedThroughput:{shape:"S23"},TableSizeBytes:{type:"long"},ItemCount:{type:"long"},TableArn:{},LocalSecondaryIndexes:{type:"list",member:{type:"structure",members:{IndexName:{},KeySchema:{shape:"S1j"},Projection:{shape:"S1o"},IndexSizeBytes:{type:"long"},ItemCount:{type:"long"},IndexArn:{}}}},GlobalSecondaryIndexes:{type:"list",member:{type:"structure",members:{IndexName:{},KeySchema:{shape:"S1j"},Projection:{shape:"S1o"},IndexStatus:{},Backfilling:{type:"boolean"},ProvisionedThroughput:{shape:"S23"},IndexSizeBytes:{type:"long"},ItemCount:{type:"long"},IndexArn:{}}}},StreamSpecification:{shape:"S1w"},LatestStreamLabel:{},LatestStreamArn:{}}},S23:{type:"structure",members:{LastIncreaseDateTime:{type:"timestamp"},LastDecreaseDateTime:{type:"timestamp"},NumberOfDecreasesToday:{type:"long"},ReadCapacityUnits:{type:"long"},WriteCapacityUnits:{type:"long"}}},S2e:{type:"map",key:{},value:{type:"structure",members:{Value:{shape:"S8"},Exists:{type:"boolean"},ComparisonOperator:{},AttributeValueList:{shape:"S2i"}}}},S2i:{type:"list",member:{shape:"S8"}},S2m:{type:"map",key:{},value:{shape:"S8"}},S35:{type:"structure",required:["ComparisonOperator"],members:{AttributeValueList:{shape:"S2i"},ComparisonOperator:{}}},S36:{type:"map",key:{},value:{shape:"S35"}}},examples:{},paginators:{BatchGetItem:{input_token:"RequestItems",output_token:"UnprocessedKeys"},ListTables:{input_token:"ExclusiveStartTableName",output_token:"LastEvaluatedTableName",limit_key:"Limit",result_key:"TableNames"},Query:{input_token:"ExclusiveStartKey",output_token:"LastEvaluatedKey",limit_key:"Limit",result_key:"Items"},Scan:{input_token:"ExclusiveStartKey",output_token:"LastEvaluatedKey",limit_key:"Limit",result_key:"Items"}},waiters:{__default__:{interval:20,max_attempts:25},__TableState:{operation:"DescribeTable"},TableExists:{"extends":"__TableState",ignore_errors:["ResourceNotFoundException"],success_type:"output",success_path:"Table.TableStatus",success_value:"ACTIVE"},TableNotExists:{"extends":"__TableState",success_type:"error",success_value:"ResourceNotFoundException"}}},a.apiLoader.services.dynamodbstreams={},a.DynamoDBStreams=a.Service.defineService("dynamodbstreams",["2012-08-10"]),a.apiLoader.services.dynamodbstreams["2012-08-10"]={version:"2.0",metadata:{apiVersion:"2012-08-10",endpointPrefix:"streams.dynamodb",jsonVersion:"1.0",serviceFullName:"Amazon DynamoDB Streams",signatureVersion:"v4",signingName:"dynamodb",targetPrefix:"DynamoDBStreams_20120810",protocol:"json"},operations:{DescribeStream:{input:{type:"structure",required:["StreamArn"],members:{StreamArn:{},Limit:{type:"integer"},ExclusiveStartShardId:{}}},output:{type:"structure",members:{StreamDescription:{type:"structure",members:{StreamArn:{},StreamLabel:{},StreamStatus:{},StreamViewType:{},CreationRequestDateTime:{type:"timestamp"},TableName:{},KeySchema:{type:"list",member:{type:"structure",required:["AttributeName","KeyType"],members:{AttributeName:{},KeyType:{}}}},Shards:{type:"list",member:{type:"structure",members:{ShardId:{},SequenceNumberRange:{type:"structure",members:{StartingSequenceNumber:{},EndingSequenceNumber:{}}},ParentShardId:{}}}},LastEvaluatedShardId:{}}}}},http:{}},GetRecords:{input:{type:"structure",required:["ShardIterator"],members:{ShardIterator:{},Limit:{type:"integer"}}},output:{type:"structure",members:{Records:{type:"list",member:{type:"structure",members:{eventID:{},eventName:{},eventVersion:{},eventSource:{},awsRegion:{},dynamodb:{type:"structure",members:{Keys:{shape:"Sr"},NewImage:{shape:"Sr"},OldImage:{shape:"Sr"},SequenceNumber:{},SizeBytes:{type:"long"},StreamViewType:{}}}}}},NextShardIterator:{}}},http:{}},GetShardIterator:{input:{type:"structure",required:["StreamArn","ShardId","ShardIteratorType"],members:{StreamArn:{},ShardId:{},ShardIteratorType:{},SequenceNumber:{}}},output:{type:"structure",members:{ShardIterator:{}}},http:{}},ListStreams:{input:{type:"structure",members:{TableName:{},Limit:{type:"integer"},ExclusiveStartStreamArn:{}}},output:{type:"structure",members:{Streams:{type:"list",member:{type:"structure",members:{StreamArn:{},TableName:{},StreamLabel:{}}}},LastEvaluatedStreamArn:{}}},http:{}}},shapes:{Sr:{type:"map",key:{},value:{shape:"St"}},St:{type:"structure",members:{S:{},N:{},B:{type:"blob"},SS:{type:"list",member:{}},NS:{type:"list",member:{}},BS:{type:"list",member:{type:"blob"}},M:{type:"map",key:{},value:{shape:"St"}},L:{type:"list",member:{shape:"St"}},NULL:{type:"boolean"},BOOL:{type:"boolean"}}}},examples:{}},a.apiLoader.services.ec2={},a.EC2=a.Service.defineService("ec2",["2015-04-15"]),e("./services/ec2"),a.apiLoader.services.ec2["2015-04-15"]={version:"2.0",metadata:{apiVersion:"2015-04-15",endpointPrefix:"ec2",serviceAbbreviation:"Amazon EC2",serviceFullName:"Amazon Elastic Compute Cloud",signatureVersion:"v4",xmlNamespace:"http://ec2.amazonaws.com/doc/2015-04-15",protocol:"ec2"},operations:{AcceptVpcPeeringConnection:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},output:{type:"structure",members:{VpcPeeringConnection:{shape:"S5",locationName:"vpcPeeringConnection"}}},http:{}},AllocateAddress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},Domain:{}}},output:{type:"structure",members:{PublicIp:{locationName:"publicIp"},Domain:{locationName:"domain"},AllocationId:{locationName:"allocationId"}}},http:{}},AssignPrivateIpAddresses:{input:{type:"structure",required:["NetworkInterfaceId"],members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},PrivateIpAddresses:{shape:"Sg",locationName:"privateIpAddress"},SecondaryPrivateIpAddressCount:{locationName:"secondaryPrivateIpAddressCount",type:"integer"},AllowReassignment:{locationName:"allowReassignment",type:"boolean"}}},http:{}},AssociateAddress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{},PublicIp:{},AllocationId:{},NetworkInterfaceId:{locationName:"networkInterfaceId"},PrivateIpAddress:{locationName:"privateIpAddress"},AllowReassociation:{locationName:"allowReassociation",type:"boolean"}}},output:{type:"structure",members:{AssociationId:{locationName:"associationId"}}},http:{}},AssociateDhcpOptions:{input:{type:"structure",required:["DhcpOptionsId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},DhcpOptionsId:{},VpcId:{}}},http:{}},AssociateRouteTable:{input:{type:"structure",required:["SubnetId","RouteTableId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SubnetId:{locationName:"subnetId"},RouteTableId:{locationName:"routeTableId"}}},output:{type:"structure",members:{AssociationId:{locationName:"associationId"}}},http:{}},AttachClassicLinkVpc:{input:{type:"structure",required:["InstanceId","VpcId","Groups"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},VpcId:{locationName:"vpcId"},Groups:{shape:"So",locationName:"SecurityGroupId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},AttachInternetGateway:{input:{type:"structure",required:["InternetGatewayId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InternetGatewayId:{locationName:"internetGatewayId"},VpcId:{locationName:"vpcId"}}},http:{}},AttachNetworkInterface:{input:{type:"structure",required:["NetworkInterfaceId","InstanceId","DeviceIndex"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceId:{locationName:"networkInterfaceId"},InstanceId:{locationName:"instanceId"},DeviceIndex:{locationName:"deviceIndex",type:"integer"}}},output:{type:"structure",members:{AttachmentId:{locationName:"attachmentId"}}},http:{}},AttachVolume:{input:{type:"structure",required:["VolumeId","InstanceId","Device"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{},InstanceId:{},Device:{}}},output:{shape:"Su",locationName:"attachment"},http:{}},AttachVpnGateway:{input:{type:"structure",required:["VpnGatewayId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnGatewayId:{},VpcId:{}}},output:{type:"structure",members:{VpcAttachment:{shape:"Sy",locationName:"attachment"}}},http:{}},AuthorizeSecurityGroupEgress:{input:{type:"structure",required:["GroupId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupId:{locationName:"groupId"},SourceSecurityGroupName:{locationName:"sourceSecurityGroupName"},SourceSecurityGroupOwnerId:{locationName:"sourceSecurityGroupOwnerId"},IpProtocol:{locationName:"ipProtocol"},FromPort:{locationName:"fromPort",type:"integer"},ToPort:{locationName:"toPort",type:"integer"},CidrIp:{locationName:"cidrIp"},IpPermissions:{shape:"S11",locationName:"ipPermissions"}}},http:{}},AuthorizeSecurityGroupIngress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{},GroupId:{},SourceSecurityGroupName:{},SourceSecurityGroupOwnerId:{},IpProtocol:{},FromPort:{type:"integer"},ToPort:{type:"integer"},CidrIp:{},IpPermissions:{shape:"S11"}}},http:{}},BundleInstance:{input:{type:"structure",required:["InstanceId","Storage"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{},Storage:{shape:"S1b"}}},output:{type:"structure",members:{BundleTask:{shape:"S1f",locationName:"bundleInstanceTask"}}},http:{}},CancelBundleTask:{input:{type:"structure",required:["BundleId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},BundleId:{}}},output:{type:"structure",members:{BundleTask:{shape:"S1f",locationName:"bundleInstanceTask"}}},http:{}},CancelConversionTask:{input:{type:"structure",required:["ConversionTaskId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ConversionTaskId:{locationName:"conversionTaskId"},ReasonMessage:{locationName:"reasonMessage"}}},http:{}},CancelExportTask:{input:{type:"structure",required:["ExportTaskId"],members:{ExportTaskId:{locationName:"exportTaskId"}}},http:{}},CancelImportTask:{input:{type:"structure",members:{DryRun:{type:"boolean"},ImportTaskId:{},CancelReason:{}}},output:{type:"structure",members:{ImportTaskId:{locationName:"importTaskId"},State:{locationName:"state"},PreviousState:{locationName:"previousState"}}},http:{}},CancelReservedInstancesListing:{input:{type:"structure",required:["ReservedInstancesListingId"],members:{ReservedInstancesListingId:{locationName:"reservedInstancesListingId"}}},output:{type:"structure",members:{ReservedInstancesListings:{shape:"S1q",locationName:"reservedInstancesListingsSet"}}},http:{}},CancelSpotFleetRequests:{input:{type:"structure",required:["SpotFleetRequestIds","TerminateInstances"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotFleetRequestIds:{shape:"S22",locationName:"spotFleetRequestId"},TerminateInstances:{locationName:"terminateInstances",type:"boolean"}}},output:{type:"structure",members:{UnsuccessfulFleetRequests:{locationName:"unsuccessfulFleetRequestSet",type:"list",member:{locationName:"item",type:"structure",required:["SpotFleetRequestId","Error"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"},Error:{locationName:"error",type:"structure",required:["Code","Message"],members:{Code:{locationName:"code"},Message:{locationName:"message"}}}}}},SuccessfulFleetRequests:{locationName:"successfulFleetRequestSet",type:"list",member:{locationName:"item",type:"structure",required:["SpotFleetRequestId","CurrentSpotFleetRequestState","PreviousSpotFleetRequestState"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"},CurrentSpotFleetRequestState:{locationName:"currentSpotFleetRequestState"},PreviousSpotFleetRequestState:{locationName:"previousSpotFleetRequestState"}}}}}},http:{}},CancelSpotInstanceRequests:{input:{type:"structure",required:["SpotInstanceRequestIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotInstanceRequestIds:{shape:"S2c",locationName:"SpotInstanceRequestId"}}},output:{type:"structure",members:{CancelledSpotInstanceRequests:{locationName:"spotInstanceRequestSet",type:"list",member:{locationName:"item",type:"structure",members:{SpotInstanceRequestId:{locationName:"spotInstanceRequestId"},State:{locationName:"state"}}}}}},http:{}},ConfirmProductInstance:{input:{type:"structure",required:["ProductCode","InstanceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ProductCode:{},InstanceId:{}}},output:{type:"structure",members:{OwnerId:{locationName:"ownerId"},Return:{locationName:"return",type:"boolean"}}},http:{}},CopyImage:{input:{type:"structure",required:["SourceRegion","SourceImageId","Name"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SourceRegion:{},SourceImageId:{},Name:{},Description:{},ClientToken:{}}},output:{type:"structure",members:{ImageId:{locationName:"imageId"}}},http:{}},CopySnapshot:{input:{type:"structure",required:["SourceRegion","SourceSnapshotId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SourceRegion:{},SourceSnapshotId:{},Description:{},DestinationRegion:{locationName:"destinationRegion"},PresignedUrl:{locationName:"presignedUrl"},Encrypted:{locationName:"encrypted",type:"boolean"},KmsKeyId:{locationName:"kmsKeyId"}}},output:{type:"structure",members:{SnapshotId:{locationName:"snapshotId"}}},http:{}},CreateCustomerGateway:{input:{type:"structure",required:["Type","PublicIp","BgpAsn"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Type:{},PublicIp:{locationName:"IpAddress"},BgpAsn:{type:"integer"}}},output:{type:"structure",members:{CustomerGateway:{shape:"S2q",locationName:"customerGateway"}}},http:{}},CreateDhcpOptions:{input:{type:"structure",required:["DhcpConfigurations"],members:{DryRun:{locationName:"dryRun",type:"boolean"},DhcpConfigurations:{locationName:"dhcpConfiguration",type:"list",member:{locationName:"item",type:"structure",members:{Key:{locationName:"key"},Values:{shape:"S22",locationName:"Value"}}}}}},output:{type:"structure",members:{DhcpOptions:{shape:"S2v",locationName:"dhcpOptions"}}},http:{}},CreateFlowLogs:{input:{type:"structure",required:["ResourceIds","ResourceType","TrafficType","LogGroupName","DeliverLogsPermissionArn"],members:{ResourceIds:{shape:"S22",locationName:"ResourceId"},ResourceType:{},TrafficType:{},LogGroupName:{},DeliverLogsPermissionArn:{},ClientToken:{}}},output:{type:"structure",members:{FlowLogIds:{shape:"S22",locationName:"flowLogIdSet"},ClientToken:{locationName:"clientToken"},Unsuccessful:{shape:"S34",locationName:"unsuccessful"}}},http:{}},CreateImage:{input:{type:"structure",required:["InstanceId","Name"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},Name:{locationName:"name"},Description:{locationName:"description"},NoReboot:{locationName:"noReboot",type:"boolean"},BlockDeviceMappings:{shape:"S38",locationName:"blockDeviceMapping"}}},output:{type:"structure",members:{ImageId:{locationName:"imageId"}}},http:{}},CreateInstanceExportTask:{input:{type:"structure",required:["InstanceId"],members:{Description:{locationName:"description"},InstanceId:{locationName:"instanceId"},TargetEnvironment:{locationName:"targetEnvironment"},ExportToS3Task:{locationName:"exportToS3",type:"structure",members:{DiskImageFormat:{locationName:"diskImageFormat"},ContainerFormat:{locationName:"containerFormat"},S3Bucket:{locationName:"s3Bucket"},S3Prefix:{locationName:"s3Prefix"}}}}},output:{type:"structure",members:{ExportTask:{shape:"S3j",locationName:"exportTask"}}},http:{}},CreateInternetGateway:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"}}},output:{type:"structure",members:{InternetGateway:{shape:"S3p",locationName:"internetGateway"}}},http:{}},CreateKeyPair:{input:{type:"structure",required:["KeyName"],members:{DryRun:{locationName:"dryRun",type:"boolean"},KeyName:{}}},output:{locationName:"keyPair",type:"structure",members:{KeyName:{locationName:"keyName"},KeyFingerprint:{locationName:"keyFingerprint"},KeyMaterial:{locationName:"keyMaterial"}}},http:{}},CreateNetworkAcl:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{locationName:"vpcId"}}},output:{type:"structure",members:{NetworkAcl:{shape:"S3w",locationName:"networkAcl"}}},http:{}},CreateNetworkAclEntry:{input:{type:"structure",required:["NetworkAclId","RuleNumber","Protocol","RuleAction","Egress","CidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkAclId:{locationName:"networkAclId"},RuleNumber:{locationName:"ruleNumber",type:"integer"},Protocol:{locationName:"protocol"},RuleAction:{locationName:"ruleAction"},Egress:{locationName:"egress",type:"boolean"},CidrBlock:{locationName:"cidrBlock"},IcmpTypeCode:{shape:"S40",locationName:"Icmp"},PortRange:{shape:"S41",locationName:"portRange"}}},http:{}},CreateNetworkInterface:{input:{type:"structure",required:["SubnetId"],members:{SubnetId:{locationName:"subnetId"},Description:{locationName:"description"},PrivateIpAddress:{locationName:"privateIpAddress"},Groups:{shape:"S46",locationName:"SecurityGroupId"},PrivateIpAddresses:{shape:"S47",locationName:"privateIpAddresses"},SecondaryPrivateIpAddressCount:{locationName:"secondaryPrivateIpAddressCount",type:"integer"},DryRun:{locationName:"dryRun",type:"boolean"}}},output:{type:"structure",members:{NetworkInterface:{shape:"S4a",locationName:"networkInterface"}}},http:{}},CreatePlacementGroup:{input:{type:"structure",required:["GroupName","Strategy"],members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{locationName:"groupName"},Strategy:{locationName:"strategy"}}},http:{}},CreateReservedInstancesListing:{input:{type:"structure",required:["ReservedInstancesId","InstanceCount","PriceSchedules","ClientToken"],members:{ReservedInstancesId:{locationName:"reservedInstancesId"},InstanceCount:{locationName:"instanceCount",type:"integer"},PriceSchedules:{locationName:"priceSchedules",type:"list",member:{locationName:"item",type:"structure",members:{Term:{locationName:"term",type:"long"},Price:{locationName:"price",type:"double"},CurrencyCode:{locationName:"currencyCode"}}}},ClientToken:{locationName:"clientToken"}}},output:{type:"structure",members:{ReservedInstancesListings:{shape:"S1q",locationName:"reservedInstancesListingsSet"}}},http:{}},CreateRoute:{input:{type:"structure",required:["RouteTableId","DestinationCidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},RouteTableId:{locationName:"routeTableId"},DestinationCidrBlock:{locationName:"destinationCidrBlock"},GatewayId:{locationName:"gatewayId"},InstanceId:{locationName:"instanceId"},NetworkInterfaceId:{locationName:"networkInterfaceId"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},CreateRouteTable:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{locationName:"vpcId"}}},output:{type:"structure",members:{RouteTable:{shape:"S4s",locationName:"routeTable"}}},http:{}},CreateSecurityGroup:{input:{type:"structure",required:["GroupName","Description"],members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{},Description:{locationName:"GroupDescription"},VpcId:{}}},output:{type:"structure",members:{GroupId:{locationName:"groupId"}}},http:{}},CreateSnapshot:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{},Description:{}}},output:{shape:"S54",locationName:"snapshot"},http:{}},CreateSpotDatafeedSubscription:{input:{type:"structure",required:["Bucket"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Bucket:{locationName:"bucket"},Prefix:{locationName:"prefix"}}},output:{type:"structure",members:{SpotDatafeedSubscription:{shape:"S58",locationName:"spotDatafeedSubscription"}}},http:{}},CreateSubnet:{input:{type:"structure",required:["VpcId","CidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{},CidrBlock:{},AvailabilityZone:{}}},output:{type:"structure",members:{Subnet:{shape:"S5d",locationName:"subnet"}}},http:{}
},CreateTags:{input:{type:"structure",required:["Resources","Tags"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Resources:{shape:"S5g",locationName:"ResourceId"},Tags:{shape:"Sa",locationName:"Tag"}}},http:{}},CreateVolume:{input:{type:"structure",required:["AvailabilityZone"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Size:{type:"integer"},SnapshotId:{},AvailabilityZone:{},VolumeType:{},Iops:{type:"integer"},Encrypted:{locationName:"encrypted",type:"boolean"},KmsKeyId:{}}},output:{shape:"S5i",locationName:"volume"},http:{}},CreateVpc:{input:{type:"structure",required:["CidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},CidrBlock:{},InstanceTenancy:{locationName:"instanceTenancy"}}},output:{type:"structure",members:{Vpc:{shape:"S5o",locationName:"vpc"}}},http:{}},CreateVpcEndpoint:{input:{type:"structure",required:["VpcId","ServiceName"],members:{DryRun:{type:"boolean"},VpcId:{},ServiceName:{},PolicyDocument:{},RouteTableIds:{shape:"S22",locationName:"RouteTableId"},ClientToken:{}}},output:{type:"structure",members:{VpcEndpoint:{shape:"S5s",locationName:"vpcEndpoint"},ClientToken:{locationName:"clientToken"}}},http:{}},CreateVpcPeeringConnection:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{locationName:"vpcId"},PeerVpcId:{locationName:"peerVpcId"},PeerOwnerId:{locationName:"peerOwnerId"}}},output:{type:"structure",members:{VpcPeeringConnection:{shape:"S5",locationName:"vpcPeeringConnection"}}},http:{}},CreateVpnConnection:{input:{type:"structure",required:["Type","CustomerGatewayId","VpnGatewayId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Type:{},CustomerGatewayId:{},VpnGatewayId:{},Options:{locationName:"options",type:"structure",members:{StaticRoutesOnly:{locationName:"staticRoutesOnly",type:"boolean"}}}}},output:{type:"structure",members:{VpnConnection:{shape:"S5z",locationName:"vpnConnection"}}},http:{}},CreateVpnConnectionRoute:{input:{type:"structure",required:["VpnConnectionId","DestinationCidrBlock"],members:{VpnConnectionId:{},DestinationCidrBlock:{}}},http:{}},CreateVpnGateway:{input:{type:"structure",required:["Type"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Type:{},AvailabilityZone:{}}},output:{type:"structure",members:{VpnGateway:{shape:"S6b",locationName:"vpnGateway"}}},http:{}},DeleteCustomerGateway:{input:{type:"structure",required:["CustomerGatewayId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},CustomerGatewayId:{}}},http:{}},DeleteDhcpOptions:{input:{type:"structure",required:["DhcpOptionsId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},DhcpOptionsId:{}}},http:{}},DeleteFlowLogs:{input:{type:"structure",required:["FlowLogIds"],members:{FlowLogIds:{shape:"S22",locationName:"FlowLogId"}}},output:{type:"structure",members:{Unsuccessful:{shape:"S34",locationName:"unsuccessful"}}},http:{}},DeleteInternetGateway:{input:{type:"structure",required:["InternetGatewayId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InternetGatewayId:{locationName:"internetGatewayId"}}},http:{}},DeleteKeyPair:{input:{type:"structure",required:["KeyName"],members:{DryRun:{locationName:"dryRun",type:"boolean"},KeyName:{}}},http:{}},DeleteNetworkAcl:{input:{type:"structure",required:["NetworkAclId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkAclId:{locationName:"networkAclId"}}},http:{}},DeleteNetworkAclEntry:{input:{type:"structure",required:["NetworkAclId","RuleNumber","Egress"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkAclId:{locationName:"networkAclId"},RuleNumber:{locationName:"ruleNumber",type:"integer"},Egress:{locationName:"egress",type:"boolean"}}},http:{}},DeleteNetworkInterface:{input:{type:"structure",required:["NetworkInterfaceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceId:{locationName:"networkInterfaceId"}}},http:{}},DeletePlacementGroup:{input:{type:"structure",required:["GroupName"],members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{locationName:"groupName"}}},http:{}},DeleteRoute:{input:{type:"structure",required:["RouteTableId","DestinationCidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},RouteTableId:{locationName:"routeTableId"},DestinationCidrBlock:{locationName:"destinationCidrBlock"}}},http:{}},DeleteRouteTable:{input:{type:"structure",required:["RouteTableId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},RouteTableId:{locationName:"routeTableId"}}},http:{}},DeleteSecurityGroup:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{},GroupId:{}}},http:{}},DeleteSnapshot:{input:{type:"structure",required:["SnapshotId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SnapshotId:{}}},http:{}},DeleteSpotDatafeedSubscription:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"}}},http:{}},DeleteSubnet:{input:{type:"structure",required:["SubnetId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SubnetId:{}}},http:{}},DeleteTags:{input:{type:"structure",required:["Resources"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Resources:{shape:"S5g",locationName:"resourceId"},Tags:{shape:"Sa",locationName:"tag"}}},http:{}},DeleteVolume:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{}}},http:{}},DeleteVpc:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{}}},http:{}},DeleteVpcEndpoints:{input:{type:"structure",required:["VpcEndpointIds"],members:{DryRun:{type:"boolean"},VpcEndpointIds:{shape:"S22",locationName:"VpcEndpointId"}}},output:{type:"structure",members:{Unsuccessful:{shape:"S34",locationName:"unsuccessful"}}},http:{}},DeleteVpcPeeringConnection:{input:{type:"structure",required:["VpcPeeringConnectionId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},DeleteVpnConnection:{input:{type:"structure",required:["VpnConnectionId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnConnectionId:{}}},http:{}},DeleteVpnConnectionRoute:{input:{type:"structure",required:["VpnConnectionId","DestinationCidrBlock"],members:{VpnConnectionId:{},DestinationCidrBlock:{}}},http:{}},DeleteVpnGateway:{input:{type:"structure",required:["VpnGatewayId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnGatewayId:{}}},http:{}},DeregisterImage:{input:{type:"structure",required:["ImageId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageId:{}}},http:{}},DescribeAccountAttributes:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},AttributeNames:{locationName:"attributeName",type:"list",member:{locationName:"attributeName"}}}},output:{type:"structure",members:{AccountAttributes:{locationName:"accountAttributeSet",type:"list",member:{locationName:"item",type:"structure",members:{AttributeName:{locationName:"attributeName"},AttributeValues:{locationName:"attributeValueSet",type:"list",member:{locationName:"item",type:"structure",members:{AttributeValue:{locationName:"attributeValue"}}}}}}}}},http:{}},DescribeAddresses:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIps:{locationName:"PublicIp",type:"list",member:{locationName:"PublicIp"}},Filters:{shape:"S7e",locationName:"Filter"},AllocationIds:{locationName:"AllocationId",type:"list",member:{locationName:"AllocationId"}}}},output:{type:"structure",members:{Addresses:{locationName:"addressesSet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},PublicIp:{locationName:"publicIp"},AllocationId:{locationName:"allocationId"},AssociationId:{locationName:"associationId"},Domain:{locationName:"domain"},NetworkInterfaceId:{locationName:"networkInterfaceId"},NetworkInterfaceOwnerId:{locationName:"networkInterfaceOwnerId"},PrivateIpAddress:{locationName:"privateIpAddress"}}}}}},http:{}},DescribeAvailabilityZones:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},ZoneNames:{locationName:"ZoneName",type:"list",member:{locationName:"ZoneName"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{AvailabilityZones:{locationName:"availabilityZoneInfo",type:"list",member:{locationName:"item",type:"structure",members:{ZoneName:{locationName:"zoneName"},State:{locationName:"zoneState"},RegionName:{locationName:"regionName"},Messages:{locationName:"messageSet",type:"list",member:{locationName:"item",type:"structure",members:{Message:{locationName:"message"}}}}}}}}},http:{}},DescribeBundleTasks:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},BundleIds:{locationName:"BundleId",type:"list",member:{locationName:"BundleId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{BundleTasks:{locationName:"bundleInstanceTasksSet",type:"list",member:{shape:"S1f",locationName:"item"}}}},http:{}},DescribeClassicLinkInstances:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{Instances:{locationName:"instancesSet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},VpcId:{locationName:"vpcId"},Groups:{shape:"S4c",locationName:"groupSet"},Tags:{shape:"Sa",locationName:"tagSet"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeConversionTasks:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},Filters:{shape:"S7e",locationName:"filter"},ConversionTaskIds:{locationName:"conversionTaskId",type:"list",member:{locationName:"item"}}}},output:{type:"structure",members:{ConversionTasks:{locationName:"conversionTasks",type:"list",member:{shape:"S85",locationName:"item"}}}},http:{}},DescribeCustomerGateways:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},CustomerGatewayIds:{locationName:"CustomerGatewayId",type:"list",member:{locationName:"CustomerGatewayId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{CustomerGateways:{locationName:"customerGatewaySet",type:"list",member:{shape:"S2q",locationName:"item"}}}},http:{}},DescribeDhcpOptions:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},DhcpOptionsIds:{locationName:"DhcpOptionsId",type:"list",member:{locationName:"DhcpOptionsId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{DhcpOptions:{locationName:"dhcpOptionsSet",type:"list",member:{shape:"S2v",locationName:"item"}}}},http:{}},DescribeExportTasks:{input:{type:"structure",members:{ExportTaskIds:{locationName:"exportTaskId",type:"list",member:{locationName:"ExportTaskId"}}}},output:{type:"structure",members:{ExportTasks:{locationName:"exportTaskSet",type:"list",member:{shape:"S3j",locationName:"item"}}}},http:{}},DescribeFlowLogs:{input:{type:"structure",members:{FlowLogIds:{shape:"S22",locationName:"FlowLogId"},Filter:{shape:"S7e"},NextToken:{},MaxResults:{type:"integer"}}},output:{type:"structure",members:{FlowLogs:{locationName:"flowLogSet",type:"list",member:{locationName:"item",type:"structure",members:{CreationTime:{locationName:"creationTime",type:"timestamp"},FlowLogId:{locationName:"flowLogId"},FlowLogStatus:{locationName:"flowLogStatus"},ResourceId:{locationName:"resourceId"},TrafficType:{locationName:"trafficType"},LogGroupName:{locationName:"logGroupName"},DeliverLogsStatus:{locationName:"deliverLogsStatus"},DeliverLogsErrorMessage:{locationName:"deliverLogsErrorMessage"},DeliverLogsPermissionArn:{locationName:"deliverLogsPermissionArn"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeImageAttribute:{input:{type:"structure",required:["ImageId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageId:{},Attribute:{}}},output:{locationName:"imageAttribute",type:"structure",members:{ImageId:{locationName:"imageId"},LaunchPermissions:{shape:"S8x",locationName:"launchPermission"},ProductCodes:{shape:"S90",locationName:"productCodes"},KernelId:{shape:"S2z",locationName:"kernel"},RamdiskId:{shape:"S2z",locationName:"ramdisk"},Description:{shape:"S2z",locationName:"description"},SriovNetSupport:{shape:"S2z",locationName:"sriovNetSupport"},BlockDeviceMappings:{shape:"S93",locationName:"blockDeviceMapping"}}},http:{}},DescribeImages:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageIds:{locationName:"ImageId",type:"list",member:{locationName:"ImageId"}},Owners:{shape:"S96",locationName:"Owner"},ExecutableUsers:{locationName:"ExecutableBy",type:"list",member:{locationName:"ExecutableBy"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{Images:{locationName:"imagesSet",type:"list",member:{locationName:"item",type:"structure",members:{ImageId:{locationName:"imageId"},ImageLocation:{locationName:"imageLocation"},State:{locationName:"imageState"},OwnerId:{locationName:"imageOwnerId"},CreationDate:{locationName:"creationDate"},Public:{locationName:"isPublic",type:"boolean"},ProductCodes:{shape:"S90",locationName:"productCodes"},Architecture:{locationName:"architecture"},ImageType:{locationName:"imageType"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},Platform:{locationName:"platform"},SriovNetSupport:{locationName:"sriovNetSupport"},StateReason:{shape:"S9e",locationName:"stateReason"},ImageOwnerAlias:{locationName:"imageOwnerAlias"},Name:{locationName:"name"},Description:{locationName:"description"},RootDeviceType:{locationName:"rootDeviceType"},RootDeviceName:{locationName:"rootDeviceName"},BlockDeviceMappings:{shape:"S93",locationName:"blockDeviceMapping"},VirtualizationType:{locationName:"virtualizationType"},Tags:{shape:"Sa",locationName:"tagSet"},Hypervisor:{locationName:"hypervisor"}}}}}},http:{}},DescribeImportImageTasks:{input:{type:"structure",members:{DryRun:{type:"boolean"},ImportTaskIds:{shape:"S9j",locationName:"ImportTaskId"},NextToken:{},MaxResults:{type:"integer"},Filters:{shape:"S7e"}}},output:{type:"structure",members:{ImportImageTasks:{locationName:"importImageTaskSet",type:"list",member:{locationName:"item",type:"structure",members:{ImportTaskId:{locationName:"importTaskId"},Architecture:{locationName:"architecture"},LicenseType:{locationName:"licenseType"},Platform:{locationName:"platform"},Hypervisor:{locationName:"hypervisor"},Description:{locationName:"description"},SnapshotDetails:{shape:"S9n",locationName:"snapshotDetailSet"},ImageId:{locationName:"imageId"},Progress:{locationName:"progress"},StatusMessage:{locationName:"statusMessage"},Status:{locationName:"status"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeImportSnapshotTasks:{input:{type:"structure",members:{DryRun:{type:"boolean"},ImportTaskIds:{shape:"S9j",locationName:"ImportTaskId"},NextToken:{},MaxResults:{type:"integer"},Filters:{shape:"S7e"}}},output:{type:"structure",members:{ImportSnapshotTasks:{locationName:"importSnapshotTaskSet",type:"list",member:{locationName:"item",type:"structure",members:{ImportTaskId:{locationName:"importTaskId"},SnapshotTaskDetail:{shape:"S9u",locationName:"snapshotTaskDetail"},Description:{locationName:"description"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeInstanceAttribute:{input:{type:"structure",required:["InstanceId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},Attribute:{locationName:"attribute"}}},output:{type:"structure",members:{InstanceId:{locationName:"instanceId"},InstanceType:{shape:"S2z",locationName:"instanceType"},KernelId:{shape:"S2z",locationName:"kernel"},RamdiskId:{shape:"S2z",locationName:"ramdisk"},UserData:{shape:"S2z",locationName:"userData"},DisableApiTermination:{shape:"S9y",locationName:"disableApiTermination"},InstanceInitiatedShutdownBehavior:{shape:"S2z",locationName:"instanceInitiatedShutdownBehavior"},RootDeviceName:{shape:"S2z",locationName:"rootDeviceName"},BlockDeviceMappings:{shape:"S9z",locationName:"blockDeviceMapping"},ProductCodes:{shape:"S90",locationName:"productCodes"},EbsOptimized:{shape:"S9y",locationName:"ebsOptimized"},SriovNetSupport:{shape:"S2z",locationName:"sriovNetSupport"},SourceDestCheck:{shape:"S9y",locationName:"sourceDestCheck"},Groups:{shape:"S4c",locationName:"groupSet"}}},http:{}},DescribeInstanceStatus:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{},MaxResults:{type:"integer"},IncludeAllInstances:{locationName:"includeAllInstances",type:"boolean"}}},output:{type:"structure",members:{InstanceStatuses:{locationName:"instanceStatusSet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},AvailabilityZone:{locationName:"availabilityZone"},Events:{locationName:"eventsSet",type:"list",member:{locationName:"item",type:"structure",members:{Code:{locationName:"code"},Description:{locationName:"description"},NotBefore:{locationName:"notBefore",type:"timestamp"},NotAfter:{locationName:"notAfter",type:"timestamp"}}}},InstanceState:{shape:"Sa9",locationName:"instanceState"},SystemStatus:{shape:"Sab",locationName:"systemStatus"},InstanceStatus:{shape:"Sab",locationName:"instanceStatus"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeInstances:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{Reservations:{locationName:"reservationSet",type:"list",member:{shape:"Sak",locationName:"item"}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeInternetGateways:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},InternetGatewayIds:{shape:"S22",locationName:"internetGatewayId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{InternetGateways:{locationName:"internetGatewaySet",type:"list",member:{shape:"S3p",locationName:"item"}}}},http:{}},DescribeKeyPairs:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},KeyNames:{locationName:"KeyName",type:"list",member:{locationName:"KeyName"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{KeyPairs:{locationName:"keySet",type:"list",member:{locationName:"item",type:"structure",members:{KeyName:{locationName:"keyName"},KeyFingerprint:{locationName:"keyFingerprint"}}}}}},http:{}},DescribeMovingAddresses:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIps:{shape:"S22",locationName:"publicIp"},NextToken:{locationName:"nextToken"},Filters:{shape:"S7e",locationName:"filter"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{MovingAddressStatuses:{locationName:"movingAddressStatusSet",type:"list",member:{locationName:"item",type:"structure",members:{PublicIp:{locationName:"publicIp"},MoveStatus:{locationName:"moveStatus"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeNetworkAcls:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkAclIds:{shape:"S22",locationName:"NetworkAclId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{NetworkAcls:{locationName:"networkAclSet",type:"list",member:{shape:"S3w",locationName:"item"}}}},http:{}},DescribeNetworkInterfaceAttribute:{input:{type:"structure",required:["NetworkInterfaceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceId:{locationName:"networkInterfaceId"},Attribute:{locationName:"attribute"}}},output:{type:"structure",members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},Description:{shape:"S2z",locationName:"description"},SourceDestCheck:{shape:"S9y",locationName:"sourceDestCheck"},Groups:{shape:"S4c",locationName:"groupSet"},Attachment:{shape:"S4e",locationName:"attachment"}}},http:{}},DescribeNetworkInterfaces:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceIds:{locationName:"NetworkInterfaceId",type:"list",member:{locationName:"item"}},Filters:{shape:"S7e",locationName:"filter"}}},output:{type:"structure",members:{NetworkInterfaces:{locationName:"networkInterfaceSet",type:"list",member:{shape:"S4a",locationName:"item"}}}},http:{}},DescribePlacementGroups:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupNames:{locationName:"groupName",type:"list",member:{}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{PlacementGroups:{locationName:"placementGroupSet",type:"list",member:{locationName:"item",type:"structure",members:{GroupName:{locationName:"groupName"},Strategy:{locationName:"strategy"},State:{locationName:"state"}}}}}},http:{}},DescribePrefixLists:{input:{type:"structure",members:{DryRun:{type:"boolean"},PrefixListIds:{shape:"S22",locationName:"PrefixListId"},Filters:{shape:"S7e",locationName:"Filter"},MaxResults:{type:"integer"},NextToken:{}}},output:{type:"structure",members:{PrefixLists:{locationName:"prefixListSet",type:"list",member:{locationName:"item",type:"structure",members:{PrefixListId:{locationName:"prefixListId"},PrefixListName:{locationName:"prefixListName"},Cidrs:{shape:"S22",locationName:"cidrSet"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeRegions:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},RegionNames:{locationName:"RegionName",type:"list",member:{locationName:"RegionName"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{Regions:{locationName:"regionInfo",type:"list",member:{locationName:"item",type:"structure",members:{RegionName:{locationName:"regionName"},Endpoint:{locationName:"regionEndpoint"}}}}}},http:{}},DescribeReservedInstances:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},ReservedInstancesIds:{shape:"Sc2",locationName:"ReservedInstancesId"},Filters:{shape:"S7e",locationName:"Filter"},OfferingType:{locationName:"offeringType"}}},output:{type:"structure",members:{ReservedInstances:{locationName:"reservedInstancesSet",type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesId:{locationName:"reservedInstancesId"},InstanceType:{locationName:"instanceType"},AvailabilityZone:{locationName:"availabilityZone"},Start:{locationName:"start",type:"timestamp"},End:{locationName:"end",type:"timestamp"},Duration:{locationName:"duration",type:"long"},UsagePrice:{locationName:"usagePrice",type:"float"},FixedPrice:{locationName:"fixedPrice",type:"float"},InstanceCount:{locationName:"instanceCount",type:"integer"},ProductDescription:{locationName:"productDescription"},State:{locationName:"state"},Tags:{shape:"Sa",locationName:"tagSet"},InstanceTenancy:{locationName:"instanceTenancy"},CurrencyCode:{locationName:"currencyCode"},OfferingType:{locationName:"offeringType"},RecurringCharges:{shape:"Sca",locationName:"recurringCharges"}}}}}},http:{}},DescribeReservedInstancesListings:{input:{type:"structure",members:{ReservedInstancesId:{locationName:"reservedInstancesId"},ReservedInstancesListingId:{locationName:"reservedInstancesListingId"},Filters:{shape:"S7e",locationName:"filters"}}},output:{type:"structure",members:{ReservedInstancesListings:{shape:"S1q",locationName:"reservedInstancesListingsSet"}}},http:{}},DescribeReservedInstancesModifications:{input:{type:"structure",members:{ReservedInstancesModificationIds:{locationName:"ReservedInstancesModificationId",type:"list",member:{locationName:"ReservedInstancesModificationId"}},NextToken:{locationName:"nextToken"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{ReservedInstancesModifications:{locationName:"reservedInstancesModificationsSet",type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesModificationId:{locationName:"reservedInstancesModificationId"},ReservedInstancesIds:{locationName:"reservedInstancesSet",type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesId:{locationName:"reservedInstancesId"}}}},ModificationResults:{locationName:"modificationResultSet",type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesId:{locationName:"reservedInstancesId"},TargetConfiguration:{shape:"Sco",locationName:"targetConfiguration"}}}},CreateDate:{locationName:"createDate",type:"timestamp"},UpdateDate:{locationName:"updateDate",type:"timestamp"},EffectiveDate:{locationName:"effectiveDate",type:"timestamp"},Status:{locationName:"status"},StatusMessage:{locationName:"statusMessage"},ClientToken:{locationName:"clientToken"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeReservedInstancesOfferings:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},ReservedInstancesOfferingIds:{locationName:"ReservedInstancesOfferingId",type:"list",member:{}},InstanceType:{},AvailabilityZone:{},ProductDescription:{},Filters:{shape:"S7e",locationName:"Filter"},InstanceTenancy:{locationName:"instanceTenancy"},OfferingType:{locationName:"offeringType"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"},IncludeMarketplace:{type:"boolean"},MinDuration:{type:"long"},MaxDuration:{type:"long"},MaxInstanceCount:{type:"integer"}}},output:{type:"structure",members:{ReservedInstancesOfferings:{locationName:"reservedInstancesOfferingsSet",type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesOfferingId:{locationName:"reservedInstancesOfferingId"},InstanceType:{locationName:"instanceType"},AvailabilityZone:{locationName:"availabilityZone"},Duration:{locationName:"duration",type:"long"},UsagePrice:{locationName:"usagePrice",type:"float"},FixedPrice:{locationName:"fixedPrice",type:"float"},ProductDescription:{locationName:"productDescription"},InstanceTenancy:{locationName:"instanceTenancy"},CurrencyCode:{locationName:"currencyCode"},OfferingType:{locationName:"offeringType"},RecurringCharges:{shape:"Sca",locationName:"recurringCharges"},Marketplace:{locationName:"marketplace",type:"boolean"},PricingDetails:{locationName:"pricingDetailsSet",type:"list",member:{locationName:"item",type:"structure",members:{Price:{locationName:"price",type:"double"},Count:{locationName:"count",type:"integer"}}}}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeRouteTables:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},RouteTableIds:{shape:"S22",locationName:"RouteTableId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{RouteTables:{locationName:"routeTableSet",type:"list",member:{shape:"S4s",locationName:"item"}}}},http:{}},DescribeSecurityGroups:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupNames:{shape:"Sd0",locationName:"GroupName"},GroupIds:{shape:"So",locationName:"GroupId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{SecurityGroups:{locationName:"securityGroupInfo",type:"list",member:{locationName:"item",type:"structure",members:{OwnerId:{locationName:"ownerId"},GroupName:{locationName:"groupName"},GroupId:{locationName:"groupId"},Description:{locationName:"groupDescription"},IpPermissions:{shape:"S11",locationName:"ipPermissions"},IpPermissionsEgress:{shape:"S11",locationName:"ipPermissionsEgress"},VpcId:{locationName:"vpcId"},Tags:{shape:"Sa",locationName:"tagSet"}}}}}},http:{}},DescribeSnapshotAttribute:{input:{type:"structure",required:["SnapshotId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SnapshotId:{},Attribute:{}}},output:{type:"structure",members:{SnapshotId:{locationName:"snapshotId"},CreateVolumePermissions:{shape:"Sd7",locationName:"createVolumePermission"},ProductCodes:{shape:"S90",locationName:"productCodes"}}},http:{}},DescribeSnapshots:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},SnapshotIds:{locationName:"SnapshotId",type:"list",member:{locationName:"SnapshotId"}},OwnerIds:{shape:"S96",locationName:"Owner"},RestorableByUserIds:{locationName:"RestorableBy",type:"list",member:{}},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{},MaxResults:{type:"integer"}}},output:{type:"structure",members:{Snapshots:{locationName:"snapshotSet",type:"list",member:{shape:"S54",locationName:"item"}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeSpotDatafeedSubscription:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"}}},output:{type:"structure",members:{SpotDatafeedSubscription:{shape:"S58",locationName:"spotDatafeedSubscription"}}},http:{}},DescribeSpotFleetInstances:{input:{type:"structure",required:["SpotFleetRequestId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotFleetRequestId:{locationName:"spotFleetRequestId"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",required:["SpotFleetRequestId","ActiveInstances"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"},ActiveInstances:{locationName:"activeInstanceSet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceType:{locationName:"instanceType"},InstanceId:{locationName:"instanceId"},SpotInstanceRequestId:{locationName:"spotInstanceRequestId"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeSpotFleetRequestHistory:{input:{type:"structure",required:["SpotFleetRequestId","StartTime"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotFleetRequestId:{locationName:"spotFleetRequestId"},EventType:{locationName:"eventType"},StartTime:{locationName:"startTime",type:"timestamp"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",required:["SpotFleetRequestId","StartTime","LastEvaluatedTime","HistoryRecords"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"},StartTime:{locationName:"startTime",type:"timestamp"},LastEvaluatedTime:{locationName:"lastEvaluatedTime",type:"timestamp"},HistoryRecords:{locationName:"historyRecordSet",type:"list",member:{locationName:"item",type:"structure",required:["Timestamp","EventType","EventInformation"],members:{Timestamp:{locationName:"timestamp",type:"timestamp"},EventType:{locationName:"eventType"},EventInformation:{locationName:"eventInformation",type:"structure",members:{InstanceId:{locationName:"instanceId"},EventSubType:{locationName:"eventSubType"},EventDescription:{locationName:"eventDescription"}}}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeSpotFleetRequests:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotFleetRequestIds:{shape:"S22",locationName:"spotFleetRequestId"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",required:["SpotFleetRequestConfigs"],members:{SpotFleetRequestConfigs:{locationName:"spotFleetRequestConfigSet",type:"list",member:{locationName:"item",type:"structure",required:["SpotFleetRequestId","SpotFleetRequestState","SpotFleetRequestConfig"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"},SpotFleetRequestState:{locationName:"spotFleetRequestState"},SpotFleetRequestConfig:{shape:"Sdu",locationName:"spotFleetRequestConfig"}}}},NextToken:{locationName:"nextToken"
}}},http:{}},DescribeSpotInstanceRequests:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotInstanceRequestIds:{shape:"S2c",locationName:"SpotInstanceRequestId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{SpotInstanceRequests:{shape:"Se4",locationName:"spotInstanceRequestSet"}}},http:{}},DescribeSpotPriceHistory:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},StartTime:{locationName:"startTime",type:"timestamp"},EndTime:{locationName:"endTime",type:"timestamp"},InstanceTypes:{locationName:"InstanceType",type:"list",member:{}},ProductDescriptions:{locationName:"ProductDescription",type:"list",member:{}},Filters:{shape:"S7e",locationName:"Filter"},AvailabilityZone:{locationName:"availabilityZone"},MaxResults:{locationName:"maxResults",type:"integer"},NextToken:{locationName:"nextToken"}}},output:{type:"structure",members:{SpotPriceHistory:{locationName:"spotPriceHistorySet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceType:{locationName:"instanceType"},ProductDescription:{locationName:"productDescription"},SpotPrice:{locationName:"spotPrice"},Timestamp:{locationName:"timestamp",type:"timestamp"},AvailabilityZone:{locationName:"availabilityZone"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeSubnets:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},SubnetIds:{locationName:"SubnetId",type:"list",member:{locationName:"SubnetId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{Subnets:{locationName:"subnetSet",type:"list",member:{shape:"S5d",locationName:"item"}}}},http:{}},DescribeTags:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},Filters:{shape:"S7e",locationName:"Filter"},MaxResults:{locationName:"maxResults",type:"integer"},NextToken:{locationName:"nextToken"}}},output:{type:"structure",members:{Tags:{locationName:"tagSet",type:"list",member:{locationName:"item",type:"structure",members:{ResourceId:{locationName:"resourceId"},ResourceType:{locationName:"resourceType"},Key:{locationName:"key"},Value:{locationName:"value"}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeVolumeAttribute:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{},Attribute:{}}},output:{type:"structure",members:{VolumeId:{locationName:"volumeId"},AutoEnableIO:{shape:"S9y",locationName:"autoEnableIO"},ProductCodes:{shape:"S90",locationName:"productCodes"}}},http:{}},DescribeVolumeStatus:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeIds:{shape:"Seu",locationName:"VolumeId"},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{},MaxResults:{type:"integer"}}},output:{type:"structure",members:{VolumeStatuses:{locationName:"volumeStatusSet",type:"list",member:{locationName:"item",type:"structure",members:{VolumeId:{locationName:"volumeId"},AvailabilityZone:{locationName:"availabilityZone"},VolumeStatus:{locationName:"volumeStatus",type:"structure",members:{Status:{locationName:"status"},Details:{locationName:"details",type:"list",member:{locationName:"item",type:"structure",members:{Name:{locationName:"name"},Status:{locationName:"status"}}}}}},Events:{locationName:"eventsSet",type:"list",member:{locationName:"item",type:"structure",members:{EventType:{locationName:"eventType"},Description:{locationName:"description"},NotBefore:{locationName:"notBefore",type:"timestamp"},NotAfter:{locationName:"notAfter",type:"timestamp"},EventId:{locationName:"eventId"}}}},Actions:{locationName:"actionsSet",type:"list",member:{locationName:"item",type:"structure",members:{Code:{locationName:"code"},Description:{locationName:"description"},EventType:{locationName:"eventType"},EventId:{locationName:"eventId"}}}}}}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeVolumes:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeIds:{shape:"Seu",locationName:"VolumeId"},Filters:{shape:"S7e",locationName:"Filter"},NextToken:{locationName:"nextToken"},MaxResults:{locationName:"maxResults",type:"integer"}}},output:{type:"structure",members:{Volumes:{locationName:"volumeSet",type:"list",member:{shape:"S5i",locationName:"item"}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeVpcAttribute:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{},Attribute:{}}},output:{type:"structure",members:{VpcId:{locationName:"vpcId"},EnableDnsSupport:{shape:"S9y",locationName:"enableDnsSupport"},EnableDnsHostnames:{shape:"S9y",locationName:"enableDnsHostnames"}}},http:{}},DescribeVpcClassicLink:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcIds:{locationName:"VpcId",type:"list",member:{locationName:"VpcId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{Vpcs:{locationName:"vpcSet",type:"list",member:{locationName:"item",type:"structure",members:{VpcId:{locationName:"vpcId"},ClassicLinkEnabled:{locationName:"classicLinkEnabled",type:"boolean"},Tags:{shape:"Sa",locationName:"tagSet"}}}}}},http:{}},DescribeVpcEndpointServices:{input:{type:"structure",members:{DryRun:{type:"boolean"},MaxResults:{type:"integer"},NextToken:{}}},output:{type:"structure",members:{ServiceNames:{shape:"S22",locationName:"serviceNameSet"},NextToken:{locationName:"nextToken"}}},http:{}},DescribeVpcEndpoints:{input:{type:"structure",members:{DryRun:{type:"boolean"},VpcEndpointIds:{shape:"S22",locationName:"VpcEndpointId"},Filters:{shape:"S7e",locationName:"Filter"},MaxResults:{type:"integer"},NextToken:{}}},output:{type:"structure",members:{VpcEndpoints:{locationName:"vpcEndpointSet",type:"list",member:{shape:"S5s",locationName:"item"}},NextToken:{locationName:"nextToken"}}},http:{}},DescribeVpcPeeringConnections:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcPeeringConnectionIds:{shape:"S22",locationName:"VpcPeeringConnectionId"},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{VpcPeeringConnections:{locationName:"vpcPeeringConnectionSet",type:"list",member:{shape:"S5",locationName:"item"}}}},http:{}},DescribeVpcs:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcIds:{locationName:"VpcId",type:"list",member:{locationName:"VpcId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{Vpcs:{locationName:"vpcSet",type:"list",member:{shape:"S5o",locationName:"item"}}}},http:{}},DescribeVpnConnections:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnConnectionIds:{locationName:"VpnConnectionId",type:"list",member:{locationName:"VpnConnectionId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{VpnConnections:{locationName:"vpnConnectionSet",type:"list",member:{shape:"S5z",locationName:"item"}}}},http:{}},DescribeVpnGateways:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnGatewayIds:{locationName:"VpnGatewayId",type:"list",member:{locationName:"VpnGatewayId"}},Filters:{shape:"S7e",locationName:"Filter"}}},output:{type:"structure",members:{VpnGateways:{locationName:"vpnGatewaySet",type:"list",member:{shape:"S6b",locationName:"item"}}}},http:{}},DetachClassicLinkVpc:{input:{type:"structure",required:["InstanceId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},VpcId:{locationName:"vpcId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},DetachInternetGateway:{input:{type:"structure",required:["InternetGatewayId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InternetGatewayId:{locationName:"internetGatewayId"},VpcId:{locationName:"vpcId"}}},http:{}},DetachNetworkInterface:{input:{type:"structure",required:["AttachmentId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},AttachmentId:{locationName:"attachmentId"},Force:{locationName:"force",type:"boolean"}}},http:{}},DetachVolume:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{},InstanceId:{},Device:{},Force:{type:"boolean"}}},output:{shape:"Su",locationName:"attachment"},http:{}},DetachVpnGateway:{input:{type:"structure",required:["VpnGatewayId","VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpnGatewayId:{},VpcId:{}}},http:{}},DisableVgwRoutePropagation:{input:{type:"structure",required:["RouteTableId","GatewayId"],members:{RouteTableId:{},GatewayId:{}}},http:{}},DisableVpcClassicLink:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{locationName:"vpcId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},DisassociateAddress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIp:{},AssociationId:{}}},http:{}},DisassociateRouteTable:{input:{type:"structure",required:["AssociationId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},AssociationId:{locationName:"associationId"}}},http:{}},EnableVgwRoutePropagation:{input:{type:"structure",required:["RouteTableId","GatewayId"],members:{RouteTableId:{},GatewayId:{}}},http:{}},EnableVolumeIO:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{locationName:"volumeId"}}},http:{}},EnableVpcClassicLink:{input:{type:"structure",required:["VpcId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcId:{locationName:"vpcId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},GetConsoleOutput:{input:{type:"structure",required:["InstanceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{}}},output:{type:"structure",members:{InstanceId:{locationName:"instanceId"},Timestamp:{locationName:"timestamp",type:"timestamp"},Output:{locationName:"output"}}},http:{}},GetPasswordData:{input:{type:"structure",required:["InstanceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{}}},output:{type:"structure",members:{InstanceId:{locationName:"instanceId"},Timestamp:{locationName:"timestamp",type:"timestamp"},PasswordData:{locationName:"passwordData"}}},http:{}},ImportImage:{input:{type:"structure",members:{DryRun:{type:"boolean"},Description:{},DiskContainers:{locationName:"DiskContainer",type:"list",member:{locationName:"item",type:"structure",members:{Description:{},Format:{},Url:{},UserBucket:{shape:"Sgo"},DeviceName:{},SnapshotId:{}}}},LicenseType:{},Hypervisor:{},Architecture:{},Platform:{},ClientData:{shape:"Sgp"},ClientToken:{},RoleName:{}}},output:{type:"structure",members:{ImportTaskId:{locationName:"importTaskId"},Architecture:{locationName:"architecture"},LicenseType:{locationName:"licenseType"},Platform:{locationName:"platform"},Hypervisor:{locationName:"hypervisor"},Description:{locationName:"description"},SnapshotDetails:{shape:"S9n",locationName:"snapshotDetailSet"},ImageId:{locationName:"imageId"},Progress:{locationName:"progress"},StatusMessage:{locationName:"statusMessage"},Status:{locationName:"status"}}},http:{}},ImportInstance:{input:{type:"structure",required:["Platform"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Description:{locationName:"description"},LaunchSpecification:{locationName:"launchSpecification",type:"structure",members:{Architecture:{locationName:"architecture"},GroupNames:{shape:"Sgt",locationName:"GroupName"},GroupIds:{shape:"S46",locationName:"GroupId"},AdditionalInfo:{locationName:"additionalInfo"},UserData:{locationName:"userData",type:"structure",members:{Data:{locationName:"data"}}},InstanceType:{locationName:"instanceType"},Placement:{shape:"Sao",locationName:"placement"},Monitoring:{locationName:"monitoring",type:"boolean"},SubnetId:{locationName:"subnetId"},InstanceInitiatedShutdownBehavior:{locationName:"instanceInitiatedShutdownBehavior"},PrivateIpAddress:{locationName:"privateIpAddress"}}},DiskImages:{locationName:"diskImage",type:"list",member:{type:"structure",members:{Image:{shape:"Sgy"},Description:{},Volume:{shape:"Sgz"}}}},Platform:{locationName:"platform"}}},output:{type:"structure",members:{ConversionTask:{shape:"S85",locationName:"conversionTask"}}},http:{}},ImportKeyPair:{input:{type:"structure",required:["KeyName","PublicKeyMaterial"],members:{DryRun:{locationName:"dryRun",type:"boolean"},KeyName:{locationName:"keyName"},PublicKeyMaterial:{locationName:"publicKeyMaterial",type:"blob"}}},output:{type:"structure",members:{KeyName:{locationName:"keyName"},KeyFingerprint:{locationName:"keyFingerprint"}}},http:{}},ImportSnapshot:{input:{type:"structure",members:{DryRun:{type:"boolean"},Description:{},DiskContainer:{type:"structure",members:{Description:{},Format:{},Url:{},UserBucket:{shape:"Sgo"}}},ClientData:{shape:"Sgp"},ClientToken:{},RoleName:{}}},output:{type:"structure",members:{ImportTaskId:{locationName:"importTaskId"},SnapshotTaskDetail:{shape:"S9u",locationName:"snapshotTaskDetail"},Description:{locationName:"description"}}},http:{}},ImportVolume:{input:{type:"structure",required:["AvailabilityZone","Image","Volume"],members:{DryRun:{locationName:"dryRun",type:"boolean"},AvailabilityZone:{locationName:"availabilityZone"},Image:{shape:"Sgy",locationName:"image"},Description:{locationName:"description"},Volume:{shape:"Sgz",locationName:"volume"}}},output:{type:"structure",members:{ConversionTask:{shape:"S85",locationName:"conversionTask"}}},http:{}},ModifyImageAttribute:{input:{type:"structure",required:["ImageId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageId:{},Attribute:{},OperationType:{},UserIds:{shape:"Sh9",locationName:"UserId"},UserGroups:{locationName:"UserGroup",type:"list",member:{locationName:"UserGroup"}},ProductCodes:{locationName:"ProductCode",type:"list",member:{locationName:"ProductCode"}},Value:{},LaunchPermission:{type:"structure",members:{Add:{shape:"S8x"},Remove:{shape:"S8x"}}},Description:{shape:"S2z"}}},http:{}},ModifyInstanceAttribute:{input:{type:"structure",required:["InstanceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},Attribute:{locationName:"attribute"},Value:{locationName:"value"},BlockDeviceMappings:{locationName:"blockDeviceMapping",type:"list",member:{locationName:"item",type:"structure",members:{DeviceName:{locationName:"deviceName"},Ebs:{locationName:"ebs",type:"structure",members:{VolumeId:{locationName:"volumeId"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}},VirtualName:{locationName:"virtualName"},NoDevice:{locationName:"noDevice"}}}},SourceDestCheck:{shape:"S9y"},DisableApiTermination:{shape:"S9y",locationName:"disableApiTermination"},InstanceType:{shape:"S2z",locationName:"instanceType"},Kernel:{shape:"S2z",locationName:"kernel"},Ramdisk:{shape:"S2z",locationName:"ramdisk"},UserData:{locationName:"userData",type:"structure",members:{Value:{locationName:"value",type:"blob"}}},InstanceInitiatedShutdownBehavior:{shape:"S2z",locationName:"instanceInitiatedShutdownBehavior"},Groups:{shape:"So",locationName:"GroupId"},EbsOptimized:{shape:"S9y",locationName:"ebsOptimized"},SriovNetSupport:{shape:"S2z",locationName:"sriovNetSupport"}}},http:{}},ModifyNetworkInterfaceAttribute:{input:{type:"structure",required:["NetworkInterfaceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceId:{locationName:"networkInterfaceId"},Description:{shape:"S2z",locationName:"description"},SourceDestCheck:{shape:"S9y",locationName:"sourceDestCheck"},Groups:{shape:"S46",locationName:"SecurityGroupId"},Attachment:{locationName:"attachment",type:"structure",members:{AttachmentId:{locationName:"attachmentId"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}}}},http:{}},ModifyReservedInstances:{input:{type:"structure",required:["ReservedInstancesIds","TargetConfigurations"],members:{ClientToken:{locationName:"clientToken"},ReservedInstancesIds:{shape:"Sc2",locationName:"ReservedInstancesId"},TargetConfigurations:{locationName:"ReservedInstancesConfigurationSetItemType",type:"list",member:{shape:"Sco",locationName:"item"}}}},output:{type:"structure",members:{ReservedInstancesModificationId:{locationName:"reservedInstancesModificationId"}}},http:{}},ModifySnapshotAttribute:{input:{type:"structure",required:["SnapshotId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SnapshotId:{},Attribute:{},OperationType:{},UserIds:{shape:"Sh9",locationName:"UserId"},GroupNames:{shape:"Sd0",locationName:"UserGroup"},CreateVolumePermission:{type:"structure",members:{Add:{shape:"Sd7"},Remove:{shape:"Sd7"}}}}},http:{}},ModifySubnetAttribute:{input:{type:"structure",required:["SubnetId"],members:{SubnetId:{locationName:"subnetId"},MapPublicIpOnLaunch:{shape:"S9y"}}},http:{}},ModifyVolumeAttribute:{input:{type:"structure",required:["VolumeId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VolumeId:{},AutoEnableIO:{shape:"S9y"}}},http:{}},ModifyVpcAttribute:{input:{type:"structure",required:["VpcId"],members:{VpcId:{locationName:"vpcId"},EnableDnsSupport:{shape:"S9y"},EnableDnsHostnames:{shape:"S9y"}}},http:{}},ModifyVpcEndpoint:{input:{type:"structure",required:["VpcEndpointId"],members:{DryRun:{type:"boolean"},VpcEndpointId:{},ResetPolicy:{type:"boolean"},PolicyDocument:{},AddRouteTableIds:{shape:"S22",locationName:"AddRouteTableId"},RemoveRouteTableIds:{shape:"S22",locationName:"RemoveRouteTableId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},MonitorInstances:{input:{type:"structure",required:["InstanceIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"}}},output:{type:"structure",members:{InstanceMonitorings:{shape:"Shw",locationName:"instancesSet"}}},http:{}},MoveAddressToVpc:{input:{type:"structure",required:["PublicIp"],members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIp:{locationName:"publicIp"}}},output:{type:"structure",members:{AllocationId:{locationName:"allocationId"},Status:{locationName:"status"}}},http:{}},PurchaseReservedInstancesOffering:{input:{type:"structure",required:["ReservedInstancesOfferingId","InstanceCount"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ReservedInstancesOfferingId:{},InstanceCount:{type:"integer"},LimitPrice:{locationName:"limitPrice",type:"structure",members:{Amount:{locationName:"amount",type:"double"},CurrencyCode:{locationName:"currencyCode"}}}}},output:{type:"structure",members:{ReservedInstancesId:{locationName:"reservedInstancesId"}}},http:{}},RebootInstances:{input:{type:"structure",required:["InstanceIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"}}},http:{}},RegisterImage:{input:{type:"structure",required:["Name"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageLocation:{},Name:{locationName:"name"},Description:{locationName:"description"},Architecture:{locationName:"architecture"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},RootDeviceName:{locationName:"rootDeviceName"},BlockDeviceMappings:{shape:"S38",locationName:"BlockDeviceMapping"},VirtualizationType:{locationName:"virtualizationType"},SriovNetSupport:{locationName:"sriovNetSupport"}}},output:{type:"structure",members:{ImageId:{locationName:"imageId"}}},http:{}},RejectVpcPeeringConnection:{input:{type:"structure",required:["VpcPeeringConnectionId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},output:{type:"structure",members:{Return:{locationName:"return",type:"boolean"}}},http:{}},ReleaseAddress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIp:{},AllocationId:{}}},http:{}},ReplaceNetworkAclAssociation:{input:{type:"structure",required:["AssociationId","NetworkAclId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},AssociationId:{locationName:"associationId"},NetworkAclId:{locationName:"networkAclId"}}},output:{type:"structure",members:{NewAssociationId:{locationName:"newAssociationId"}}},http:{}},ReplaceNetworkAclEntry:{input:{type:"structure",required:["NetworkAclId","RuleNumber","Protocol","RuleAction","Egress","CidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkAclId:{locationName:"networkAclId"},RuleNumber:{locationName:"ruleNumber",type:"integer"},Protocol:{locationName:"protocol"},RuleAction:{locationName:"ruleAction"},Egress:{locationName:"egress",type:"boolean"},CidrBlock:{locationName:"cidrBlock"},IcmpTypeCode:{shape:"S40",locationName:"Icmp"},PortRange:{shape:"S41",locationName:"portRange"}}},http:{}},ReplaceRoute:{input:{type:"structure",required:["RouteTableId","DestinationCidrBlock"],members:{DryRun:{locationName:"dryRun",type:"boolean"},RouteTableId:{locationName:"routeTableId"},DestinationCidrBlock:{locationName:"destinationCidrBlock"},GatewayId:{locationName:"gatewayId"},InstanceId:{locationName:"instanceId"},NetworkInterfaceId:{locationName:"networkInterfaceId"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},http:{}},ReplaceRouteTableAssociation:{input:{type:"structure",required:["AssociationId","RouteTableId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},AssociationId:{locationName:"associationId"},RouteTableId:{locationName:"routeTableId"}}},output:{type:"structure",members:{NewAssociationId:{locationName:"newAssociationId"}}},http:{}},ReportInstanceStatus:{input:{type:"structure",required:["Instances","Status","ReasonCodes"],members:{DryRun:{locationName:"dryRun",type:"boolean"},Instances:{shape:"S7x",locationName:"instanceId"},Status:{locationName:"status"},StartTime:{locationName:"startTime",type:"timestamp"},EndTime:{locationName:"endTime",type:"timestamp"},ReasonCodes:{locationName:"reasonCode",type:"list",member:{locationName:"item"}},Description:{locationName:"description"}}},http:{}},RequestSpotFleet:{input:{type:"structure",required:["SpotFleetRequestConfig"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotFleetRequestConfig:{shape:"Sdu",locationName:"spotFleetRequestConfig"}}},output:{type:"structure",required:["SpotFleetRequestId"],members:{SpotFleetRequestId:{locationName:"spotFleetRequestId"}}},http:{}},RequestSpotInstances:{input:{type:"structure",required:["SpotPrice"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SpotPrice:{locationName:"spotPrice"},ClientToken:{locationName:"clientToken"},InstanceCount:{locationName:"instanceCount",type:"integer"},Type:{locationName:"type"},ValidFrom:{locationName:"validFrom",type:"timestamp"},ValidUntil:{locationName:"validUntil",type:"timestamp"},LaunchGroup:{locationName:"launchGroup"},AvailabilityZoneGroup:{locationName:"availabilityZoneGroup"},LaunchSpecification:{type:"structure",members:{ImageId:{locationName:"imageId"},KeyName:{locationName:"keyName"},SecurityGroups:{shape:"S22",locationName:"SecurityGroup"},UserData:{locationName:"userData"},AddressingType:{locationName:"addressingType"},InstanceType:{locationName:"instanceType"},Placement:{shape:"Sdx",locationName:"placement"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},BlockDeviceMappings:{shape:"S93",locationName:"blockDeviceMapping"},SubnetId:{locationName:"subnetId"},NetworkInterfaces:{shape:"Sdz",locationName:"NetworkInterface"},IamInstanceProfile:{shape:"Se1",locationName:"iamInstanceProfile"},EbsOptimized:{locationName:"ebsOptimized",type:"boolean"},Monitoring:{shape:"Sea",locationName:"monitoring"},SecurityGroupIds:{shape:"S22",locationName:"SecurityGroupId"}}}}},output:{type:"structure",members:{SpotInstanceRequests:{shape:"Se4",locationName:"spotInstanceRequestSet"}}},http:{}},ResetImageAttribute:{input:{type:"structure",required:["ImageId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageId:{},Attribute:{}}},http:{}},ResetInstanceAttribute:{input:{type:"structure",required:["InstanceId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceId:{locationName:"instanceId"},Attribute:{locationName:"attribute"}}},http:{}},ResetNetworkInterfaceAttribute:{input:{type:"structure",required:["NetworkInterfaceId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},NetworkInterfaceId:{locationName:"networkInterfaceId"},SourceDestCheck:{locationName:"sourceDestCheck"}}},http:{}},ResetSnapshotAttribute:{input:{type:"structure",required:["SnapshotId","Attribute"],members:{DryRun:{locationName:"dryRun",type:"boolean"},SnapshotId:{},Attribute:{}}},http:{}},RestoreAddressToClassic:{input:{type:"structure",required:["PublicIp"],members:{DryRun:{locationName:"dryRun",type:"boolean"},PublicIp:{locationName:"publicIp"}}},output:{type:"structure",members:{Status:{locationName:"status"},PublicIp:{locationName:"publicIp"}}},http:{}},RevokeSecurityGroupEgress:{input:{type:"structure",required:["GroupId"],members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupId:{locationName:"groupId"},SourceSecurityGroupName:{locationName:"sourceSecurityGroupName"},SourceSecurityGroupOwnerId:{locationName:"sourceSecurityGroupOwnerId"},IpProtocol:{locationName:"ipProtocol"},FromPort:{locationName:"fromPort",type:"integer"},ToPort:{locationName:"toPort",type:"integer"},CidrIp:{locationName:"cidrIp"},IpPermissions:{shape:"S11",locationName:"ipPermissions"}}},http:{}},RevokeSecurityGroupIngress:{input:{type:"structure",members:{DryRun:{locationName:"dryRun",type:"boolean"},GroupName:{},GroupId:{},SourceSecurityGroupName:{},SourceSecurityGroupOwnerId:{},IpProtocol:{},FromPort:{type:"integer"},ToPort:{type:"integer"},CidrIp:{},IpPermissions:{shape:"S11"}}},http:{}},RunInstances:{input:{type:"structure",required:["ImageId","MinCount","MaxCount"],members:{DryRun:{locationName:"dryRun",type:"boolean"},ImageId:{},MinCount:{type:"integer"},MaxCount:{type:"integer"},KeyName:{},SecurityGroups:{shape:"Sgt",locationName:"SecurityGroup"},SecurityGroupIds:{shape:"S46",locationName:"SecurityGroupId"},UserData:{},InstanceType:{},Placement:{shape:"Sao"},KernelId:{},RamdiskId:{},BlockDeviceMappings:{shape:"S38",locationName:"BlockDeviceMapping"},Monitoring:{shape:"Sea"},SubnetId:{},DisableApiTermination:{locationName:"disableApiTermination",type:"boolean"},InstanceInitiatedShutdownBehavior:{locationName:"instanceInitiatedShutdownBehavior"},PrivateIpAddress:{locationName:"privateIpAddress"},ClientToken:{locationName:"clientToken"},AdditionalInfo:{locationName:"additionalInfo"},NetworkInterfaces:{shape:"Sdz",locationName:"networkInterface"},IamInstanceProfile:{shape:"Se1",locationName:"iamInstanceProfile"},EbsOptimized:{locationName:"ebsOptimized",type:"boolean"}}},output:{shape:"Sak",locationName:"reservation"},http:{}},StartInstances:{input:{type:"structure",required:["InstanceIds"],members:{InstanceIds:{shape:"S7x",locationName:"InstanceId"},AdditionalInfo:{locationName:"additionalInfo"},DryRun:{locationName:"dryRun",type:"boolean"}}},output:{type:"structure",members:{StartingInstances:{shape:"Sj1",locationName:"instancesSet"}}},http:{}},StopInstances:{input:{type:"structure",required:["InstanceIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"},Force:{locationName:"force",type:"boolean"}}},output:{type:"structure",members:{StoppingInstances:{shape:"Sj1",locationName:"instancesSet"}}},http:{}},TerminateInstances:{input:{type:"structure",required:["InstanceIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"}}},output:{type:"structure",members:{TerminatingInstances:{shape:"Sj1",locationName:"instancesSet"}}},http:{}},UnassignPrivateIpAddresses:{input:{type:"structure",required:["NetworkInterfaceId","PrivateIpAddresses"],members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},PrivateIpAddresses:{shape:"Sg",locationName:"privateIpAddress"}}},http:{}},UnmonitorInstances:{input:{type:"structure",required:["InstanceIds"],members:{DryRun:{locationName:"dryRun",type:"boolean"},InstanceIds:{shape:"S7x",locationName:"InstanceId"}}},output:{type:"structure",members:{InstanceMonitorings:{shape:"Shw",locationName:"instancesSet"}}},http:{}}},shapes:{S5:{type:"structure",members:{AccepterVpcInfo:{shape:"S6",locationName:"accepterVpcInfo"},ExpirationTime:{locationName:"expirationTime",type:"timestamp"},RequesterVpcInfo:{shape:"S6",locationName:"requesterVpcInfo"},Status:{locationName:"status",type:"structure",members:{Code:{locationName:"code"},Message:{locationName:"message"}}},Tags:{shape:"Sa",locationName:"tagSet"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"}}},S6:{type:"structure",members:{CidrBlock:{locationName:"cidrBlock"},OwnerId:{locationName:"ownerId"},VpcId:{locationName:"vpcId"}}},Sa:{type:"list",member:{locationName:"item",type:"structure",members:{Key:{locationName:"key"},Value:{locationName:"value"}}}},Sg:{type:"list",member:{locationName:"PrivateIpAddress"}},So:{type:"list",member:{locationName:"groupId"}},Su:{type:"structure",members:{VolumeId:{locationName:"volumeId"},InstanceId:{locationName:"instanceId"},Device:{locationName:"device"},State:{locationName:"status"},AttachTime:{locationName:"attachTime",type:"timestamp"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}},Sy:{type:"structure",members:{VpcId:{locationName:"vpcId"},State:{locationName:"state"}}},S11:{type:"list",member:{locationName:"item",type:"structure",members:{IpProtocol:{locationName:"ipProtocol"},FromPort:{locationName:"fromPort",type:"integer"},ToPort:{locationName:"toPort",type:"integer"},UserIdGroupPairs:{locationName:"groups",type:"list",member:{locationName:"item",type:"structure",members:{UserId:{locationName:"userId"},GroupName:{locationName:"groupName"},GroupId:{locationName:"groupId"}}}},IpRanges:{locationName:"ipRanges",type:"list",member:{locationName:"item",type:"structure",members:{CidrIp:{locationName:"cidrIp"}}}},PrefixListIds:{locationName:"prefixListIds",type:"list",member:{locationName:"item",type:"structure",members:{PrefixListId:{locationName:"prefixListId"}}}}}}},S1b:{type:"structure",members:{S3:{type:"structure",members:{Bucket:{locationName:"bucket"},Prefix:{locationName:"prefix"},AWSAccessKeyId:{},UploadPolicy:{locationName:"uploadPolicy",type:"blob"},UploadPolicySignature:{locationName:"uploadPolicySignature"}}}}},S1f:{type:"structure",members:{InstanceId:{locationName:"instanceId"},BundleId:{locationName:"bundleId"},State:{locationName:"state"},StartTime:{locationName:"startTime",type:"timestamp"},UpdateTime:{locationName:"updateTime",type:"timestamp"},Storage:{shape:"S1b",locationName:"storage"},Progress:{locationName:"progress"},BundleTaskError:{locationName:"error",type:"structure",members:{Code:{locationName:"code"},Message:{locationName:"message"}}}}},S1q:{type:"list",member:{locationName:"item",type:"structure",members:{ReservedInstancesListingId:{locationName:"reservedInstancesListingId"},ReservedInstancesId:{locationName:"reservedInstancesId"},CreateDate:{locationName:"createDate",type:"timestamp"},UpdateDate:{locationName:"updateDate",type:"timestamp"},Status:{locationName:"status"},StatusMessage:{locationName:"statusMessage"},InstanceCounts:{locationName:"instanceCounts",type:"list",member:{locationName:"item",type:"structure",members:{State:{locationName:"state"},InstanceCount:{locationName:"instanceCount",type:"integer"}}}},PriceSchedules:{locationName:"priceSchedules",type:"list",member:{locationName:"item",type:"structure",members:{Term:{locationName:"term",type:"long"},Price:{locationName:"price",type:"double"},CurrencyCode:{locationName:"currencyCode"},Active:{locationName:"active",type:"boolean"}}}},Tags:{shape:"Sa",locationName:"tagSet"},ClientToken:{locationName:"clientToken"}}}},S22:{type:"list",member:{locationName:"item"
}},S2c:{type:"list",member:{locationName:"SpotInstanceRequestId"}},S2q:{type:"structure",members:{CustomerGatewayId:{locationName:"customerGatewayId"},State:{locationName:"state"},Type:{locationName:"type"},IpAddress:{locationName:"ipAddress"},BgpAsn:{locationName:"bgpAsn"},Tags:{shape:"Sa",locationName:"tagSet"}}},S2v:{type:"structure",members:{DhcpOptionsId:{locationName:"dhcpOptionsId"},DhcpConfigurations:{locationName:"dhcpConfigurationSet",type:"list",member:{locationName:"item",type:"structure",members:{Key:{locationName:"key"},Values:{locationName:"valueSet",type:"list",member:{shape:"S2z",locationName:"item"}}}}},Tags:{shape:"Sa",locationName:"tagSet"}}},S2z:{type:"structure",members:{Value:{locationName:"value"}}},S34:{type:"list",member:{locationName:"item",type:"structure",required:["Error"],members:{ResourceId:{locationName:"resourceId"},Error:{locationName:"error",type:"structure",required:["Code","Message"],members:{Code:{locationName:"code"},Message:{locationName:"message"}}}}}},S38:{type:"list",member:{shape:"S39",locationName:"BlockDeviceMapping"}},S39:{type:"structure",members:{VirtualName:{locationName:"virtualName"},DeviceName:{locationName:"deviceName"},Ebs:{locationName:"ebs",type:"structure",members:{SnapshotId:{locationName:"snapshotId"},VolumeSize:{locationName:"volumeSize",type:"integer"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"},VolumeType:{locationName:"volumeType"},Iops:{locationName:"iops",type:"integer"},Encrypted:{locationName:"encrypted",type:"boolean"}}},NoDevice:{locationName:"noDevice"}}},S3j:{type:"structure",members:{ExportTaskId:{locationName:"exportTaskId"},Description:{locationName:"description"},State:{locationName:"state"},StatusMessage:{locationName:"statusMessage"},InstanceExportDetails:{locationName:"instanceExport",type:"structure",members:{InstanceId:{locationName:"instanceId"},TargetEnvironment:{locationName:"targetEnvironment"}}},ExportToS3Task:{locationName:"exportToS3",type:"structure",members:{DiskImageFormat:{locationName:"diskImageFormat"},ContainerFormat:{locationName:"containerFormat"},S3Bucket:{locationName:"s3Bucket"},S3Key:{locationName:"s3Key"}}}}},S3p:{type:"structure",members:{InternetGatewayId:{locationName:"internetGatewayId"},Attachments:{locationName:"attachmentSet",type:"list",member:{locationName:"item",type:"structure",members:{VpcId:{locationName:"vpcId"},State:{locationName:"state"}}}},Tags:{shape:"Sa",locationName:"tagSet"}}},S3w:{type:"structure",members:{NetworkAclId:{locationName:"networkAclId"},VpcId:{locationName:"vpcId"},IsDefault:{locationName:"default",type:"boolean"},Entries:{locationName:"entrySet",type:"list",member:{locationName:"item",type:"structure",members:{RuleNumber:{locationName:"ruleNumber",type:"integer"},Protocol:{locationName:"protocol"},RuleAction:{locationName:"ruleAction"},Egress:{locationName:"egress",type:"boolean"},CidrBlock:{locationName:"cidrBlock"},IcmpTypeCode:{shape:"S40",locationName:"icmpTypeCode"},PortRange:{shape:"S41",locationName:"portRange"}}}},Associations:{locationName:"associationSet",type:"list",member:{locationName:"item",type:"structure",members:{NetworkAclAssociationId:{locationName:"networkAclAssociationId"},NetworkAclId:{locationName:"networkAclId"},SubnetId:{locationName:"subnetId"}}}},Tags:{shape:"Sa",locationName:"tagSet"}}},S40:{type:"structure",members:{Type:{locationName:"type",type:"integer"},Code:{locationName:"code",type:"integer"}}},S41:{type:"structure",members:{From:{locationName:"from",type:"integer"},To:{locationName:"to",type:"integer"}}},S46:{type:"list",member:{locationName:"SecurityGroupId"}},S47:{type:"list",member:{locationName:"item",type:"structure",required:["PrivateIpAddress"],members:{PrivateIpAddress:{locationName:"privateIpAddress"},Primary:{locationName:"primary",type:"boolean"}}}},S4a:{type:"structure",members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},SubnetId:{locationName:"subnetId"},VpcId:{locationName:"vpcId"},AvailabilityZone:{locationName:"availabilityZone"},Description:{locationName:"description"},OwnerId:{locationName:"ownerId"},RequesterId:{locationName:"requesterId"},RequesterManaged:{locationName:"requesterManaged",type:"boolean"},Status:{locationName:"status"},MacAddress:{locationName:"macAddress"},PrivateIpAddress:{locationName:"privateIpAddress"},PrivateDnsName:{locationName:"privateDnsName"},SourceDestCheck:{locationName:"sourceDestCheck",type:"boolean"},Groups:{shape:"S4c",locationName:"groupSet"},Attachment:{shape:"S4e",locationName:"attachment"},Association:{shape:"S4f",locationName:"association"},TagSet:{shape:"Sa",locationName:"tagSet"},PrivateIpAddresses:{locationName:"privateIpAddressesSet",type:"list",member:{locationName:"item",type:"structure",members:{PrivateIpAddress:{locationName:"privateIpAddress"},PrivateDnsName:{locationName:"privateDnsName"},Primary:{locationName:"primary",type:"boolean"},Association:{shape:"S4f",locationName:"association"}}}}}},S4c:{type:"list",member:{locationName:"item",type:"structure",members:{GroupName:{locationName:"groupName"},GroupId:{locationName:"groupId"}}}},S4e:{type:"structure",members:{AttachmentId:{locationName:"attachmentId"},InstanceId:{locationName:"instanceId"},InstanceOwnerId:{locationName:"instanceOwnerId"},DeviceIndex:{locationName:"deviceIndex",type:"integer"},Status:{locationName:"status"},AttachTime:{locationName:"attachTime",type:"timestamp"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}},S4f:{type:"structure",members:{PublicIp:{locationName:"publicIp"},PublicDnsName:{locationName:"publicDnsName"},IpOwnerId:{locationName:"ipOwnerId"},AllocationId:{locationName:"allocationId"},AssociationId:{locationName:"associationId"}}},S4s:{type:"structure",members:{RouteTableId:{locationName:"routeTableId"},VpcId:{locationName:"vpcId"},Routes:{locationName:"routeSet",type:"list",member:{locationName:"item",type:"structure",members:{DestinationCidrBlock:{locationName:"destinationCidrBlock"},DestinationPrefixListId:{locationName:"destinationPrefixListId"},GatewayId:{locationName:"gatewayId"},InstanceId:{locationName:"instanceId"},InstanceOwnerId:{locationName:"instanceOwnerId"},NetworkInterfaceId:{locationName:"networkInterfaceId"},VpcPeeringConnectionId:{locationName:"vpcPeeringConnectionId"},State:{locationName:"state"},Origin:{locationName:"origin"}}}},Associations:{locationName:"associationSet",type:"list",member:{locationName:"item",type:"structure",members:{RouteTableAssociationId:{locationName:"routeTableAssociationId"},RouteTableId:{locationName:"routeTableId"},SubnetId:{locationName:"subnetId"},Main:{locationName:"main",type:"boolean"}}}},Tags:{shape:"Sa",locationName:"tagSet"},PropagatingVgws:{locationName:"propagatingVgwSet",type:"list",member:{locationName:"item",type:"structure",members:{GatewayId:{locationName:"gatewayId"}}}}}},S54:{type:"structure",members:{SnapshotId:{locationName:"snapshotId"},VolumeId:{locationName:"volumeId"},State:{locationName:"status"},StartTime:{locationName:"startTime",type:"timestamp"},Progress:{locationName:"progress"},OwnerId:{locationName:"ownerId"},Description:{locationName:"description"},VolumeSize:{locationName:"volumeSize",type:"integer"},OwnerAlias:{locationName:"ownerAlias"},Tags:{shape:"Sa",locationName:"tagSet"},Encrypted:{locationName:"encrypted",type:"boolean"},KmsKeyId:{locationName:"kmsKeyId"}}},S58:{type:"structure",members:{OwnerId:{locationName:"ownerId"},Bucket:{locationName:"bucket"},Prefix:{locationName:"prefix"},State:{locationName:"state"},Fault:{shape:"S5a",locationName:"fault"}}},S5a:{type:"structure",members:{Code:{locationName:"code"},Message:{locationName:"message"}}},S5d:{type:"structure",members:{SubnetId:{locationName:"subnetId"},State:{locationName:"state"},VpcId:{locationName:"vpcId"},CidrBlock:{locationName:"cidrBlock"},AvailableIpAddressCount:{locationName:"availableIpAddressCount",type:"integer"},AvailabilityZone:{locationName:"availabilityZone"},DefaultForAz:{locationName:"defaultForAz",type:"boolean"},MapPublicIpOnLaunch:{locationName:"mapPublicIpOnLaunch",type:"boolean"},Tags:{shape:"Sa",locationName:"tagSet"}}},S5g:{type:"list",member:{}},S5i:{type:"structure",members:{VolumeId:{locationName:"volumeId"},Size:{locationName:"size",type:"integer"},SnapshotId:{locationName:"snapshotId"},AvailabilityZone:{locationName:"availabilityZone"},State:{locationName:"status"},CreateTime:{locationName:"createTime",type:"timestamp"},Attachments:{locationName:"attachmentSet",type:"list",member:{shape:"Su",locationName:"item"}},Tags:{shape:"Sa",locationName:"tagSet"},VolumeType:{locationName:"volumeType"},Iops:{locationName:"iops",type:"integer"},Encrypted:{locationName:"encrypted",type:"boolean"},KmsKeyId:{locationName:"kmsKeyId"}}},S5o:{type:"structure",members:{VpcId:{locationName:"vpcId"},State:{locationName:"state"},CidrBlock:{locationName:"cidrBlock"},DhcpOptionsId:{locationName:"dhcpOptionsId"},Tags:{shape:"Sa",locationName:"tagSet"},InstanceTenancy:{locationName:"instanceTenancy"},IsDefault:{locationName:"isDefault",type:"boolean"}}},S5s:{type:"structure",members:{VpcEndpointId:{locationName:"vpcEndpointId"},VpcId:{locationName:"vpcId"},ServiceName:{locationName:"serviceName"},State:{locationName:"state"},PolicyDocument:{locationName:"policyDocument"},RouteTableIds:{shape:"S22",locationName:"routeTableIdSet"},CreationTimestamp:{locationName:"creationTimestamp",type:"timestamp"}}},S5z:{type:"structure",members:{VpnConnectionId:{locationName:"vpnConnectionId"},State:{locationName:"state"},CustomerGatewayConfiguration:{locationName:"customerGatewayConfiguration"},Type:{locationName:"type"},CustomerGatewayId:{locationName:"customerGatewayId"},VpnGatewayId:{locationName:"vpnGatewayId"},Tags:{shape:"Sa",locationName:"tagSet"},VgwTelemetry:{locationName:"vgwTelemetry",type:"list",member:{locationName:"item",type:"structure",members:{OutsideIpAddress:{locationName:"outsideIpAddress"},Status:{locationName:"status"},LastStatusChange:{locationName:"lastStatusChange",type:"timestamp"},StatusMessage:{locationName:"statusMessage"},AcceptedRouteCount:{locationName:"acceptedRouteCount",type:"integer"}}}},Options:{locationName:"options",type:"structure",members:{StaticRoutesOnly:{locationName:"staticRoutesOnly",type:"boolean"}}},Routes:{locationName:"routes",type:"list",member:{locationName:"item",type:"structure",members:{DestinationCidrBlock:{locationName:"destinationCidrBlock"},Source:{locationName:"source"},State:{locationName:"state"}}}}}},S6b:{type:"structure",members:{VpnGatewayId:{locationName:"vpnGatewayId"},State:{locationName:"state"},Type:{locationName:"type"},AvailabilityZone:{locationName:"availabilityZone"},VpcAttachments:{locationName:"attachments",type:"list",member:{shape:"Sy",locationName:"item"}},Tags:{shape:"Sa",locationName:"tagSet"}}},S7e:{type:"list",member:{locationName:"Filter",type:"structure",members:{Name:{},Values:{shape:"S22",locationName:"Value"}}}},S7x:{type:"list",member:{locationName:"InstanceId"}},S85:{type:"structure",required:["ConversionTaskId","State"],members:{ConversionTaskId:{locationName:"conversionTaskId"},ExpirationTime:{locationName:"expirationTime"},ImportInstance:{locationName:"importInstance",type:"structure",required:["Volumes"],members:{Volumes:{locationName:"volumes",type:"list",member:{locationName:"item",type:"structure",required:["BytesConverted","AvailabilityZone","Image","Volume","Status"],members:{BytesConverted:{locationName:"bytesConverted",type:"long"},AvailabilityZone:{locationName:"availabilityZone"},Image:{shape:"S89",locationName:"image"},Volume:{shape:"S8a",locationName:"volume"},Status:{locationName:"status"},StatusMessage:{locationName:"statusMessage"},Description:{locationName:"description"}}}},InstanceId:{locationName:"instanceId"},Platform:{locationName:"platform"},Description:{locationName:"description"}}},ImportVolume:{locationName:"importVolume",type:"structure",required:["BytesConverted","AvailabilityZone","Image","Volume"],members:{BytesConverted:{locationName:"bytesConverted",type:"long"},AvailabilityZone:{locationName:"availabilityZone"},Description:{locationName:"description"},Image:{shape:"S89",locationName:"image"},Volume:{shape:"S8a",locationName:"volume"}}},State:{locationName:"state"},StatusMessage:{locationName:"statusMessage"},Tags:{shape:"Sa",locationName:"tagSet"}}},S89:{type:"structure",required:["Format","Size","ImportManifestUrl"],members:{Format:{locationName:"format"},Size:{locationName:"size",type:"long"},ImportManifestUrl:{locationName:"importManifestUrl"},Checksum:{locationName:"checksum"}}},S8a:{type:"structure",required:["Id"],members:{Size:{locationName:"size",type:"long"},Id:{locationName:"id"}}},S8x:{type:"list",member:{locationName:"item",type:"structure",members:{UserId:{locationName:"userId"},Group:{locationName:"group"}}}},S90:{type:"list",member:{locationName:"item",type:"structure",members:{ProductCodeId:{locationName:"productCode"},ProductCodeType:{locationName:"type"}}}},S93:{type:"list",member:{shape:"S39",locationName:"item"}},S96:{type:"list",member:{locationName:"Owner"}},S9e:{type:"structure",members:{Code:{locationName:"code"},Message:{locationName:"message"}}},S9j:{type:"list",member:{locationName:"ImportTaskId"}},S9n:{type:"list",member:{locationName:"item",type:"structure",members:{DiskImageSize:{locationName:"diskImageSize",type:"double"},Description:{locationName:"description"},Format:{locationName:"format"},Url:{locationName:"url"},UserBucket:{shape:"S9p",locationName:"userBucket"},DeviceName:{locationName:"deviceName"},SnapshotId:{locationName:"snapshotId"},Progress:{locationName:"progress"},StatusMessage:{locationName:"statusMessage"},Status:{locationName:"status"}}}},S9p:{type:"structure",members:{S3Bucket:{locationName:"s3Bucket"},S3Key:{locationName:"s3Key"}}},S9u:{type:"structure",members:{DiskImageSize:{locationName:"diskImageSize",type:"double"},Description:{locationName:"description"},Format:{locationName:"format"},Url:{locationName:"url"},UserBucket:{shape:"S9p",locationName:"userBucket"},SnapshotId:{locationName:"snapshotId"},Progress:{locationName:"progress"},StatusMessage:{locationName:"statusMessage"},Status:{locationName:"status"}}},S9y:{type:"structure",members:{Value:{locationName:"value",type:"boolean"}}},S9z:{type:"list",member:{locationName:"item",type:"structure",members:{DeviceName:{locationName:"deviceName"},Ebs:{locationName:"ebs",type:"structure",members:{VolumeId:{locationName:"volumeId"},Status:{locationName:"status"},AttachTime:{locationName:"attachTime",type:"timestamp"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}}}}},Sa9:{type:"structure",members:{Code:{locationName:"code",type:"integer"},Name:{locationName:"name"}}},Sab:{type:"structure",members:{Status:{locationName:"status"},Details:{locationName:"details",type:"list",member:{locationName:"item",type:"structure",members:{Name:{locationName:"name"},Status:{locationName:"status"},ImpairedSince:{locationName:"impairedSince",type:"timestamp"}}}}}},Sak:{type:"structure",members:{ReservationId:{locationName:"reservationId"},OwnerId:{locationName:"ownerId"},RequesterId:{locationName:"requesterId"},Groups:{shape:"S4c",locationName:"groupSet"},Instances:{locationName:"instancesSet",type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},ImageId:{locationName:"imageId"},State:{shape:"Sa9",locationName:"instanceState"},PrivateDnsName:{locationName:"privateDnsName"},PublicDnsName:{locationName:"dnsName"},StateTransitionReason:{locationName:"reason"},KeyName:{locationName:"keyName"},AmiLaunchIndex:{locationName:"amiLaunchIndex",type:"integer"},ProductCodes:{shape:"S90",locationName:"productCodes"},InstanceType:{locationName:"instanceType"},LaunchTime:{locationName:"launchTime",type:"timestamp"},Placement:{shape:"Sao",locationName:"placement"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},Platform:{locationName:"platform"},Monitoring:{shape:"Sap",locationName:"monitoring"},SubnetId:{locationName:"subnetId"},VpcId:{locationName:"vpcId"},PrivateIpAddress:{locationName:"privateIpAddress"},PublicIpAddress:{locationName:"ipAddress"},StateReason:{shape:"S9e",locationName:"stateReason"},Architecture:{locationName:"architecture"},RootDeviceType:{locationName:"rootDeviceType"},RootDeviceName:{locationName:"rootDeviceName"},BlockDeviceMappings:{shape:"S9z",locationName:"blockDeviceMapping"},VirtualizationType:{locationName:"virtualizationType"},InstanceLifecycle:{locationName:"instanceLifecycle"},SpotInstanceRequestId:{locationName:"spotInstanceRequestId"},ClientToken:{locationName:"clientToken"},Tags:{shape:"Sa",locationName:"tagSet"},SecurityGroups:{shape:"S4c",locationName:"groupSet"},SourceDestCheck:{locationName:"sourceDestCheck",type:"boolean"},Hypervisor:{locationName:"hypervisor"},NetworkInterfaces:{locationName:"networkInterfaceSet",type:"list",member:{locationName:"item",type:"structure",members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},SubnetId:{locationName:"subnetId"},VpcId:{locationName:"vpcId"},Description:{locationName:"description"},OwnerId:{locationName:"ownerId"},Status:{locationName:"status"},MacAddress:{locationName:"macAddress"},PrivateIpAddress:{locationName:"privateIpAddress"},PrivateDnsName:{locationName:"privateDnsName"},SourceDestCheck:{locationName:"sourceDestCheck",type:"boolean"},Groups:{shape:"S4c",locationName:"groupSet"},Attachment:{locationName:"attachment",type:"structure",members:{AttachmentId:{locationName:"attachmentId"},DeviceIndex:{locationName:"deviceIndex",type:"integer"},Status:{locationName:"status"},AttachTime:{locationName:"attachTime",type:"timestamp"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"}}},Association:{shape:"Sav",locationName:"association"},PrivateIpAddresses:{locationName:"privateIpAddressesSet",type:"list",member:{locationName:"item",type:"structure",members:{PrivateIpAddress:{locationName:"privateIpAddress"},PrivateDnsName:{locationName:"privateDnsName"},Primary:{locationName:"primary",type:"boolean"},Association:{shape:"Sav",locationName:"association"}}}}}}},IamInstanceProfile:{locationName:"iamInstanceProfile",type:"structure",members:{Arn:{locationName:"arn"},Id:{locationName:"id"}}},EbsOptimized:{locationName:"ebsOptimized",type:"boolean"},SriovNetSupport:{locationName:"sriovNetSupport"}}}}}},Sao:{type:"structure",members:{AvailabilityZone:{locationName:"availabilityZone"},GroupName:{locationName:"groupName"},Tenancy:{locationName:"tenancy"}}},Sap:{type:"structure",members:{State:{locationName:"state"}}},Sav:{type:"structure",members:{PublicIp:{locationName:"publicIp"},PublicDnsName:{locationName:"publicDnsName"},IpOwnerId:{locationName:"ipOwnerId"}}},Sc2:{type:"list",member:{locationName:"ReservedInstancesId"}},Sca:{type:"list",member:{locationName:"item",type:"structure",members:{Frequency:{locationName:"frequency"},Amount:{locationName:"amount",type:"double"}}}},Sco:{type:"structure",members:{AvailabilityZone:{locationName:"availabilityZone"},Platform:{locationName:"platform"},InstanceCount:{locationName:"instanceCount",type:"integer"},InstanceType:{locationName:"instanceType"}}},Sd0:{type:"list",member:{locationName:"GroupName"}},Sd7:{type:"list",member:{locationName:"item",type:"structure",members:{UserId:{locationName:"userId"},Group:{locationName:"group"}}}},Sdu:{type:"structure",required:["SpotPrice","TargetCapacity","IamFleetRole","LaunchSpecifications"],members:{ClientToken:{locationName:"clientToken"},SpotPrice:{locationName:"spotPrice"},TargetCapacity:{locationName:"targetCapacity",type:"integer"},ValidFrom:{locationName:"validFrom",type:"timestamp"},ValidUntil:{locationName:"validUntil",type:"timestamp"},TerminateInstancesWithExpiration:{locationName:"terminateInstancesWithExpiration",type:"boolean"},IamFleetRole:{locationName:"iamFleetRole"},LaunchSpecifications:{locationName:"launchSpecifications",type:"list",member:{locationName:"item",type:"structure",members:{ImageId:{locationName:"imageId"},KeyName:{locationName:"keyName"},SecurityGroups:{shape:"S4c",locationName:"groupSet"},UserData:{locationName:"userData"},AddressingType:{locationName:"addressingType"},InstanceType:{locationName:"instanceType"},Placement:{shape:"Sdx",locationName:"placement"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},BlockDeviceMappings:{shape:"S93",locationName:"blockDeviceMapping"},Monitoring:{locationName:"monitoring",type:"structure",members:{Enabled:{locationName:"enabled",type:"boolean"}}},SubnetId:{locationName:"subnetId"},NetworkInterfaces:{shape:"Sdz",locationName:"networkInterfaceSet"},IamInstanceProfile:{shape:"Se1",locationName:"iamInstanceProfile"},EbsOptimized:{locationName:"ebsOptimized",type:"boolean"}}}}}},Sdx:{type:"structure",members:{AvailabilityZone:{locationName:"availabilityZone"},GroupName:{locationName:"groupName"}}},Sdz:{type:"list",member:{locationName:"item",type:"structure",members:{NetworkInterfaceId:{locationName:"networkInterfaceId"},DeviceIndex:{locationName:"deviceIndex",type:"integer"},SubnetId:{locationName:"subnetId"},Description:{locationName:"description"},PrivateIpAddress:{locationName:"privateIpAddress"},Groups:{shape:"S46",locationName:"SecurityGroupId"},DeleteOnTermination:{locationName:"deleteOnTermination",type:"boolean"},PrivateIpAddresses:{shape:"S47",locationName:"privateIpAddressesSet",queryName:"PrivateIpAddresses"},SecondaryPrivateIpAddressCount:{locationName:"secondaryPrivateIpAddressCount",type:"integer"},AssociatePublicIpAddress:{locationName:"associatePublicIpAddress",type:"boolean"}}}},Se1:{type:"structure",members:{Arn:{locationName:"arn"},Name:{locationName:"name"}}},Se4:{type:"list",member:{locationName:"item",type:"structure",members:{SpotInstanceRequestId:{locationName:"spotInstanceRequestId"},SpotPrice:{locationName:"spotPrice"},Type:{locationName:"type"},State:{locationName:"state"},Fault:{shape:"S5a",locationName:"fault"},Status:{locationName:"status",type:"structure",members:{Code:{locationName:"code"},UpdateTime:{locationName:"updateTime",type:"timestamp"},Message:{locationName:"message"}}},ValidFrom:{locationName:"validFrom",type:"timestamp"},ValidUntil:{locationName:"validUntil",type:"timestamp"},LaunchGroup:{locationName:"launchGroup"},AvailabilityZoneGroup:{locationName:"availabilityZoneGroup"},LaunchSpecification:{locationName:"launchSpecification",type:"structure",members:{ImageId:{locationName:"imageId"},KeyName:{locationName:"keyName"},SecurityGroups:{shape:"S4c",locationName:"groupSet"},UserData:{locationName:"userData"},AddressingType:{locationName:"addressingType"},InstanceType:{locationName:"instanceType"},Placement:{shape:"Sdx",locationName:"placement"},KernelId:{locationName:"kernelId"},RamdiskId:{locationName:"ramdiskId"},BlockDeviceMappings:{shape:"S93",locationName:"blockDeviceMapping"},SubnetId:{locationName:"subnetId"},NetworkInterfaces:{shape:"Sdz",locationName:"networkInterfaceSet"},IamInstanceProfile:{shape:"Se1",locationName:"iamInstanceProfile"},EbsOptimized:{locationName:"ebsOptimized",type:"boolean"},Monitoring:{shape:"Sea",locationName:"monitoring"}}},InstanceId:{locationName:"instanceId"},CreateTime:{locationName:"createTime",type:"timestamp"},ProductDescription:{locationName:"productDescription"},Tags:{shape:"Sa",locationName:"tagSet"},LaunchedAvailabilityZone:{locationName:"launchedAvailabilityZone"}}}},Sea:{type:"structure",required:["Enabled"],members:{Enabled:{locationName:"enabled",type:"boolean"}}},Seu:{type:"list",member:{locationName:"VolumeId"}},Sgo:{type:"structure",members:{S3Bucket:{},S3Key:{}}},Sgp:{type:"structure",members:{UploadStart:{type:"timestamp"},UploadEnd:{type:"timestamp"},UploadSize:{type:"double"},Comment:{}}},Sgt:{type:"list",member:{locationName:"SecurityGroup"}},Sgy:{type:"structure",required:["Format","Bytes","ImportManifestUrl"],members:{Format:{locationName:"format"},Bytes:{locationName:"bytes",type:"long"},ImportManifestUrl:{locationName:"importManifestUrl"}}},Sgz:{type:"structure",required:["Size"],members:{Size:{locationName:"size",type:"long"}}},Sh9:{type:"list",member:{locationName:"UserId"}},Shw:{type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},Monitoring:{shape:"Sap",locationName:"monitoring"}}}},Sj1:{type:"list",member:{locationName:"item",type:"structure",members:{InstanceId:{locationName:"instanceId"},CurrentState:{shape:"Sa9",locationName:"currentState"},PreviousState:{shape:"Sa9",locationName:"previousState"}}}}},examples:{},paginators:{DescribeAccountAttributes:{result_key:"AccountAttributes"},DescribeAddresses:{result_key:"Addresses"},DescribeAvailabilityZones:{result_key:"AvailabilityZones"},DescribeBundleTasks:{result_key:"BundleTasks"},DescribeConversionTasks:{result_key:"ConversionTasks"},DescribeCustomerGateways:{result_key:"CustomerGateways"},DescribeDhcpOptions:{result_key:"DhcpOptions"},DescribeExportTasks:{result_key:"ExportTasks"},DescribeImages:{result_key:"Images"},DescribeInstanceStatus:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"InstanceStatuses"},DescribeInstances:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"Reservations"},DescribeInternetGateways:{result_key:"InternetGateways"},DescribeKeyPairs:{result_key:"KeyPairs"},DescribeNetworkAcls:{result_key:"NetworkAcls"},DescribeNetworkInterfaces:{result_key:"NetworkInterfaces"},DescribePlacementGroups:{result_key:"PlacementGroups"},DescribeRegions:{result_key:"Regions"},DescribeReservedInstances:{result_key:"ReservedInstances"},DescribeReservedInstancesListings:{result_key:"ReservedInstancesListings"},DescribeReservedInstancesOfferings:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"ReservedInstancesOfferings"},DescribeReservedInstancesModifications:{input_token:"NextToken",output_token:"NextToken",result_key:"ReservedInstancesModifications"},DescribeRouteTables:{result_key:"RouteTables"},DescribeSecurityGroups:{result_key:"SecurityGroups"},DescribeSnapshots:{input_token:"NextToken",output_token:"NextToken",result_key:"Snapshots"},DescribeSpotInstanceRequests:{result_key:"SpotInstanceRequests"},DescribeSpotPriceHistory:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"SpotPriceHistory"},DescribeSubnets:{result_key:"Subnets"},DescribeTags:{result_key:"Tags"},DescribeVolumeStatus:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"VolumeStatuses"},DescribeVolumes:{input_token:"NextToken",output_token:"NextToken",limit_key:"MaxResults",result_key:"Volumes"},DescribeVpcs:{result_key:"Vpcs"},DescribeVpnConnections:{result_key:"VpnConnections"},DescribeVpnGateways:{result_key:"VpnGateways"}},waiters:{__default__:{interval:15,max_attempts:40,acceptor_type:"output"},__InstanceState:{operation:"DescribeInstances",acceptor_path:"Reservations[].Instances[].State.Name"},__InstanceStatus:{operation:"DescribeInstanceStatus",success_value:"ok"},SystemStatusOk:{"extends":"__InstanceStatus",acceptor_path:"InstanceStatuses[].SystemStatus.Status"},InstanceStatusOk:{"extends":"__InstanceStatus",acceptor_path:"InstanceStatuses[].InstanceStatus.Status"},ImageAvailable:{operation:"DescribeImages",acceptor_path:"Images[].State",success_value:"available",failure_value:["failed"]},InstanceRunning:{"extends":"__InstanceState",success_value:"running",failure_value:["shutting-down","terminated","stopping"]},InstanceStopped:{"extends":"__InstanceState",success_value:"stopped",failure_value:["pending","terminated"]},InstanceTerminated:{"extends":"__InstanceState",success_value:"terminated",failure_value:["pending","stopping"]},__ExportTaskState:{operation:"DescribeExportTasks",acceptor_path:"ExportTasks[].State"},ExportTaskCompleted:{"extends":"__ExportTaskState",success_value:"completed"},ExportTaskCancelled:{"extends":"__ExportTaskState",success_value:"cancelled"},SnapshotCompleted:{operation:"DescribeSnapshots",success_path:"Snapshots[].State",success_value:"completed"},SubnetAvailable:{operation:"DescribeSubnets",success_path:"Subnets[].State",success_value:"available"},__VolumeStatus:{operation:"DescribeVolumes",acceptor_path:"Volumes[].State"},VolumeAvailable:{"extends":"__VolumeStatus",success_value:"available",failure_value:["deleted"]},VolumeInUse:{"extends":"__VolumeStatus",success_value:"in-use",failure_value:["deleted"]},VolumeDeleted:{"extends":"__VolumeStatus",success_type:"error",success_value:"InvalidVolume.NotFound"},VpcAvailable:{operation:"DescribeVpcs",success_path:"Vpcs[].State",success_value:"available"},__VpnConnectionState:{operation:"DescribeVpnConnections",acceptor_path:"VpnConnections[].State"},VpnConnectionAvailable:{"extends":"__VpnConnectionState",success_value:"available",failure_value:["deleting","deleted"]},VpnConnectionDeleted:{"extends":"__VpnConnectionState",success_value:"deleted",failure_value:["pending"]},BundleTaskComplete:{operation:"DescribeBundleTasks",acceptor_path:"BundleTasks[].State",success_value:"complete",failure_value:["failed"]},__ConversionTaskState:{operation:"DescribeConversionTasks",acceptor_path:"ConversionTasks[].State"},ConversionTaskCompleted:{"extends":"__ConversionTaskState",success_value:"completed",failure_value:["cancelled","cancelling"]},ConversionTaskCancelled:{"extends":"__ConversionTaskState",success_value:"cancelled"},__CustomerGatewayState:{operation:"DescribeCustomerGateways",acceptor_path:"CustomerGateways[].State"},CustomerGatewayAvailable:{"extends":"__CustomerGatewayState",success_value:"available",failure_value:["deleted","deleting"]},ConversionTaskDeleted:{"extends":"__CustomerGatewayState",success_value:"deleted"},__SpotInstanceRequestState:{operation:"DescribeSpotInstanceRequests",acceptor_path:"SpotInstanceRequests[].Status.Code"},SpotInstanceRequestFulfilled:{"extends":"__SpotInstanceRequestState",success_value:"fulfilled",failure_value:["schedule-expired","canceled-before-fulfillment","bad-parameters","system-error"]}}},a.apiLoader.services.elastictranscoder={},a.ElasticTranscoder=a.Service.defineService("elastictranscoder",["2012-09-25"]),a.apiLoader.services.elastictranscoder["2012-09-25"]={version:"2.0",metadata:{apiVersion:"2012-09-25",endpointPrefix:"elastictranscoder",serviceFullName:"Amazon Elastic Transcoder",signatureVersion:"v4",protocol:"rest-json"},operations:{CancelJob:{http:{method:"DELETE",requestUri:"/2012-09-25/jobs/{Id}",responseCode:202},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{}}},CreateJob:{http:{requestUri:"/2012-09-25/jobs",responseCode:201},input:{type:"structure",required:["PipelineId","Input"],members:{PipelineId:{},Input:{shape:"S5"},Output:{shape:"Sk"},Outputs:{type:"list",member:{shape:"Sk"}},OutputKeyPrefix:{},Playlists:{type:"list",member:{type:"structure",members:{Name:{},Format:{},OutputKeys:{shape:"S1i"},HlsContentProtection:{shape:"S1j"},PlayReadyDrm:{shape:"S1n"}}}},UserMetadata:{shape:"S1s"}}},output:{type:"structure",members:{Job:{shape:"S1v"}}}},CreatePipeline:{http:{requestUri:"/2012-09-25/pipelines",responseCode:201},input:{type:"structure",required:["Name","InputBucket","Role"],members:{Name:{},InputBucket:{},OutputBucket:{},Role:{},AwsKmsKeyArn:{},Notifications:{shape:"S27"},ContentConfig:{shape:"S29"},ThumbnailConfig:{shape:"S29"}}},output:{type:"structure",members:{Pipeline:{shape:"S2i"},Warnings:{shape:"S2k"}}}},CreatePreset:{http:{requestUri:"/2012-09-25/presets",responseCode:201},input:{type:"structure",required:["Name","Container"],members:{Name:{},Description:{},Container:{},Video:{shape:"S2o"},Audio:{shape:"S34"},Thumbnails:{shape:"S3f"}}},output:{type:"structure",members:{Preset:{shape:"S3j"},Warning:{}}}},DeletePipeline:{http:{method:"DELETE",requestUri:"/2012-09-25/pipelines/{Id}",responseCode:202},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{}}},DeletePreset:{
http:{method:"DELETE",requestUri:"/2012-09-25/presets/{Id}",responseCode:202},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{}}},ListJobsByPipeline:{http:{method:"GET",requestUri:"/2012-09-25/jobsByPipeline/{PipelineId}"},input:{type:"structure",required:["PipelineId"],members:{PipelineId:{location:"uri",locationName:"PipelineId"},Ascending:{location:"querystring",locationName:"Ascending"},PageToken:{location:"querystring",locationName:"PageToken"}}},output:{type:"structure",members:{Jobs:{shape:"S3s"},NextPageToken:{}}}},ListJobsByStatus:{http:{method:"GET",requestUri:"/2012-09-25/jobsByStatus/{Status}"},input:{type:"structure",required:["Status"],members:{Status:{location:"uri",locationName:"Status"},Ascending:{location:"querystring",locationName:"Ascending"},PageToken:{location:"querystring",locationName:"PageToken"}}},output:{type:"structure",members:{Jobs:{shape:"S3s"},NextPageToken:{}}}},ListPipelines:{http:{method:"GET",requestUri:"/2012-09-25/pipelines"},input:{type:"structure",members:{Ascending:{location:"querystring",locationName:"Ascending"},PageToken:{location:"querystring",locationName:"PageToken"}}},output:{type:"structure",members:{Pipelines:{type:"list",member:{shape:"S2i"}},NextPageToken:{}}}},ListPresets:{http:{method:"GET",requestUri:"/2012-09-25/presets"},input:{type:"structure",members:{Ascending:{location:"querystring",locationName:"Ascending"},PageToken:{location:"querystring",locationName:"PageToken"}}},output:{type:"structure",members:{Presets:{type:"list",member:{shape:"S3j"}},NextPageToken:{}}}},ReadJob:{http:{method:"GET",requestUri:"/2012-09-25/jobs/{Id}"},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{Job:{shape:"S1v"}}}},ReadPipeline:{http:{method:"GET",requestUri:"/2012-09-25/pipelines/{Id}"},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{Pipeline:{shape:"S2i"},Warnings:{shape:"S2k"}}}},ReadPreset:{http:{method:"GET",requestUri:"/2012-09-25/presets/{Id}"},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"}}},output:{type:"structure",members:{Preset:{shape:"S3j"}}}},TestRole:{http:{requestUri:"/2012-09-25/roleTests",responseCode:200},input:{type:"structure",required:["Role","InputBucket","OutputBucket","Topics"],members:{Role:{},InputBucket:{},OutputBucket:{},Topics:{type:"list",member:{}}}},output:{type:"structure",members:{Success:{},Messages:{type:"list",member:{}}}}},UpdatePipeline:{http:{method:"PUT",requestUri:"/2012-09-25/pipelines/{Id}",responseCode:200},input:{type:"structure",required:["Id"],members:{Id:{location:"uri",locationName:"Id"},Name:{},InputBucket:{},Role:{},AwsKmsKeyArn:{},Notifications:{shape:"S27"},ContentConfig:{shape:"S29"},ThumbnailConfig:{shape:"S29"}}},output:{type:"structure",members:{Pipeline:{shape:"S2i"},Warnings:{shape:"S2k"}}}},UpdatePipelineNotifications:{http:{requestUri:"/2012-09-25/pipelines/{Id}/notifications"},input:{type:"structure",required:["Id","Notifications"],members:{Id:{location:"uri",locationName:"Id"},Notifications:{shape:"S27"}}},output:{type:"structure",members:{Pipeline:{shape:"S2i"}}}},UpdatePipelineStatus:{http:{requestUri:"/2012-09-25/pipelines/{Id}/status"},input:{type:"structure",required:["Id","Status"],members:{Id:{location:"uri",locationName:"Id"},Status:{}}},output:{type:"structure",members:{Pipeline:{shape:"S2i"}}}}},shapes:{S5:{type:"structure",members:{Key:{},FrameRate:{},Resolution:{},AspectRatio:{},Interlaced:{},Container:{},Encryption:{shape:"Sc"},DetectedProperties:{type:"structure",members:{Width:{type:"integer"},Height:{type:"integer"},FrameRate:{},FileSize:{type:"long"},DurationMillis:{type:"long"}}}}},Sc:{type:"structure",members:{Mode:{},Key:{},KeyMd5:{},InitializationVector:{}}},Sk:{type:"structure",members:{Key:{},ThumbnailPattern:{},ThumbnailEncryption:{shape:"Sc"},Rotate:{},PresetId:{},SegmentDuration:{},Watermarks:{shape:"Sn"},AlbumArt:{shape:"Sr"},Composition:{shape:"Sz"},Captions:{shape:"S13"},Encryption:{shape:"Sc"}}},Sn:{type:"list",member:{type:"structure",members:{PresetWatermarkId:{},InputKey:{},Encryption:{shape:"Sc"}}}},Sr:{type:"structure",members:{MergePolicy:{},Artwork:{type:"list",member:{type:"structure",members:{InputKey:{},MaxWidth:{},MaxHeight:{},SizingPolicy:{},PaddingPolicy:{},AlbumArtFormat:{},Encryption:{shape:"Sc"}}}}}},Sz:{type:"list",member:{type:"structure",members:{TimeSpan:{type:"structure",members:{StartTime:{},Duration:{}}}}}},S13:{type:"structure",members:{MergePolicy:{},CaptionSources:{type:"list",member:{type:"structure",members:{Key:{},Language:{},TimeOffset:{},Label:{},Encryption:{shape:"Sc"}}}},CaptionFormats:{type:"list",member:{type:"structure",members:{Format:{},Pattern:{},Encryption:{shape:"Sc"}}}}}},S1i:{type:"list",member:{}},S1j:{type:"structure",members:{Method:{},Key:{},KeyMd5:{},InitializationVector:{},LicenseAcquisitionUrl:{},KeyStoragePolicy:{}}},S1n:{type:"structure",members:{Format:{},Key:{},KeyMd5:{},KeyId:{},InitializationVector:{},LicenseAcquisitionUrl:{}}},S1s:{type:"map",key:{},value:{}},S1v:{type:"structure",members:{Id:{},Arn:{},PipelineId:{},Input:{shape:"S5"},Output:{shape:"S1w"},Outputs:{type:"list",member:{shape:"S1w"}},OutputKeyPrefix:{},Playlists:{type:"list",member:{type:"structure",members:{Name:{},Format:{},OutputKeys:{shape:"S1i"},HlsContentProtection:{shape:"S1j"},PlayReadyDrm:{shape:"S1n"},Status:{},StatusDetail:{}}}},Status:{},UserMetadata:{shape:"S1s"},Timing:{type:"structure",members:{SubmitTimeMillis:{type:"long"},StartTimeMillis:{type:"long"},FinishTimeMillis:{type:"long"}}}}},S1w:{type:"structure",members:{Id:{},Key:{},ThumbnailPattern:{},ThumbnailEncryption:{shape:"Sc"},Rotate:{},PresetId:{},SegmentDuration:{},Status:{},StatusDetail:{},Duration:{type:"long"},Width:{type:"integer"},Height:{type:"integer"},FrameRate:{},FileSize:{type:"long"},DurationMillis:{type:"long"},Watermarks:{shape:"Sn"},AlbumArt:{shape:"Sr"},Composition:{shape:"Sz"},Captions:{shape:"S13"},Encryption:{shape:"Sc"},AppliedColorSpaceConversion:{}}},S27:{type:"structure",members:{Progressing:{},Completed:{},Warning:{},Error:{}}},S29:{type:"structure",members:{Bucket:{},StorageClass:{},Permissions:{type:"list",member:{type:"structure",members:{GranteeType:{},Grantee:{},Access:{type:"list",member:{}}}}}}},S2i:{type:"structure",members:{Id:{},Arn:{},Name:{},Status:{},InputBucket:{},OutputBucket:{},Role:{},AwsKmsKeyArn:{},Notifications:{shape:"S27"},ContentConfig:{shape:"S29"},ThumbnailConfig:{shape:"S29"}}},S2k:{type:"list",member:{type:"structure",members:{Code:{},Message:{}}}},S2o:{type:"structure",members:{Codec:{},CodecOptions:{type:"map",key:{},value:{}},KeyframesMaxDist:{},FixedGOP:{},BitRate:{},FrameRate:{},MaxFrameRate:{},Resolution:{},AspectRatio:{},MaxWidth:{},MaxHeight:{},DisplayAspectRatio:{},SizingPolicy:{},PaddingPolicy:{},Watermarks:{type:"list",member:{type:"structure",members:{Id:{},MaxWidth:{},MaxHeight:{},SizingPolicy:{},HorizontalAlign:{},HorizontalOffset:{},VerticalAlign:{},VerticalOffset:{},Opacity:{},Target:{}}}}}},S34:{type:"structure",members:{Codec:{},SampleRate:{},BitRate:{},Channels:{},AudioPackingMode:{},CodecOptions:{type:"structure",members:{Profile:{},BitDepth:{},BitOrder:{},Signed:{}}}}},S3f:{type:"structure",members:{Format:{},Interval:{},Resolution:{},AspectRatio:{},MaxWidth:{},MaxHeight:{},SizingPolicy:{},PaddingPolicy:{}}},S3j:{type:"structure",members:{Id:{},Arn:{},Name:{},Description:{},Container:{},Audio:{shape:"S34"},Video:{shape:"S2o"},Thumbnails:{shape:"S3f"},Type:{}}},S3s:{type:"list",member:{shape:"S1v"}}},paginators:{ListJobsByPipeline:{input_token:"PageToken",output_token:"NextPageToken",result_key:"Jobs"},ListJobsByStatus:{input_token:"PageToken",output_token:"NextPageToken",result_key:"Jobs"},ListPipelines:{input_token:"PageToken",output_token:"NextPageToken",result_key:"Pipelines"},ListPresets:{input_token:"PageToken",output_token:"NextPageToken",result_key:"Presets"}},waiters:{JobComplete:{operation:"ReadJob",success_type:"output",success_path:"Job.Status",interval:30,max_attempts:120,success_value:"Complete",failure_value:["Canceled","Error"]}}},a.apiLoader.services.kinesis={},a.Kinesis=a.Service.defineService("kinesis",["2013-12-02"]),a.apiLoader.services.kinesis["2013-12-02"]={version:"2.0",metadata:{apiVersion:"2013-12-02",endpointPrefix:"kinesis",jsonVersion:"1.1",serviceAbbreviation:"Kinesis",serviceFullName:"Amazon Kinesis",signatureVersion:"v4",targetPrefix:"Kinesis_20131202",protocol:"json"},operations:{AddTagsToStream:{input:{type:"structure",required:["StreamName","Tags"],members:{StreamName:{},Tags:{type:"map",key:{},value:{}}}},http:{}},CreateStream:{input:{type:"structure",required:["StreamName","ShardCount"],members:{StreamName:{},ShardCount:{type:"integer"}}},http:{}},DeleteStream:{input:{type:"structure",required:["StreamName"],members:{StreamName:{}}},http:{}},DescribeStream:{input:{type:"structure",required:["StreamName"],members:{StreamName:{},Limit:{type:"integer"},ExclusiveStartShardId:{}}},output:{type:"structure",required:["StreamDescription"],members:{StreamDescription:{type:"structure",required:["StreamName","StreamARN","StreamStatus","Shards","HasMoreShards"],members:{StreamName:{},StreamARN:{},StreamStatus:{},Shards:{type:"list",member:{type:"structure",required:["ShardId","HashKeyRange","SequenceNumberRange"],members:{ShardId:{},ParentShardId:{},AdjacentParentShardId:{},HashKeyRange:{type:"structure",required:["StartingHashKey","EndingHashKey"],members:{StartingHashKey:{},EndingHashKey:{}}},SequenceNumberRange:{type:"structure",required:["StartingSequenceNumber"],members:{StartingSequenceNumber:{},EndingSequenceNumber:{}}}}}},HasMoreShards:{type:"boolean"}}}}},http:{}},GetRecords:{input:{type:"structure",required:["ShardIterator"],members:{ShardIterator:{},Limit:{type:"integer"}}},output:{type:"structure",required:["Records"],members:{Records:{type:"list",member:{type:"structure",required:["SequenceNumber","Data","PartitionKey"],members:{SequenceNumber:{},Data:{type:"blob"},PartitionKey:{}}}},NextShardIterator:{},MillisBehindLatest:{type:"long"}}},http:{}},GetShardIterator:{input:{type:"structure",required:["StreamName","ShardId","ShardIteratorType"],members:{StreamName:{},ShardId:{},ShardIteratorType:{},StartingSequenceNumber:{}}},output:{type:"structure",members:{ShardIterator:{}}},http:{}},ListStreams:{input:{type:"structure",members:{Limit:{type:"integer"},ExclusiveStartStreamName:{}}},output:{type:"structure",required:["StreamNames","HasMoreStreams"],members:{StreamNames:{type:"list",member:{}},HasMoreStreams:{type:"boolean"}}},http:{}},ListTagsForStream:{input:{type:"structure",required:["StreamName"],members:{StreamName:{},ExclusiveStartTagKey:{},Limit:{type:"integer"}}},output:{type:"structure",required:["Tags","HasMoreTags"],members:{Tags:{type:"list",member:{type:"structure",required:["Key"],members:{Key:{},Value:{}}}},HasMoreTags:{type:"boolean"}}},http:{}},MergeShards:{input:{type:"structure",required:["StreamName","ShardToMerge","AdjacentShardToMerge"],members:{StreamName:{},ShardToMerge:{},AdjacentShardToMerge:{}}},http:{}},PutRecord:{input:{type:"structure",required:["StreamName","Data","PartitionKey"],members:{StreamName:{},Data:{type:"blob"},PartitionKey:{},ExplicitHashKey:{},SequenceNumberForOrdering:{}}},output:{type:"structure",required:["ShardId","SequenceNumber"],members:{ShardId:{},SequenceNumber:{}}},http:{}},PutRecords:{input:{type:"structure",required:["Records","StreamName"],members:{Records:{type:"list",member:{type:"structure",required:["Data","PartitionKey"],members:{Data:{type:"blob"},ExplicitHashKey:{},PartitionKey:{}}}},StreamName:{}}},output:{type:"structure",required:["Records"],members:{FailedRecordCount:{type:"integer"},Records:{type:"list",member:{type:"structure",members:{SequenceNumber:{},ShardId:{},ErrorCode:{},ErrorMessage:{}}}}}},http:{}},RemoveTagsFromStream:{input:{type:"structure",required:["StreamName","TagKeys"],members:{StreamName:{},TagKeys:{type:"list",member:{}}}},http:{}},SplitShard:{input:{type:"structure",required:["StreamName","ShardToSplit","NewStartingHashKey"],members:{StreamName:{},ShardToSplit:{},NewStartingHashKey:{}}},http:{}}},shapes:{},paginators:{DescribeStream:{input_token:"ExclusiveStartShardId",limit_key:"Limit",more_results:"StreamDescription.HasMoreShards",output_token:"StreamDescription.Shards[-1].ShardId",result_key:"StreamDescription.Shards"},ListStreams:{input_token:"ExclusiveStartStreamName",limit_key:"Limit",more_results:"HasMoreStreams",output_token:"StreamNames[-1]",result_key:"StreamNames"}}},a.apiLoader.services.lambda={},a.Lambda=a.Service.defineService("lambda",["2014-11-11","2015-03-31"]),a.apiLoader.services.lambda["2015-03-31"]={version:"2.0",metadata:{apiVersion:"2015-03-31",endpointPrefix:"lambda",serviceFullName:"AWS Lambda",signatureVersion:"v4",protocol:"rest-json"},operations:{AddPermission:{http:{requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/policy",responseCode:201},input:{type:"structure",required:["FunctionName","StatementId","Action","Principal"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},StatementId:{},Action:{},Principal:{},SourceArn:{},SourceAccount:{}}},output:{type:"structure",members:{Statement:{}}}},CreateEventSourceMapping:{http:{requestUri:"/2015-03-31/event-source-mappings/",responseCode:202},input:{type:"structure",required:["EventSourceArn","FunctionName","StartingPosition"],members:{EventSourceArn:{},FunctionName:{},Enabled:{type:"boolean"},BatchSize:{type:"integer"},StartingPosition:{}}},output:{shape:"Se"}},CreateFunction:{http:{requestUri:"/2015-03-31/functions",responseCode:201},input:{type:"structure",required:["FunctionName","Runtime","Role","Handler","Code"],members:{FunctionName:{},Runtime:{},Role:{},Handler:{},Description:{},Timeout:{type:"integer"},MemorySize:{type:"integer"},Code:{type:"structure",members:{ZipFile:{type:"blob"},S3Bucket:{},S3Key:{},S3ObjectVersion:{}}}}},output:{shape:"St"}},DeleteEventSourceMapping:{http:{method:"DELETE",requestUri:"/2015-03-31/event-source-mappings/{UUID}",responseCode:202},input:{type:"structure",required:["UUID"],members:{UUID:{location:"uri",locationName:"UUID"}}},output:{shape:"Se"}},DeleteFunction:{http:{method:"DELETE",requestUri:"/2015-03-31/functions/{FunctionName}",responseCode:204},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"}}}},GetEventSourceMapping:{http:{method:"GET",requestUri:"/2015-03-31/event-source-mappings/{UUID}",responseCode:200},input:{type:"structure",required:["UUID"],members:{UUID:{location:"uri",locationName:"UUID"}}},output:{shape:"Se"}},GetFunction:{http:{method:"GET",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD",responseCode:200},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"}}},output:{type:"structure",members:{Configuration:{shape:"St"},Code:{type:"structure",members:{RepositoryType:{},Location:{}}}}}},GetFunctionConfiguration:{http:{method:"GET",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/configuration",responseCode:200},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"}}},output:{shape:"St"}},GetPolicy:{http:{method:"GET",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/policy",responseCode:200},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"}}},output:{type:"structure",members:{Policy:{}}}},Invoke:{http:{requestUri:"/2015-03-31/functions/{FunctionName}/invocations"},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},InvocationType:{location:"header",locationName:"X-Amz-Invocation-Type"},LogType:{location:"header",locationName:"X-Amz-Log-Type"},ClientContext:{location:"header",locationName:"X-Amz-Client-Context"},Payload:{type:"blob"}},payload:"Payload"},output:{type:"structure",members:{StatusCode:{location:"statusCode",type:"integer"},FunctionError:{location:"header",locationName:"X-Amz-Function-Error"},LogResult:{location:"header",locationName:"X-Amz-Log-Result"},Payload:{type:"blob"}},payload:"Payload"}},InvokeAsync:{http:{requestUri:"/2014-11-13/functions/{FunctionName}/invoke-async/",responseCode:202},input:{deprecated:!0,type:"structure",required:["FunctionName","InvokeArgs"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},InvokeArgs:{type:"blob",streaming:!0}},payload:"InvokeArgs"},output:{deprecated:!0,type:"structure",members:{Status:{location:"statusCode",type:"integer"}}},deprecated:!0},ListEventSourceMappings:{http:{method:"GET",requestUri:"/2015-03-31/event-source-mappings/",responseCode:200},input:{type:"structure",members:{EventSourceArn:{location:"querystring",locationName:"EventSourceArn"},FunctionName:{location:"querystring",locationName:"FunctionName"},Marker:{location:"querystring",locationName:"Marker"},MaxItems:{location:"querystring",locationName:"MaxItems",type:"integer"}}},output:{type:"structure",members:{NextMarker:{},EventSourceMappings:{type:"list",member:{shape:"Se"}}}}},ListFunctions:{http:{method:"GET",requestUri:"/2015-03-31/functions/",responseCode:200},input:{type:"structure",members:{Marker:{location:"querystring",locationName:"Marker"},MaxItems:{location:"querystring",locationName:"MaxItems",type:"integer"}}},output:{type:"structure",members:{NextMarker:{},Functions:{type:"list",member:{shape:"St"}}}}},RemovePermission:{http:{method:"DELETE",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/policy/{StatementId}",responseCode:204},input:{type:"structure",required:["FunctionName","StatementId"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},StatementId:{location:"uri",locationName:"StatementId"}}}},UpdateEventSourceMapping:{http:{method:"PUT",requestUri:"/2015-03-31/event-source-mappings/{UUID}",responseCode:202},input:{type:"structure",required:["UUID"],members:{UUID:{location:"uri",locationName:"UUID"},FunctionName:{},Enabled:{type:"boolean"},BatchSize:{type:"integer"}}},output:{shape:"Se"}},UpdateFunctionCode:{http:{method:"PUT",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/code",responseCode:200},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},ZipFile:{type:"blob"},S3Bucket:{},S3Key:{},S3ObjectVersion:{}}},output:{shape:"St"}},UpdateFunctionConfiguration:{http:{method:"PUT",requestUri:"/2015-03-31/functions/{FunctionName}/versions/HEAD/configuration",responseCode:200},input:{type:"structure",required:["FunctionName"],members:{FunctionName:{location:"uri",locationName:"FunctionName"},Role:{},Handler:{},Description:{},Timeout:{type:"integer"},MemorySize:{type:"integer"}}},output:{shape:"St"}}},shapes:{Se:{type:"structure",members:{UUID:{},BatchSize:{type:"integer"},EventSourceArn:{},FunctionArn:{},LastModified:{type:"timestamp"},LastProcessingResult:{},State:{},StateTransitionReason:{}}},St:{type:"structure",members:{FunctionName:{},FunctionArn:{},Runtime:{},Role:{},Handler:{},CodeSize:{type:"long"},Description:{},Timeout:{type:"integer"},MemorySize:{type:"integer"},LastModified:{}}}},paginators:{ListEventSourceMappings:{input_token:"Marker",output_token:"NextMarker",limit_key:"MaxItems",result_key:"EventSourceMappings"},ListFunctions:{input_token:"Marker",output_token:"NextMarker",limit_key:"MaxItems",result_key:"Functions"}}},a.apiLoader.services.machinelearning={},a.MachineLearning=a.Service.defineService("machinelearning",["2014-12-12"]),e("./services/machinelearning"),a.apiLoader.services.machinelearning["2014-12-12"]={version:"2.0",metadata:{apiVersion:"2014-12-12",endpointPrefix:"machinelearning",jsonVersion:"1.1",serviceFullName:"Amazon Machine Learning",signatureVersion:"v4",targetPrefix:"AmazonML_20141212",protocol:"json"},operations:{CreateBatchPrediction:{input:{type:"structure",required:["BatchPredictionId","MLModelId","BatchPredictionDataSourceId","OutputUri"],members:{BatchPredictionId:{},BatchPredictionName:{},MLModelId:{},BatchPredictionDataSourceId:{},OutputUri:{}}},output:{type:"structure",members:{BatchPredictionId:{}}},http:{}},CreateDataSourceFromRDS:{input:{type:"structure",required:["DataSourceId","RDSData","RoleARN"],members:{DataSourceId:{},DataSourceName:{},RDSData:{type:"structure",required:["DatabaseInformation","SelectSqlQuery","DatabaseCredentials","S3StagingLocation","ResourceRole","ServiceRole","SubnetId","SecurityGroupIds"],members:{DatabaseInformation:{shape:"S8"},SelectSqlQuery:{},DatabaseCredentials:{type:"structure",required:["Username","Password"],members:{Username:{},Password:{}}},S3StagingLocation:{},DataRearrangement:{},DataSchema:{},DataSchemaUri:{},ResourceRole:{},ServiceRole:{},SubnetId:{},SecurityGroupIds:{type:"list",member:{}}}},RoleARN:{},ComputeStatistics:{type:"boolean"}}},output:{type:"structure",members:{DataSourceId:{}}},http:{}},CreateDataSourceFromRedshift:{input:{type:"structure",required:["DataSourceId","DataSpec","RoleARN"],members:{DataSourceId:{},DataSourceName:{},DataSpec:{type:"structure",required:["DatabaseInformation","SelectSqlQuery","DatabaseCredentials","S3StagingLocation"],members:{DatabaseInformation:{shape:"Sr"},SelectSqlQuery:{},DatabaseCredentials:{type:"structure",required:["Username","Password"],members:{Username:{},Password:{}}},S3StagingLocation:{},DataRearrangement:{},DataSchema:{},DataSchemaUri:{}}},RoleARN:{},ComputeStatistics:{type:"boolean"}}},output:{type:"structure",members:{DataSourceId:{}}},http:{}},CreateDataSourceFromS3:{input:{type:"structure",required:["DataSourceId","DataSpec"],members:{DataSourceId:{},DataSourceName:{},DataSpec:{type:"structure",required:["DataLocationS3"],members:{DataLocationS3:{},DataRearrangement:{},DataSchema:{},DataSchemaLocationS3:{}}},ComputeStatistics:{type:"boolean"}}},output:{type:"structure",members:{DataSourceId:{}}},http:{}},CreateEvaluation:{input:{type:"structure",required:["EvaluationId","MLModelId","EvaluationDataSourceId"],members:{EvaluationId:{},EvaluationName:{},MLModelId:{},EvaluationDataSourceId:{}}},output:{type:"structure",members:{EvaluationId:{}}},http:{}},CreateMLModel:{input:{type:"structure",required:["MLModelId","MLModelType","TrainingDataSourceId"],members:{MLModelId:{},MLModelName:{},MLModelType:{},Parameters:{shape:"S16"},TrainingDataSourceId:{},Recipe:{},RecipeUri:{}}},output:{type:"structure",members:{MLModelId:{}}},http:{}},CreateRealtimeEndpoint:{input:{type:"structure",required:["MLModelId"],members:{MLModelId:{}}},output:{type:"structure",members:{MLModelId:{},RealtimeEndpointInfo:{shape:"S1c"}}},http:{}},DeleteBatchPrediction:{input:{type:"structure",required:["BatchPredictionId"],members:{BatchPredictionId:{}}},output:{type:"structure",members:{BatchPredictionId:{}}},http:{}},DeleteDataSource:{input:{type:"structure",required:["DataSourceId"],members:{DataSourceId:{}}},output:{type:"structure",members:{DataSourceId:{}}},http:{}},DeleteEvaluation:{input:{type:"structure",required:["EvaluationId"],members:{EvaluationId:{}}},output:{type:"structure",members:{EvaluationId:{}}},http:{}},DeleteMLModel:{input:{type:"structure",required:["MLModelId"],members:{MLModelId:{}}},output:{type:"structure",members:{MLModelId:{}}},http:{}},DeleteRealtimeEndpoint:{input:{type:"structure",required:["MLModelId"],members:{MLModelId:{}}},output:{type:"structure",members:{MLModelId:{},RealtimeEndpointInfo:{shape:"S1c"}}},http:{}},DescribeBatchPredictions:{input:{type:"structure",members:{FilterVariable:{},EQ:{},GT:{},LT:{},GE:{},LE:{},NE:{},Prefix:{},SortOrder:{},NextToken:{},Limit:{type:"integer"}}},output:{type:"structure",members:{Results:{type:"list",member:{type:"structure",members:{BatchPredictionId:{},MLModelId:{},BatchPredictionDataSourceId:{},InputDataLocationS3:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},OutputUri:{},Message:{}}}},NextToken:{}}},http:{}},DescribeDataSources:{input:{type:"structure",members:{FilterVariable:{},EQ:{},GT:{},LT:{},GE:{},LE:{},NE:{},Prefix:{},SortOrder:{},NextToken:{},Limit:{type:"integer"}}},output:{type:"structure",members:{Results:{type:"list",member:{type:"structure",members:{DataSourceId:{},DataLocationS3:{},DataRearrangement:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},DataSizeInBytes:{type:"long"},NumberOfFiles:{type:"long"},Name:{},Status:{},Message:{},RedshiftMetadata:{shape:"S28"},RDSMetadata:{shape:"S29"},RoleARN:{},ComputeStatistics:{type:"boolean"}}}},NextToken:{}}},http:{}},DescribeEvaluations:{input:{type:"structure",members:{FilterVariable:{},EQ:{},GT:{},LT:{},GE:{},LE:{},NE:{},Prefix:{},SortOrder:{},NextToken:{},Limit:{type:"integer"}}},output:{type:"structure",members:{Results:{type:"list",member:{type:"structure",members:{EvaluationId:{},MLModelId:{},EvaluationDataSourceId:{},InputDataLocationS3:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},PerformanceMetrics:{shape:"S2g"},Message:{}}}},NextToken:{}}},http:{}},DescribeMLModels:{input:{type:"structure",members:{FilterVariable:{},EQ:{},GT:{},LT:{},GE:{},LE:{},NE:{},Prefix:{},SortOrder:{},NextToken:{},Limit:{type:"integer"}}},output:{type:"structure",members:{Results:{type:"list",member:{type:"structure",members:{MLModelId:{},TrainingDataSourceId:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},SizeInBytes:{type:"long"},EndpointInfo:{shape:"S1c"},TrainingParameters:{shape:"S16"},InputDataLocationS3:{},Algorithm:{},MLModelType:{},ScoreThreshold:{type:"float"},ScoreThresholdLastUpdatedAt:{type:"timestamp"},Message:{}}}},NextToken:{}}},http:{}},GetBatchPrediction:{input:{type:"structure",required:["BatchPredictionId"],members:{BatchPredictionId:{}}},output:{type:"structure",members:{BatchPredictionId:{},MLModelId:{},BatchPredictionDataSourceId:{},InputDataLocationS3:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},OutputUri:{},LogUri:{},Message:{}}},http:{}},GetDataSource:{input:{type:"structure",required:["DataSourceId"],members:{DataSourceId:{},Verbose:{type:"boolean"}}},output:{type:"structure",members:{DataSourceId:{},DataLocationS3:{},DataRearrangement:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},DataSizeInBytes:{type:"long"},NumberOfFiles:{type:"long"},Name:{},Status:{},LogUri:{},Message:{},RedshiftMetadata:{shape:"S28"},RDSMetadata:{shape:"S29"},RoleARN:{},ComputeStatistics:{type:"boolean"},DataSourceSchema:{}}},http:{}},GetEvaluation:{input:{type:"structure",required:["EvaluationId"],members:{EvaluationId:{}}},output:{type:"structure",members:{EvaluationId:{},MLModelId:{},EvaluationDataSourceId:{},InputDataLocationS3:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},PerformanceMetrics:{shape:"S2g"},LogUri:{},Message:{}}},http:{}},GetMLModel:{input:{type:"structure",required:["MLModelId"],members:{MLModelId:{},Verbose:{type:"boolean"}}},output:{type:"structure",members:{MLModelId:{},TrainingDataSourceId:{},CreatedByIamUser:{},CreatedAt:{type:"timestamp"},LastUpdatedAt:{type:"timestamp"},Name:{},Status:{},SizeInBytes:{type:"long"},EndpointInfo:{shape:"S1c"},TrainingParameters:{shape:"S16"},InputDataLocationS3:{},MLModelType:{},ScoreThreshold:{type:"float"},ScoreThresholdLastUpdatedAt:{type:"timestamp"},LogUri:{},Message:{},Recipe:{},Schema:{}}},http:{}},Predict:{input:{type:"structure",required:["MLModelId","Record","PredictEndpoint"],members:{MLModelId:{},Record:{type:"map",key:{},value:{}},PredictEndpoint:{}}},output:{type:"structure",members:{Prediction:{type:"structure",members:{predictedLabel:{},predictedValue:{type:"float"},predictedScores:{type:"map",key:{},value:{type:"float"}},details:{type:"map",key:{},value:{}}}}}},http:{}},UpdateBatchPrediction:{input:{type:"structure",required:["BatchPredictionId","BatchPredictionName"],members:{BatchPredictionId:{},BatchPredictionName:{}}},output:{type:"structure",members:{BatchPredictionId:{}}},http:{}},UpdateDataSource:{input:{type:"structure",required:["DataSourceId","DataSourceName"],members:{DataSourceId:{},DataSourceName:{}}},output:{type:"structure",members:{DataSourceId:{}}},http:{}},UpdateEvaluation:{input:{type:"structure",required:["EvaluationId","EvaluationName"],members:{EvaluationId:{},EvaluationName:{}}},output:{type:"structure",members:{EvaluationId:{}}},http:{}},UpdateMLModel:{input:{type:"structure",required:["MLModelId"],members:{MLModelId:{},MLModelName:{},ScoreThreshold:{type:"float"}}},output:{type:"structure",members:{MLModelId:{}}},http:{}}},shapes:{S8:{type:"structure",required:["InstanceIdentifier","DatabaseName"],members:{InstanceIdentifier:{},DatabaseName:{}}},Sr:{type:"structure",required:["DatabaseName","ClusterIdentifier"],members:{DatabaseName:{},ClusterIdentifier:{}}},S16:{type:"map",key:{},value:{}},S1c:{type:"structure",members:{PeakRequestsPerSecond:{type:"integer"},CreatedAt:{type:"timestamp"},EndpointUrl:{},EndpointStatus:{}}},S28:{type:"structure",members:{RedshiftDatabase:{shape:"Sr"},DatabaseUserName:{},SelectSqlQuery:{}}},S29:{type:"structure",members:{Database:{shape:"S8"},DatabaseUserName:{},SelectSqlQuery:{},ResourceRole:{},ServiceRole:{},DataPipelineId:{}}},S2g:{type:"structure",members:{Properties:{type:"map",key:{},value:{}}}}},paginators:{DescribeBatchPredictions:{limit_key:"Limit",output_token:"NextToken",input_token:"NextToken",result_key:"Results"},DescribeDataSources:{limit_key:"Limit",output_token:"NextToken",input_token:"NextToken",result_key:"Results"},DescribeEvaluations:{limit_key:"Limit",output_token:"NextToken",input_token:"NextToken",result_key:"Results"},DescribeMLModels:{limit_key:"Limit",output_token:"NextToken",input_token:"NextToken",result_key:"Results"}}},a.apiLoader.services.mobileanalytics={},a.MobileAnalytics=a.Service.defineService("mobileanalytics",["2014-06-05"]),a.apiLoader.services.mobileanalytics["2014-06-05"]={version:"2.0",metadata:{apiVersion:"2014-06-05",endpointPrefix:"mobileanalytics",serviceFullName:"Amazon Mobile Analytics",signatureVersion:"v4",protocol:"rest-json"},operations:{PutEvents:{http:{requestUri:"/2014-06-05/events",responseCode:202},input:{type:"structure",required:["events","clientContext"],members:{events:{type:"list",member:{type:"structure",required:["eventType","timestamp"],members:{eventType:{},timestamp:{},session:{type:"structure",members:{id:{},duration:{type:"long"},startTimestamp:{},stopTimestamp:{}}},version:{},attributes:{type:"map",key:{},value:{}},metrics:{type:"map",key:{},value:{type:"double"}}}}},clientContext:{location:"header",locationName:"x-amz-Client-Context"},clientContextEncoding:{location:"header",locationName:"x-amz-Client-Context-Encoding"}}}}},shapes:{}},a.apiLoader.services.opsworks={},a.OpsWorks=a.Service.defineService("opsworks",["2013-02-18"]),a.apiLoader.services.opsworks["2013-02-18"]={version:"2.0",metadata:{apiVersion:"2013-02-18",endpointPrefix:"opsworks",jsonVersion:"1.1",serviceFullName:"AWS OpsWorks",signatureVersion:"v4",targetPrefix:"OpsWorks_20130218",protocol:"json"},operations:{AssignInstance:{input:{type:"structure",required:["InstanceId","LayerIds"],members:{InstanceId:{},LayerIds:{shape:"S3"}}},http:{}},AssignVolume:{input:{type:"structure",required:["VolumeId"],members:{VolumeId:{},InstanceId:{}}},http:{}},AssociateElasticIp:{input:{type:"structure",required:["ElasticIp"],members:{ElasticIp:{},InstanceId:{}}},http:{}},AttachElasticLoadBalancer:{input:{type:"structure",required:["ElasticLoadBalancerName","LayerId"],members:{ElasticLoadBalancerName:{},LayerId:{}}},http:{}},CloneStack:{input:{type:"structure",required:["SourceStackId","ServiceRoleArn"],
members:{SourceStackId:{},Name:{},Region:{},VpcId:{},Attributes:{shape:"S8"},ServiceRoleArn:{},DefaultInstanceProfileArn:{},DefaultOs:{},HostnameTheme:{},DefaultAvailabilityZone:{},DefaultSubnetId:{},CustomJson:{},ConfigurationManager:{shape:"Sa"},ChefConfiguration:{shape:"Sb"},UseCustomCookbooks:{type:"boolean"},UseOpsworksSecurityGroups:{type:"boolean"},CustomCookbooksSource:{shape:"Sd"},DefaultSshKeyName:{},ClonePermissions:{type:"boolean"},CloneAppIds:{shape:"S3"},DefaultRootDeviceType:{},AgentVersion:{}}},output:{type:"structure",members:{StackId:{}}},http:{}},CreateApp:{input:{type:"structure",required:["StackId","Name","Type"],members:{StackId:{},Shortname:{},Name:{},Description:{},DataSources:{shape:"Si"},Type:{},AppSource:{shape:"Sd"},Domains:{shape:"S3"},EnableSsl:{type:"boolean"},SslConfiguration:{shape:"Sl"},Attributes:{shape:"Sm"},Environment:{shape:"So"}}},output:{type:"structure",members:{AppId:{}}},http:{}},CreateDeployment:{input:{type:"structure",required:["StackId","Command"],members:{StackId:{},AppId:{},InstanceIds:{shape:"S3"},Command:{shape:"Ss"},Comment:{},CustomJson:{}}},output:{type:"structure",members:{DeploymentId:{}}},http:{}},CreateInstance:{input:{type:"structure",required:["StackId","LayerIds","InstanceType"],members:{StackId:{},LayerIds:{shape:"S3"},InstanceType:{},AutoScalingType:{},Hostname:{},Os:{},AmiId:{},SshKeyName:{},AvailabilityZone:{},VirtualizationType:{},SubnetId:{},Architecture:{},RootDeviceType:{},BlockDeviceMappings:{shape:"Sz"},InstallUpdatesOnBoot:{type:"boolean"},EbsOptimized:{type:"boolean"},AgentVersion:{}}},output:{type:"structure",members:{InstanceId:{}}},http:{}},CreateLayer:{input:{type:"structure",required:["StackId","Type","Name","Shortname"],members:{StackId:{},Type:{},Name:{},Shortname:{},Attributes:{shape:"S17"},CustomInstanceProfileArn:{},CustomJson:{},CustomSecurityGroupIds:{shape:"S3"},Packages:{shape:"S3"},VolumeConfigurations:{shape:"S19"},EnableAutoHealing:{type:"boolean"},AutoAssignElasticIps:{type:"boolean"},AutoAssignPublicIps:{type:"boolean"},CustomRecipes:{shape:"S1b"},InstallUpdatesOnBoot:{type:"boolean"},UseEbsOptimizedInstances:{type:"boolean"},LifecycleEventConfiguration:{shape:"S1c"}}},output:{type:"structure",members:{LayerId:{}}},http:{}},CreateStack:{input:{type:"structure",required:["Name","Region","ServiceRoleArn","DefaultInstanceProfileArn"],members:{Name:{},Region:{},VpcId:{},Attributes:{shape:"S8"},ServiceRoleArn:{},DefaultInstanceProfileArn:{},DefaultOs:{},HostnameTheme:{},DefaultAvailabilityZone:{},DefaultSubnetId:{},CustomJson:{},ConfigurationManager:{shape:"Sa"},ChefConfiguration:{shape:"Sb"},UseCustomCookbooks:{type:"boolean"},UseOpsworksSecurityGroups:{type:"boolean"},CustomCookbooksSource:{shape:"Sd"},DefaultSshKeyName:{},DefaultRootDeviceType:{},AgentVersion:{}}},output:{type:"structure",members:{StackId:{}}},http:{}},CreateUserProfile:{input:{type:"structure",required:["IamUserArn"],members:{IamUserArn:{},SshUsername:{},SshPublicKey:{},AllowSelfManagement:{type:"boolean"}}},output:{type:"structure",members:{IamUserArn:{}}},http:{}},DeleteApp:{input:{type:"structure",required:["AppId"],members:{AppId:{}}},http:{}},DeleteInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{},DeleteElasticIp:{type:"boolean"},DeleteVolumes:{type:"boolean"}}},http:{}},DeleteLayer:{input:{type:"structure",required:["LayerId"],members:{LayerId:{}}},http:{}},DeleteStack:{input:{type:"structure",required:["StackId"],members:{StackId:{}}},http:{}},DeleteUserProfile:{input:{type:"structure",required:["IamUserArn"],members:{IamUserArn:{}}},http:{}},DeregisterEcsCluster:{input:{type:"structure",required:["EcsClusterArn"],members:{EcsClusterArn:{}}},http:{}},DeregisterElasticIp:{input:{type:"structure",required:["ElasticIp"],members:{ElasticIp:{}}},http:{}},DeregisterInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{}}},http:{}},DeregisterRdsDbInstance:{input:{type:"structure",required:["RdsDbInstanceArn"],members:{RdsDbInstanceArn:{}}},http:{}},DeregisterVolume:{input:{type:"structure",required:["VolumeId"],members:{VolumeId:{}}},http:{}},DescribeAgentVersions:{input:{type:"structure",members:{StackId:{},ConfigurationManager:{shape:"Sa"}}},output:{type:"structure",members:{AgentVersions:{type:"list",member:{type:"structure",members:{Version:{},ConfigurationManager:{shape:"Sa"}}}}}},http:{}},DescribeApps:{input:{type:"structure",members:{StackId:{},AppIds:{shape:"S3"}}},output:{type:"structure",members:{Apps:{type:"list",member:{type:"structure",members:{AppId:{},StackId:{},Shortname:{},Name:{},Description:{},DataSources:{shape:"Si"},Type:{},AppSource:{shape:"Sd"},Domains:{shape:"S3"},EnableSsl:{type:"boolean"},SslConfiguration:{shape:"Sl"},Attributes:{shape:"Sm"},CreatedAt:{},Environment:{shape:"So"}}}}}},http:{}},DescribeCommands:{input:{type:"structure",members:{DeploymentId:{},InstanceId:{},CommandIds:{shape:"S3"}}},output:{type:"structure",members:{Commands:{type:"list",member:{type:"structure",members:{CommandId:{},InstanceId:{},DeploymentId:{},CreatedAt:{},AcknowledgedAt:{},CompletedAt:{},Status:{},ExitCode:{type:"integer"},LogUrl:{},Type:{}}}}}},http:{}},DescribeDeployments:{input:{type:"structure",members:{StackId:{},AppId:{},DeploymentIds:{shape:"S3"}}},output:{type:"structure",members:{Deployments:{type:"list",member:{type:"structure",members:{DeploymentId:{},StackId:{},AppId:{},CreatedAt:{},CompletedAt:{},Duration:{type:"integer"},IamUserArn:{},Comment:{},Command:{shape:"Ss"},Status:{},CustomJson:{},InstanceIds:{shape:"S3"}}}}}},http:{}},DescribeEcsClusters:{input:{type:"structure",members:{EcsClusterArns:{shape:"S3"},StackId:{},NextToken:{},MaxResults:{type:"integer"}}},output:{type:"structure",members:{EcsClusters:{type:"list",member:{type:"structure",members:{EcsClusterArn:{},EcsClusterName:{},StackId:{},RegisteredAt:{}}}},NextToken:{}}},http:{}},DescribeElasticIps:{input:{type:"structure",members:{InstanceId:{},StackId:{},Ips:{shape:"S3"}}},output:{type:"structure",members:{ElasticIps:{type:"list",member:{type:"structure",members:{Ip:{},Name:{},Domain:{},Region:{},InstanceId:{}}}}}},http:{}},DescribeElasticLoadBalancers:{input:{type:"structure",members:{StackId:{},LayerIds:{shape:"S3"}}},output:{type:"structure",members:{ElasticLoadBalancers:{type:"list",member:{type:"structure",members:{ElasticLoadBalancerName:{},Region:{},DnsName:{},StackId:{},LayerId:{},VpcId:{},AvailabilityZones:{shape:"S3"},SubnetIds:{shape:"S3"},Ec2InstanceIds:{shape:"S3"}}}}}},http:{}},DescribeInstances:{input:{type:"structure",members:{StackId:{},LayerId:{},InstanceIds:{shape:"S3"}}},output:{type:"structure",members:{Instances:{type:"list",member:{type:"structure",members:{AgentVersion:{},AmiId:{},Architecture:{},AutoScalingType:{},AvailabilityZone:{},BlockDeviceMappings:{shape:"Sz"},CreatedAt:{},EbsOptimized:{type:"boolean"},Ec2InstanceId:{},EcsClusterArn:{},EcsContainerInstanceArn:{},ElasticIp:{},Hostname:{},InfrastructureClass:{},InstallUpdatesOnBoot:{type:"boolean"},InstanceId:{},InstanceProfileArn:{},InstanceType:{},LastServiceErrorId:{},LayerIds:{shape:"S3"},Os:{},Platform:{},PrivateDns:{},PrivateIp:{},PublicDns:{},PublicIp:{},RegisteredBy:{},ReportedAgentVersion:{},ReportedOs:{type:"structure",members:{Family:{},Name:{},Version:{}}},RootDeviceType:{},RootDeviceVolumeId:{},SecurityGroupIds:{shape:"S3"},SshHostDsaKeyFingerprint:{},SshHostRsaKeyFingerprint:{},SshKeyName:{},StackId:{},Status:{},SubnetId:{},VirtualizationType:{}}}}}},http:{}},DescribeLayers:{input:{type:"structure",members:{StackId:{},LayerIds:{shape:"S3"}}},output:{type:"structure",members:{Layers:{type:"list",member:{type:"structure",members:{StackId:{},LayerId:{},Type:{},Name:{},Shortname:{},Attributes:{shape:"S17"},CustomInstanceProfileArn:{},CustomJson:{},CustomSecurityGroupIds:{shape:"S3"},DefaultSecurityGroupNames:{shape:"S3"},Packages:{shape:"S3"},VolumeConfigurations:{shape:"S19"},EnableAutoHealing:{type:"boolean"},AutoAssignElasticIps:{type:"boolean"},AutoAssignPublicIps:{type:"boolean"},DefaultRecipes:{shape:"S1b"},CustomRecipes:{shape:"S1b"},CreatedAt:{},InstallUpdatesOnBoot:{type:"boolean"},UseEbsOptimizedInstances:{type:"boolean"},LifecycleEventConfiguration:{shape:"S1c"}}}}}},http:{}},DescribeLoadBasedAutoScaling:{input:{type:"structure",required:["LayerIds"],members:{LayerIds:{shape:"S3"}}},output:{type:"structure",members:{LoadBasedAutoScalingConfigurations:{type:"list",member:{type:"structure",members:{LayerId:{},Enable:{type:"boolean"},UpScaling:{shape:"S30"},DownScaling:{shape:"S30"}}}}}},http:{}},DescribeMyUserProfile:{output:{type:"structure",members:{UserProfile:{type:"structure",members:{IamUserArn:{},Name:{},SshUsername:{},SshPublicKey:{}}}}},http:{}},DescribePermissions:{input:{type:"structure",members:{IamUserArn:{},StackId:{}}},output:{type:"structure",members:{Permissions:{type:"list",member:{type:"structure",members:{StackId:{},IamUserArn:{},AllowSsh:{type:"boolean"},AllowSudo:{type:"boolean"},Level:{}}}}}},http:{}},DescribeRaidArrays:{input:{type:"structure",members:{InstanceId:{},StackId:{},RaidArrayIds:{shape:"S3"}}},output:{type:"structure",members:{RaidArrays:{type:"list",member:{type:"structure",members:{RaidArrayId:{},InstanceId:{},Name:{},RaidLevel:{type:"integer"},NumberOfDisks:{type:"integer"},Size:{type:"integer"},Device:{},MountPoint:{},AvailabilityZone:{},CreatedAt:{},StackId:{},VolumeType:{},Iops:{type:"integer"}}}}}},http:{}},DescribeRdsDbInstances:{input:{type:"structure",required:["StackId"],members:{StackId:{},RdsDbInstanceArns:{shape:"S3"}}},output:{type:"structure",members:{RdsDbInstances:{type:"list",member:{type:"structure",members:{RdsDbInstanceArn:{},DbInstanceIdentifier:{},DbUser:{},DbPassword:{},Region:{},Address:{},Engine:{},StackId:{},MissingOnRds:{type:"boolean"}}}}}},http:{}},DescribeServiceErrors:{input:{type:"structure",members:{StackId:{},InstanceId:{},ServiceErrorIds:{shape:"S3"}}},output:{type:"structure",members:{ServiceErrors:{type:"list",member:{type:"structure",members:{ServiceErrorId:{},StackId:{},InstanceId:{},Type:{},Message:{},CreatedAt:{}}}}}},http:{}},DescribeStackProvisioningParameters:{input:{type:"structure",required:["StackId"],members:{StackId:{}}},output:{type:"structure",members:{AgentInstallerUrl:{},Parameters:{type:"map",key:{},value:{}}}},http:{}},DescribeStackSummary:{input:{type:"structure",required:["StackId"],members:{StackId:{}}},output:{type:"structure",members:{StackSummary:{type:"structure",members:{StackId:{},Name:{},Arn:{},LayersCount:{type:"integer"},AppsCount:{type:"integer"},InstancesCount:{type:"structure",members:{Assigning:{type:"integer"},Booting:{type:"integer"},ConnectionLost:{type:"integer"},Deregistering:{type:"integer"},Online:{type:"integer"},Pending:{type:"integer"},Rebooting:{type:"integer"},Registered:{type:"integer"},Registering:{type:"integer"},Requested:{type:"integer"},RunningSetup:{type:"integer"},SetupFailed:{type:"integer"},ShuttingDown:{type:"integer"},StartFailed:{type:"integer"},Stopped:{type:"integer"},Stopping:{type:"integer"},Terminated:{type:"integer"},Terminating:{type:"integer"},Unassigning:{type:"integer"}}}}}}},http:{}},DescribeStacks:{input:{type:"structure",members:{StackIds:{shape:"S3"}}},output:{type:"structure",members:{Stacks:{type:"list",member:{type:"structure",members:{StackId:{},Name:{},Arn:{},Region:{},VpcId:{},Attributes:{shape:"S8"},ServiceRoleArn:{},DefaultInstanceProfileArn:{},DefaultOs:{},HostnameTheme:{},DefaultAvailabilityZone:{},DefaultSubnetId:{},CustomJson:{},ConfigurationManager:{shape:"Sa"},ChefConfiguration:{shape:"Sb"},UseCustomCookbooks:{type:"boolean"},UseOpsworksSecurityGroups:{type:"boolean"},CustomCookbooksSource:{shape:"Sd"},DefaultSshKeyName:{},CreatedAt:{},DefaultRootDeviceType:{},AgentVersion:{}}}}}},http:{}},DescribeTimeBasedAutoScaling:{input:{type:"structure",required:["InstanceIds"],members:{InstanceIds:{shape:"S3"}}},output:{type:"structure",members:{TimeBasedAutoScalingConfigurations:{type:"list",member:{type:"structure",members:{InstanceId:{},AutoScalingSchedule:{shape:"S40"}}}}}},http:{}},DescribeUserProfiles:{input:{type:"structure",members:{IamUserArns:{shape:"S3"}}},output:{type:"structure",members:{UserProfiles:{type:"list",member:{type:"structure",members:{IamUserArn:{},Name:{},SshUsername:{},SshPublicKey:{},AllowSelfManagement:{type:"boolean"}}}}}},http:{}},DescribeVolumes:{input:{type:"structure",members:{InstanceId:{},StackId:{},RaidArrayId:{},VolumeIds:{shape:"S3"}}},output:{type:"structure",members:{Volumes:{type:"list",member:{type:"structure",members:{VolumeId:{},Ec2VolumeId:{},Name:{},RaidArrayId:{},InstanceId:{},Status:{},Size:{type:"integer"},Device:{},MountPoint:{},Region:{},AvailabilityZone:{},VolumeType:{},Iops:{type:"integer"}}}}}},http:{}},DetachElasticLoadBalancer:{input:{type:"structure",required:["ElasticLoadBalancerName","LayerId"],members:{ElasticLoadBalancerName:{},LayerId:{}}},http:{}},DisassociateElasticIp:{input:{type:"structure",required:["ElasticIp"],members:{ElasticIp:{}}},http:{}},GetHostnameSuggestion:{input:{type:"structure",required:["LayerId"],members:{LayerId:{}}},output:{type:"structure",members:{LayerId:{},Hostname:{}}},http:{}},GrantAccess:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{},ValidForInMinutes:{type:"integer"}}},output:{type:"structure",members:{TemporaryCredential:{type:"structure",members:{Username:{},Password:{},ValidForInMinutes:{type:"integer"},InstanceId:{}}}}},http:{}},RebootInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{}}},http:{}},RegisterEcsCluster:{input:{type:"structure",required:["EcsClusterArn","StackId"],members:{EcsClusterArn:{},StackId:{}}},output:{type:"structure",members:{EcsClusterArn:{}}},http:{}},RegisterElasticIp:{input:{type:"structure",required:["ElasticIp","StackId"],members:{ElasticIp:{},StackId:{}}},output:{type:"structure",members:{ElasticIp:{}}},http:{}},RegisterInstance:{input:{type:"structure",required:["StackId"],members:{StackId:{},Hostname:{},PublicIp:{},PrivateIp:{},RsaPublicKey:{},RsaPublicKeyFingerprint:{},InstanceIdentity:{type:"structure",members:{Document:{},Signature:{}}}}},output:{type:"structure",members:{InstanceId:{}}},http:{}},RegisterRdsDbInstance:{input:{type:"structure",required:["StackId","RdsDbInstanceArn","DbUser","DbPassword"],members:{StackId:{},RdsDbInstanceArn:{},DbUser:{},DbPassword:{}}},http:{}},RegisterVolume:{input:{type:"structure",required:["StackId"],members:{Ec2VolumeId:{},StackId:{}}},output:{type:"structure",members:{VolumeId:{}}},http:{}},SetLoadBasedAutoScaling:{input:{type:"structure",required:["LayerId"],members:{LayerId:{},Enable:{type:"boolean"},UpScaling:{shape:"S30"},DownScaling:{shape:"S30"}}},http:{}},SetPermission:{input:{type:"structure",required:["StackId","IamUserArn"],members:{StackId:{},IamUserArn:{},AllowSsh:{type:"boolean"},AllowSudo:{type:"boolean"},Level:{}}},http:{}},SetTimeBasedAutoScaling:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{},AutoScalingSchedule:{shape:"S40"}}},http:{}},StartInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{}}},http:{}},StartStack:{input:{type:"structure",required:["StackId"],members:{StackId:{}}},http:{}},StopInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{}}},http:{}},StopStack:{input:{type:"structure",required:["StackId"],members:{StackId:{}}},http:{}},UnassignInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{}}},http:{}},UnassignVolume:{input:{type:"structure",required:["VolumeId"],members:{VolumeId:{}}},http:{}},UpdateApp:{input:{type:"structure",required:["AppId"],members:{AppId:{},Name:{},Description:{},DataSources:{shape:"Si"},Type:{},AppSource:{shape:"Sd"},Domains:{shape:"S3"},EnableSsl:{type:"boolean"},SslConfiguration:{shape:"Sl"},Attributes:{shape:"Sm"},Environment:{shape:"So"}}},http:{}},UpdateElasticIp:{input:{type:"structure",required:["ElasticIp"],members:{ElasticIp:{},Name:{}}},http:{}},UpdateInstance:{input:{type:"structure",required:["InstanceId"],members:{InstanceId:{},LayerIds:{shape:"S3"},InstanceType:{},AutoScalingType:{},Hostname:{},Os:{},AmiId:{},SshKeyName:{},Architecture:{},InstallUpdatesOnBoot:{type:"boolean"},EbsOptimized:{type:"boolean"},AgentVersion:{}}},http:{}},UpdateLayer:{input:{type:"structure",required:["LayerId"],members:{LayerId:{},Name:{},Shortname:{},Attributes:{shape:"S17"},CustomInstanceProfileArn:{},CustomJson:{},CustomSecurityGroupIds:{shape:"S3"},Packages:{shape:"S3"},VolumeConfigurations:{shape:"S19"},EnableAutoHealing:{type:"boolean"},AutoAssignElasticIps:{type:"boolean"},AutoAssignPublicIps:{type:"boolean"},CustomRecipes:{shape:"S1b"},InstallUpdatesOnBoot:{type:"boolean"},UseEbsOptimizedInstances:{type:"boolean"},LifecycleEventConfiguration:{shape:"S1c"}}},http:{}},UpdateMyUserProfile:{input:{type:"structure",members:{SshPublicKey:{}}},http:{}},UpdateRdsDbInstance:{input:{type:"structure",required:["RdsDbInstanceArn"],members:{RdsDbInstanceArn:{},DbUser:{},DbPassword:{}}},http:{}},UpdateStack:{input:{type:"structure",required:["StackId"],members:{StackId:{},Name:{},Attributes:{shape:"S8"},ServiceRoleArn:{},DefaultInstanceProfileArn:{},DefaultOs:{},HostnameTheme:{},DefaultAvailabilityZone:{},DefaultSubnetId:{},CustomJson:{},ConfigurationManager:{shape:"Sa"},ChefConfiguration:{shape:"Sb"},UseCustomCookbooks:{type:"boolean"},CustomCookbooksSource:{shape:"Sd"},DefaultSshKeyName:{},DefaultRootDeviceType:{},UseOpsworksSecurityGroups:{type:"boolean"},AgentVersion:{}}},http:{}},UpdateUserProfile:{input:{type:"structure",required:["IamUserArn"],members:{IamUserArn:{},SshUsername:{},SshPublicKey:{},AllowSelfManagement:{type:"boolean"}}},http:{}},UpdateVolume:{input:{type:"structure",required:["VolumeId"],members:{VolumeId:{},Name:{},MountPoint:{}}},http:{}}},shapes:{S3:{type:"list",member:{}},S8:{type:"map",key:{},value:{}},Sa:{type:"structure",members:{Name:{},Version:{}}},Sb:{type:"structure",members:{ManageBerkshelf:{type:"boolean"},BerkshelfVersion:{}}},Sd:{type:"structure",members:{Type:{},Url:{},Username:{},Password:{},SshKey:{},Revision:{}}},Si:{type:"list",member:{type:"structure",members:{Type:{},Arn:{},DatabaseName:{}}}},Sl:{type:"structure",required:["Certificate","PrivateKey"],members:{Certificate:{},PrivateKey:{},Chain:{}}},Sm:{type:"map",key:{},value:{}},So:{type:"list",member:{type:"structure",required:["Key","Value"],members:{Key:{},Value:{},Secure:{type:"boolean"}}}},Ss:{type:"structure",required:["Name"],members:{Name:{},Args:{type:"map",key:{},value:{shape:"S3"}}}},Sz:{type:"list",member:{type:"structure",members:{DeviceName:{},NoDevice:{},VirtualName:{},Ebs:{type:"structure",members:{SnapshotId:{},Iops:{type:"integer"},VolumeSize:{type:"integer"},VolumeType:{},DeleteOnTermination:{type:"boolean"}}}}}},S17:{type:"map",key:{},value:{}},S19:{type:"list",member:{type:"structure",required:["MountPoint","NumberOfDisks","Size"],members:{MountPoint:{},RaidLevel:{type:"integer"},NumberOfDisks:{type:"integer"},Size:{type:"integer"},VolumeType:{},Iops:{type:"integer"}}}},S1b:{type:"structure",members:{Setup:{shape:"S3"},Configure:{shape:"S3"},Deploy:{shape:"S3"},Undeploy:{shape:"S3"},Shutdown:{shape:"S3"}}},S1c:{type:"structure",members:{Shutdown:{type:"structure",members:{ExecutionTimeout:{type:"integer"},DelayUntilElbConnectionsDrained:{type:"boolean"}}}}},S30:{type:"structure",members:{InstanceCount:{type:"integer"},ThresholdsWaitTime:{type:"integer"},IgnoreMetricsTime:{type:"integer"},CpuThreshold:{type:"double"},MemoryThreshold:{type:"double"},LoadThreshold:{type:"double"},Alarms:{shape:"S3"}}},S40:{type:"structure",members:{Monday:{shape:"S41"},Tuesday:{shape:"S41"},Wednesday:{shape:"S41"},Thursday:{shape:"S41"},Friday:{shape:"S41"},Saturday:{shape:"S41"},Sunday:{shape:"S41"}}},S41:{type:"map",key:{},value:{}}},examples:{},paginators:{DescribeApps:{result_key:"Apps"},DescribeCommands:{result_key:"Commands"},DescribeDeployments:{result_key:"Deployments"},DescribeElasticIps:{result_key:"ElasticIps"},DescribeElasticLoadBalancers:{result_key:"ElasticLoadBalancers"},DescribeInstances:{result_key:"Instances"},DescribeLayers:{result_key:"Layers"},DescribeLoadBasedAutoScaling:{result_key:"LoadBasedAutoScalingConfigurations"},DescribePermissions:{result_key:"Permissions"},DescribeRaidArrays:{result_key:"RaidArrays"},DescribeServiceErrors:{result_key:"ServiceErrors"},DescribeStacks:{result_key:"Stacks"},DescribeTimeBasedAutoScaling:{result_key:"TimeBasedAutoScalingConfigurations"},DescribeUserProfiles:{result_key:"UserProfiles"},DescribeVolumes:{result_key:"Volumes"}}},a.apiLoader.services.s3={},a.S3=a.Service.defineService("s3",["2006-03-01"]),e("./services/s3"),a.apiLoader.services.s3["2006-03-01"]={version:"2.0",metadata:{apiVersion:"2006-03-01",checksumFormat:"md5",endpointPrefix:"s3",globalEndpoint:"s3.amazonaws.com",serviceAbbreviation:"Amazon S3",serviceFullName:"Amazon Simple Storage Service",signatureVersion:"s3",timestampFormat:"rfc822",protocol:"rest-xml"},operations:{AbortMultipartUpload:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CompleteMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MultipartUpload:{locationName:"CompleteMultipartUpload",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{ETag:{},PartNumber:{type:"integer"}}},flattened:!0}}},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"MultipartUpload"},output:{type:"structure",members:{Location:{},Bucket:{},Key:{},Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CopyObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},MetadataDirective:{location:"header",locationName:"x-amz-metadata-directive"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1b",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{CopyObjectResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},Expiration:{location:"header",locationName:"x-amz-expiration"},CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyObjectResult"},alias:"PutObjectCopy"},CreateBucket:{http:{method:"PUT",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CreateBucketConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"CreateBucketConfiguration",type:"structure",members:{LocationConstraint:{}}},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"CreateBucketConfiguration"},output:{type:"structure",members:{Location:{location:"header",locationName:"Location"}}},alias:"PutBucket"},CreateMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}?uploads"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Bucket:{locationName:"Bucket"},Key:{},UploadId:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}},alias:"InitiateMultipartUpload"},DeleteBucket:{http:{method:"DELETE",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketCors:{http:{method:"DELETE",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketLifecycle:{http:{method:"DELETE",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketPolicy:{http:{method:"DELETE",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketReplication:{http:{method:"DELETE",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketTagging:{http:{method:"DELETE",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketWebsite:{http:{method:"DELETE",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteObject:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MFA:{location:"header",locationName:"x-amz-mfa"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},VersionId:{location:"header",locationName:"x-amz-version-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},DeleteObjects:{http:{requestUri:"/{Bucket}?delete"},input:{type:"structure",required:["Bucket","Delete"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delete:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"Delete",type:"structure",required:["Objects"],members:{Objects:{locationName:"Object",type:"list",member:{type:"structure",required:["Key"],members:{Key:{},VersionId:{}}},flattened:!0},Quiet:{type:"boolean"}}},MFA:{location:"header",locationName:"x-amz-mfa"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Delete"},output:{type:"structure",members:{Deleted:{type:"list",member:{type:"structure",members:{Key:{},VersionId:{},DeleteMarker:{type:"boolean"},DeleteMarkerVersionId:{}}},flattened:!0},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},Errors:{locationName:"Error",type:"list",member:{type:"structure",members:{Key:{},VersionId:{},
Code:{},Message:{}}},flattened:!0}}},alias:"DeleteMultipleObjects"},GetBucketAcl:{http:{method:"GET",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Owner:{shape:"S2f"},Grants:{shape:"S2i",locationName:"AccessControlList"}}}},GetBucketCors:{http:{method:"GET",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{CORSRules:{shape:"S2r",locationName:"CORSRule"}}}},GetBucketLifecycle:{http:{method:"GET",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Rules:{shape:"S34",locationName:"Rule"}}}},GetBucketLocation:{http:{method:"GET",requestUri:"/{Bucket}?location"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LocationConstraint:{}}}},GetBucketLogging:{http:{method:"GET",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LoggingEnabled:{shape:"S3j"}}}},GetBucketNotification:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S3p"},output:{shape:"S3q"},deprecated:!0},GetBucketNotificationConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S3p"},output:{shape:"S41"}},GetBucketPolicy:{http:{method:"GET",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Policy:{}},payload:"Policy"}},GetBucketReplication:{http:{method:"GET",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{ReplicationConfiguration:{shape:"S4e"}},payload:"ReplicationConfiguration"}},GetBucketRequestPayment:{http:{method:"GET",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Payer:{}}}},GetBucketTagging:{http:{method:"GET",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",required:["TagSet"],members:{TagSet:{shape:"S4p"}}}},GetBucketVersioning:{http:{method:"GET",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Status:{},MFADelete:{locationName:"MfaDelete"}}}},GetBucketWebsite:{http:{method:"GET",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{RedirectAllRequestsTo:{shape:"S4y"},IndexDocument:{shape:"S51"},ErrorDocument:{shape:"S53"},RoutingRules:{shape:"S54"}}}},GetObject:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},ResponseCacheControl:{location:"querystring",locationName:"response-cache-control"},ResponseContentDisposition:{location:"querystring",locationName:"response-content-disposition"},ResponseContentEncoding:{location:"querystring",locationName:"response-content-encoding"},ResponseContentLanguage:{location:"querystring",locationName:"response-content-language"},ResponseContentType:{location:"querystring",locationName:"response-content-type"},ResponseExpires:{location:"querystring",locationName:"response-expires",type:"timestamp"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Body:{streaming:!0,type:"blob"},DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"integer"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentRange:{location:"header",locationName:"Content-Range"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"}},payload:"Body"}},GetObjectAcl:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Owner:{shape:"S2f"},Grants:{shape:"S2i",locationName:"AccessControlList"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},GetObjectTorrent:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?torrent"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Body:{streaming:!0,type:"blob"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"Body"}},HeadBucket:{http:{method:"HEAD",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},HeadObject:{http:{method:"HEAD",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"integer"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"}}}},ListBuckets:{http:{method:"GET"},output:{type:"structure",members:{Buckets:{type:"list",member:{locationName:"Bucket",type:"structure",members:{Name:{},CreationDate:{type:"timestamp"}}}},Owner:{shape:"S2f"}}},alias:"GetService"},ListMultipartUploads:{http:{method:"GET",requestUri:"/{Bucket}?uploads"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxUploads:{location:"querystring",locationName:"max-uploads",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},UploadIdMarker:{location:"querystring",locationName:"upload-id-marker"}}},output:{type:"structure",members:{Bucket:{},KeyMarker:{},UploadIdMarker:{},NextKeyMarker:{},Prefix:{},Delimiter:{},NextUploadIdMarker:{},MaxUploads:{type:"integer"},IsTruncated:{type:"boolean"},Uploads:{locationName:"Upload",type:"list",member:{type:"structure",members:{UploadId:{},Key:{},Initiated:{type:"timestamp"},StorageClass:{},Owner:{shape:"S2f"},Initiator:{shape:"S6l"}}},flattened:!0},CommonPrefixes:{shape:"S6m"},EncodingType:{}}}},ListObjectVersions:{http:{method:"GET",requestUri:"/{Bucket}?versions"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},VersionIdMarker:{location:"querystring",locationName:"version-id-marker"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},KeyMarker:{},VersionIdMarker:{},NextKeyMarker:{},NextVersionIdMarker:{},Versions:{locationName:"Version",type:"list",member:{type:"structure",members:{ETag:{},Size:{type:"integer"},StorageClass:{},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"},Owner:{shape:"S2f"}}},flattened:!0},DeleteMarkers:{locationName:"DeleteMarker",type:"list",member:{type:"structure",members:{Owner:{shape:"S2f"},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"}}},flattened:!0},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S6m"},EncodingType:{}}},alias:"GetBucketObjectVersions"},ListObjects:{http:{method:"GET",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},Marker:{location:"querystring",locationName:"marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},Marker:{},NextMarker:{},Contents:{type:"list",member:{type:"structure",members:{Key:{},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"},StorageClass:{},Owner:{shape:"S2f"}}},flattened:!0},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S6m"},EncodingType:{}}},alias:"GetBucket"},ListParts:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MaxParts:{location:"querystring",locationName:"max-parts",type:"integer"},PartNumberMarker:{location:"querystring",locationName:"part-number-marker",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Bucket:{},Key:{},UploadId:{},PartNumberMarker:{type:"integer"},NextPartNumberMarker:{type:"integer"},MaxParts:{type:"integer"},IsTruncated:{type:"boolean"},Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{PartNumber:{type:"integer"},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"}}},flattened:!0},Initiator:{shape:"S6l"},Owner:{shape:"S2f"},StorageClass:{},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutBucketAcl:{http:{method:"PUT",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"S7f",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"AccessControlPolicy"},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"AccessControlPolicy"}},PutBucketCors:{http:{method:"PUT",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},CORSConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"CORSConfiguration",type:"structure",members:{CORSRules:{shape:"S2r",locationName:"CORSRule"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"CORSConfiguration"}},PutBucketLifecycle:{http:{method:"PUT",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},LifecycleConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"LifecycleConfiguration",type:"structure",required:["Rules"],members:{Rules:{shape:"S34",locationName:"Rule"}}}},payload:"LifecycleConfiguration"}},PutBucketLogging:{http:{method:"PUT",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket","BucketLoggingStatus"],members:{Bucket:{location:"uri",locationName:"Bucket"},BucketLoggingStatus:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"BucketLoggingStatus",type:"structure",members:{LoggingEnabled:{shape:"S3j"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"BucketLoggingStatus"}},PutBucketNotification:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},NotificationConfiguration:{shape:"S3q",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"NotificationConfiguration"}},payload:"NotificationConfiguration"},deprecated:!0},PutBucketNotificationConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},NotificationConfiguration:{shape:"S41",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"NotificationConfiguration"}},payload:"NotificationConfiguration"}},PutBucketPolicy:{http:{method:"PUT",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket","Policy"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},Policy:{}},payload:"Policy"}},PutBucketReplication:{http:{method:"PUT",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket","ReplicationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ReplicationConfiguration:{shape:"S4e",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"ReplicationConfiguration"}},payload:"ReplicationConfiguration"}},PutBucketRequestPayment:{http:{method:"PUT",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket","RequestPaymentConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},RequestPaymentConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"RequestPaymentConfiguration",type:"structure",required:["Payer"],members:{Payer:{}}}},payload:"RequestPaymentConfiguration"}},PutBucketTagging:{http:{method:"PUT",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket","Tagging"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},Tagging:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"Tagging",type:"structure",required:["TagSet"],members:{TagSet:{shape:"S4p"}}}},payload:"Tagging"}},PutBucketVersioning:{http:{method:"PUT",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket","VersioningConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},MFA:{location:"header",locationName:"x-amz-mfa"},VersioningConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"VersioningConfiguration",type:"structure",members:{MFADelete:{locationName:"MfaDelete"},Status:{}}}},payload:"VersioningConfiguration"}},PutBucketWebsite:{http:{method:"PUT",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket","WebsiteConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},WebsiteConfiguration:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"WebsiteConfiguration",type:"structure",members:{ErrorDocument:{shape:"S53"},IndexDocument:{shape:"S51"},RedirectAllRequestsTo:{shape:"S4y"},RoutingRules:{shape:"S54"}}}},payload:"WebsiteConfiguration"}},PutObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Body:{streaming:!0,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentLength:{location:"header",locationName:"Content-Length",type:"integer"},ContentMD5:{location:"header",locationName:"Content-MD5"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Body"},output:{type:"structure",members:{Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{location:"header",locationName:"ETag"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutObjectAcl:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"S7f",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"AccessControlPolicy"},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"AccessControlPolicy"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},RestoreObject:{http:{requestUri:"/{Bucket}/{Key+}?restore"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RestoreRequest:{xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},locationName:"RestoreRequest",type:"structure",required:["Days"],members:{Days:{type:"integer"}}},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"RestoreRequest"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}},alias:"PostObjectRestore"},UploadPart:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","PartNumber","UploadId"],members:{Body:{streaming:!0,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},ContentLength:{location:"header",locationName:"Content-Length",type:"integer"},ContentMD5:{location:"header",locationName:"Content-MD5"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Body"},output:{type:"structure",members:{ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},ETag:{location:"header",locationName:"ETag"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},UploadPartCopy:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key","PartNumber","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},CopySourceRange:{location:"header",locationName:"x-amz-copy-source-range"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S18",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1b",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},CopyPartResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyPartResult"}}},shapes:{Sj:{type:"string",sensitive:!0},S11:{type:"map",key:{},value:{}},S18:{type:"blob",sensitive:!0},S1b:{type:"blob",sensitive:!0},S2f:{type:"structure",members:{DisplayName:{},ID:{}}},S2i:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S2k"},Permission:{}}}},S2k:{type:"structure",required:["Type"],members:{DisplayName:{},EmailAddress:{},ID:{},Type:{xmlAttribute:!0,locationName:"xsi:type"},URI:{}},xmlNamespace:{prefix:"xsi",uri:"http://www.w3.org/2001/XMLSchema-instance"}},S2r:{type:"list",member:{type:"structure",members:{AllowedHeaders:{locationName:"AllowedHeader",type:"list",member:{},flattened:!0},AllowedMethods:{locationName:"AllowedMethod",type:"list",member:{},flattened:!0},AllowedOrigins:{locationName:"AllowedOrigin",type:"list",member:{},flattened:!0},ExposeHeaders:{locationName:"ExposeHeader",type:"list",member:{},flattened:!0},MaxAgeSeconds:{type:"integer"}}},flattened:!0},S34:{type:"list",member:{type:"structure",required:["Prefix","Status"],members:{Expiration:{type:"structure",members:{Date:{shape:"S37"},Days:{type:"integer"}}},ID:{},Prefix:{},Status:{},Transition:{type:"structure",members:{Date:{shape:"S37"},Days:{type:"integer"},StorageClass:{}}},NoncurrentVersionTransition:{type:"structure",members:{NoncurrentDays:{type:"integer"},StorageClass:{}}},NoncurrentVersionExpiration:{type:"structure",members:{NoncurrentDays:{type:"integer"}}}}},flattened:!0},S37:{type:"timestamp",timestampFormat:"iso8601"},S3j:{type:"structure",members:{TargetBucket:{},TargetGrants:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S2k"},Permission:{}}}},TargetPrefix:{}}},S3p:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},S3q:{type:"structure",members:{TopicConfiguration:{type:"structure",members:{Id:{},Events:{shape:"S3t",locationName:"Event"},Event:{deprecated:!0},Topic:{}}},QueueConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:!0},Events:{shape:"S3t",locationName:"Event"},Queue:{}}},CloudFunctionConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:!0},Events:{shape:"S3t",locationName:"Event"},CloudFunction:{},InvocationRole:{}}}}},S3t:{type:"list",member:{},flattened:!0},S41:{type:"structure",members:{TopicConfigurations:{locationName:"TopicConfiguration",type:"list",member:{type:"structure",required:["TopicArn","Events"],members:{Id:{},TopicArn:{locationName:"Topic"},Events:{shape:"S3t",locationName:"Event"}}},flattened:!0},QueueConfigurations:{locationName:"QueueConfiguration",type:"list",member:{type:"structure",required:["QueueArn","Events"],members:{Id:{},QueueArn:{locationName:"Queue"},Events:{shape:"S3t",locationName:"Event"}}},flattened:!0},LambdaFunctionConfigurations:{locationName:"CloudFunctionConfiguration",type:"list",member:{type:"structure",required:["LambdaFunctionArn","Events"],members:{Id:{},LambdaFunctionArn:{locationName:"CloudFunction"},Events:{shape:"S3t",locationName:"Event"}}},flattened:!0}}},S4e:{type:"structure",required:["Role","Rules"],members:{Role:{},Rules:{locationName:"Rule",type:"list",member:{type:"structure",required:["Prefix","Status","Destination"],members:{ID:{},Prefix:{},Status:{},Destination:{type:"structure",required:["Bucket"],members:{Bucket:{}}}}},
flattened:!0}}},S4p:{type:"list",member:{locationName:"Tag",type:"structure",required:["Key","Value"],members:{Key:{},Value:{}}}},S4y:{type:"structure",required:["HostName"],members:{HostName:{},Protocol:{}}},S51:{type:"structure",required:["Suffix"],members:{Suffix:{}}},S53:{type:"structure",required:["Key"],members:{Key:{}}},S54:{type:"list",member:{locationName:"RoutingRule",type:"structure",required:["Redirect"],members:{Condition:{type:"structure",members:{HttpErrorCodeReturnedEquals:{},KeyPrefixEquals:{}}},Redirect:{type:"structure",members:{HostName:{},HttpRedirectCode:{},Protocol:{},ReplaceKeyPrefixWith:{},ReplaceKeyWith:{}}}}}},S6l:{type:"structure",members:{ID:{},DisplayName:{}}},S6m:{type:"list",member:{type:"structure",members:{Prefix:{}}},flattened:!0},S7f:{type:"structure",members:{Grants:{shape:"S2i",locationName:"AccessControlList"},Owner:{shape:"S2f"}}}},examples:{},paginators:{ListBuckets:{result_key:"Buckets"},ListMultipartUploads:{limit_key:"MaxUploads",more_results:"IsTruncated",output_token:["NextKeyMarker","NextUploadIdMarker"],input_token:["KeyMarker","UploadIdMarker"],result_key:["Uploads","CommonPrefixes"]},ListObjectVersions:{more_results:"IsTruncated",limit_key:"MaxKeys",output_token:["NextKeyMarker","NextVersionIdMarker"],input_token:["KeyMarker","VersionIdMarker"],result_key:["Versions","DeleteMarkers","CommonPrefixes"]},ListObjects:{more_results:"IsTruncated",limit_key:"MaxKeys",output_token:"NextMarker || Contents[-1].Key",input_token:"Marker",result_key:["Contents","CommonPrefixes"]},ListParts:{more_results:"IsTruncated",limit_key:"MaxParts",output_token:"NextPartNumberMarker",input_token:"PartNumberMarker",result_key:"Parts"}},waiters:{__default__:{interval:5,max_attempts:20},BucketExists:{operation:"HeadBucket",ignore_errors:[404],success_type:"output"},BucketNotExists:{operation:"HeadBucket",success_type:"error",success_value:404},ObjectExists:{operation:"HeadObject",ignore_errors:[404],success_type:"output"},ObjectNotExists:{operation:"HeadObject",success_type:"error",success_value:404}}},a.apiLoader.services.sns={},a.SNS=a.Service.defineService("sns",["2010-03-31"]),a.apiLoader.services.sns["2010-03-31"]={metadata:{apiVersion:"2010-03-31",endpointPrefix:"sns",serviceAbbreviation:"Amazon SNS",serviceFullName:"Amazon Simple Notification Service",signatureVersion:"v4",xmlNamespace:"http://sns.amazonaws.com/doc/2010-03-31/",protocol:"query"},operations:{AddPermission:{input:{type:"structure",required:["TopicArn","Label","AWSAccountId","ActionName"],members:{TopicArn:{},Label:{},AWSAccountId:{type:"list",member:{}},ActionName:{type:"list",member:{}}}},http:{}},ConfirmSubscription:{input:{type:"structure",required:["TopicArn","Token"],members:{TopicArn:{},Token:{},AuthenticateOnUnsubscribe:{}}},output:{resultWrapper:"ConfirmSubscriptionResult",type:"structure",members:{SubscriptionArn:{}}},http:{}},CreatePlatformApplication:{input:{type:"structure",required:["Name","Platform","Attributes"],members:{Name:{},Platform:{},Attributes:{shape:"Sf"}}},output:{resultWrapper:"CreatePlatformApplicationResult",type:"structure",members:{PlatformApplicationArn:{}}},http:{}},CreatePlatformEndpoint:{input:{type:"structure",required:["PlatformApplicationArn","Token"],members:{PlatformApplicationArn:{},Token:{},CustomUserData:{},Attributes:{shape:"Sf"}}},output:{resultWrapper:"CreatePlatformEndpointResult",type:"structure",members:{EndpointArn:{}}},http:{}},CreateTopic:{input:{type:"structure",required:["Name"],members:{Name:{}}},output:{resultWrapper:"CreateTopicResult",type:"structure",members:{TopicArn:{}}},http:{}},DeleteEndpoint:{input:{type:"structure",required:["EndpointArn"],members:{EndpointArn:{}}},http:{}},DeletePlatformApplication:{input:{type:"structure",required:["PlatformApplicationArn"],members:{PlatformApplicationArn:{}}},http:{}},DeleteTopic:{input:{type:"structure",required:["TopicArn"],members:{TopicArn:{}}},http:{}},GetEndpointAttributes:{input:{type:"structure",required:["EndpointArn"],members:{EndpointArn:{}}},output:{resultWrapper:"GetEndpointAttributesResult",type:"structure",members:{Attributes:{shape:"Sf"}}},http:{}},GetPlatformApplicationAttributes:{input:{type:"structure",required:["PlatformApplicationArn"],members:{PlatformApplicationArn:{}}},output:{resultWrapper:"GetPlatformApplicationAttributesResult",type:"structure",members:{Attributes:{shape:"Sf"}}},http:{}},GetSubscriptionAttributes:{input:{type:"structure",required:["SubscriptionArn"],members:{SubscriptionArn:{}}},output:{resultWrapper:"GetSubscriptionAttributesResult",type:"structure",members:{Attributes:{type:"map",key:{},value:{}}}},http:{}},GetTopicAttributes:{input:{type:"structure",required:["TopicArn"],members:{TopicArn:{}}},output:{resultWrapper:"GetTopicAttributesResult",type:"structure",members:{Attributes:{type:"map",key:{},value:{}}}},http:{}},ListEndpointsByPlatformApplication:{input:{type:"structure",required:["PlatformApplicationArn"],members:{PlatformApplicationArn:{},NextToken:{}}},output:{resultWrapper:"ListEndpointsByPlatformApplicationResult",type:"structure",members:{Endpoints:{type:"list",member:{type:"structure",members:{EndpointArn:{},Attributes:{shape:"Sf"}}}},NextToken:{}}},http:{}},ListPlatformApplications:{input:{type:"structure",members:{NextToken:{}}},output:{resultWrapper:"ListPlatformApplicationsResult",type:"structure",members:{PlatformApplications:{type:"list",member:{type:"structure",members:{PlatformApplicationArn:{},Attributes:{shape:"Sf"}}}},NextToken:{}}},http:{}},ListSubscriptions:{input:{type:"structure",members:{NextToken:{}}},output:{resultWrapper:"ListSubscriptionsResult",type:"structure",members:{Subscriptions:{shape:"S1c"},NextToken:{}}},http:{}},ListSubscriptionsByTopic:{input:{type:"structure",required:["TopicArn"],members:{TopicArn:{},NextToken:{}}},output:{resultWrapper:"ListSubscriptionsByTopicResult",type:"structure",members:{Subscriptions:{shape:"S1c"},NextToken:{}}},http:{}},ListTopics:{input:{type:"structure",members:{NextToken:{}}},output:{resultWrapper:"ListTopicsResult",type:"structure",members:{Topics:{type:"list",member:{type:"structure",members:{TopicArn:{}}}},NextToken:{}}},http:{}},Publish:{input:{type:"structure",required:["Message"],members:{TopicArn:{},TargetArn:{},Message:{},Subject:{},MessageStructure:{},MessageAttributes:{type:"map",key:{locationName:"Name"},value:{locationName:"Value",type:"structure",required:["DataType"],members:{DataType:{},StringValue:{},BinaryValue:{type:"blob"}}}}}},output:{resultWrapper:"PublishResult",type:"structure",members:{MessageId:{}}},http:{}},RemovePermission:{input:{type:"structure",required:["TopicArn","Label"],members:{TopicArn:{},Label:{}}},http:{}},SetEndpointAttributes:{input:{type:"structure",required:["EndpointArn","Attributes"],members:{EndpointArn:{},Attributes:{shape:"Sf"}}},http:{}},SetPlatformApplicationAttributes:{input:{type:"structure",required:["PlatformApplicationArn","Attributes"],members:{PlatformApplicationArn:{},Attributes:{shape:"Sf"}}},http:{}},SetSubscriptionAttributes:{input:{type:"structure",required:["SubscriptionArn","AttributeName"],members:{SubscriptionArn:{},AttributeName:{},AttributeValue:{}}},http:{}},SetTopicAttributes:{input:{type:"structure",required:["TopicArn","AttributeName"],members:{TopicArn:{},AttributeName:{},AttributeValue:{}}},http:{}},Subscribe:{input:{type:"structure",required:["TopicArn","Protocol"],members:{TopicArn:{},Protocol:{},Endpoint:{}}},output:{resultWrapper:"SubscribeResult",type:"structure",members:{SubscriptionArn:{}}},http:{}},Unsubscribe:{input:{type:"structure",required:["SubscriptionArn"],members:{SubscriptionArn:{}}},http:{}}},shapes:{Sf:{type:"map",key:{},value:{}},S1c:{type:"list",member:{type:"structure",members:{SubscriptionArn:{},Owner:{},Protocol:{},Endpoint:{},TopicArn:{}}}}},paginators:{ListEndpointsByPlatformApplication:{input_token:"NextToken",output_token:"NextToken",result_key:"Endpoints"},ListPlatformApplications:{input_token:"NextToken",output_token:"NextToken",result_key:"PlatformApplications"},ListSubscriptions:{input_token:"NextToken",output_token:"NextToken",result_key:"Subscriptions"},ListSubscriptionsByTopic:{input_token:"NextToken",output_token:"NextToken",result_key:"Subscriptions"},ListTopics:{input_token:"NextToken",output_token:"NextToken",result_key:"Topics"}}},a.apiLoader.services.sqs={},a.SQS=a.Service.defineService("sqs",["2012-11-05"]),e("./services/sqs"),a.apiLoader.services.sqs["2012-11-05"]={metadata:{apiVersion:"2012-11-05",endpointPrefix:"sqs",serviceAbbreviation:"Amazon SQS",serviceFullName:"Amazon Simple Queue Service",signatureVersion:"v4",xmlNamespace:"http://queue.amazonaws.com/doc/2012-11-05/",protocol:"query"},operations:{AddPermission:{input:{type:"structure",required:["QueueUrl","Label","AWSAccountIds","Actions"],members:{QueueUrl:{},Label:{},AWSAccountIds:{type:"list",member:{locationName:"AWSAccountId"},flattened:!0},Actions:{type:"list",member:{locationName:"ActionName"},flattened:!0}}},http:{}},ChangeMessageVisibility:{input:{type:"structure",required:["QueueUrl","ReceiptHandle","VisibilityTimeout"],members:{QueueUrl:{},ReceiptHandle:{},VisibilityTimeout:{type:"integer"}}},http:{}},ChangeMessageVisibilityBatch:{input:{type:"structure",required:["QueueUrl","Entries"],members:{QueueUrl:{},Entries:{type:"list",member:{locationName:"ChangeMessageVisibilityBatchRequestEntry",type:"structure",required:["Id","ReceiptHandle"],members:{Id:{},ReceiptHandle:{},VisibilityTimeout:{type:"integer"}}},flattened:!0}}},output:{resultWrapper:"ChangeMessageVisibilityBatchResult",type:"structure",required:["Successful","Failed"],members:{Successful:{type:"list",member:{locationName:"ChangeMessageVisibilityBatchResultEntry",type:"structure",required:["Id"],members:{Id:{}}},flattened:!0},Failed:{shape:"Sd"}}},http:{}},CreateQueue:{input:{type:"structure",required:["QueueName"],members:{QueueName:{},Attributes:{shape:"Sh",locationName:"Attribute"}}},output:{resultWrapper:"CreateQueueResult",type:"structure",members:{QueueUrl:{}}},http:{}},DeleteMessage:{input:{type:"structure",required:["QueueUrl","ReceiptHandle"],members:{QueueUrl:{},ReceiptHandle:{}}},http:{}},DeleteMessageBatch:{input:{type:"structure",required:["QueueUrl","Entries"],members:{QueueUrl:{},Entries:{type:"list",member:{locationName:"DeleteMessageBatchRequestEntry",type:"structure",required:["Id","ReceiptHandle"],members:{Id:{},ReceiptHandle:{}}},flattened:!0}}},output:{resultWrapper:"DeleteMessageBatchResult",type:"structure",required:["Successful","Failed"],members:{Successful:{type:"list",member:{locationName:"DeleteMessageBatchResultEntry",type:"structure",required:["Id"],members:{Id:{}}},flattened:!0},Failed:{shape:"Sd"}}},http:{}},DeleteQueue:{input:{type:"structure",required:["QueueUrl"],members:{QueueUrl:{}}},http:{}},GetQueueAttributes:{input:{type:"structure",required:["QueueUrl"],members:{QueueUrl:{},AttributeNames:{shape:"St"}}},output:{resultWrapper:"GetQueueAttributesResult",type:"structure",members:{Attributes:{shape:"Sh",locationName:"Attribute"}}},http:{}},GetQueueUrl:{input:{type:"structure",required:["QueueName"],members:{QueueName:{},QueueOwnerAWSAccountId:{}}},output:{resultWrapper:"GetQueueUrlResult",type:"structure",members:{QueueUrl:{}}},http:{}},ListDeadLetterSourceQueues:{input:{type:"structure",required:["QueueUrl"],members:{QueueUrl:{}}},output:{resultWrapper:"ListDeadLetterSourceQueuesResult",type:"structure",required:["queueUrls"],members:{queueUrls:{shape:"Sz"}}},http:{}},ListQueues:{input:{type:"structure",members:{QueueNamePrefix:{}}},output:{resultWrapper:"ListQueuesResult",type:"structure",members:{QueueUrls:{shape:"Sz"}}},http:{}},PurgeQueue:{input:{type:"structure",required:["QueueUrl"],members:{QueueUrl:{}}},http:{}},ReceiveMessage:{input:{type:"structure",required:["QueueUrl"],members:{QueueUrl:{},AttributeNames:{shape:"St"},MessageAttributeNames:{type:"list",member:{locationName:"MessageAttributeName"},flattened:!0},MaxNumberOfMessages:{type:"integer"},VisibilityTimeout:{type:"integer"},WaitTimeSeconds:{type:"integer"}}},output:{resultWrapper:"ReceiveMessageResult",type:"structure",members:{Messages:{type:"list",member:{locationName:"Message",type:"structure",members:{MessageId:{},ReceiptHandle:{},MD5OfBody:{},Body:{},Attributes:{shape:"Sh",locationName:"Attribute"},MD5OfMessageAttributes:{},MessageAttributes:{shape:"S19",locationName:"MessageAttribute"}}},flattened:!0}}},http:{}},RemovePermission:{input:{type:"structure",required:["QueueUrl","Label"],members:{QueueUrl:{},Label:{}}},http:{}},SendMessage:{input:{type:"structure",required:["QueueUrl","MessageBody"],members:{QueueUrl:{},MessageBody:{},DelaySeconds:{type:"integer"},MessageAttributes:{shape:"S19",locationName:"MessageAttribute"}}},output:{resultWrapper:"SendMessageResult",type:"structure",members:{MD5OfMessageBody:{},MD5OfMessageAttributes:{},MessageId:{}}},http:{}},SendMessageBatch:{input:{type:"structure",required:["QueueUrl","Entries"],members:{QueueUrl:{},Entries:{type:"list",member:{locationName:"SendMessageBatchRequestEntry",type:"structure",required:["Id","MessageBody"],members:{Id:{},MessageBody:{},DelaySeconds:{type:"integer"},MessageAttributes:{shape:"S19",locationName:"MessageAttribute"}}},flattened:!0}}},output:{resultWrapper:"SendMessageBatchResult",type:"structure",required:["Successful","Failed"],members:{Successful:{type:"list",member:{locationName:"SendMessageBatchResultEntry",type:"structure",required:["Id","MessageId","MD5OfMessageBody"],members:{Id:{},MessageId:{},MD5OfMessageBody:{},MD5OfMessageAttributes:{}}},flattened:!0},Failed:{shape:"Sd"}}},http:{}},SetQueueAttributes:{input:{type:"structure",required:["QueueUrl","Attributes"],members:{QueueUrl:{},Attributes:{shape:"Sh",locationName:"Attribute"}}},http:{}}},shapes:{Sd:{type:"list",member:{locationName:"BatchResultErrorEntry",type:"structure",required:["Id","SenderFault","Code"],members:{Id:{},SenderFault:{type:"boolean"},Code:{},Message:{}}},flattened:!0},Sh:{type:"map",key:{locationName:"Name"},value:{locationName:"Value"},flattened:!0,locationName:"Attribute"},St:{type:"list",member:{locationName:"AttributeName"},flattened:!0},Sz:{type:"list",member:{locationName:"QueueUrl"},flattened:!0},S19:{type:"map",key:{locationName:"Name"},value:{locationName:"Value",type:"structure",required:["DataType"],members:{StringValue:{},BinaryValue:{type:"blob"},StringListValues:{flattened:!0,locationName:"StringListValue",type:"list",member:{locationName:"StringListValue"}},BinaryListValues:{flattened:!0,locationName:"BinaryListValue",type:"list",member:{locationName:"BinaryListValue",type:"blob"}},DataType:{}}},flattened:!0}},paginators:{ListQueues:{result_key:"QueueUrls"}}},a.apiLoader.services.sts={},a.STS=a.Service.defineService("sts",["2011-06-15"]),e("./services/sts"),a.apiLoader.services.sts["2011-06-15"]={version:"2.0",metadata:{apiVersion:"2011-06-15",endpointPrefix:"sts",globalEndpoint:"sts.amazonaws.com",serviceAbbreviation:"AWS STS",serviceFullName:"AWS Security Token Service",signatureVersion:"v4",xmlNamespace:"https://sts.amazonaws.com/doc/2011-06-15/",protocol:"query"},operations:{AssumeRole:{input:{type:"structure",required:["RoleArn","RoleSessionName"],members:{RoleArn:{},RoleSessionName:{},Policy:{},DurationSeconds:{type:"integer"},ExternalId:{},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"AssumeRoleResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"}}},http:{}},AssumeRoleWithSAML:{input:{type:"structure",required:["RoleArn","PrincipalArn","SAMLAssertion"],members:{RoleArn:{},PrincipalArn:{},SAMLAssertion:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithSAMLResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Subject:{},SubjectType:{},Issuer:{},Audience:{},NameQualifier:{}}},http:{}},AssumeRoleWithWebIdentity:{input:{type:"structure",required:["RoleArn","RoleSessionName","WebIdentityToken"],members:{RoleArn:{},RoleSessionName:{},WebIdentityToken:{},ProviderId:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithWebIdentityResult",type:"structure",members:{Credentials:{shape:"Sa"},SubjectFromWebIdentityToken:{},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Provider:{},Audience:{}}},http:{}},DecodeAuthorizationMessage:{input:{type:"structure",required:["EncodedMessage"],members:{EncodedMessage:{}}},output:{resultWrapper:"DecodeAuthorizationMessageResult",type:"structure",members:{DecodedMessage:{}}},http:{}},GetFederationToken:{input:{type:"structure",required:["Name"],members:{Name:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"GetFederationTokenResult",type:"structure",members:{Credentials:{shape:"Sa"},FederatedUser:{type:"structure",required:["FederatedUserId","Arn"],members:{FederatedUserId:{},Arn:{}}},PackedPolicySize:{type:"integer"}}},http:{}},GetSessionToken:{input:{type:"structure",members:{DurationSeconds:{type:"integer"},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"GetSessionTokenResult",type:"structure",members:{Credentials:{shape:"Sa"}}},http:{}}},shapes:{Sa:{type:"structure",required:["AccessKeyId","SecretAccessKey","SessionToken","Expiration"],members:{AccessKeyId:{},SecretAccessKey:{},SessionToken:{},Expiration:{type:"timestamp"}}},Sf:{type:"structure",required:["AssumedRoleId","Arn"],members:{AssumedRoleId:{},Arn:{}}}}}},{"./core":3,"./http/xhr":12,"./services/cognitoidentity":36,"./services/dynamodb":37,"./services/ec2":38,"./services/machinelearning":39,"./services/s3":40,"./services/sqs":41,"./services/sts":42,"./xml/browser_parser":52}],2:[function(e,t,r){var a=e("./core");e("./credentials"),e("./credentials/credential_provider_chain"),a.Config=a.util.inherit({constructor:function(e){void 0===e&&(e={}),e=this.extractCredentials(e),a.util.each.call(this,this.keys,function(t,r){this.set(t,e[t],r)})},getCredentials:function(e){function t(t){e(t,t?null:i.credentials)}function r(e,t){return new a.util.error(t||new Error,{code:"CredentialsError",message:e})}function o(){i.credentials.get(function(e){if(e){var a="Could not load credentials from "+i.credentials.constructor.name;e=r(a,e)}t(e)})}function n(){var e=null;i.credentials.accessKeyId&&i.credentials.secretAccessKey||(e=r("Missing credentials")),t(e)}var i=this;i.credentials?"function"==typeof i.credentials.get?o():n():i.credentialProvider?i.credentialProvider.resolve(function(e,a){e&&(e=r("Could not load credentials from any providers",e)),i.credentials=a,t(e)}):t(r("No credentials to load"))},update:function(e,t){t=t||!1,e=this.extractCredentials(e),a.util.each.call(this,e,function(e,r){(t||this.keys.hasOwnProperty(e)||a.Service.hasService(e))&&this.set(e,r)})},loadFromPath:function(e){this.clear();var t=JSON.parse(a.util.readFileSync(e)),r=new a.FileSystemCredentials(e),o=new a.CredentialProviderChain;return o.providers.unshift(r),o.resolve(function(e,r){if(e)throw e;t.credentials=r}),this.constructor(t),this},clear:function(){a.util.each.call(this,this.keys,function(e){delete this[e]}),this.set("credentials",void 0),this.set("credentialProvider",void 0)},set:function(e,t,r){void 0===t?(void 0===r&&(r=this.keys[e]),"function"==typeof r?this[e]=r.call(this):this[e]=r):"httpOptions"===e&&this[e]?this[e]=a.util.merge(this[e],t):this[e]=t},keys:{credentials:null,credentialProvider:null,region:null,logger:null,apiVersions:{},apiVersion:null,endpoint:void 0,httpOptions:{timeout:12e4},maxRetries:void 0,maxRedirects:10,paramValidation:!0,sslEnabled:!0,s3ForcePathStyle:!1,s3BucketEndpoint:!1,computeChecksums:!0,convertResponseTypes:!0,dynamoDbCrc32:!0,systemClockOffset:0,signatureVersion:null},extractCredentials:function(e){return e.accessKeyId&&e.secretAccessKey&&(e=a.util.copy(e),e.credentials=new a.Credentials(e)),e}}),a.config=new a.Config},{"./core":3,"./credentials":4,"./credentials/credential_provider_chain":6}],3:[function(e,t,r){var a={util:e("./util")},o={};o.toString(),t.exports=a,a.util.update(a,{VERSION:"2.1.43",Signers:{},Protocol:{Json:e("./protocol/json"),Query:e("./protocol/query"),Rest:e("./protocol/rest"),RestJson:e("./protocol/rest_json"),RestXml:e("./protocol/rest_xml")},XML:{Builder:e("./xml/builder"),Parser:null},JSON:{Builder:e("./json/builder"),Parser:e("./json/parser")},Model:{Api:e("./model/api"),Operation:e("./model/operation"),Shape:e("./model/shape"),Paginator:e("./model/paginator"),ResourceWaiter:e("./model/resource_waiter")},util:e("./util"),apiLoader:function(){throw new Error("No API loader set")}}),e("./service"),e("./credentials"),e("./credentials/credential_provider_chain"),e("./credentials/temporary_credentials"),e("./credentials/web_identity_credentials"),e("./credentials/cognito_identity_credentials"),e("./credentials/saml_credentials"),e("./config"),e("./http"),e("./sequential_executor"),e("./event_listeners"),e("./request"),e("./response"),e("./resource_waiter"),e("./signers/request_signer"),e("./param_validator"),a.events=new a.SequentialExecutor},{"./config":2,"./credentials":4,"./credentials/cognito_identity_credentials":5,"./credentials/credential_provider_chain":6,"./credentials/saml_credentials":7,"./credentials/temporary_credentials":8,"./credentials/web_identity_credentials":9,"./event_listeners":10,"./http":11,"./json/builder":13,"./json/parser":14,"./model/api":15,"./model/operation":17,"./model/paginator":18,"./model/resource_waiter":19,"./model/shape":20,"./param_validator":21,"./protocol/json":22,"./protocol/query":23,"./protocol/rest":24,"./protocol/rest_json":25,"./protocol/rest_xml":26,"./request":30,"./resource_waiter":31,"./response":32,"./sequential_executor":34,"./service":35,"./signers/request_signer":44,"./util":51,"./xml/builder":53}],4:[function(e,t,r){var a=e("./core");a.Credentials=a.util.inherit({constructor:function(){if(a.util.hideProperties(this,["secretAccessKey"]),this.expired=!1,this.expireTime=null,1===arguments.length&&"object"==typeof arguments[0]){var e=arguments[0].credentials||arguments[0];this.accessKeyId=e.accessKeyId,this.secretAccessKey=e.secretAccessKey,this.sessionToken=e.sessionToken}else this.accessKeyId=arguments[0],this.secretAccessKey=arguments[1],this.sessionToken=arguments[2]},expiryWindow:15,needsRefresh:function(){var e=a.util.date.getDate().getTime(),t=new Date(e+1e3*this.expiryWindow);return this.expireTime&&t>this.expireTime?!0:this.expired||!this.accessKeyId||!this.secretAccessKey},get:function(e){var t=this;this.needsRefresh()?this.refresh(function(r){r||(t.expired=!1),e&&e(r)}):e&&e()},refresh:function(e){this.expired=!1,e()}})},{"./core":3}],5:[function(e,t,r){var a=e("../core");a.CognitoIdentityCredentials=a.util.inherit(a.Credentials,{localStorageKey:{id:"aws.cognito.identity-id.",providers:"aws.cognito.identity-providers."},constructor:function(e){a.Credentials.call(this),this.expired=!0,this.params=e,this.data=null,this.identityId=null,this.loadCachedId()},refresh:function(e){var t=this;t.createClients(),t.data=null,t.identityId=null,t.getId(function(r){r?(t.clearCachedId(),e(r)):t.params.RoleArn?t.getCredentialsFromSTS(e):t.getCredentialsForIdentity(e)})},clearCachedId:function(){this.identityId=null,delete this.params.IdentityId;var e=this.params.IdentityPoolId;delete this.storage[this.localStorageKey.id+e],delete this.storage[this.localStorageKey.providers+e]},getId:function(e){var t=this;return"string"==typeof t.params.IdentityId?e(null,t.params.IdentityId):void t.cognito.getId(function(r,a){!r&&a.IdentityId?(t.params.IdentityId=a.IdentityId,e(null,a.IdentityId)):e(r)})},loadCredentials:function(e,t){e&&t&&(t.expired=!1,t.accessKeyId=e.Credentials.AccessKeyId,t.secretAccessKey=e.Credentials.SecretKey,t.sessionToken=e.Credentials.SessionToken,t.expireTime=e.Credentials.Expiration)},getCredentialsForIdentity:function(e){var t=this;t.cognito.getCredentialsForIdentity(function(r,a){r?t.clearCachedId():(t.cacheId(a),t.data=a,t.loadCredentials(t.data,t)),e(r)})},getCredentialsFromSTS:function(e){var t=this;t.cognito.getOpenIdToken(function(r,a){r?(t.clearCachedId(),e(r)):(t.cacheId(a),t.params.WebIdentityToken=a.Token,t.webIdentityCredentials.refresh(function(r){r?t.clearCachedId():(t.data=t.webIdentityCredentials.data,t.sts.credentialsFrom(t.data,t)),e(r)}))})},loadCachedId:function(){var e=this;if(a.util.isBrowser()&&!e.params.IdentityId){var t=e.getStorage("id");if(t&&e.params.Logins){var r=Object.keys(e.params.Logins),o=(e.getStorage("providers")||"").split(","),n=o.filter(function(e){return-1!==r.indexOf(e)});0!==n.length&&(e.params.IdentityId=t)}else t&&(e.params.IdentityId=t)}},createClients:function(){this.webIdentityCredentials=this.webIdentityCredentials||new a.WebIdentityCredentials(this.params),this.cognito=this.cognito||new a.CognitoIdentity({params:this.params}),this.sts=this.sts||new a.STS},cacheId:function(e){this.identityId=e.IdentityId,this.params.IdentityId=this.identityId,a.util.isBrowser()&&(this.setStorage("id",e.IdentityId),this.params.Logins&&this.setStorage("providers",Object.keys(this.params.Logins).join(",")))},getStorage:function(e){return this.storage[this.localStorageKey[e]+this.params.IdentityPoolId]},setStorage:function(e,t){try{this.storage[this.localStorageKey[e]+this.params.IdentityPoolId]=t}catch(r){}},storage:function(){try{return a.util.isBrowser()&&null!==window.localStorage&&"object"==typeof window.localStorage?window.localStorage:{}}catch(e){return{}}}()})},{"../core":3}],6:[function(e,t,r){var a=e("../core");a.CredentialProviderChain=a.util.inherit(a.Credentials,{constructor:function(e){e?this.providers=e:this.providers=a.CredentialProviderChain.defaultProviders.slice(0)},resolve:function(e){function t(o,n){if(!o&&n||r===a.length)return void e(o,n);var i=a[r++];n="function"==typeof i?i.call():i,n.get?n.get(function(e){t(e,e?null:n)}):t(null,n)}if(0===this.providers.length)return e(new Error("No providers")),this;var r=0,a=this.providers.slice(0);return t(),this}}),a.CredentialProviderChain.defaultProviders=[]},{"../core":3}],7:[function(e,t,r){var a=e("../core");a.SAMLCredentials=a.util.inherit(a.Credentials,{constructor:function(e){a.Credentials.call(this),this.expired=!0,this.params=e},refresh:function(e){var t=this;t.createClients(),e||(e=function(e){if(e)throw e}),t.service.assumeRoleWithSAML(function(r,a){r||t.service.credentialsFrom(a,t),e(r)})},createClients:function(){this.service=this.service||new a.STS({params:this.params})}})},{"../core":3}],8:[function(e,t,r){var a=e("../core");a.TemporaryCredentials=a.util.inherit(a.Credentials,{constructor:function(e){a.Credentials.call(this),this.loadMasterCredentials(),this.expired=!0,this.params=e||{},this.params.RoleArn&&(this.params.RoleSessionName=this.params.RoleSessionName||"temporary-credentials")},refresh:function(e){var t=this;t.createClients(),e||(e=function(e){if(e)throw e}),t.service.config.credentials=t.masterCredentials;var r=t.params.RoleArn?t.service.assumeRole:t.service.getSessionToken;r.call(t.service,function(r,a){r||t.service.credentialsFrom(a,t),e(r)})},loadMasterCredentials:function(){for(this.masterCredentials=a.config.credentials;this.masterCredentials.masterCredentials;)this.masterCredentials=this.masterCredentials.masterCredentials},createClients:function(){this.service=this.service||new a.STS({params:this.params})}})},{"../core":3}],9:[function(e,t,r){var a=e("../core");a.WebIdentityCredentials=a.util.inherit(a.Credentials,{constructor:function(e){a.Credentials.call(this),this.expired=!0,this.params=e,this.params.RoleSessionName=this.params.RoleSessionName||"web-identity",this.data=null},refresh:function(e){var t=this;t.createClients(),e||(e=function(e){if(e)throw e}),t.service.assumeRoleWithWebIdentity(function(r,a){t.data=null,r||(t.data=a,t.service.credentialsFrom(a,t)),e(r)})},createClients:function(){this.service=this.service||new a.STS({params:this.params})}})},{"../core":3}],10:[function(e,t,r){var a=e("./core"),o=e("./sequential_executor");a.EventListeners={Core:{}},a.EventListeners={Core:(new o).addNamedListeners(function(e,t){t("VALIDATE_CREDENTIALS","validate",function(e,t){return e.service.api.signatureVersion?void e.service.config.getCredentials(function(r){r&&(e.response.error=a.util.error(r,{code:"CredentialsError",message:"Missing credentials in config"})),t()}):t()}),e("VALIDATE_REGION","validate",function(e){e.service.config.region||e.service.isGlobalEndpoint||(e.response.error=a.util.error(new Error,{code:"ConfigError",message:"Missing region in config"}))}),e("VALIDATE_PARAMETERS","validate",function(e){var t=e.service.api.operations[e.operation].input;(new a.ParamValidator).validate(t,e.params)}),t("COMPUTE_SHA256","afterBuild",function(e,t){if(e.haltHandlersOnError(),!e.service.api.signatureVersion)return t();if(e.service.getSignerClass(e)===a.Signers.V4){var r=e.httpRequest.body||"";a.util.computeSha256(r,function(r,a){r?t(r):(e.httpRequest.headers["X-Amz-Content-Sha256"]=a,t())})}else t()}),e("SET_CONTENT_LENGTH","afterBuild",function(e){if(void 0===e.httpRequest.headers["Content-Length"]){var t=a.util.string.byteLength(e.httpRequest.body);e.httpRequest.headers["Content-Length"]=t}}),e("SET_HTTP_HOST","afterBuild",function(e){e.httpRequest.headers.Host=e.httpRequest.endpoint.host}),e("RESTART","restart",function(){var e=this.response.error;e&&e.retryable&&(this.httpRequest=new a.HttpRequest(this.service.endpoint,this.service.region),this.response.retryCount<this.service.config.maxRetries?this.response.retryCount++:this.response.error=null)}),t("SIGN","sign",function(e,t){return e.service.api.signatureVersion?void e.service.config.getCredentials(function(r,o){if(r)return e.response.error=r,t();try{var n=a.util.date.getDate(),i=e.service.getSignerClass(e),s=new i(e.httpRequest,e.service.api.signingName||e.service.api.endpointPrefix);delete e.httpRequest.headers.Authorization,delete e.httpRequest.headers.Date,delete e.httpRequest.headers["X-Amz-Date"],s.addAuthorization(o,n),e.signedAt=n}catch(u){e.response.error=u}t()}):t()}),e("VALIDATE_RESPONSE","validateResponse",function(e){this.service.successfulResponse(e,this)?(e.data={},e.error=null):(e.data=null,e.error=a.util.error(new Error,{code:"UnknownError",message:"An unknown error occurred."}))}),t("SEND","send",function(e,t){function r(r){e.httpResponse.stream=r,r.on("headers",function(t,o){e.request.emit("httpHeaders",[t,o,e]),e.httpResponse.streaming||(2===a.HttpClient.streamsApiVersion?r.on("readable",function(){var t=r.read();null!==t&&e.request.emit("httpData",[t,e])}):r.on("data",function(t){e.request.emit("httpData",[t,e])}))}),r.on("end",function(){e.request.emit("httpDone"),t()})}function o(t){t.on("sendProgress",function(t){e.request.emit("httpUploadProgress",[t,e])}),t.on("receiveProgress",function(t){e.request.emit("httpDownloadProgress",[t,e])})}function n(r){e.error=a.util.error(r,{code:"NetworkingError",region:e.request.httpRequest.region,hostname:e.request.httpRequest.endpoint.hostname,retryable:!0}),e.request.emit("httpError",[e.error,e],function(){t()})}function i(){var t=a.HttpClient.getInstance(),i=e.request.service.config.httpOptions||{};try{var s=t.handleRequest(e.request.httpRequest,i,r,n);o(s)}catch(u){n(u)}}e.httpResponse._abortCallback=t,e.error=null,e.data=null;var s=(a.util.date.getDate()-this.signedAt)/1e3;s>=600?this.emit("sign",[this],function(e){e?t(e):i()}):i()}),e("HTTP_HEADERS","httpHeaders",function(e,t,r){r.httpResponse.statusCode=e,r.httpResponse.headers=t,r.httpResponse.body=new a.util.Buffer(""),r.httpResponse.buffers=[],r.httpResponse.numBytes=0}),e("HTTP_DATA","httpData",function(e,t){if(e){if(a.util.isNode()){t.httpResponse.numBytes+=e.length;var r=t.httpResponse.headers["content-length"],o={loaded:t.httpResponse.numBytes,total:r};t.request.emit("httpDownloadProgress",[o,t])}t.httpResponse.buffers.push(new a.util.Buffer(e))}}),e("HTTP_DONE","httpDone",function(e){if(e.httpResponse.buffers&&e.httpResponse.buffers.length>0){var t=a.util.buffer.concat(e.httpResponse.buffers);e.httpResponse.body=t;
}delete e.httpResponse.numBytes,delete e.httpResponse.buffers}),e("FINALIZE_ERROR","retry",function(e){e.httpResponse.statusCode&&(e.error.statusCode=e.httpResponse.statusCode,void 0===e.error.retryable&&(e.error.retryable=this.service.retryableError(e.error,this)))}),e("INVALIDATE_CREDENTIALS","retry",function(e){if(e.error)switch(e.error.code){case"RequestExpired":case"ExpiredTokenException":case"ExpiredToken":e.error.retryable=!0,e.request.service.config.credentials.expired=!0}}),e("EXPIRED_SIGNATURE","retry",function(e){var t=e.error;t&&"string"==typeof t.code&&"string"==typeof t.message&&t.code.match(/Signature/)&&t.message.match(/expired/)&&(e.error.retryable=!0)}),e("REDIRECT","retry",function(e){e.error&&e.error.statusCode>=300&&e.error.statusCode<400&&e.httpResponse.headers.location&&(this.httpRequest.endpoint=new a.Endpoint(e.httpResponse.headers.location),this.httpRequest.headers.Host=this.httpRequest.endpoint.host,e.error.redirect=!0,e.error.retryable=!0)}),e("RETRY_CHECK","retry",function(e){if(e.error)if(e.error.redirect&&e.redirectCount<e.maxRedirects)e.error.retryDelay=0;else if(e.retryCount<e.maxRetries){var t=this.service.retryDelays();e.error.retryDelay=t[e.retryCount]||0}}),t("RESET_RETRY_STATE","afterRetry",function(e,t){var r,a=!1;e.error&&(r=e.error.retryDelay||0,e.error.retryable&&e.retryCount<e.maxRetries?(e.retryCount++,a=!0):e.error.redirect&&e.redirectCount<e.maxRedirects&&(e.redirectCount++,a=!0)),a?(e.error=null,setTimeout(t,r)):t()})}),CorePost:(new o).addNamedListeners(function(e){e("EXTRACT_REQUEST_ID","extractData",function(e){e.requestId||(e.requestId=e.httpResponse.headers["x-amz-request-id"]||e.httpResponse.headers["x-amzn-requestid"]),!e.requestId&&e.data&&e.data.ResponseMetadata&&(e.requestId=e.data.ResponseMetadata.RequestId)}),e("ENOTFOUND_ERROR","httpError",function(e){if("NetworkingError"===e.code&&"ENOTFOUND"===e.errno){var t="Inaccessible host: `"+e.hostname+"'. This service may not be available in the `"+e.region+"' region.";this.response.error=a.util.error(new Error(t),{code:"UnknownEndpoint",region:e.region,hostname:e.hostname,retryable:!0,originalError:e})}})}),Logger:(new o).addNamedListeners(function(t){t("LOG_REQUEST","complete",function(t){function r(){var r=a.util.date.getDate().getTime(),i=(r-o.startTime.getTime())/1e3,s=n.isTTY?!0:!1,u=t.httpResponse.statusCode,c=e("util").inspect(o.params,!0,null),p="";return s&&(p+="[33m"),p+="[AWS "+o.service.serviceIdentifier+" "+u,p+=" "+i.toString()+"s "+t.retryCount+" retries]",s&&(p+="[0;1m"),p+=" "+a.util.string.lowerFirst(o.operation),p+="("+c+")",s&&(p+="[0m"),p}var o=t.request,n=o.service.config.logger;if(n){var i=r();"function"==typeof n.log?n.log(i):"function"==typeof n.write&&n.write(i+"\n")}})}),Json:(new o).addNamedListeners(function(t){var r=e("./protocol/json");t("BUILD","build",r.buildRequest),t("EXTRACT_DATA","extractData",r.extractData),t("EXTRACT_ERROR","extractError",r.extractError)}),Rest:(new o).addNamedListeners(function(t){var r=e("./protocol/rest");t("BUILD","build",r.buildRequest),t("EXTRACT_DATA","extractData",r.extractData),t("EXTRACT_ERROR","extractError",r.extractError)}),RestJson:(new o).addNamedListeners(function(t){var r=e("./protocol/rest_json");t("BUILD","build",r.buildRequest),t("EXTRACT_DATA","extractData",r.extractData),t("EXTRACT_ERROR","extractError",r.extractError)}),RestXml:(new o).addNamedListeners(function(t){var r=e("./protocol/rest_xml");t("BUILD","build",r.buildRequest),t("EXTRACT_DATA","extractData",r.extractData),t("EXTRACT_ERROR","extractError",r.extractError)}),Query:(new o).addNamedListeners(function(t){var r=e("./protocol/query");t("BUILD","build",r.buildRequest),t("EXTRACT_DATA","extractData",r.extractData),t("EXTRACT_ERROR","extractError",r.extractError)})}},{"./core":3,"./protocol/json":22,"./protocol/query":23,"./protocol/rest":24,"./protocol/rest_json":25,"./protocol/rest_xml":26,"./sequential_executor":34,util:72}],11:[function(e,t,r){var a=e("./core"),o=a.util.inherit;a.Endpoint=o({constructor:function(e,t){if(a.util.hideProperties(this,["slashes","auth","hash","search","query"]),"undefined"==typeof e||null===e)throw new Error("Invalid endpoint: "+e);if("string"!=typeof e)return a.util.copy(e);if(!e.match(/^http/)){var r=t&&void 0!==t.sslEnabled?t.sslEnabled:a.config.sslEnabled;e=(r?"https":"http")+"://"+e}a.util.update(this,a.util.urlParse(e)),this.port?this.port=parseInt(this.port,10):this.port="https:"===this.protocol?443:80}}),a.HttpRequest=o({constructor:function(e,t){e=new a.Endpoint(e),this.method="POST",this.path=e.path||"/",this.headers={},this.body="",this.endpoint=e,this.region=t,this.setUserAgent()},setUserAgent:function(){var e=a.util.isBrowser()?"X-Amz-":"";this.headers[e+"User-Agent"]=a.util.userAgent()},pathname:function(){return this.path.split("?",1)[0]},search:function(){var e=this.path.split("?",2)[1];return e?(e=a.util.queryStringParse(e),a.util.queryParamsToString(e)):""}}),a.HttpResponse=o({constructor:function(){this.statusCode=void 0,this.headers={},this.body=void 0,this.streaming=!1,this.stream=null},createUnbufferedStream:function(){return this.streaming=!0,this.stream}}),a.HttpClient=o({}),a.HttpClient.getInstance=function(){return void 0===this.singleton&&(this.singleton=new this),this.singleton}},{"./core":3}],12:[function(e,t,r){var a=e("../core"),o=e("events").EventEmitter;e("../http"),a.XHRClient=a.util.inherit({handleRequest:function(e,t,r,n){var i=this,s=e.endpoint,u=new o,c=s.protocol+"//"+s.hostname;80!==s.port&&443!==s.port&&(c+=":"+s.port),c+=e.path;var p=new XMLHttpRequest,m=!1;e.stream=p,p.addEventListener("readystatechange",function(){try{if(0===p.status)return}catch(e){return}if(this.readyState>=this.HEADERS_RECEIVED&&!m){try{p.responseType="arraybuffer"}catch(e){}u.statusCode=p.status,u.headers=i.parseHeaders(p.getAllResponseHeaders()),u.emit("headers",u.statusCode,u.headers),m=!0}this.readyState===this.DONE&&i.finishRequest(p,u)},!1),p.upload.addEventListener("progress",function(e){u.emit("sendProgress",e)}),p.addEventListener("progress",function(e){u.emit("receiveProgress",e)},!1),p.addEventListener("timeout",function(){n(a.util.error(new Error("Timeout"),{code:"TimeoutError"}))},!1),p.addEventListener("error",function(){n(a.util.error(new Error("Network Failure"),{code:"NetworkingError"}))},!1),r(u),p.open(e.method,c,t.xhrAsync!==!1),a.util.each(e.headers,function(e,t){"Content-Length"!==e&&"User-Agent"!==e&&"Host"!==e&&p.setRequestHeader(e,t)}),t.timeout&&t.xhrAsync!==!1&&(p.timeout=t.timeout),t.xhrWithCredentials&&(p.withCredentials=!0);try{p.send(e.body)}catch(l){if(!e.body||"object"!=typeof e.body.buffer)throw l;p.send(e.body.buffer)}return u},parseHeaders:function(e){var t={};return a.util.arrayEach(e.split(/\r?\n/),function(e){var r=e.split(":",1)[0],a=e.substring(r.length+2);r.length>0&&(t[r.toLowerCase()]=a)}),t},finishRequest:function(e,t){var r;if("arraybuffer"===e.responseType&&e.response){var o=e.response;r=new a.util.Buffer(o.byteLength);for(var n=new Uint8Array(o),i=0;i<r.length;++i)r[i]=n[i]}try{r||"string"!=typeof e.responseText||(r=new a.util.Buffer(e.responseText))}catch(s){}r&&t.emit("data",r),t.emit("end")}}),a.HttpClient.prototype=a.XHRClient.prototype,a.HttpClient.streamsApiVersion=1},{"../core":3,"../http":11,events:63}],13:[function(e,t,r){function a(){}function o(e,t){if(!t||void 0===e||null===e)return void 0;switch(t.type){case"structure":return n(e,t);case"map":return s(e,t);case"list":return i(e,t);default:return u(e,t)}}function n(e,t){var r={};return c.each(e,function(e,a){var n=t.members[e];if(n){if("body"!==n.location)return;var i=o(a,n);void 0!==i&&(r[e]=i)}}),r}function i(e,t){var r=[];return c.arrayEach(e,function(e){var a=o(e,t.member);void 0!==a&&r.push(a)}),r}function s(e,t){var r={};return c.each(e,function(e,a){var n=o(a,t.value);void 0!==n&&(r[e]=n)}),r}function u(e,t){return t.toWireFormat(e)}var c=e("../util");a.prototype.build=function(e,t){return JSON.stringify(o(e,t))},t.exports=a},{"../util":51}],14:[function(e,t,r){function a(){}function o(e,t){if(!t||void 0===e)return void 0;switch(t.type){case"structure":return n(e,t);case"map":return s(e,t);case"list":return i(e,t);default:return u(e,t)}}function n(e,t){if(null==e)return void 0;var r={};return c.each(e,function(e,a){var n=t.members[e];if(n){var i=o(a,n);void 0!==i&&(r[e]=i)}}),r}function i(e,t){if(null==e)return void 0;var r=[];return c.arrayEach(e,function(e){var a=o(e,t.member);void 0===a?r.push(null):r.push(a)}),r}function s(e,t){if(null==e)return void 0;var r={};return c.each(e,function(e,a){var n=o(a,t.value);void 0===n?r[e]=null:r[e]=n}),r}function u(e,t){return t.toType(e)}var c=e("../util");a.prototype.parse=function(e,t){return o(JSON.parse(e),t)},t.exports=a},{"../util":51}],15:[function(e,t,r){function a(e,t){e=e||{},t=t||{},t.api=this,e.metadata=e.metadata||{},p(this,"isApi",!0,!1),p(this,"apiVersion",e.metadata.apiVersion),p(this,"endpointPrefix",e.metadata.endpointPrefix),p(this,"signingName",e.metadata.signingName),p(this,"globalEndpoint",e.metadata.globalEndpoint),p(this,"signatureVersion",e.metadata.signatureVersion),p(this,"jsonVersion",e.metadata.jsonVersion),p(this,"targetPrefix",e.metadata.targetPrefix),p(this,"protocol",e.metadata.protocol),p(this,"timestampFormat",e.metadata.timestampFormat),p(this,"xmlNamespaceUri",e.metadata.xmlNamespace),p(this,"abbreviation",e.metadata.serviceAbbreviation),p(this,"fullName",e.metadata.serviceFullName),m(this,"className",function(){var t=e.metadata.serviceAbbreviation||e.metadata.serviceFullName;return t?(t=t.replace(/^Amazon|AWS\s*|\(.*|\s+|\W+/g,""),"ElasticLoadBalancing"===t&&(t="ELB"),t):null}),p(this,"operations",new o(e.operations,t,function(e,r){return new n(e,r,t)},c.string.lowerFirst)),p(this,"shapes",new o(e.shapes,t,function(e,r){return i.create(r,t)})),p(this,"paginators",new o(e.paginators,t,function(e,r){return new s(e,r,t)})),p(this,"waiters",new o(e.waiters,t,function(e,r){return new u(e,r,t)},c.string.lowerFirst)),t.documentation&&(p(this,"documentation",e.documentation),p(this,"documentationUrl",e.documentationUrl))}var o=e("./collection"),n=e("./operation"),i=e("./shape"),s=e("./paginator"),u=e("./resource_waiter"),c=e("../util"),p=c.property,m=c.memoizedProperty;t.exports=a},{"../util":51,"./collection":16,"./operation":17,"./paginator":18,"./resource_waiter":19,"./shape":20}],16:[function(e,t,r){function a(e,t,r,a){n(this,a(e),function(){return r(e,t)})}function o(e,t,r,o){o=o||String;var n=this;for(var i in e)e.hasOwnProperty(i)&&a.call(n,i,e[i],r,o)}var n=e("../util").memoizedProperty;t.exports=o},{"../util":51}],17:[function(e,t,r){function a(e,t,r){r=r||{},i(this,"name",t.name||e),i(this,"api",r.api,!1),t.http=t.http||{},i(this,"httpMethod",t.http.method||"POST"),i(this,"httpPath",t.http.requestUri||"/"),s(this,"input",function(){return t.input?o.create(t.input,r):new o.create({type:"structure"},r)}),s(this,"output",function(){return t.output?o.create(t.output,r):new o.create({type:"structure"},r)}),s(this,"errors",function(){var e=[];if(!t.errors)return null;for(var a=0;a<t.errors.length;a++)e.push(o.create(t.errors[a],r));return e}),s(this,"paginator",function(){return r.api.paginators[e]}),r.documentation&&(i(this,"documentation",t.documentation),i(this,"documentationUrl",t.documentationUrl))}var o=e("./shape"),n=e("../util"),i=n.property,s=n.memoizedProperty;t.exports=a},{"../util":51,"./shape":20}],18:[function(e,t,r){function a(e,t){o(this,"inputToken",t.input_token),o(this,"limitKey",t.limit_key),o(this,"moreResults",t.more_results),o(this,"outputToken",t.output_token),o(this,"resultKey",t.result_key)}var o=e("../util").property;t.exports=a},{"../util":51}],19:[function(e,t,r){function a(e,t,r){function a(){n(this,"name",e),n(this,"api",r.api,!1),t.operation&&n(this,"operation",o.string.lowerFirst(t.operation));var a=this,i={ignoreErrors:"ignore_errors",successType:"success_type",successValue:"success_value",successPath:"success_path",acceptorType:"acceptor_type",acceptorValue:"acceptor_value",acceptorPath:"acceptor_path",failureType:"failure_type",failureValue:"failure_value",failurePath:"success_path",interval:"interval",maxAttempts:"max_attempts"};Object.keys(i).forEach(function(e){var r=t[i[e]];r&&n(a,e,r)})}if(r=r||{},r.api){var i=null;t["extends"]?i=r.api.waiters[t["extends"]]:"__default__"!==e&&(i=r.api.waiters.__default__),i&&(a.prototype=i)}return new a}var o=e("../util"),n=o.property;t.exports=a},{"../util":51}],20:[function(e,t,r){function a(e,t,r){null!==r&&void 0!==r&&S.property.apply(this,arguments)}function o(e,t){e.constructor.prototype[t]||S.memoizedProperty.apply(this,arguments)}function n(e,t,r){t=t||{},a(this,"shape",e.shape),a(this,"api",t.api,!1),a(this,"type",e.type),a(this,"location",e.location||this.location||"body"),a(this,"name",this.name||e.xmlName||e.queryName||e.locationName||r),a(this,"isStreaming",e.streaming||this.isStreaming||!1),a(this,"isComposite",e.isComposite||!1),a(this,"isShape",!0,!1),a(this,"isQueryName",e.queryName?!0:!1,!1),a(this,"isLocationName",e.locationName?!0:!1,!1),t.documentation&&(a(this,"documentation",e.documentation),a(this,"documentationUrl",e.documentationUrl)),e.xmlAttribute&&a(this,"isXmlAttribute",e.xmlAttribute||!1),a(this,"defaultValue",null),this.toWireFormat=function(e){return null===e||void 0===e?"":e},this.toType=function(e){return e}}function i(e){n.apply(this,arguments),a(this,"isComposite",!0),e.flattened&&a(this,"flattened",e.flattened||!1)}function s(e,t){var r=null,s=!this.isShape;i.apply(this,arguments),s&&(a(this,"defaultValue",function(){return{}}),a(this,"members",{}),a(this,"memberNames",[]),a(this,"required",[]),a(this,"isRequired",function(){return!1})),e.members&&(a(this,"members",new f(e.members,t,function(e,r){return n.create(r,t,e)})),o(this,"memberNames",function(){return e.xmlOrder||Object.keys(e.members)})),e.required&&(a(this,"required",e.required),a(this,"isRequired",function(t){if(!r){r={};for(var a=0;a<e.required.length;a++)r[e.required[a]]=!0}return r[t]},!1,!0)),a(this,"resultWrapper",e.resultWrapper||null),e.payload&&a(this,"payload",e.payload),"string"==typeof e.xmlNamespace?a(this,"xmlNamespaceUri",e.xmlNamespace):"object"==typeof e.xmlNamespace&&(a(this,"xmlNamespacePrefix",e.xmlNamespace.prefix),a(this,"xmlNamespaceUri",e.xmlNamespace.uri))}function u(e,t){var r=this,s=!this.isShape;if(i.apply(this,arguments),s&&a(this,"defaultValue",function(){return[]}),e.member&&o(this,"member",function(){return n.create(e.member,t)}),this.flattened){var u=this.name;o(this,"name",function(){return r.member.name||u})}}function c(e,t){var r=!this.isShape;i.apply(this,arguments),r&&(a(this,"defaultValue",function(){return{}}),a(this,"key",n.create({type:"string"},t)),a(this,"value",n.create({type:"string"},t))),e.key&&o(this,"key",function(){return n.create(e.key,t)}),e.value&&o(this,"value",function(){return n.create(e.value,t)})}function p(e){var t=this;if(n.apply(this,arguments),"header"===this.location)a(this,"timestampFormat","rfc822");else if(e.timestampFormat)a(this,"timestampFormat",e.timestampFormat);else if(this.api)if(this.api.timestampFormat)a(this,"timestampFormat",this.api.timestampFormat);else switch(this.api.protocol){case"json":case"rest-json":a(this,"timestampFormat","unixTimestamp");break;case"rest-xml":case"query":case"ec2":a(this,"timestampFormat","iso8601")}this.toType=function(e){return null===e||void 0===e?null:"function"==typeof e.toUTCString?e:"string"==typeof e||"number"==typeof e?S.date.parseTimestamp(e):null},this.toWireFormat=function(e){return S.date.format(e,t.timestampFormat)}}function m(){if(n.apply(this,arguments),this.api)switch(this.api.protocol){case"rest-xml":case"query":case"ec2":this.toType=function(e){return e||""}}}function l(){n.apply(this,arguments),this.toType=function(e){return null===e||void 0===e?null:parseFloat(e)},this.toWireFormat=this.toType}function d(){n.apply(this,arguments),this.toType=function(e){return null===e||void 0===e?null:parseInt(e,10)},this.toWireFormat=this.toType}function y(){n.apply(this,arguments),this.toType=S.base64.decode,this.toWireFormat=S.base64.encode}function h(){y.apply(this,arguments)}function b(){n.apply(this,arguments),this.toType=function(e){return"boolean"==typeof e?e:null===e||void 0===e?null:"true"===e}}var f=e("./collection"),S=e("../util");n.normalizedTypes={character:"string","double":"float","long":"integer","short":"integer",biginteger:"integer",bigdecimal:"float",blob:"binary"},n.types={structure:s,list:u,map:c,"boolean":b,timestamp:p,"float":l,integer:d,string:m,base64:h,binary:y},n.resolve=function(e,t){if(e.shape){var r=t.api.shapes[e.shape];if(!r)throw new Error("Cannot find shape reference: "+e.shape);return r}return null},n.create=function(e,t,r){if(e.isShape)return e;var a=n.resolve(e,t);if(a){var o=Object.keys(e);if(t.documentation||(o=o.filter(function(e){return!e.match(/documentation/)})),o===["shape"])return a;var i=function(){a.constructor.call(this,e,t,r)};return i.prototype=a,new i}e.type||(e.members?e.type="structure":e.member?e.type="list":e.key?e.type="map":e.type="string");var s=e.type;if(n.normalizedTypes[e.type]&&(e.type=n.normalizedTypes[e.type]),n.types[e.type])return new n.types[e.type](e,t,r);throw new Error("Unrecognized shape type: "+s)},n.shapes={StructureShape:s,ListShape:u,MapShape:c,StringShape:m,BooleanShape:b,Base64Shape:h},t.exports=n},{"../util":51,"./collection":16}],21:[function(e,t,r){var a=e("./core");a.ParamValidator=a.util.inherit({validate:function(e,t,r){if(this.errors=[],this.validateMember(e,t||{},r||"params"),!(this.errors.length>1)){if(1===this.errors.length)throw this.errors[0];return!0}var o=this.errors.join("\n* ");if(this.errors.length>1)throw o="There were "+this.errors.length+" validation errors:\n* "+o,a.util.error(new Error(o),{code:"MultipleValidationErrors",errors:this.errors})},validateStructure:function(e,t,r){this.validateType(r,t,["object"],"structure");for(var a,o=0;e.required&&o<e.required.length;o++){a=e.required[o];var n=t[a];(void 0===n||null===n)&&this.fail("MissingRequiredParameter","Missing required key '"+a+"' in "+r)}for(a in t)if(t.hasOwnProperty(a)){var i=t[a],s=e.members[a];if(void 0!==s){var u=[r,a].join(".");this.validateMember(s,i,u)}else this.fail("UnexpectedParameter","Unexpected key '"+a+"' found in "+r)}return!0},validateMember:function(e,t,r){switch(e.type){case"structure":return this.validateStructure(e,t,r);case"list":return this.validateList(e,t,r);case"map":return this.validateMap(e,t,r);default:return this.validateScalar(e,t,r)}},validateList:function(e,t,r){this.validateType(r,t,[Array]);for(var a=0;a<t.length;a++)this.validateMember(e.member,t[a],r+"["+a+"]")},validateMap:function(e,t,r){this.validateType(r,t,["object"],"map");for(var a in t)t.hasOwnProperty(a)&&this.validateMember(e.value,t[a],r+"['"+a+"']")},validateScalar:function(e,t,r){switch(e.type){case null:case void 0:case"string":return this.validateType(r,t,["string"]);case"base64":case"binary":return this.validatePayload(r,t);case"integer":case"float":return this.validateNumber(r,t);case"boolean":return this.validateType(r,t,["boolean"]);case"timestamp":return this.validateType(r,t,[Date,/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$/,"number"],"Date object, ISO-8601 string, or a UNIX timestamp");default:return this.fail("UnkownType","Unhandled type "+e.type+" for "+r)}},fail:function(e,t){this.errors.push(a.util.error(new Error(t),{code:e}))},validateType:function(e,t,r,o){if(null!==t&&void 0!==t){for(var n=!1,i=0;i<r.length;i++){if("string"==typeof r[i]){if(typeof t===r[i])return}else if(r[i]instanceof RegExp){if((t||"").toString().match(r[i]))return}else{if(t instanceof r[i])return;if(a.util.isType(t,r[i]))return;o||n||(r=r.slice()),r[i]=a.util.typeName(r[i])}n=!0}var s=o;s||(s=r.join(", ").replace(/,([^,]+)$/,", or$1"));var u=s.match(/^[aeiou]/i)?"n":"";this.fail("InvalidParameterType","Expected "+e+" to be a"+u+" "+s)}},validateNumber:function(e,t){if(null!==t&&void 0!==t){if("string"==typeof t){var r=parseFloat(t);r.toString()===t&&(t=r)}this.validateType(e,t,["number"])}},validatePayload:function(e,t){if(null!==t&&void 0!==t&&"string"!=typeof t&&(!t||"number"!=typeof t.byteLength)){if(a.util.isNode()){var r=a.util.nodeRequire("stream").Stream;if(a.util.Buffer.isBuffer(t)||t instanceof r)return}var o=["Buffer","Stream","File","Blob","ArrayBuffer","DataView"];if(t)for(var n=0;n<o.length;n++){if(a.util.isType(t,o[n]))return;if(a.util.typeName(t.constructor)===o[n])return}this.fail("InvalidParameterType","Expected "+e+" to be a string, Buffer, Stream, Blob, or typed array object")}}})},{"./core":3}],22:[function(e,t,r){function a(e){var t=e.httpRequest,r=e.service.api,a=r.targetPrefix+"."+r.operations[e.operation].name,o=r.jsonVersion||"1.0",n=r.operations[e.operation].input,i=new s;1===o&&(o="1.0"),t.body=i.build(e.params||{},n),t.headers["Content-Type"]="application/x-amz-json-"+o,t.headers["X-Amz-Target"]=a}function o(e){var t={},r=e.httpResponse;if(t.code=r.headers["x-amzn-errortype"]||"UnknownError","string"==typeof t.code&&(t.code=t.code.split(":")[0]),r.body.length>0){var a=JSON.parse(r.body.toString());(a.__type||a.code)&&(t.code=(a.__type||a.code).split("#").pop()),"RequestEntityTooLarge"===t.code?t.message="Request body must be less than 1 MB":t.message=a.message||a.Message||null}else t.statusCode=r.statusCode,t.message=r.statusCode.toString();e.error=i.error(new Error,t)}function n(e){var t=e.httpResponse.body.toString()||"{}";if(e.request.service.config.convertResponseTypes===!1)e.data=JSON.parse(t);else{var r=e.request.service.api.operations[e.request.operation],a=r.output||{},o=new u;e.data=o.parse(t,a)}}var i=e("../util"),s=e("../json/builder"),u=e("../json/parser");t.exports={buildRequest:a,extractError:o,extractData:n}},{"../json/builder":13,"../json/parser":14,"../util":51}],23:[function(e,t,r){function a(e){var t=e.service.api.operations[e.operation],r=e.httpRequest;r.headers["Content-Type"]="application/x-www-form-urlencoded; charset=utf-8",r.params={Version:e.service.api.apiVersion,Action:t.name};var a=new u;a.serialize(e.params,t.input,function(e,t){r.params[e]=t}),r.body=s.queryParamsToString(r.params)}function o(e){var t,r=e.httpResponse.body.toString();t=r.match("<UnknownOperationException")?{Code:"UnknownOperation",Message:"Unknown operation "+e.request.operation}:(new i.XML.Parser).parse(r),t.requestId&&!e.requestId&&(e.requestId=t.requestId),t.Errors&&(t=t.Errors),t.Error&&(t=t.Error),t.Code?e.error=s.error(new Error,{code:t.Code,message:t.Message}):e.error=s.error(new Error,{code:e.httpResponse.statusCode,message:null})}function n(e){var t=e.request,r=t.service.api.operations[t.operation],a=r.output||{},o=a;if(o.resultWrapper){var n=c.create({type:"structure"});n.members[o.resultWrapper]=a,n.memberNames=[o.resultWrapper],s.property(a,"name",a.resultWrapper),a=n}var u=new i.XML.Parser;if(a&&a.members&&!a.members._XAMZRequestId){var p=c.create({type:"string"},{api:{protocol:"query"}},"requestId");a.members._XAMZRequestId=p}var m=u.parse(e.httpResponse.body.toString(),a);e.requestId=m._XAMZRequestId||m.requestId,m._XAMZRequestId&&delete m._XAMZRequestId,o.resultWrapper&&m[o.resultWrapper]&&(s.update(m,m[o.resultWrapper]),delete m[o.resultWrapper]),e.data=m}var i=e("../core"),s=e("../util"),u=e("../query/query_param_serializer"),c=e("../model/shape");t.exports={buildRequest:a,extractError:o,extractData:n}},{"../core":3,"../model/shape":20,"../query/query_param_serializer":27,"../util":51}],24:[function(e,t,r){function a(e){e.httpRequest.method=e.service.api.operations[e.operation].httpMethod}function o(e){var t=e.service.api.operations[e.operation],r=t.input,a=[e.httpRequest.endpoint.path,t.httpPath].join("/");a=a.replace(/\/+/g,"/");var o={},n=!1;if(c.each(r.members,function(t,r){var i=e.params[t];if(null!==i&&void 0!==i)if("uri"===r.location){var s=new RegExp("\\{"+r.name+"(\\+)?\\}");a=a.replace(s,function(e,t){var r=t?c.uriEscapePath:c.uriEscape;return r(String(i))})}else"querystring"===r.location&&(n=!0,"list"===r.type?o[r.name]=i.map(function(e){return c.uriEscape(String(e))}):o[r.name]=c.uriEscape(String(i)))}),n){a+=a.indexOf("?")>=0?"&":"?";var i=[];c.arrayEach(Object.keys(o).sort(),function(e){Array.isArray(o[e])||(o[e]=[o[e]]);for(var t=0;t<o[e].length;t++)i.push(c.uriEscape(String(e))+"="+o[e][t])}),a+=i.join("&")}e.httpRequest.path=a}function n(e){var t=e.service.api.operations[e.operation];c.each(t.input.members,function(t,r){var a=e.params[t];null!==a&&void 0!==a&&("headers"===r.location&&"map"===r.type?c.each(a,function(t,a){e.httpRequest.headers[r.name+t]=a}):"header"===r.location&&(a=r.toWireFormat(a).toString(),e.httpRequest.headers[r.name]=a))})}function i(e){a(e),o(e),n(e)}function s(){}function u(e){var t=e.request,r={},a=e.httpResponse,o=t.service.api.operations[t.operation],n=o.output,i={};c.each(a.headers,function(e,t){i[e.toLowerCase()]=t}),c.each(n.members,function(e,t){var o=(t.name||e).toLowerCase();if("headers"===t.location&&"map"===t.type){r[e]={};var n=t.isLocationName?t.name:"",s=new RegExp("^"+n+"(.+)","i");c.each(a.headers,function(t,a){var o=t.match(s);null!==o&&(r[e][o[1]]=a)})}else"header"===t.location?void 0!==i[o]&&(r[e]=i[o]):"statusCode"===t.location&&(r[e]=parseInt(a.statusCode,10))}),e.data=r}var c=e("../util");t.exports={buildRequest:i,extractError:s,extractData:u}},{"../util":51}],25:[function(e,t,r){function a(e){var t=new p,r=e.service.api.operations[e.operation].input;if(r.payload){var a={},o=r.members[r.payload];if(a=e.params[r.payload],void 0===a)return;"structure"===o.type?e.httpRequest.body=t.build(a,o):e.httpRequest.body=a}else e.httpRequest.body=t.build(e.params,r)}function o(e){u.buildRequest(e),["GET","HEAD"].indexOf(e.httpRequest.method)<0&&a(e)}function n(e){c.extractError(e)}function i(e){u.extractData(e);var t=e.request,r=t.service.api.operations[t.operation].output||{};if(r.payload){var a=r.members[r.payload],o=e.httpResponse.body;if(a.isStreaming)e.data[r.payload]=o;else if("structure"===a.type){var n=new m;e.data[r.payload]=n.parse(o,a)}else e.data[r.payload]=o.toString()}else{var i=e.data;c.extractData(e),e.data=s.merge(i,e.data)}}var s=e("../util"),u=e("./rest"),c=e("./json"),p=e("../json/builder"),m=e("../json/parser");t.exports={buildRequest:o,extractError:n,extractData:i}},{"../json/builder":13,"../json/parser":14,"../util":51,"./json":22,"./rest":24}],26:[function(e,t,r){function a(e){var t=e.service.api.operations[e.operation].input,r=new s.XML.Builder,a=e.params,o=t.payload;if(o){var n=t.members[o];if(a=a[o],void 0===a)return;if("structure"===n.type){var i=n.name;e.httpRequest.body=r.toXML(a,n,i,!0)}else e.httpRequest.body=a}else e.httpRequest.body=r.toXML(a,t,t.name||t.shape||u.string.upperFirst(e.operation)+"Request")}function o(e){c.buildRequest(e),["GET","HEAD"].indexOf(e.httpRequest.method)<0&&a(e)}function n(e){c.extractError(e);var t=(new s.XML.Parser).parse(e.httpResponse.body.toString());t.Errors&&(t=t.Errors),t.Error&&(t=t.Error),t.Code?e.error=u.error(new Error,{code:t.Code,message:t.Message}):e.error=u.error(new Error,{code:e.httpResponse.statusCode,message:null})}function i(e){c.extractData(e);var t,r=e.request,a=e.httpResponse.body,o=r.service.api.operations[r.operation],n=o.output,i=n.payload;if(i){var p=n.members[i];p.isStreaming?e.data[i]=a:"structure"===p.type?(t=new s.XML.Parser,e.data[i]=t.parse(a.toString(),p)):e.data[i]=a.toString()}else if(a.length>0){t=new s.XML.Parser;var m=t.parse(a.toString(),n);u.update(e.data,m)}}var s=e("../core"),u=e("../util"),c=e("./rest");t.exports={buildRequest:o,extractError:n,extractData:i}},{"../core":3,"../util":51,"./rest":24}],27:[function(e,t,r){function a(){}function o(e){return e.isQueryName||"ec2"!==e.api.protocol?e.name:e.name[0].toUpperCase()+e.name.substr(1)}function n(e,t,r,a){c.each(r.members,function(r,n){var i=t[r];if(null!==i&&void 0!==i){var s=o(n);s=e?e+"."+s:s,u(s,i,n,a)}})}function i(e,t,r,a){var o=1;c.each(t,function(t,n){var i=r.flattened?".":".entry.",s=i+o++ +".",c=s+(r.key.name||"key"),p=s+(r.value.name||"value");u(e+c,t,r.key,a),u(e+p,n,r.value,a)})}function s(e,t,r,a){var n=r.member||{};return 0===t.length?void a.call(this,e,null):void c.arrayEach(t,function(t,i){var s="."+(i+1);if("ec2"===r.api.protocol)s+="";else if(r.flattened){if(n.name){var c=e.split(".");c.pop(),c.push(o(n)),e=c.join(".")}}else s=".member"+s;u(e+s,t,n,a)})}function u(e,t,r,a){null!==t&&void 0!==t&&("structure"===r.type?n(e,t,r,a):"list"===r.type?s(e,t,r,a):"map"===r.type?i(e,t,r,a):a(e,r.toWireFormat(t).toString()))}var c=e("../util");a.prototype.serialize=function(e,t,r){n("",e,t,r)},t.exports=a},{"../util":51}],28:[function(e,t,r){function a(e){if(!e)return null;var t=e.split("-");return t.length<3?null:t.slice(0,t.length-2).join("-")+"-*"}function o(e){var t=e.config.region,r=a(t),o=e.api.endpointPrefix;return[[t,o],[r,o],[t,"*"],[r,"*"],["*",o],["*","*"]].map(function(e){return e[0]&&e[1]?e.join("/"):null})}function n(e,t){s.each(t,function(t,r){"globalEndpoint"!==t&&(void 0===e.config[t]||null===e.config[t])&&(e.config[t]=r)})}function i(e){for(var t=o(e),r=0;r<t.length;r++){var a=t[r];if(a&&u.rules.hasOwnProperty(a)){var i=u.rules[a];return"string"==typeof i&&(i=u.patterns[i]),e.isGlobalEndpoint=!!i.globalEndpoint,i.signatureVersion||(i.signatureVersion="v4"),void n(e,i)}}}var s=e("./util"),u=e("./region_config.json");t.exports=i},{"./region_config.json":29,"./util":51}],29:[function(e,t,r){t.exports={rules:{"*/*":{endpoint:"{service}.{region}.amazonaws.com"},"cn-*/*":{endpoint:"{service}.{region}.amazonaws.com.cn"},"*/cloudfront":"globalSSL","*/iam":"globalSSL","*/sts":"globalSSL","*/importexport":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2",globalEndpoint:!0},"*/route53":{endpoint:"https://{service}.amazonaws.com",signatureVersion:"v3https",globalEndpoint:!0},"us-gov-*/iam":"globalGovCloud","us-gov-*/sts":{endpoint:"{service}.{region}.amazonaws.com"},"us-gov-west-1/s3":"s3dash","us-west-1/s3":"s3dash","us-west-2/s3":"s3dash","eu-west-1/s3":"s3dash","ap-southeast-1/s3":"s3dash","ap-southeast-2/s3":"s3dash","ap-northeast-1/s3":"s3dash","sa-east-1/s3":"s3dash","us-east-1/s3":{endpoint:"{service}.amazonaws.com",signatureVersion:"s3"},"us-east-1/sdb":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2"},"*/sdb":{endpoint:"{service}.{region}.amazonaws.com",signatureVersion:"v2"}},patterns:{globalSSL:{endpoint:"https://{service}.amazonaws.com",globalEndpoint:!0},globalGovCloud:{endpoint:"{service}.us-gov.amazonaws.com"},s3dash:{endpoint:"{service}-{region}.amazonaws.com",signatureVersion:"s3"}}}},{}],30:[function(e,t,r){(function(t){function r(e){return s.hasOwnProperty(e._asm.currentState)}var a=e("./core"),o=e("./state_machine"),n=a.util.inherit,i=a.util.nodeRequire("domain"),s={success:1,error:1,complete:1},u=new o;u.setupStates=function(){var e=function(e,t){var a=this;a._haltHandlersOnError=!1,a.emit(a._asm.currentState,function(e){if(e)if(r(a)){if(!(i&&a.domain instanceof i.Domain))throw e;e.domainEmitter=a,e.domain=a.domain,e.domainThrown=!1,a.domain.emit("error",e)}else a.response.error=e,t(e);else t(a.response.error)})};this.addState("validate","build","error",e),this.addState("build","afterBuild","restart",e),this.addState("afterBuild","sign","restart",e),this.addState("sign","send","retry",e),this.addState("retry","afterRetry","afterRetry",e),this.addState("afterRetry","sign","error",e),this.addState("send","validateResponse","retry",e),this.addState("validateResponse","extractData","extractError",e),this.addState("extractError","extractData","retry",e),this.addState("extractData","success","retry",e),this.addState("restart","build","error",e),this.addState("success","complete","complete",e),this.addState("error","complete","complete",e),this.addState("complete",null,null,e)},u.setupStates(),a.Request=n({constructor:function(e,t,r){var n=e.endpoint,s=e.config.region;e.isGlobalEndpoint&&(s="us-east-1"),this.domain=i&&i.active,this.service=e,this.operation=t,this.params=r||{},this.httpRequest=new a.HttpRequest(n,s),
this.startTime=a.util.date.getDate(),this.response=new a.Response(this),this._asm=new o(u.states,"validate"),this._haltHandlersOnError=!1,a.SequentialExecutor.call(this),this.emit=this.emitEvent},send:function(e){return e&&this.on("complete",function(t){e.call(t,t.error,t.data)}),this.runTo(),this.response},build:function(e){return this.runTo("send",e)},runTo:function(e,t){return this._asm.runTo(e,t,this),this},abort:function(){return this.removeAllListeners("validateResponse"),this.removeAllListeners("extractError"),this.on("validateResponse",function(e){e.error=a.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:!1})}),this.httpRequest.stream&&(this.httpRequest.stream.abort(),this.httpRequest._abortCallback?this.httpRequest._abortCallback():this.removeAllListeners("send")),this},eachPage:function(e){function t(r){e.call(r,r.error,r.data,function(o){o!==!1&&(r.hasNextPage()?r.nextPage().on("complete",t).send():e.call(r,null,null,a.util.fn.noop))})}e=a.util.fn.makeAsync(e,3),this.on("complete",t).send()},eachItem:function(e){function t(t,o){if(t)return e(t,null);if(null===o)return e(null,null);var n=r.service.paginationConfig(r.operation),i=n.resultKey;Array.isArray(i)&&(i=i[0]);var s=a.util.jamespath.query(i,o);a.util.arrayEach(s,function(t){a.util.arrayEach(t,function(t){e(null,t)})})}var r=this;this.eachPage(t)},isPageable:function(){return this.service.paginationConfig(this.operation)?!0:!1},createReadStream:function(){var e=a.util.nodeRequire("stream"),r=this,o=null;return 2===a.HttpClient.streamsApiVersion?(o=new e.PassThrough,r.send()):(o=new e.Stream,o.readable=!0,o.sent=!1,o.on("newListener",function(e){o.sent||"data"!==e||(o.sent=!0,t.nextTick(function(){r.send()}))})),this.on("httpHeaders",function(e,t,n){if(300>e){r.removeListener("httpData",a.EventListeners.Core.HTTP_DATA),r.removeListener("httpError",a.EventListeners.Core.HTTP_ERROR),r.on("httpError",function(e){n.error=e,n.error.retryable=!1});var i=n.httpResponse.createUnbufferedStream();2===a.HttpClient.streamsApiVersion?i.pipe(o):(i.on("data",function(e){o.emit("data",e)}),i.on("end",function(){o.emit("end")})),i.on("error",function(e){o.emit("error",e)})}}),this.on("error",function(e){o.emit("error",e)}),o},emitEvent:function(e,t,r){"function"==typeof t&&(r=t,t=null),r||(r=function(){}),t||(t=this.eventParameters(e,this.response));var o=a.SequentialExecutor.prototype.emit;o.call(this,e,t,function(e){e&&(this.response.error=e),r.call(this,e)})},eventParameters:function(e){switch(e){case"restart":case"validate":case"sign":case"build":case"afterValidate":case"afterBuild":return[this];case"error":return[this.response.error,this.response];default:return[this.response]}},presign:function(e,t){return t||"function"!=typeof e||(t=e,e=null),(new a.Signers.Presign).sign(this.toGet(),e,t)},toUnauthenticated:function(){return this.removeListener("validate",a.EventListeners.Core.VALIDATE_CREDENTIALS),this.removeListener("sign",a.EventListeners.Core.SIGN),this.toGet()},toGet:function(){return("query"===this.service.api.protocol||"ec2"===this.service.api.protocol)&&(this.removeListener("build",this.buildAsGet),this.addListener("build",this.buildAsGet)),this},buildAsGet:function(e){e.httpRequest.method="GET",e.httpRequest.path=e.service.endpoint.path+"?"+e.httpRequest.body,e.httpRequest.body="",delete e.httpRequest.headers["Content-Length"],delete e.httpRequest.headers["Content-Type"]},haltHandlersOnError:function(){this._haltHandlersOnError=!0}}),a.util.mixin(a.Request,a.SequentialExecutor)}).call(this,e("FWaASH"))},{"./core":3,"./state_machine":50,FWaASH:65}],31:[function(e,t,r){var a=e("./core"),o=a.util.inherit;a.ResourceWaiter=o({constructor:function(e,t){this.service=e,this.state=t,"object"==typeof this.state&&a.util.each.call(this,this.state,function(e,t){this.state=e,this.expectedValue=t}),this.loadWaiterConfig(this.state),this.expectedValue||(this.expectedValue=this.config.successValue)},service:null,state:null,expectedValue:null,config:null,waitDone:!1,Listeners:{retry:(new a.SequentialExecutor).addNamedListeners(function(e){e("RETRY_CHECK","retry",function(e){var t=e.request._waiter;e.error&&"ResourceNotReady"===e.error.code&&(e.error.retryDelay=1e3*t.config.interval)})}),output:(new a.SequentialExecutor).addNamedListeners(function(e){e("CHECK_OUT_ERROR","extractError",function(e){e.error&&e.request._waiter.setError(e,!0)}),e("CHECK_OUTPUT","extractData",function(e){var t=e.request._waiter,r=t.checkSuccess(e);r?e.error=null:t.setError(e,null===r?!1:!0)})}),error:(new a.SequentialExecutor).addNamedListeners(function(e){e("CHECK_ERROR","extractError",function(e){var t=e.request._waiter,r=t.checkError(e);r?(e.error=null,e.data={},e.request.removeAllListeners("extractData")):t.setError(e,null===r?!1:!0)}),e("CHECK_ERR_OUTPUT","extractData",function(e){e.request._waiter.setError(e,!0)})})},wait:function(e,t){"function"==typeof e&&(t=e,e=void 0);var r=this.service.makeRequest(this.config.operation,e),a=this.Listeners[this.config.successType];return r._waiter=this,r.response.maxRetries=this.config.maxAttempts,r.addListeners(this.Listeners.retry),a&&r.addListeners(a),t&&r.send(t),r},setError:function(e,t){e.data=null,e.error=a.util.error(e.error||new Error,{code:"ResourceNotReady",message:"Resource is not in the state "+this.state,retryable:t})},checkSuccess:function(e){if(!this.config.successPath)return e.httpResponse.statusCode<300;var t=a.util.jamespath.find(this.config.successPath,e.data);return this.config.failureValue&&this.config.failureValue.indexOf(t)>=0?null:this.expectedValue?t===this.expectedValue:t?!0:!1},checkError:function(e){var t=this.config.successValue;return"number"==typeof t?e.httpResponse.statusCode===t:e.error&&e.error.code===t},loadWaiterConfig:function(e,t){if(!this.service.api.waiters[e]){if(t)return;throw new a.util.error(new Error,{code:"StateNotFoundError",message:"State "+e+" not found."})}this.config=this.service.api.waiters[e];var r=this.config;!function(){r.successType=r.successType||r.acceptorType,r.successPath=r.successPath||r.acceptorPath,r.successValue=r.successValue||r.acceptorValue,r.failureType=r.failureType||r.acceptorType,r.failurePath=r.failurePath||r.acceptorPath,r.failureValue=r.failureValue||r.acceptorValue}()}})},{"./core":3}],32:[function(e,t,r){var a=e("./core"),o=a.util.inherit;a.Response=o({constructor:function(e){this.request=e,this.data=null,this.error=null,this.retryCount=0,this.redirectCount=0,this.httpResponse=new a.HttpResponse,e&&(this.maxRetries=e.service.numRetries(),this.maxRedirects=e.service.config.maxRedirects)},nextPage:function(e){var t,r=this.request.service,o=this.request.operation;try{t=r.paginationConfig(o,!0)}catch(n){this.error=n}if(!this.hasNextPage()){if(e)e(this.error,null);else if(this.error)throw this.error;return null}var i=a.util.copy(this.request.params);if(this.nextPageTokens){var s=t.inputToken;"string"==typeof s&&(s=[s]);for(var u=0;u<s.length;u++)i[s[u]]=this.nextPageTokens[u];return r.makeRequest(this.request.operation,i,e)}return e?e(null,null):null},hasNextPage:function(){return this.cacheNextPageTokens(),this.nextPageTokens?!0:void 0===this.nextPageTokens?void 0:!1},cacheNextPageTokens:function(){if(this.hasOwnProperty("nextPageTokens"))return this.nextPageTokens;this.nextPageTokens=void 0;var e=this.request.service.paginationConfig(this.request.operation);if(!e)return this.nextPageTokens;if(this.nextPageTokens=null,e.moreResults&&!a.util.jamespath.find(e.moreResults,this.data))return this.nextPageTokens;var t=e.outputToken;return"string"==typeof t&&(t=[t]),a.util.arrayEach.call(this,t,function(e){var t=a.util.jamespath.find(e,this.data);t&&(this.nextPageTokens=this.nextPageTokens||[],this.nextPageTokens.push(t))}),this.nextPageTokens}})},{"./core":3}],33:[function(e,t,r){(function(r){var a=e("../core"),o=a.util.string.byteLength;a.S3.ManagedUpload=a.util.inherit({constructor:function(e){var t=this;a.SequentialExecutor.call(t),t.body=null,t.sliceFn=null,t.callback=null,t.parts={},t.completeInfo=[],t.fillQueue=function(){t.callback(new Error("Unsupported body payload "+typeof t.body))},t.configure(e)},configure:function(e){if(e=e||{},this.partSize=this.minPartSize,e.queueSize&&(this.queueSize=e.queueSize),e.partSize&&(this.partSize=e.partSize),e.leavePartsOnError&&(this.leavePartsOnError=!0),this.partSize<this.minPartSize)throw new Error("partSize must be greater than "+this.minPartSize);this.service=e.service,this.bindServiceObject(e.params),this.validateBody(),this.adjustTotalBytes()},leavePartsOnError:!1,queueSize:4,partSize:null,minPartSize:5242880,maxTotalParts:1e4,send:function(e){var t=this;t.callback=e||function(e){if(e)throw e};var r=!0;if(t.sliceFn)t.fillQueue=t.fillBuffer;else if(a.util.isNode()){var o=a.util.nodeRequire("stream").Stream;t.body instanceof o&&(r=!1,t.fillQueue=t.fillStream,t.partBuffers=[],t.body.on("readable",function(){t.fillQueue()}).on("end",function(){t.isDoneChunking=!0,t.numParts=t.totalPartNumbers,t.fillQueue.call(t)}))}r&&t.fillQueue.call(t)},abort:function(){this.cleanup(a.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:!1}))},validateBody:function(){var e=this;if(e.body=e.service.config.params.Body,!e.body)throw new Error("params.Body is required");"string"==typeof e.body&&(e.body=new a.util.Buffer(e.body)),e.sliceFn=a.util.arraySliceFn(e.body)},bindServiceObject:function(e){e=e||{};var t=this;if(t.service){var r=a.util.copy(t.service.config);t.service=new t.service.constructor.__super__(r),t.service.config.params=a.util.merge(t.service.config.params||{},e)}else t.service=new a.S3({params:e})},adjustTotalBytes:function(){var e=this;try{e.totalBytes=o(e.body)}catch(t){}if(e.totalBytes){var r=Math.ceil(e.totalBytes/e.maxTotalParts);r>e.partSize&&(e.partSize=r)}else e.totalBytes=void 0},isDoneChunking:!1,partPos:0,totalChunkedBytes:0,totalUploadedBytes:0,totalBytes:void 0,numParts:0,totalPartNumbers:0,activeParts:0,doneParts:0,parts:null,completeInfo:null,failed:!1,multipartReq:null,partBuffers:null,partBufferLength:0,fillBuffer:function(){var e=this,t=o(e.body);if(0===t)return e.isDoneChunking=!0,e.numParts=1,void e.nextChunk(e.body);for(;e.activeParts<e.queueSize&&e.partPos<t;){var r=Math.min(e.partPos+e.partSize,t),a=e.sliceFn.call(e.body,e.partPos,r);e.partPos+=e.partSize,(o(a)<e.partSize||e.partPos===t)&&(e.isDoneChunking=!0,e.numParts=e.totalPartNumbers+1),e.nextChunk(a)}},fillStream:function(){var e=this;if(!(e.activeParts>=e.queueSize)){var t=e.body.read(e.partSize-e.partBufferLength)||e.body.read();if(t&&(e.partBuffers.push(t),e.partBufferLength+=t.length,e.totalChunkedBytes+=t.length),e.partBufferLength>=e.partSize){var a=r.concat(e.partBuffers);if(e.partBuffers=[],e.partBufferLength=0,a.length>e.partSize){var o=a.slice(e.partSize);e.partBuffers.push(o),e.partBufferLength+=o.length,a=a.slice(0,e.partSize)}e.nextChunk(a)}e.isDoneChunking&&!e.isDoneSending&&(a=r.concat(e.partBuffers),e.partBuffers=[],e.partBufferLength=0,e.totalBytes=e.totalChunkedBytes,e.isDoneSending=!0,(0===e.numParts||a.length>0)&&(e.numParts++,e.nextChunk(a))),e.body.read(0)}},nextChunk:function(e){var t=this;if(t.failed)return null;var r=++t.totalPartNumbers;if(t.isDoneChunking&&1===r){var a=t.service.putObject({Body:e});return a._managedUpload=t,a.on("httpUploadProgress",t.progress).send(t.finishSinglePart),null}t.activeParts++,t.service.config.params.UploadId?t.uploadPart(e,r):t.multipartReq?t.queueChunks(e,r):(t.multipartReq=t.service.createMultipartUpload(),t.multipartReq.on("success",function(e){t.service.config.params.UploadId=e.data.UploadId,t.multipartReq=null}),t.queueChunks(e,r),t.multipartReq.on("error",function(e){t.cleanup(e)}),t.multipartReq.send())},uploadPart:function(e,t){var r=this,o={Body:e,ContentLength:a.util.string.byteLength(e),PartNumber:t},n={ETag:null,PartNumber:t};r.completeInfo.push(n);var i=r.service.uploadPart(o);r.parts[t]=i,i._lastUploadedBytes=0,i._managedUpload=r,i.on("httpUploadProgress",r.progress),i.send(function(e,t){if(delete r.parts[o.PartNumber],r.activeParts--,!(e||t&&t.ETag)){var i="No access to ETag property on response.";a.util.isBrowser()&&(i+=" Check CORS configuration to expose ETag header."),e=a.util.error(new Error(i),{code:"ETagMissing",retryable:!1})}return e?r.cleanup(e):(n.ETag=t.ETag,r.doneParts++,void(r.isDoneChunking&&r.doneParts===r.numParts?r.finishMultiPart():r.fillQueue.call(r)))})},queueChunks:function(e,t){var r=this;r.multipartReq.on("success",function(){r.uploadPart(e,t)})},cleanup:function(e){var t=this;t.failed||("function"==typeof t.body.removeAllListeners&&"function"==typeof t.body.resume&&(t.body.removeAllListeners("readable"),t.body.removeAllListeners("end"),t.body.resume()),t.service.config.params.UploadId&&!t.leavePartsOnError&&t.service.abortMultipartUpload().send(),a.util.each(t.parts,function(e,t){t.removeAllListeners("complete"),t.abort()}),t.parts={},t.callback(e),t.failed=!0)},finishMultiPart:function(){var e=this,t={MultipartUpload:{Parts:e.completeInfo}};e.service.completeMultipartUpload(t,function(t,r){return t?e.cleanup(t):void e.callback(t,r)})},finishSinglePart:function(e,t){var r=this.request._managedUpload,o=this.request.httpRequest,n=a.util.urlFormat(o.endpoint);return e?r.callback(e):(t.Location=n.substr(0,n.length-1)+o.path,void r.callback(e,t))},progress:function(e){var t=this._managedUpload;"putObject"===this.operation?e.part=1:(t.totalUploadedBytes+=e.loaded-this._lastUploadedBytes,this._lastUploadedBytes=e.loaded,e={loaded:t.totalUploadedBytes,total:t.totalBytes,part:this.params.PartNumber}),t.emit("httpUploadProgress",[e])}}),a.util.mixin(a.S3.ManagedUpload,a.SequentialExecutor),t.exports=a.S3.ManagedUpload}).call(this,e("buffer").Buffer)},{"../core":3,buffer:54}],34:[function(e,t,r){var a=e("./core");a.SequentialExecutor=a.util.inherit({constructor:function(){this._events={}},listeners:function(e){return this._events[e]?this._events[e].slice(0):[]},on:function(e,t){return this._events[e]?this._events[e].push(t):this._events[e]=[t],this},onAsync:function(e,t){return t._isAsync=!0,this.on(e,t)},removeListener:function(e,t){var r=this._events[e];if(r){for(var a=r.length,o=-1,n=0;a>n;++n)r[n]===t&&(o=n);o>-1&&r.splice(o,1)}return this},removeAllListeners:function(e){return e?delete this._events[e]:this._events={},this},emit:function(e,t,r){r||(r=function(){});var a=this.listeners(e),o=a.length;return this.callListeners(a,t,r),o>0},callListeners:function(e,t,r,o){function n(o){return o&&(s=a.util.error(s||new Error,o),i._haltHandlersOnError)?r.call(i,s):void i.callListeners(e,t,r,s)}for(var i=this,s=o||null;e.length>0;){var u=e.shift();if(u._isAsync)return void u.apply(i,t.concat([n]));try{u.apply(i,t)}catch(c){s=a.util.error(s||new Error,c)}if(s&&i._haltHandlersOnError)return void r.call(i,s)}r.call(i,s)},addListeners:function(e){var t=this;return e._events&&(e=e._events),a.util.each(e,function(e,r){"function"==typeof r&&(r=[r]),a.util.arrayEach(r,function(r){t.on(e,r)})}),t},addNamedListener:function(e,t,r){return this[e]=r,this.addListener(t,r),this},addNamedAsyncListener:function(e,t,r){return r._isAsync=!0,this.addNamedListener(e,t,r)},addNamedListeners:function(e){var t=this;return e(function(){t.addNamedListener.apply(t,arguments)},function(){t.addNamedAsyncListener.apply(t,arguments)}),this}}),a.SequentialExecutor.prototype.addListener=a.SequentialExecutor.prototype.on,t.exports=a.SequentialExecutor},{"./core":3}],35:[function(e,t,r){var a=e("./core"),o=e("./model/api"),n=e("./region_config"),i=a.util.inherit;a.Service=i({constructor:function(e){if(!this.loadServiceClass)throw a.util.error(new Error,"Service must be constructed with `new' operator");var t=this.loadServiceClass(e||{});return t?new t(e):void this.initialize(e)},initialize:function(e){var t=a.config[this.serviceIdentifier];this.config=new a.Config(a.config),t&&this.config.update(t,!0),e&&this.config.update(e,!0),this.validateService(),this.config.endpoint||n(this),this.config.endpoint=this.endpointFromTemplate(this.config.endpoint),this.setEndpoint(this.config.endpoint)},validateService:function(){},loadServiceClass:function(e){var t=e;if(a.util.isEmpty(this.api)){if(t.apiConfig)return a.Service.defineServiceApi(this.constructor,t.apiConfig);if(this.constructor.services){t=new a.Config(a.config),t.update(e,!0);var r=t.apiVersions[this.constructor.serviceIdentifier];return r=r||t.apiVersion,this.getLatestServiceClass(r)}return null}return null},getLatestServiceClass:function(e){return e=this.getLatestServiceVersion(e),null===this.constructor.services[e]&&a.Service.defineServiceApi(this.constructor,e),this.constructor.services[e]},getLatestServiceVersion:function(e){if(!this.constructor.services||0===this.constructor.services.length)throw new Error("No services defined on "+this.constructor.serviceIdentifier);if(e?a.util.isType(e,Date)&&(e=a.util.date.iso8601(e).split("T")[0]):e="latest",Object.hasOwnProperty(this.constructor.services,e))return e;for(var t=Object.keys(this.constructor.services).sort(),r=null,o=t.length-1;o>=0;o--)if("*"!==t[o][t[o].length-1]&&(r=t[o]),t[o].substr(0,10)<=e)return r;throw new Error("Could not find "+this.constructor.serviceIdentifier+" API to satisfy version constraint `"+e+"'")},api:{},defaultRetryCount:3,makeRequest:function(e,t,r){if("function"==typeof t&&(r=t,t=null),t=t||{},this.config.params){var o=this.api.operations[e];o&&(t=a.util.copy(t),a.util.each(this.config.params,function(e,r){o.input.members[e]&&(void 0===t[e]||null===t[e])&&(t[e]=r)}))}var n=new a.Request(this,e,t);return this.addAllRequestListeners(n),r&&n.send(r),n},makeUnauthenticatedRequest:function(e,t,r){"function"==typeof t&&(r=t,t={});var a=this.makeRequest(e,t).toUnauthenticated();return r?a.send(r):a},waitFor:function(e,t,r){var o=new a.ResourceWaiter(this,e);return o.wait(t,r)},addAllRequestListeners:function(e){for(var t=[a.events,a.EventListeners.Core,this.serviceInterface(),a.EventListeners.CorePost],r=0;r<t.length;r++)t[r]&&e.addListeners(t[r]);this.config.paramValidation||e.removeListener("validate",a.EventListeners.Core.VALIDATE_PARAMETERS),this.config.logger&&e.addListeners(a.EventListeners.Logger),this.setupRequestListeners(e)},setupRequestListeners:function(){},getSignerClass:function(){var e;return e=this.config.signatureVersion?this.config.signatureVersion:this.api.signatureVersion,a.Signers.RequestSigner.getVersion(e)},serviceInterface:function(){switch(this.api.protocol){case"ec2":return a.EventListeners.Query;case"query":return a.EventListeners.Query;case"json":return a.EventListeners.Json;case"rest-json":return a.EventListeners.RestJson;case"rest-xml":return a.EventListeners.RestXml}if(this.api.protocol)throw new Error("Invalid service `protocol' "+this.api.protocol+" in API config")},successfulResponse:function(e){return e.httpResponse.statusCode<300},numRetries:function(){return void 0!==this.config.maxRetries?this.config.maxRetries:this.defaultRetryCount},retryDelays:function(){for(var e=this.numRetries(),t=[],r=0;e>r;++r)t[r]=30*Math.pow(2,r);return t},retryableError:function(e){return this.networkingError(e)?!0:this.expiredCredentialsError(e)?!0:this.throttledError(e)?!0:e.statusCode>=500?!0:!1},networkingError:function(e){return"NetworkingError"===e.code},expiredCredentialsError:function(e){return"ExpiredTokenException"===e.code},throttledError:function(e){switch(e.code){case"ProvisionedThroughputExceededException":case"Throttling":case"ThrottlingException":case"RequestLimitExceeded":case"RequestThrottled":return!0;default:return!1}},endpointFromTemplate:function(e){if("string"!=typeof e)return e;var t=e;return t=t.replace(/\{service\}/g,this.api.endpointPrefix),t=t.replace(/\{region\}/g,this.config.region),t=t.replace(/\{scheme\}/g,this.config.sslEnabled?"https":"http")},setEndpoint:function(e){this.endpoint=new a.Endpoint(e,this.config)},paginationConfig:function(e,t){var r=this.api.operations[e].paginator;if(!r){if(t){var o=new Error;throw a.util.error(o,"No pagination configuration for "+e)}return null}return r}}),a.util.update(a.Service,{defineMethods:function(e){a.util.each(e.prototype.api.operations,function(t){e.prototype[t]||(e.prototype[t]=function(e,r){return this.makeRequest(t,e,r)})})},defineService:function(e,t,r){a.Service._serviceMap[e]=!0,Array.isArray(t)||(r=t,t=[]);var o=i(a.Service,r||{});if("string"==typeof e){a.Service.addVersions(o,t);var n=o.serviceIdentifier||e;o.serviceIdentifier=n}else o.prototype.api=e,a.Service.defineMethods(o);return o},addVersions:function(e,t){Array.isArray(t)||(t=[t]),e.services=e.services||{};for(var r=0;r<t.length;r++)void 0===e.services[t[r]]&&(e.services[t[r]]=null);e.apiVersions=Object.keys(e.services).sort()},defineServiceApi:function(e,t,r){function n(e){e.isApi?s.prototype.api=e:s.prototype.api=new o(e)}var s=i(e,{serviceIdentifier:e.serviceIdentifier});if("string"==typeof t){if(r)n(r);else try{n(a.apiLoader(e.serviceIdentifier,t))}catch(u){throw a.util.error(u,{message:"Could not find API configuration "+e.serviceIdentifier+"-"+t})}e.services.hasOwnProperty(t)||(e.apiVersions=e.apiVersions.concat(t).sort()),e.services[t]=s}else n(t);return a.Service.defineMethods(s),s},hasService:function(e){return a.Service._serviceMap.hasOwnProperty(e)},_serviceMap:{}})},{"./core":3,"./model/api":15,"./region_config":28}],36:[function(e,t,r){var a=e("../core");a.util.update(a.CognitoIdentity.prototype,{getOpenIdToken:function(e,t){return this.makeUnauthenticatedRequest("getOpenIdToken",e,t)},getId:function(e,t){return this.makeUnauthenticatedRequest("getId",e,t)},getCredentialsForIdentity:function(e,t){return this.makeUnauthenticatedRequest("getCredentialsForIdentity",e,t)}})},{"../core":3}],37:[function(e,t,r){var a=e("../core");a.util.update(a.DynamoDB.prototype,{setupRequestListeners:function(e){e.service.config.dynamoDbCrc32&&e.addListener("extractData",this.checkCrc32)},checkCrc32:function(e){e.httpResponse.streaming||e.request.service.crc32IsValid(e)||(e.error=a.util.error(new Error,{code:"CRC32CheckFailed",message:"CRC32 integrity check failed",retryable:!0}))},crc32IsValid:function(e){var t=e.httpResponse.headers["x-amz-crc32"];return t?parseInt(t,10)===a.util.crypto.crc32(e.httpResponse.body):!0},defaultRetryCount:10,retryDelays:function(){for(var e=this.numRetries(),t=[],r=0;e>r;++r)0===r?t.push(0):t.push(50*Math.pow(2,r-1));return t}})},{"../core":3}],38:[function(e,t,r){var a=e("../core");a.util.update(a.EC2.prototype,{setupRequestListeners:function(e){e.removeListener("extractError",a.EventListeners.Query.EXTRACT_ERROR),e.addListener("extractError",this.extractError),"copySnapshot"===e.operation&&e.onAsync("validate",this.buildCopySnapshotPresignedUrl)},buildCopySnapshotPresignedUrl:function(e,t){if(e.params.PresignedUrl||e._subRequest)return t();e.params=a.util.copy(e.params),e.params.DestinationRegion=e.service.config.region;var r=a.util.copy(e.service.config);delete r.endpoint,r.region=e.params.SourceRegion;var o=new e.service.constructor(r),n=o[e.operation](e.params);n._subRequest=!0,n.presign(function(r,a){r?t(r):(e.params.PresignedUrl=a,t())})},extractError:function(e){var t=e.httpResponse,r=(new a.XML.Parser).parse(t.body.toString()||"");r.Errors?e.error=a.util.error(new Error,{code:r.Errors.Error.Code,message:r.Errors.Error.Message}):e.error=a.util.error(new Error,{code:t.statusCode,message:null})}})},{"../core":3}],39:[function(e,t,r){var a=e("../core");a.util.update(a.MachineLearning.prototype,{setupRequestListeners:function(e){"predict"===e.operation&&e.addListener("build",this.buildEndpoint)},buildEndpoint:function(e){var t=e.params.PredictEndpoint;t&&(e.httpRequest.endpoint=new a.Endpoint(t))}})},{"../core":3}],40:[function(e,t,r){var a=e("../core");e("../s3/managed_upload"),a.util.update(a.S3.prototype,{validateService:function(){if(this.config.region||(this.config.region="us-east-1"),!this.config.endpoint&&this.config.s3BucketEndpoint){var e="An endpoint must be provided when configuring `s3BucketEndpoint` to true.";throw a.util.error(new Error,{name:"InvalidEndpoint",message:e})}},setupRequestListeners:function(e){e.addListener("validate",this.validateScheme),e.addListener("validate",this.validateBucketEndpoint),e.addListener("build",this.addContentType),e.addListener("build",this.populateURI),e.addListener("build",this.computeContentMd5),e.addListener("build",this.computeSseCustomerKeyMd5),e.addListener("afterBuild",this.addExpect100Continue),e.removeListener("validate",a.EventListeners.Core.VALIDATE_REGION),e.addListener("extractError",this.extractError),e.addListener("extractData",this.extractData),e.addListener("extractData",a.util.hoistPayloadMember),e.addListener("beforePresign",this.prepareSignedUrl)},validateScheme:function(e){var t=e.params,r=e.httpRequest.endpoint.protocol,o=t.SSECustomerKey||t.CopySourceSSECustomerKey;if(o&&"https:"!==r){var n="Cannot send SSE keys over HTTP. Set 'sslEnabled'to 'true' in your configuration";throw a.util.error(new Error,{code:"ConfigError",message:n})}},validateBucketEndpoint:function(e){if(!e.params.Bucket&&e.service.config.s3BucketEndpoint){var t="Cannot send requests to root API with `s3BucketEndpoint` set.";throw a.util.error(new Error,{code:"ConfigError",message:t})}},populateURI:function(e){var t=e.httpRequest,r=e.params.Bucket;if(r&&!e.service.pathStyleBucketName(r)){if(!e.service.config.s3BucketEndpoint){t.endpoint.hostname=r+"."+t.endpoint.hostname;var a=t.endpoint.port;80!==a&&443!==a?t.endpoint.host=t.endpoint.hostname+":"+t.endpoint.port:t.endpoint.host=t.endpoint.hostname}t.virtualHostedBucket=r,t.path=t.path.replace(new RegExp("/"+r),""),"/"!==t.path[0]&&(t.path="/"+t.path)}},addExpect100Continue:function(e){var t=e.httpRequest.headers["Content-Length"];a.util.isNode()&&t>=1048576&&(e.httpRequest.headers.Expect="100-continue")},addContentType:function(e){var t=e.httpRequest;if("GET"===t.method||"HEAD"===t.method)return void delete t.headers["Content-Type"];t.headers["Content-Type"]||(t.headers["Content-Type"]="application/octet-stream");var r=t.headers["Content-Type"];if(a.util.isBrowser())if("string"!=typeof t.body||r.match(/;\s*charset=/)){var o=function(e,t,r){return t+r.toUpperCase()};t.headers["Content-Type"]=r.replace(/(;\s*charset=)(.+)$/,o)}else{var n="; charset=UTF-8";t.headers["Content-Type"]+=n}},computableChecksumOperations:{putBucketCors:!0,putBucketLifecycle:!0,putBucketTagging:!0,deleteObjects:!0},willComputeChecksums:function(e){if(this.computableChecksumOperations[e.operation])return!0;if(!this.config.computeChecksums)return!1;if(!a.util.Buffer.isBuffer(e.httpRequest.body)&&"string"!=typeof e.httpRequest.body)return!1;var t=e.service.api.operations[e.operation].input.members;return e.service.getSignerClass(e)===a.Signers.V4&&t.ContentMD5&&!t.ContentMD5.required?!1:t.ContentMD5&&!e.params.ContentMD5?!0:void 0},computeContentMd5:function(e){if(e.service.willComputeChecksums(e)){var t=a.util.crypto.md5(e.httpRequest.body,"base64");e.httpRequest.headers["Content-MD5"]=t}},computeSseCustomerKeyMd5:function(e){var t={SSECustomerKey:"x-amz-server-side-encryption-customer-key-MD5",CopySourceSSECustomerKey:"x-amz-copy-source-server-side-encryption-customer-key-MD5"};a.util.each(t,function(t,r){if(e.params[t]){var o=a.util.crypto.md5(e.params[t],"base64");e.httpRequest.headers[r]=o}})},pathStyleBucketName:function(e){return this.config.s3ForcePathStyle?!0:this.config.s3BucketEndpoint?!1:this.dnsCompatibleBucketName(e)?this.config.sslEnabled&&e.match(/\./)?!0:!1:!0},dnsCompatibleBucketName:function(e){var t=e,r=new RegExp(/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/),a=new RegExp(/(\d+\.){3}\d+/),o=new RegExp(/\.\./);return!t.match(r)||t.match(a)||t.match(o)?!1:!0},successfulResponse:function(e){var t=e.request,r=e.httpResponse;return"completeMultipartUpload"===t.operation&&r.body.toString().match("<Error>")?!1:r.statusCode<300},retryableError:function(e,t){if("completeMultipartUpload"===t.operation&&200===e.statusCode)return!0;if(e&&"RequestTimeout"===e.code)return!0;var r=a.Service.prototype.retryableError;return r.call(this,e,t)},extractData:function(e){var t=e.request;if("getBucketLocation"===t.operation){var r=e.httpResponse.body.toString().match(/>(.+)<\/Location/);delete e.data._,r?e.data.LocationConstraint=r[1]:e.data.LocationConstraint=""}},extractError:function(e){var t={304:"NotModified",403:"Forbidden",400:"BadRequest",404:"NotFound"},r=e.httpResponse.statusCode,o=e.httpResponse.body||"";if(t[r]&&0===o.length)e.error=a.util.error(new Error,{code:t[e.httpResponse.statusCode],message:null});else{var n=(new a.XML.Parser).parse(o.toString());e.error=a.util.error(new Error,{code:n.Code||r,message:n.Message||null})}},getSignedUrl:function(e,t,r){t=a.util.copy(t||{});var o=t.Expires||900;delete t.Expires;var n=this.makeRequest(e,t);return n.presign(o,r)},prepareSignedUrl:function(e){e.addListener("validate",e.service.noPresignedContentLength),e.removeListener("build",e.service.addContentType),e.params.Body?e.addListener("afterBuild",a.EventListeners.Core.COMPUTE_SHA256):e.removeListener("build",e.service.computeContentMd5)},noPresignedContentLength:function(e){if(void 0!==e.params.ContentLength)throw a.util.error(new Error,{code:"UnexpectedParameter",message:"ContentLength is not supported in pre-signed URLs."})},createBucket:function(e,t){e||(e={});var r=this.endpoint.hostname;return r===this.api.globalEndpoint||e.CreateBucketConfiguration||(e.CreateBucketConfiguration={LocationConstraint:this.config.region}),this.makeRequest("createBucket",e,t)},upload:function(e,t,r){"function"==typeof t&&void 0===r&&(r=t,t=null),t=t||{},t=a.util.merge(t||{},{service:this,params:e});var o=new a.S3.ManagedUpload(t);return"function"==typeof r&&o.send(r),o}})},{"../core":3,"../s3/managed_upload":33}],41:[function(e,t,r){var a=e("../core");a.util.update(a.SQS.prototype,{setupRequestListeners:function(e){e.addListener("build",this.buildEndpoint),e.service.config.computeChecksums&&("sendMessage"===e.operation?e.addListener("extractData",this.verifySendMessageChecksum):"sendMessageBatch"===e.operation?e.addListener("extractData",this.verifySendMessageBatchChecksum):"receiveMessage"===e.operation&&e.addListener("extractData",this.verifyReceiveMessageChecksum))},verifySendMessageChecksum:function(e){if(e.data){var t=e.data.MD5OfMessageBody,r=this.params.MessageBody,a=this.service.calculateChecksum(r);if(a!==t){var o='Got "'+e.data.MD5OfMessageBody+'", expecting "'+a+'".';this.service.throwInvalidChecksumError(e,[e.data.MessageId],o)}}},verifySendMessageBatchChecksum:function(e){if(e.data){var t=this.service,r={},o=[],n=[];a.util.arrayEach(e.data.Successful,function(e){r[e.Id]=e}),a.util.arrayEach(this.params.Entries,function(e){if(r[e.Id]){var a=r[e.Id].MD5OfMessageBody,i=e.MessageBody;t.isChecksumValid(a,i)||(o.push(e.Id),n.push(r[e.Id].MessageId))}}),o.length>0&&t.throwInvalidChecksumError(e,n,"Invalid messages: "+o.join(", "))}},verifyReceiveMessageChecksum:function(e){if(e.data){var t=this.service,r=[];a.util.arrayEach(e.data.Messages,function(e){var a=e.MD5OfBody,o=e.Body;t.isChecksumValid(a,o)||r.push(e.MessageId)}),r.length>0&&t.throwInvalidChecksumError(e,r,"Invalid messages: "+r.join(", "))}},throwInvalidChecksumError:function(e,t,r){e.error=a.util.error(new Error,{retryable:!0,code:"InvalidChecksum",messageIds:t,message:e.request.operation+" returned an invalid MD5 response. "+r})},isChecksumValid:function(e,t){return this.calculateChecksum(t)===e},calculateChecksum:function(e){return a.util.crypto.md5(e,"hex")},buildEndpoint:function(e){var t=e.httpRequest.params.QueueUrl;if(t){e.httpRequest.endpoint=new a.Endpoint(t);var r=e.httpRequest.endpoint.host.match(/^sqs\.(.+?)\./);r&&(e.httpRequest.region=r[1])}}})},{"../core":3}],42:[function(e,t,r){var a=e("../core");a.util.update(a.STS.prototype,{credentialsFrom:function(e,t){return e?(t||(t=new a.TemporaryCredentials),t.expired=!1,t.accessKeyId=e.Credentials.AccessKeyId,t.secretAccessKey=e.Credentials.SecretAccessKey,t.sessionToken=e.Credentials.SessionToken,t.expireTime=e.Credentials.Expiration,
t):null},assumeRoleWithWebIdentity:function(e,t){return this.makeUnauthenticatedRequest("assumeRoleWithWebIdentity",e,t)},assumeRoleWithSAML:function(e,t){return this.makeUnauthenticatedRequest("assumeRoleWithSAML",e,t)}})},{"../core":3}],43:[function(e,t,r){function a(e){var t=e.httpRequest.headers[s];if(delete e.httpRequest.headers["User-Agent"],delete e.httpRequest.headers["X-Amz-User-Agent"],e.service.getSignerClass()===n.Signers.V4){if(t>604800){var r="Presigning does not support expiry time greater than a week with SigV4 signing.";throw n.util.error(new Error,{code:"InvalidExpiryTime",message:r,retryable:!1})}e.httpRequest.headers[s]=t}else{if(e.service.getSignerClass()!==n.Signers.S3)throw n.util.error(new Error,{message:"Presigning only supports S3 or SigV4 signing.",code:"UnsupportedSigner",retryable:!1});e.httpRequest.headers[s]=parseInt(n.util.date.unixTimestamp()+t,10).toString()}}function o(e){var t=e.httpRequest.endpoint,r=n.util.urlParse(e.httpRequest.path),a={};r.search&&(a=n.util.queryStringParse(r.search.substr(1))),n.util.each(e.httpRequest.headers,function(e,t){e===s&&(e="Expires"),a[e]=t}),delete e.httpRequest.headers[s];var o=a.Authorization.split(" ");if("AWS"===o[0])o=o[1].split(":"),a.AWSAccessKeyId=o[0],a.Signature=o[1];else if("AWS4-HMAC-SHA256"===o[0]){o.shift();var i=o.join(" "),u=i.match(/Signature=(.*?)(?:,|\s|\r?\n|$)/)[1];a["X-Amz-Signature"]=u,delete a.Expires}delete a.Authorization,delete a.Host,t.pathname=r.pathname,t.search=n.util.queryParamsToString(a)}var n=e("../core"),i=n.util.inherit,s="presigned-expires";n.Signers.Presign=i({sign:function(e,t,r){if(e.httpRequest.headers[s]=t||3600,e.on("build",a),e.on("sign",o),e.removeListener("afterBuild",n.EventListeners.Core.SET_CONTENT_LENGTH),e.removeListener("afterBuild",n.EventListeners.Core.COMPUTE_SHA256),e.emit("beforePresign",[e]),!r){if(e.build(),e.response.error)throw e.response.error;return n.util.urlFormat(e.httpRequest.endpoint)}e.build(function(){this.response.error?r(this.response.error):r(null,n.util.urlFormat(e.httpRequest.endpoint))})}}),t.exports=n.Signers.Presign},{"../core":3}],44:[function(e,t,r){var a=e("../core"),o=a.util.inherit;a.Signers.RequestSigner=o({constructor:function(e){this.request=e}}),a.Signers.RequestSigner.getVersion=function(e){switch(e){case"v2":return a.Signers.V2;case"v3":return a.Signers.V3;case"v4":return a.Signers.V4;case"s3":return a.Signers.S3;case"v3https":return a.Signers.V3Https}throw new Error("Unknown signing version "+e)},e("./v2"),e("./v3"),e("./v3https"),e("./v4"),e("./s3"),e("./presign")},{"../core":3,"./presign":43,"./s3":45,"./v2":46,"./v3":47,"./v3https":48,"./v4":49}],45:[function(e,t,r){var a=e("../core"),o=a.util.inherit;a.Signers.S3=o(a.Signers.RequestSigner,{subResources:{acl:1,cors:1,lifecycle:1,"delete":1,location:1,logging:1,notification:1,partNumber:1,policy:1,requestPayment:1,restore:1,tagging:1,torrent:1,uploadId:1,uploads:1,versionId:1,versioning:1,versions:1,website:1},responseHeaders:{"response-content-type":1,"response-content-language":1,"response-expires":1,"response-cache-control":1,"response-content-disposition":1,"response-content-encoding":1},addAuthorization:function(e,t){this.request.headers["presigned-expires"]||(this.request.headers["X-Amz-Date"]=a.util.date.rfc822(t)),e.sessionToken&&(this.request.headers["x-amz-security-token"]=e.sessionToken);var r=this.sign(e.secretAccessKey,this.stringToSign()),o="AWS "+e.accessKeyId+":"+r;this.request.headers.Authorization=o},stringToSign:function(){var e=this.request,t=[];t.push(e.method),t.push(e.headers["Content-MD5"]||""),t.push(e.headers["Content-Type"]||""),t.push(e.headers["presigned-expires"]||"");var r=this.canonicalizedAmzHeaders();return r&&t.push(r),t.push(this.canonicalizedResource()),t.join("\n")},canonicalizedAmzHeaders:function(){var e=[];a.util.each(this.request.headers,function(t){t.match(/^x-amz-/i)&&e.push(t)}),e.sort(function(e,t){return e.toLowerCase()<t.toLowerCase()?-1:1});var t=[];return a.util.arrayEach.call(this,e,function(e){t.push(e.toLowerCase()+":"+String(this.request.headers[e]))}),t.join("\n")},canonicalizedResource:function(){var e=this.request,t=e.path.split("?"),r=t[0],o=t[1],n="";if(e.virtualHostedBucket&&(n+="/"+e.virtualHostedBucket),n+=r,o){var i=[];a.util.arrayEach.call(this,o.split("&"),function(e){var t=e.split("=")[0],r=e.split("=")[1];if(this.subResources[t]||this.responseHeaders[t]){var a={name:t};void 0!==r&&(this.subResources[t]?a.value=r:a.value=decodeURIComponent(r)),i.push(a)}}),i.sort(function(e,t){return e.name<t.name?-1:1}),i.length&&(o=[],a.util.arrayEach(i,function(e){void 0===e.value?o.push(e.name):o.push(e.name+"="+e.value)}),n+="?"+o.join("&"))}return n},sign:function(e,t){return a.util.crypto.hmac(e,t,"base64","sha1")}}),t.exports=a.Signers.S3},{"../core":3}],46:[function(e,t,r){var a=e("../core"),o=a.util.inherit;a.Signers.V2=o(a.Signers.RequestSigner,{addAuthorization:function(e,t){t||(t=a.util.date.getDate());var r=this.request;r.params.Timestamp=a.util.date.iso8601(t),r.params.SignatureVersion="2",r.params.SignatureMethod="HmacSHA256",r.params.AWSAccessKeyId=e.accessKeyId,e.sessionToken&&(r.params.SecurityToken=e.sessionToken),delete r.params.Signature,r.params.Signature=this.signature(e),r.body=a.util.queryParamsToString(r.params),r.headers["Content-Length"]=r.body.length},signature:function(e){return a.util.crypto.hmac(e.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function(){var e=[];return e.push(this.request.method),e.push(this.request.endpoint.host.toLowerCase()),e.push(this.request.pathname()),e.push(a.util.queryParamsToString(this.request.params)),e.join("\n")}}),t.exports=a.Signers.V2},{"../core":3}],47:[function(e,t,r){var a=e("../core"),o=a.util.inherit;a.Signers.V3=o(a.Signers.RequestSigner,{addAuthorization:function(e,t){var r=a.util.date.rfc822(t);this.request.headers["X-Amz-Date"]=r,e.sessionToken&&(this.request.headers["x-amz-security-token"]=e.sessionToken),this.request.headers["X-Amzn-Authorization"]=this.authorization(e,r)},authorization:function(e){return"AWS3 AWSAccessKeyId="+e.accessKeyId+",Algorithm=HmacSHA256,SignedHeaders="+this.signedHeaders()+",Signature="+this.signature(e)},signedHeaders:function(){var e=[];return a.util.arrayEach(this.headersToSign(),function(t){e.push(t.toLowerCase())}),e.sort().join(";")},canonicalHeaders:function(){var e=this.request.headers,t=[];return a.util.arrayEach(this.headersToSign(),function(r){t.push(r.toLowerCase().trim()+":"+String(e[r]).trim())}),t.sort().join("\n")+"\n"},headersToSign:function(){var e=[];return a.util.each(this.request.headers,function(t){("Host"===t||"Content-Encoding"===t||t.match(/^X-Amz/i))&&e.push(t)}),e},signature:function(e){return a.util.crypto.hmac(e.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function(){var e=[];return e.push(this.request.method),e.push("/"),e.push(""),e.push(this.canonicalHeaders()),e.push(this.request.body),a.util.crypto.sha256(e.join("\n"))}}),t.exports=a.Signers.V3},{"../core":3}],48:[function(e,t,r){var a=e("../core"),o=a.util.inherit;e("./v3"),a.Signers.V3Https=o(a.Signers.V3,{authorization:function(e){return"AWS3-HTTPS AWSAccessKeyId="+e.accessKeyId+",Algorithm=HmacSHA256,Signature="+this.signature(e)},stringToSign:function(){return this.request.headers["X-Amz-Date"]}}),t.exports=a.Signers.V3Https},{"../core":3,"./v3":47}],49:[function(e,t,r){var a=e("../core"),o=a.util.inherit,n={},i="presigned-expires";a.Signers.V4=o(a.Signers.RequestSigner,{constructor:function(e,t){a.Signers.RequestSigner.call(this,e),this.serviceName=t},algorithm:"AWS4-HMAC-SHA256",addAuthorization:function(e,t){var r=a.util.date.iso8601(t).replace(/[:\-]|\.\d{3}/g,"");this.isPresigned()?this.updateForPresigned(e,r):this.addHeaders(e,r),this.request.headers.Authorization=this.authorization(e,r)},addHeaders:function(e,t){this.request.headers["X-Amz-Date"]=t,e.sessionToken&&(this.request.headers["x-amz-security-token"]=e.sessionToken)},updateForPresigned:function(e,t){var r=this.credentialString(t),o={"X-Amz-Date":t,"X-Amz-Algorithm":this.algorithm,"X-Amz-Credential":e.accessKeyId+"/"+r,"X-Amz-Expires":this.request.headers[i],"X-Amz-SignedHeaders":this.signedHeaders()};e.sessionToken&&(o["X-Amz-Security-Token"]=e.sessionToken),this.request.headers["Content-Type"]&&(o["Content-Type"]=this.request.headers["Content-Type"]),a.util.each.call(this,this.request.headers,function(e,t){e!==i&&this.isSignableHeader(e)&&0===e.toLowerCase().indexOf("x-amz-")&&(o[e]=t)});var n=this.request.path.indexOf("?")>=0?"&":"?";this.request.path+=n+a.util.queryParamsToString(o)},authorization:function(e,t){var r=[],a=this.credentialString(t);return r.push(this.algorithm+" Credential="+e.accessKeyId+"/"+a),r.push("SignedHeaders="+this.signedHeaders()),r.push("Signature="+this.signature(e,t)),r.join(", ")},signature:function(e,t){var r=n[this.serviceName],o=t.substr(0,8);if(!r||r.akid!==e.accessKeyId||r.region!==this.request.region||r.date!==o){var i=e.secretAccessKey,s=a.util.crypto.hmac("AWS4"+i,o,"buffer"),u=a.util.crypto.hmac(s,this.request.region,"buffer"),c=a.util.crypto.hmac(u,this.serviceName,"buffer"),p=a.util.crypto.hmac(c,"aws4_request","buffer");n[this.serviceName]={region:this.request.region,date:o,key:p,akid:e.accessKeyId}}var m=n[this.serviceName].key;return a.util.crypto.hmac(m,this.stringToSign(t),"hex")},stringToSign:function(e){var t=[];return t.push("AWS4-HMAC-SHA256"),t.push(e),t.push(this.credentialString(e)),t.push(this.hexEncodedHash(this.canonicalString())),t.join("\n")},canonicalString:function(){var e=[],t=this.request.pathname();return"s3"!==this.serviceName&&(t=a.util.uriEscapePath(t)),e.push(this.request.method),e.push(t),e.push(this.request.search()),e.push(this.canonicalHeaders()+"\n"),e.push(this.signedHeaders()),e.push(this.hexEncodedBodyHash()),e.join("\n")},canonicalHeaders:function(){var e=[];a.util.each.call(this,this.request.headers,function(t,r){e.push([t,r])}),e.sort(function(e,t){return e[0].toLowerCase()<t[0].toLowerCase()?-1:1});var t=[];return a.util.arrayEach.call(this,e,function(e){var r=e[0].toLowerCase();this.isSignableHeader(r)&&t.push(r+":"+this.canonicalHeaderValues(e[1].toString()))}),t.join("\n")},canonicalHeaderValues:function(e){return e.replace(/\s+/g," ").replace(/^\s+|\s+$/g,"")},signedHeaders:function(){var e=[];return a.util.each.call(this,this.request.headers,function(t){t=t.toLowerCase(),this.isSignableHeader(t)&&e.push(t)}),e.sort().join(";")},credentialString:function(e){var t=[];return t.push(e.substr(0,8)),t.push(this.request.region),t.push(this.serviceName),t.push("aws4_request"),t.join("/")},hexEncodedHash:function(e){return a.util.crypto.sha256(e,"hex")},hexEncodedBodyHash:function(){return this.isPresigned()&&"s3"===this.serviceName?"UNSIGNED-PAYLOAD":this.request.headers["X-Amz-Content-Sha256"]?this.request.headers["X-Amz-Content-Sha256"]:this.hexEncodedHash(this.request.body||"")},unsignableHeaders:["authorization","content-type","content-length","user-agent",i],isSignableHeader:function(e){return 0===e.toLowerCase().indexOf("x-amz-")?!0:this.unsignableHeaders.indexOf(e)<0},isPresigned:function(){return this.request.headers[i]?!0:!1}}),t.exports=a.Signers.V4},{"../core":3}],50:[function(e,t,r){function a(e,t){this.currentState=t||null,this.states=e||{}}a.prototype.runTo=function(e,t,r,a){"function"==typeof e&&(a=r,r=t,t=e,e=null);var o=this,n=o.states[o.currentState];n.fn.call(r||o,a,function(a){if(a){if(!n.fail)return t?t.call(r,a):null;o.currentState=n.fail}else{if(!n.accept)return t?t.call(r):null;o.currentState=n.accept}return o.currentState===e?t?t.call(r,a):null:void o.runTo(e,t,r,a)})},a.prototype.addState=function(e,t,r,a){return"function"==typeof t?(a=t,t=null,r=null):"function"==typeof r&&(a=r,r=null),this.currentState||(this.currentState=e),this.states[e]={accept:t,fail:r,fn:a},this},t.exports=a},{}],51:[function(e,t,r){(function(r){var a,o=e("crypto"),n=e("buffer").Buffer,i={engine:function(){return i.isBrowser()&&"undefined"!=typeof navigator?navigator.userAgent:r.platform+"/"+r.version},userAgent:function(){var t=i.isBrowser()?"js":"nodejs",r="aws-sdk-"+t+"/"+e("./core").VERSION;return"nodejs"===t&&(r+=" "+i.engine()),r},isBrowser:function(){return r&&r.browser},isNode:function(){return!i.isBrowser()},nodeRequire:function(t){return i.isNode()?e(t):void 0},multiRequire:function(t,r){return e(i.isNode()?t:r)},uriEscape:function(e){var t=encodeURIComponent(e);return t=t.replace(/[^A-Za-z0-9_.~\-%]+/g,escape),t=t.replace(/[*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})},uriEscapePath:function(e){var t=[];return i.arrayEach(e.split("/"),function(e){t.push(i.uriEscape(e))}),t.join("/")},urlParse:function(t){return e("url").parse(t)},urlFormat:function(t){return e("url").format(t)},queryStringParse:function(t){return e("querystring").parse(t)},queryParamsToString:function(e){var t=[],r=i.uriEscape,a=Object.keys(e).sort();return i.arrayEach(a,function(a){var o=e[a],n=r(a),s=n+"=";if(Array.isArray(o)){var u=[];i.arrayEach(o,function(e){u.push(r(e))}),s=n+"="+u.sort().join("&"+n+"=")}else void 0!==o&&null!==o&&(s=n+"="+r(o));t.push(s)}),t.join("&")},readFileSync:function(e){return"undefined"!=typeof window?null:i.nodeRequire("fs").readFileSync(e,"utf-8")},base64:{encode:function(e){return new n(e).toString("base64")},decode:function(e){return new n(e,"base64")}},Buffer:n,buffer:{toStream:function(e){i.Buffer.isBuffer(e)||(e=new i.Buffer(e));var t=new(i.nodeRequire("stream").Readable),r=0;return t._read=function(a){if(r>=e.length)return t.push(null);var o=r+a;o>e.length&&(o=e.length),t.push(e.slice(r,o)),r=o},t},concat:function(e){var t,r=0,a=0,o=null;for(t=0;t<e.length;t++)r+=e[t].length;for(o=new n(r),t=0;t<e.length;t++)e[t].copy(o,a),a+=e[t].length;return o}},string:{byteLength:function(e){if(null===e||void 0===e)return 0;if("string"==typeof e&&(e=new n(e)),"number"==typeof e.byteLength)return e.byteLength;if("number"==typeof e.length)return e.length;if("number"==typeof e.size)return e.size;if("string"==typeof e.path)return i.nodeRequire("fs").lstatSync(e.path).size;throw i.error(new Error("Cannot determine length of "+e),{object:e})},upperFirst:function(e){return e[0].toUpperCase()+e.substr(1)},lowerFirst:function(e){return e[0].toLowerCase()+e.substr(1)}},ini:{parse:function(e){var t,r={};return i.arrayEach(e.split(/\r?\n/),function(e){e=e.split(/(^|\s);/)[0];var a=e.match(/^\s*\[([^\[\]]+)\]\s*$/);if(a)t=a[1];else if(t){var o=e.match(/^\s*(.+?)\s*=\s*(.+?)\s*$/);o&&(r[t]=r[t]||{},r[t][o[1]]=o[2])}}),r}},fn:{noop:function(){},makeAsync:function(e,t){return t&&t<=e.length?e:function(){var t=Array.prototype.slice.call(arguments,0),r=t.pop(),a=e.apply(null,t);r(a)}}},jamespath:{query:function(e,t){if(!t)return[];var r=[],a=e.split(/\s+\|\|\s+/);return i.arrayEach.call(this,a,function(e){var a=[t],o=e.split(".");return i.arrayEach.call(this,o,function(e){var t=e.match("^(.+?)(?:\\[(-?\\d+|\\*|)\\])?$"),r=[];return i.arrayEach.call(this,a,function(e){"*"===t[1]?i.arrayEach.call(this,e,function(e){r.push(e)}):e.hasOwnProperty(t[1])&&r.push(e[t[1]])}),a=r,void 0!==t[2]&&(r=[],i.arrayEach.call(this,a,function(e){if(Array.isArray(e))if("*"===t[2]||""===t[2])r=r.concat(e);else{var a=parseInt(t[2],10);0>a&&(a=e.length+a),r.push(e[a])}}),a=r),0===a.length?i.abort:void 0}),a.length>0?(r=a,i.abort):void 0}),r},find:function(e,t){return i.jamespath.query(e,t)[0]}},date:{getDate:function(){return a||(a=e("./core")),a.config.systemClockOffset?new Date((new Date).getTime()+a.config.systemClockOffset):new Date},iso8601:function(e){return void 0===e&&(e=i.date.getDate()),e.toISOString().replace(/\.\d{3}Z$/,"Z")},rfc822:function(e){return void 0===e&&(e=i.date.getDate()),e.toUTCString()},unixTimestamp:function(e){return void 0===e&&(e=i.date.getDate()),e.getTime()/1e3},from:function(e){return"number"==typeof e?new Date(1e3*e):new Date(e)},format:function(e,t){return t||(t="iso8601"),i.date[t](i.date.from(e))},parseTimestamp:function(e){if("number"==typeof e)return new Date(1e3*e);if(e.match(/^\d+$/))return new Date(1e3*e);if(e.match(/^\d{4}/))return new Date(e);if(e.match(/^\w{3},/))return new Date(e);throw i.error(new Error("unhandled timestamp format: "+e),{code:"TimestampParserError"})}},crypto:{crc32Table:[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],crc32:function(e){var t=i.crypto.crc32Table,r=-1;"string"==typeof e&&(e=new n(e));for(var a=0;a<e.length;a++){var o=e.readUInt8(a);r=r>>>8^t[255&(r^o)]}return(-1^r)>>>0},hmac:function(e,t,r,a){return r||(r="binary"),"buffer"===r&&(r=void 0),a||(a="sha256"),"string"==typeof t&&(t=new n(t)),o.createHmac(a,e).update(t).digest(r)},md5:function(e,t,r){return i.crypto.hash("md5",e,t,r)},sha256:function(e,t,r){return i.crypto.hash("sha256",e,t,r)},hash:function(e,t,r,a){var o=i.crypto.createHash(e);r||(r="binary"),"buffer"===r&&(r=void 0),"string"==typeof t&&(t=new n(t));var s=i.arraySliceFn(t),u=n.isBuffer(t);if(a&&"object"==typeof t&&"function"==typeof t.on&&!u)t.on("data",function(e){o.update(e)}),t.on("error",function(e){a(e)}),t.on("end",function(){a(null,o.digest(r))});else{if(!a||!s||u||"undefined"==typeof FileReader){i.isBrowser()&&"object"==typeof t&&!u&&(t=new n(new Uint8Array(t)));var c=o.update(t).digest(r);return a&&a(null,c),c}var p=0,m=524288,l=new FileReader;l.onerror=function(){a(new Error("Failed to read data."))},l.onload=function(){var e=new n(new Uint8Array(l.result));o.update(e),p+=e.length,l._continueReading()},l._continueReading=function(){if(p>=t.size)return void a(null,o.digest(r));var e=p+m;e>t.size&&(e=t.size),l.readAsArrayBuffer(s.call(t,p,e))},l._continueReading()}},toHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push(("0"+e.charCodeAt(r).toString(16)).substr(-2,2));return t.join("")},createHash:function(e){return o.createHash(e)}},abort:{},each:function(e,t){for(var r in e)if(e.hasOwnProperty(r)){var a=t.call(this,r,e[r]);if(a===i.abort)break}},arrayEach:function(e,t){for(var r in e)if(e.hasOwnProperty(r)){var a=t.call(this,e[r],parseInt(r,10));if(a===i.abort)break}},update:function(e,t){return i.each(t,function(t,r){e[t]=r}),e},merge:function(e,t){return i.update(i.copy(e),t)},copy:function(e){if(null===e||void 0===e)return e;var t={};for(var r in e)t[r]=e[r];return t},isEmpty:function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0},arraySliceFn:function(e){var t=e.slice||e.webkitSlice||e.mozSlice;return"function"==typeof t?t:null},isType:function(e,t){return"function"==typeof t&&(t=i.typeName(t)),Object.prototype.toString.call(e)==="[object "+t+"]"},typeName:function(e){if(e.hasOwnProperty("name"))return e.name;var t=e.toString(),r=t.match(/^\s*function (.+)\(/);return r?r[1]:t},error:function(e,t){var r=null;return"string"==typeof e.message&&""!==e.message&&("string"==typeof t||t&&t.message)&&(r=i.copy(e),r.message=e.message),e.message=e.message||null,"string"==typeof t?e.message=t:"object"==typeof t&&(i.update(e,t),t.message&&(e.message=t.message),(t.code||t.name)&&(e.code=t.code||t.name),t.stack&&(e.stack=t.stack)),"function"==typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=t&&t.name||e.name||e.code||"Error",e.time=new Date,r&&(e.originalError=r),e},inherit:function(e,t){var r=null;if(void 0===t)t=e,e=Object,r={};else{var a=function(){};a.prototype=e.prototype,r=new a}return t.constructor===Object&&(t.constructor=function(){return e!==Object?e.apply(this,arguments):void 0}),t.constructor.prototype=r,i.update(t.constructor.prototype,t),t.constructor.__super__=e,t.constructor},mixin:function(){for(var e=arguments[0],t=1;t<arguments.length;t++)for(var r in arguments[t].prototype){var a=arguments[t].prototype[r];"constructor"!==r&&(e.prototype[r]=a)}return e},hideProperties:function(e,t){"function"==typeof Object.defineProperty&&i.arrayEach(t,function(t){Object.defineProperty(e,t,{enumerable:!1,writable:!0,configurable:!0})})},property:function(e,t,r,a,o){var n={configurable:!0,enumerable:void 0!==a?a:!0};"function"!=typeof r||o?(n.value=r,n.writable=!0):n.get=r,Object.defineProperty(e,t,n)},memoizedProperty:function(e,t,r,a){var o=null;i.property(e,t,function(){return null===o&&(o=r()),o},a)},hoistPayloadMember:function(e){var t=e.request,r=t.operation,a=t.service.api.operations[r].output;if(a.payload){var o=a.members[a.payload],n=e.data[a.payload];"structure"===o.type&&i.each(n,function(t,r){i.property(e.data,t,r,!1)})}},computeSha256:function(e,t){if(i.isNode()){var r=i.nodeRequire("stream").Stream,a=i.nodeRequire("fs");if(e instanceof r){if("string"!=typeof e.path)return t(new Error("Non-file stream objects are not supported with SigV4"));e=a.createReadStream(e.path)}}i.crypto.sha256(e,"hex",function(e,r){e?t(e):t(null,r)})}};t.exports=i}).call(this,e("FWaASH"))},{"./core":3,FWaASH:65,buffer:54,crypto:58,querystring:69,url:70}],52:[function(e,t,r){function a(){}function o(e,t){switch(t||(t={}),t.type){case"structure":return n(e,t);case"map":return i(e,t);case"list":return s(e,t);case void 0:case null:return c(e);default:return u(e,t)}}function n(e,t){var r={};return null===e?r:(p.each(t.members,function(t,a){if(a.isXmlAttribute){if(e.attributes.hasOwnProperty(a.name)){var n=e.attributes[a.name].value;r[t]=o({textContent:n},a)}}else{var i=a.flattened?e:e.getElementsByTagName(a.name)[0];i?r[t]=o(i,a):a.flattened||"list"!==a.type||(r[t]=a.defaultValue)}}),r)}function i(e,t){for(var r={},a=t.key.name||"key",n=t.value.name||"value",i=t.flattened?t.name:"entry",s=e.firstElementChild;s;){if(s.nodeName===i){var u=s.getElementsByTagName(a)[0].textContent,c=s.getElementsByTagName(n)[0];r[u]=o(c,t.value)}s=s.nextElementSibling}return r}function s(e,t){for(var r=[],a=t.flattened?t.name:t.member.name||"member",n=e.firstElementChild;n;)n.nodeName===a&&r.push(o(n,t.member)),n=n.nextElementSibling;return r}function u(e,t){if(e.getAttribute){var r=e.getAttribute("encoding");"base64"===r&&(t=new m.create({type:r}))}var a=e.textContent;return""===a&&(a=null),"function"==typeof t.toType?t.toType(a):a}function c(e){if(void 0===e||null===e)return"";if(!e.firstElementChild)return null===e.parentNode.parentNode?{}:0===e.childNodes.length?"":e.textContent;for(var t={type:"structure",members:{}},r=e.firstElementChild;r;){var a=r.nodeName;t.members.hasOwnProperty(a)?t.members[a].type="list":t.members[a]={name:a},r=r.nextElementSibling}return n(e,t)}var p=e("../util"),m=e("../model/shape");a.prototype.parse=function(e,t){if(""===e.replace(/^\s+/,""))return{};var r,a;try{if(window.DOMParser){try{var n=new DOMParser;r=n.parseFromString(e,"text/xml")}catch(i){throw p.error(new Error("Parse error in document"),{originalError:i})}if(null===r.documentElement)throw new Error("Cannot parse empty document.");var s=r.getElementsByTagName("parsererror")[0];if(s&&(s.parentNode===r||"body"===s.parentNode.nodeName))throw new Error(s.getElementsByTagName("div")[0].textContent)}else{if(!window.ActiveXObject)throw new Error("Cannot load XML parser");if(r=new window.ActiveXObject("Microsoft.XMLDOM"),r.async=!1,!r.loadXML(e))throw new Error("Parse error in document")}}catch(u){a=u}if(r&&r.documentElement&&!a){var c=o(r.documentElement,t),m=r.getElementsByTagName("ResponseMetadata")[0];return m&&(c.ResponseMetadata=o(m,{})),c}if(a)throw p.error(a||new Error,{code:"XMLParserError"});return{}},t.exports=a},{"../model/shape":20,"../util":51}],53:[function(e,t,r){function a(){}function o(e,t,r){switch(r.type){case"structure":return n(e,t,r);case"map":return i(e,t,r);case"list":return s(e,t,r);default:return u(e,t,r)}}function n(e,t,r){p.arrayEach(r.memberNames,function(a){var n=r.members[a];if("body"===n.location){var i=t[a],s=n.name;if(void 0!==i&&null!==i)if(n.isXmlAttribute)e.att(s,i);else if(n.flattened)o(e,i,n);else{var u=e.ele(s);c(u,n),o(u,i,n)}}})}function i(e,t,r){var a=r.key.name||"key",n=r.value.name||"value";p.each(t,function(t,i){var s=e.ele(r.flattened?r.name:"entry");o(s.ele(a),t,r.key),o(s.ele(n),i,r.value)})}function s(e,t,r){r.flattened?p.arrayEach(t,function(t){var a=r.member.name||r.name,n=e.ele(a);o(n,t,r.member)}):p.arrayEach(t,function(t){var a=r.member.name||"member",n=e.ele(a);o(n,t,r.member)})}function u(e,t,r){e.txt(r.toWireFormat(t))}function c(e,t){var r,a="xmlns";t.xmlNamespaceUri?(r=t.xmlNamespaceUri,t.xmlNamespacePrefix&&(a+=":"+t.xmlNamespacePrefix)):e.isRoot&&t.api.xmlNamespaceUri&&(r=t.api.xmlNamespaceUri),r&&e.att(a,r)}var p=e("../util"),m=e("xmlbuilder");a.prototype.toXML=function(e,t,r,a){var n=m.create(r);return c(n,t),o(n,e,t),n.children.length>0||a?n.root().toString():""},t.exports=a},{"../util":51,xmlbuilder:75}],54:[function(e,t,r){function a(e,t,r){if(!(this instanceof a))return new a(e,t,r);var o=typeof e;if("base64"===t&&"string"===o)for(e=A(e);e.length%4!==0;)e+="=";var n;if("number"===o)n=x(e);else if("string"===o)n=a.byteLength(e,t);else{if("object"!==o)throw new Error("First argument needs to be a number, array or string.");n=x(e.length)}var i;a._useTypedArrays?i=a._augment(new Uint8Array(n)):(i=this,i.length=n,i._isBuffer=!0);var s;if(a._useTypedArrays&&"number"==typeof e.byteLength)i._set(e);else if(P(e))for(s=0;n>s;s++)a.isBuffer(e)?i[s]=e.readUInt8(s):i[s]=e[s];else if("string"===o)i.write(e,0,t);else if("number"===o&&!a._useTypedArrays&&!r)for(s=0;n>s;s++)i[s]=0;return i}function o(e,t,r,o){r=Number(r)||0;var n=e.length-r;o?(o=Number(o),o>n&&(o=n)):o=n;var i=t.length;G(i%2===0,"Invalid hex string"),o>i/2&&(o=i/2);for(var s=0;o>s;s++){var u=parseInt(t.substr(2*s,2),16);G(!isNaN(u),"Invalid hex string"),e[r+s]=u}return a._charsWritten=2*s,s}function n(e,t,r,o){var n=a._charsWritten=_(L(t),e,r,o);return n}function i(e,t,r,o){var n=a._charsWritten=_(M(t),e,r,o);return n}function s(e,t,r,a){return i(e,t,r,a)}function u(e,t,r,o){var n=a._charsWritten=_(U(t),e,r,o);return n}function c(e,t,r,o){var n=a._charsWritten=_(B(t),e,r,o);return n}function p(e,t,r){return 0===t&&r===e.length?K.fromByteArray(e):K.fromByteArray(e.slice(t,r))}function m(e,t,r){var a="",o="";r=Math.min(e.length,r);for(var n=t;r>n;n++)e[n]<=127?(a+=V(o)+String.fromCharCode(e[n]),o=""):o+="%"+e[n].toString(16);return a+V(o)}function l(e,t,r){var a="";r=Math.min(e.length,r);for(var o=t;r>o;o++)a+=String.fromCharCode(e[o]);return a}function d(e,t,r){return l(e,t,r)}function y(e,t,r){var a=e.length;(!t||0>t)&&(t=0),(!r||0>r||r>a)&&(r=a);for(var o="",n=t;r>n;n++)o+=w(e[n]);return o}function h(e,t,r){for(var a=e.slice(t,r),o="",n=0;n<a.length;n+=2)o+=String.fromCharCode(a[n]+256*a[n+1]);return o}function b(e,t,r,a){a||(G("boolean"==typeof r,"missing or invalid endian"),G(void 0!==t&&null!==t,"missing offset"),G(t+1<e.length,"Trying to read beyond buffer length"));var o=e.length;if(!(t>=o)){var n;return r?(n=e[t],o>t+1&&(n|=e[t+1]<<8)):(n=e[t]<<8,o>t+1&&(n|=e[t+1])),n}}function f(e,t,r,a){a||(G("boolean"==typeof r,"missing or invalid endian"),G(void 0!==t&&null!==t,"missing offset"),G(t+3<e.length,"Trying to read beyond buffer length"));var o=e.length;if(!(t>=o)){var n;return r?(o>t+2&&(n=e[t+2]<<16),o>t+1&&(n|=e[t+1]<<8),n|=e[t],o>t+3&&(n+=e[t+3]<<24>>>0)):(o>t+1&&(n=e[t+1]<<16),o>t+2&&(n|=e[t+2]<<8),o>t+3&&(n|=e[t+3]),n+=e[t]<<24>>>0),n}}function S(e,t,r,a){a||(G("boolean"==typeof r,"missing or invalid endian"),G(void 0!==t&&null!==t,"missing offset"),G(t+1<e.length,"Trying to read beyond buffer length"));var o=e.length;if(!(t>=o)){var n=b(e,t,r,!0),i=32768&n;return i?-1*(65535-n+1):n}}function g(e,t,r,a){a||(G("boolean"==typeof r,"missing or invalid endian"),G(void 0!==t&&null!==t,"missing offset"),G(t+3<e.length,"Trying to read beyond buffer length"));var o=e.length;if(!(t>=o)){var n=f(e,t,r,!0),i=2147483648&n;return i?-1*(4294967295-n+1):n}}function N(e,t,r,a){return a||(G("boolean"==typeof r,"missing or invalid endian"),G(t+3<e.length,"Trying to read beyond buffer length")),j.read(e,t,r,23,4)}function I(e,t,r,a){return a||(G("boolean"==typeof r,"missing or invalid endian"),G(t+7<e.length,"Trying to read beyond buffer length")),j.read(e,t,r,52,8)}function v(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),G(void 0!==r&&null!==r,"missing offset"),G(r+1<e.length,"trying to write beyond buffer length"),z(t,65535));var n=e.length;if(!(r>=n))for(var i=0,s=Math.min(n-r,2);s>i;i++)e[r+i]=(t&255<<8*(a?i:1-i))>>>8*(a?i:1-i)}function k(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),G(void 0!==r&&null!==r,"missing offset"),G(r+3<e.length,"trying to write beyond buffer length"),z(t,4294967295));var n=e.length;if(!(r>=n))for(var i=0,s=Math.min(n-r,4);s>i;i++)e[r+i]=t>>>8*(a?i:3-i)&255}function R(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),G(void 0!==r&&null!==r,"missing offset"),G(r+1<e.length,"Trying to write beyond buffer length"),F(t,32767,-32768));var n=e.length;r>=n||(t>=0?v(e,t,r,a,o):v(e,65535+t+1,r,a,o))}function C(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),G(void 0!==r&&null!==r,"missing offset"),G(r+3<e.length,"Trying to write beyond buffer length"),F(t,2147483647,-2147483648));var n=e.length;r>=n||(t>=0?k(e,t,r,a,o):k(e,4294967295+t+1,r,a,o))}function T(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),
G(void 0!==r&&null!==r,"missing offset"),G(r+3<e.length,"Trying to write beyond buffer length"),O(t,3.4028234663852886e38,-3.4028234663852886e38));var n=e.length;r>=n||j.write(e,t,r,a,23,4)}function D(e,t,r,a,o){o||(G(void 0!==t&&null!==t,"missing value"),G("boolean"==typeof a,"missing or invalid endian"),G(void 0!==r&&null!==r,"missing offset"),G(r+7<e.length,"Trying to write beyond buffer length"),O(t,1.7976931348623157e308,-1.7976931348623157e308));var n=e.length;r>=n||j.write(e,t,r,a,52,8)}function A(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function q(e,t,r){return"number"!=typeof e?r:(e=~~e,e>=t?t:e>=0?e:(e+=t,e>=0?e:0))}function x(e){return e=~~Math.ceil(+e),0>e?0:e}function E(e){return(Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)})(e)}function P(e){return E(e)||a.isBuffer(e)||e&&"object"==typeof e&&"number"==typeof e.length}function w(e){return 16>e?"0"+e.toString(16):e.toString(16)}function L(e){for(var t=[],r=0;r<e.length;r++){var a=e.charCodeAt(r);if(127>=a)t.push(e.charCodeAt(r));else{var o=r;a>=55296&&57343>=a&&r++;for(var n=encodeURIComponent(e.slice(o,r+1)).substr(1).split("%"),i=0;i<n.length;i++)t.push(parseInt(n[i],16))}}return t}function M(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t}function B(e){for(var t,r,a,o=[],n=0;n<e.length;n++)t=e.charCodeAt(n),r=t>>8,a=t%256,o.push(a),o.push(r);return o}function U(e){return K.toByteArray(e)}function _(e,t,r,a){for(var o=0;a>o&&!(o+r>=t.length||o>=e.length);o++)t[o+r]=e[o];return o}function V(e){try{return decodeURIComponent(e)}catch(t){return String.fromCharCode(65533)}}function z(e,t){G("number"==typeof e,"cannot write a non-number as a number"),G(e>=0,"specified a negative value for writing an unsigned value"),G(t>=e,"value is larger than maximum value for type"),G(Math.floor(e)===e,"value has a fractional component")}function F(e,t,r){G("number"==typeof e,"cannot write a non-number as a number"),G(t>=e,"value larger than maximum allowed value"),G(e>=r,"value smaller than minimum allowed value"),G(Math.floor(e)===e,"value has a fractional component")}function O(e,t,r){G("number"==typeof e,"cannot write a non-number as a number"),G(t>=e,"value larger than maximum allowed value"),G(e>=r,"value smaller than minimum allowed value")}function G(e,t){if(!e)throw new Error(t||"Failed assertion")}var K=e("base64-js"),j=e("ieee754");r.Buffer=a,r.SlowBuffer=a,r.INSPECT_MAX_BYTES=50,a.poolSize=8192,a._useTypedArrays=function(){try{var e=new ArrayBuffer(0),t=new Uint8Array(e);return t.foo=function(){return 42},42===t.foo()&&"function"==typeof t.subarray}catch(r){return!1}}(),a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.isBuffer=function(e){return!(null===e||void 0===e||!e._isBuffer)},a.byteLength=function(e,t){var r;switch(e+="",t||"utf8"){case"hex":r=e.length/2;break;case"utf8":case"utf-8":r=L(e).length;break;case"ascii":case"binary":case"raw":r=e.length;break;case"base64":r=U(e).length;break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":r=2*e.length;break;default:throw new Error("Unknown encoding")}return r},a.concat=function(e,t){if(G(E(e),"Usage: Buffer.concat(list, [totalLength])\nlist should be an Array."),0===e.length)return new a(0);if(1===e.length)return e[0];var r;if("number"!=typeof t)for(t=0,r=0;r<e.length;r++)t+=e[r].length;var o=new a(t),n=0;for(r=0;r<e.length;r++){var i=e[r];i.copy(o,n),n+=i.length}return o},a.prototype.write=function(e,t,r,a){if(isFinite(t))isFinite(r)||(a=r,r=void 0);else{var p=a;a=t,t=r,r=p}t=Number(t)||0;var m=this.length-t;r?(r=Number(r),r>m&&(r=m)):r=m,a=String(a||"utf8").toLowerCase();var l;switch(a){case"hex":l=o(this,e,t,r);break;case"utf8":case"utf-8":l=n(this,e,t,r);break;case"ascii":l=i(this,e,t,r);break;case"binary":l=s(this,e,t,r);break;case"base64":l=u(this,e,t,r);break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":l=c(this,e,t,r);break;default:throw new Error("Unknown encoding")}return l},a.prototype.toString=function(e,t,r){var a=this;if(e=String(e||"utf8").toLowerCase(),t=Number(t)||0,r=void 0!==r?Number(r):r=a.length,r===t)return"";var o;switch(e){case"hex":o=y(a,t,r);break;case"utf8":case"utf-8":o=m(a,t,r);break;case"ascii":o=l(a,t,r);break;case"binary":o=d(a,t,r);break;case"base64":o=p(a,t,r);break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":o=h(a,t,r);break;default:throw new Error("Unknown encoding")}return o},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.copy=function(e,t,r,o){var n=this;if(r||(r=0),o||0===o||(o=this.length),t||(t=0),o!==r&&0!==e.length&&0!==n.length){G(o>=r,"sourceEnd < sourceStart"),G(t>=0&&t<e.length,"targetStart out of bounds"),G(r>=0&&r<n.length,"sourceStart out of bounds"),G(o>=0&&o<=n.length,"sourceEnd out of bounds"),o>this.length&&(o=this.length),e.length-t<o-r&&(o=e.length-t+r);var i=o-r;if(100>i||!a._useTypedArrays)for(var s=0;i>s;s++)e[s+t]=this[s+r];else e._set(this.subarray(r,r+i),t)}},a.prototype.slice=function(e,t){var r=this.length;if(e=q(e,r,0),t=q(t,r,r),a._useTypedArrays)return a._augment(this.subarray(e,t));for(var o=t-e,n=new a(o,void 0,!0),i=0;o>i;i++)n[i]=this[i+e];return n},a.prototype.get=function(e){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(e)},a.prototype.set=function(e,t){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(e,t)},a.prototype.readUInt8=function(e,t){return t||(G(void 0!==e&&null!==e,"missing offset"),G(e<this.length,"Trying to read beyond buffer length")),e>=this.length?void 0:this[e]},a.prototype.readUInt16LE=function(e,t){return b(this,e,!0,t)},a.prototype.readUInt16BE=function(e,t){return b(this,e,!1,t)},a.prototype.readUInt32LE=function(e,t){return f(this,e,!0,t)},a.prototype.readUInt32BE=function(e,t){return f(this,e,!1,t)},a.prototype.readInt8=function(e,t){if(t||(G(void 0!==e&&null!==e,"missing offset"),G(e<this.length,"Trying to read beyond buffer length")),!(e>=this.length)){var r=128&this[e];return r?-1*(255-this[e]+1):this[e]}},a.prototype.readInt16LE=function(e,t){return S(this,e,!0,t)},a.prototype.readInt16BE=function(e,t){return S(this,e,!1,t)},a.prototype.readInt32LE=function(e,t){return g(this,e,!0,t)},a.prototype.readInt32BE=function(e,t){return g(this,e,!1,t)},a.prototype.readFloatLE=function(e,t){return N(this,e,!0,t)},a.prototype.readFloatBE=function(e,t){return N(this,e,!1,t)},a.prototype.readDoubleLE=function(e,t){return I(this,e,!0,t)},a.prototype.readDoubleBE=function(e,t){return I(this,e,!1,t)},a.prototype.writeUInt8=function(e,t,r){r||(G(void 0!==e&&null!==e,"missing value"),G(void 0!==t&&null!==t,"missing offset"),G(t<this.length,"trying to write beyond buffer length"),z(e,255)),t>=this.length||(this[t]=e)},a.prototype.writeUInt16LE=function(e,t,r){v(this,e,t,!0,r)},a.prototype.writeUInt16BE=function(e,t,r){v(this,e,t,!1,r)},a.prototype.writeUInt32LE=function(e,t,r){k(this,e,t,!0,r)},a.prototype.writeUInt32BE=function(e,t,r){k(this,e,t,!1,r)},a.prototype.writeInt8=function(e,t,r){r||(G(void 0!==e&&null!==e,"missing value"),G(void 0!==t&&null!==t,"missing offset"),G(t<this.length,"Trying to write beyond buffer length"),F(e,127,-128)),t>=this.length||(e>=0?this.writeUInt8(e,t,r):this.writeUInt8(255+e+1,t,r))},a.prototype.writeInt16LE=function(e,t,r){R(this,e,t,!0,r)},a.prototype.writeInt16BE=function(e,t,r){R(this,e,t,!1,r)},a.prototype.writeInt32LE=function(e,t,r){C(this,e,t,!0,r)},a.prototype.writeInt32BE=function(e,t,r){C(this,e,t,!1,r)},a.prototype.writeFloatLE=function(e,t,r){T(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){T(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){D(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){D(this,e,t,!1,r)},a.prototype.fill=function(e,t,r){if(e||(e=0),t||(t=0),r||(r=this.length),"string"==typeof e&&(e=e.charCodeAt(0)),G("number"==typeof e&&!isNaN(e),"value is not a number"),G(r>=t,"end < start"),r!==t&&0!==this.length){G(t>=0&&t<this.length,"start out of bounds"),G(r>=0&&r<=this.length,"end out of bounds");for(var a=t;r>a;a++)this[a]=e}},a.prototype.inspect=function(){for(var e=[],t=this.length,a=0;t>a;a++)if(e[a]=w(this[a]),a===r.INSPECT_MAX_BYTES){e[a+1]="...";break}return"<Buffer "+e.join(" ")+">"},a.prototype.toArrayBuffer=function(){if("undefined"!=typeof Uint8Array){if(a._useTypedArrays)return new a(this).buffer;for(var e=new Uint8Array(this.length),t=0,r=e.length;r>t;t+=1)e[t]=this[t];return e.buffer}throw new Error("Buffer.toArrayBuffer not supported in this browser")};var H=a.prototype;a._augment=function(e){return e._isBuffer=!0,e._get=e.get,e._set=e.set,e.get=H.get,e.set=H.set,e.write=H.write,e.toString=H.toString,e.toLocaleString=H.toString,e.toJSON=H.toJSON,e.copy=H.copy,e.slice=H.slice,e.readUInt8=H.readUInt8,e.readUInt16LE=H.readUInt16LE,e.readUInt16BE=H.readUInt16BE,e.readUInt32LE=H.readUInt32LE,e.readUInt32BE=H.readUInt32BE,e.readInt8=H.readInt8,e.readInt16LE=H.readInt16LE,e.readInt16BE=H.readInt16BE,e.readInt32LE=H.readInt32LE,e.readInt32BE=H.readInt32BE,e.readFloatLE=H.readFloatLE,e.readFloatBE=H.readFloatBE,e.readDoubleLE=H.readDoubleLE,e.readDoubleBE=H.readDoubleBE,e.writeUInt8=H.writeUInt8,e.writeUInt16LE=H.writeUInt16LE,e.writeUInt16BE=H.writeUInt16BE,e.writeUInt32LE=H.writeUInt32LE,e.writeUInt32BE=H.writeUInt32BE,e.writeInt8=H.writeInt8,e.writeInt16LE=H.writeInt16LE,e.writeInt16BE=H.writeInt16BE,e.writeInt32LE=H.writeInt32LE,e.writeInt32BE=H.writeInt32BE,e.writeFloatLE=H.writeFloatLE,e.writeFloatBE=H.writeFloatBE,e.writeDoubleLE=H.writeDoubleLE,e.writeDoubleBE=H.writeDoubleBE,e.fill=H.fill,e.inspect=H.inspect,e.toArrayBuffer=H.toArrayBuffer,e}},{"base64-js":55,ieee754:56}],55:[function(e,t,r){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";!function(e){"use strict";function t(e){var t=e.charCodeAt(0);return t===i||t===m?62:t===s||t===l?63:u>t?-1:u+10>t?t-u+26+26:p+26>t?t-p:c+26>t?t-c+26:void 0}function r(e){function r(e){c[m++]=e}var a,o,i,s,u,c;if(e.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var p=e.length;u="="===e.charAt(p-2)?2:"="===e.charAt(p-1)?1:0,c=new n(3*e.length/4-u),i=u>0?e.length-4:e.length;var m=0;for(a=0,o=0;i>a;a+=4,o+=3)s=t(e.charAt(a))<<18|t(e.charAt(a+1))<<12|t(e.charAt(a+2))<<6|t(e.charAt(a+3)),r((16711680&s)>>16),r((65280&s)>>8),r(255&s);return 2===u?(s=t(e.charAt(a))<<2|t(e.charAt(a+1))>>4,r(255&s)):1===u&&(s=t(e.charAt(a))<<10|t(e.charAt(a+1))<<4|t(e.charAt(a+2))>>2,r(s>>8&255),r(255&s)),c}function o(e){function t(e){return a.charAt(e)}function r(e){return t(e>>18&63)+t(e>>12&63)+t(e>>6&63)+t(63&e)}var o,n,i,s=e.length%3,u="";for(o=0,i=e.length-s;i>o;o+=3)n=(e[o]<<16)+(e[o+1]<<8)+e[o+2],u+=r(n);switch(s){case 1:n=e[e.length-1],u+=t(n>>2),u+=t(n<<4&63),u+="==";break;case 2:n=(e[e.length-2]<<8)+e[e.length-1],u+=t(n>>10),u+=t(n>>4&63),u+=t(n<<2&63),u+="="}return u}var n="undefined"!=typeof Uint8Array?Uint8Array:Array,i="+".charCodeAt(0),s="/".charCodeAt(0),u="0".charCodeAt(0),c="a".charCodeAt(0),p="A".charCodeAt(0),m="-".charCodeAt(0),l="_".charCodeAt(0);e.toByteArray=r,e.fromByteArray=o}("undefined"==typeof r?this.base64js={}:r)},{}],56:[function(e,t,r){r.read=function(e,t,r,a,o){var n,i,s=8*o-a-1,u=(1<<s)-1,c=u>>1,p=-7,m=r?o-1:0,l=r?-1:1,d=e[t+m];for(m+=l,n=d&(1<<-p)-1,d>>=-p,p+=s;p>0;n=256*n+e[t+m],m+=l,p-=8);for(i=n&(1<<-p)-1,n>>=-p,p+=a;p>0;i=256*i+e[t+m],m+=l,p-=8);if(0===n)n=1-c;else{if(n===u)return i?NaN:(d?-1:1)*(1/0);i+=Math.pow(2,a),n-=c}return(d?-1:1)*i*Math.pow(2,n-a)},r.write=function(e,t,r,a,o,n){var i,s,u,c=8*n-o-1,p=(1<<c)-1,m=p>>1,l=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,d=a?0:n-1,y=a?1:-1,h=0>t||0===t&&0>1/t?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,i=p):(i=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-i))<1&&(i--,u*=2),t+=i+m>=1?l/u:l*Math.pow(2,1-m),t*u>=2&&(i++,u/=2),i+m>=p?(s=0,i=p):i+m>=1?(s=(t*u-1)*Math.pow(2,o),i+=m):(s=t*Math.pow(2,m-1)*Math.pow(2,o),i=0));o>=8;e[r+d]=255&s,d+=y,s/=256,o-=8);for(i=i<<o|s,c+=o;c>0;e[r+d]=255&i,d+=y,i/=256,c-=8);e[r+d-y]|=128*h}},{}],57:[function(e,t,r){function a(e,t){if(e.length%s!==0){var r=e.length+(s-e.length%s);e=i.concat([e,u],r)}for(var a=[],o=t?e.readInt32BE:e.readInt32LE,n=0;n<e.length;n+=s)a.push(o.call(e,n));return a}function o(e,t,r){for(var a=new i(t),o=r?a.writeInt32BE:a.writeInt32LE,n=0;n<e.length;n++)o.call(a,e[n],4*n,!0);return a}function n(e,t,r,n){i.isBuffer(e)||(e=new i(e));var s=t(a(e,n),e.length*c);return o(s,r,n)}var i=e("buffer").Buffer,s=4,u=new i(s);u.fill(0);var c=8;t.exports={hash:n}},{buffer:54}],58:[function(e,t,r){function a(e,t,r){s.isBuffer(t)||(t=new s(t)),s.isBuffer(r)||(r=new s(r)),t.length>d?t=e(t):t.length<d&&(t=s.concat([t,y],d));for(var a=new s(d),o=new s(d),n=0;d>n;n++)a[n]=54^t[n],o[n]=92^t[n];var i=e(s.concat([a,r]));return e(s.concat([o,i]))}function o(e,t){e=e||"sha1";var r=l[e],o=[],i=0;return r||n("algorithm:",e,"is not yet supported"),{update:function(e){return s.isBuffer(e)||(e=new s(e)),o.push(e),i+=e.length,this},digest:function(e){var n=s.concat(o),i=t?a(r,t,n):r(n);return o=null,e?i.toString(e):i}}}function n(){var e=[].slice.call(arguments).join(" ");throw new Error([e,"we accept pull requests","http://github.com/dominictarr/crypto-browserify"].join("\n"))}function i(e,t){for(var r in e)t(e[r],r)}var s=e("buffer").Buffer,u=e("./sha"),c=e("./sha256"),p=e("./rng"),m=e("./md5"),l={sha1:u,sha256:c,md5:m},d=64,y=new s(d);y.fill(0),r.createHash=function(e){return o(e)},r.createHmac=function(e,t){return o(e,t)},r.randomBytes=function(e,t){if(!t||!t.call)return new s(p(e));try{t.call(this,void 0,new s(p(e)))}catch(r){t(r)}},i(["createCredentials","createCipher","createCipheriv","createDecipher","createDecipheriv","createSign","createVerify","createDiffieHellman","pbkdf2"],function(e){r[e]=function(){n("sorry,",e,"is not implemented yet")}})},{"./md5":59,"./rng":60,"./sha":61,"./sha256":62,buffer:54}],59:[function(e,t,r){function a(e,t){e[t>>5]|=128<<t%32,e[(t+64>>>9<<4)+14]=t;for(var r=1732584193,a=-271733879,o=-1732584194,p=271733878,m=0;m<e.length;m+=16){var l=r,d=a,y=o,h=p;r=n(r,a,o,p,e[m+0],7,-680876936),p=n(p,r,a,o,e[m+1],12,-389564586),o=n(o,p,r,a,e[m+2],17,606105819),a=n(a,o,p,r,e[m+3],22,-1044525330),r=n(r,a,o,p,e[m+4],7,-176418897),p=n(p,r,a,o,e[m+5],12,1200080426),o=n(o,p,r,a,e[m+6],17,-1473231341),a=n(a,o,p,r,e[m+7],22,-45705983),r=n(r,a,o,p,e[m+8],7,1770035416),p=n(p,r,a,o,e[m+9],12,-1958414417),o=n(o,p,r,a,e[m+10],17,-42063),a=n(a,o,p,r,e[m+11],22,-1990404162),r=n(r,a,o,p,e[m+12],7,1804603682),p=n(p,r,a,o,e[m+13],12,-40341101),o=n(o,p,r,a,e[m+14],17,-1502002290),a=n(a,o,p,r,e[m+15],22,1236535329),r=i(r,a,o,p,e[m+1],5,-165796510),p=i(p,r,a,o,e[m+6],9,-1069501632),o=i(o,p,r,a,e[m+11],14,643717713),a=i(a,o,p,r,e[m+0],20,-373897302),r=i(r,a,o,p,e[m+5],5,-701558691),p=i(p,r,a,o,e[m+10],9,38016083),o=i(o,p,r,a,e[m+15],14,-660478335),a=i(a,o,p,r,e[m+4],20,-405537848),r=i(r,a,o,p,e[m+9],5,568446438),p=i(p,r,a,o,e[m+14],9,-1019803690),o=i(o,p,r,a,e[m+3],14,-187363961),a=i(a,o,p,r,e[m+8],20,1163531501),r=i(r,a,o,p,e[m+13],5,-1444681467),p=i(p,r,a,o,e[m+2],9,-51403784),o=i(o,p,r,a,e[m+7],14,1735328473),a=i(a,o,p,r,e[m+12],20,-1926607734),r=s(r,a,o,p,e[m+5],4,-378558),p=s(p,r,a,o,e[m+8],11,-2022574463),o=s(o,p,r,a,e[m+11],16,1839030562),a=s(a,o,p,r,e[m+14],23,-35309556),r=s(r,a,o,p,e[m+1],4,-1530992060),p=s(p,r,a,o,e[m+4],11,1272893353),o=s(o,p,r,a,e[m+7],16,-155497632),a=s(a,o,p,r,e[m+10],23,-1094730640),r=s(r,a,o,p,e[m+13],4,681279174),p=s(p,r,a,o,e[m+0],11,-358537222),o=s(o,p,r,a,e[m+3],16,-722521979),a=s(a,o,p,r,e[m+6],23,76029189),r=s(r,a,o,p,e[m+9],4,-640364487),p=s(p,r,a,o,e[m+12],11,-421815835),o=s(o,p,r,a,e[m+15],16,530742520),a=s(a,o,p,r,e[m+2],23,-995338651),r=u(r,a,o,p,e[m+0],6,-198630844),p=u(p,r,a,o,e[m+7],10,1126891415),o=u(o,p,r,a,e[m+14],15,-1416354905),a=u(a,o,p,r,e[m+5],21,-57434055),r=u(r,a,o,p,e[m+12],6,1700485571),p=u(p,r,a,o,e[m+3],10,-1894986606),o=u(o,p,r,a,e[m+10],15,-1051523),a=u(a,o,p,r,e[m+1],21,-2054922799),r=u(r,a,o,p,e[m+8],6,1873313359),p=u(p,r,a,o,e[m+15],10,-30611744),o=u(o,p,r,a,e[m+6],15,-1560198380),a=u(a,o,p,r,e[m+13],21,1309151649),r=u(r,a,o,p,e[m+4],6,-145523070),p=u(p,r,a,o,e[m+11],10,-1120210379),o=u(o,p,r,a,e[m+2],15,718787259),a=u(a,o,p,r,e[m+9],21,-343485551),r=c(r,l),a=c(a,d),o=c(o,y),p=c(p,h)}return Array(r,a,o,p)}function o(e,t,r,a,o,n){return c(p(c(c(t,e),c(a,n)),o),r)}function n(e,t,r,a,n,i,s){return o(t&r|~t&a,e,t,n,i,s)}function i(e,t,r,a,n,i,s){return o(t&a|r&~a,e,t,n,i,s)}function s(e,t,r,a,n,i,s){return o(t^r^a,e,t,n,i,s)}function u(e,t,r,a,n,i,s){return o(r^(t|~a),e,t,n,i,s)}function c(e,t){var r=(65535&e)+(65535&t),a=(e>>16)+(t>>16)+(r>>16);return a<<16|65535&r}function p(e,t){return e<<t|e>>>32-t}var m=e("./helpers");t.exports=function(e){return m.hash(e,a,16)}},{"./helpers":57}],60:[function(e,t,r){!function(){var e,r,a=this;e=function(e){for(var t,t,r=new Array(e),a=0;e>a;a++)0==(3&a)&&(t=4294967296*Math.random()),r[a]=t>>>((3&a)<<3)&255;return r},a.crypto&&crypto.getRandomValues&&(r=function(e){var t=new Uint8Array(e);return crypto.getRandomValues(t),t}),t.exports=r||e}()},{}],61:[function(e,t,r){function a(e,t){e[t>>5]|=128<<24-t%32,e[(t+64>>9<<4)+15]=t;for(var r=Array(80),a=1732584193,u=-271733879,c=-1732584194,p=271733878,m=-1009589776,l=0;l<e.length;l+=16){for(var d=a,y=u,h=c,b=p,f=m,S=0;80>S;S++){16>S?r[S]=e[l+S]:r[S]=s(r[S-3]^r[S-8]^r[S-14]^r[S-16],1);var g=i(i(s(a,5),o(S,u,c,p)),i(i(m,r[S]),n(S)));m=p,p=c,c=s(u,30),u=a,a=g}a=i(a,d),u=i(u,y),c=i(c,h),p=i(p,b),m=i(m,f)}return Array(a,u,c,p,m)}function o(e,t,r,a){return 20>e?t&r|~t&a:40>e?t^r^a:60>e?t&r|t&a|r&a:t^r^a}function n(e){return 20>e?1518500249:40>e?1859775393:60>e?-1894007588:-899497514}function i(e,t){var r=(65535&e)+(65535&t),a=(e>>16)+(t>>16)+(r>>16);return a<<16|65535&r}function s(e,t){return e<<t|e>>>32-t}var u=e("./helpers");t.exports=function(e){return u.hash(e,a,20,!0)}},{"./helpers":57}],62:[function(e,t,r){var a=e("./helpers"),o=function(e,t){var r=(65535&e)+(65535&t),a=(e>>16)+(t>>16)+(r>>16);return a<<16|65535&r},n=function(e,t){return e>>>t|e<<32-t},i=function(e,t){return e>>>t},s=function(e,t,r){return e&t^~e&r},u=function(e,t,r){return e&t^e&r^t&r},c=function(e){return n(e,2)^n(e,13)^n(e,22)},p=function(e){return n(e,6)^n(e,11)^n(e,25)},m=function(e){return n(e,7)^n(e,18)^i(e,3)},l=function(e){return n(e,17)^n(e,19)^i(e,10)},d=function(e,t){var r,a,n,i,d,y,h,b,f,S,g,N,I=new Array(1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298),v=new Array(1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225),k=new Array(64);e[t>>5]|=128<<24-t%32,e[(t+64>>9<<4)+15]=t;for(var f=0;f<e.length;f+=16){r=v[0],a=v[1],n=v[2],i=v[3],d=v[4],y=v[5],h=v[6],b=v[7];for(var S=0;64>S;S++)16>S?k[S]=e[S+f]:k[S]=o(o(o(l(k[S-2]),k[S-7]),m(k[S-15])),k[S-16]),g=o(o(o(o(b,p(d)),s(d,y,h)),I[S]),k[S]),N=o(c(r),u(r,a,n)),b=h,h=y,y=d,d=o(i,g),i=n,n=a,a=r,r=o(g,N);v[0]=o(r,v[0]),v[1]=o(a,v[1]),v[2]=o(n,v[2]),v[3]=o(i,v[3]),v[4]=o(d,v[4]),v[5]=o(y,v[5]),v[6]=o(h,v[6]),v[7]=o(b,v[7])}return v};t.exports=function(e){return a.hash(e,d,32,!0)}},{"./helpers":57}],63:[function(e,t,r){function a(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function o(e){return"function"==typeof e}function n(e){return"number"==typeof e}function i(e){return"object"==typeof e&&null!==e}function s(e){return void 0===e}t.exports=a,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._maxListeners=void 0,a.defaultMaxListeners=10,a.prototype.setMaxListeners=function(e){if(!n(e)||0>e||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},a.prototype.emit=function(e){var t,r,a,n,u,c;if(this._events||(this._events={}),"error"===e&&(!this._events.error||i(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;throw TypeError('Uncaught, unspecified "error" event.')}if(r=this._events[e],s(r))return!1;if(o(r))switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:for(a=arguments.length,n=new Array(a-1),u=1;a>u;u++)n[u-1]=arguments[u];r.apply(this,n)}else if(i(r)){for(a=arguments.length,n=new Array(a-1),u=1;a>u;u++)n[u-1]=arguments[u];for(c=r.slice(),a=c.length,u=0;a>u;u++)c[u].apply(this,n)}return!0},a.prototype.addListener=function(e,t){var r;if(!o(t))throw TypeError("listener must be a function");if(this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,o(t.listener)?t.listener:t),this._events[e]?i(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,i(this._events[e])&&!this._events[e].warned){var r;r=s(this._maxListeners)?a.defaultMaxListeners:this._maxListeners,r&&r>0&&this._events[e].length>r&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace())}return this},a.prototype.on=a.prototype.addListener,a.prototype.once=function(e,t){function r(){this.removeListener(e,r),a||(a=!0,t.apply(this,arguments))}if(!o(t))throw TypeError("listener must be a function");var a=!1;return r.listener=t,this.on(e,r),this},a.prototype.removeListener=function(e,t){var r,a,n,s;if(!o(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(r=this._events[e],n=r.length,a=-1,r===t||o(r.listener)&&r.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(i(r)){for(s=n;s-->0;)if(r[s]===t||r[s].listener&&r[s].listener===t){a=s;break}if(0>a)return this;1===r.length?(r.length=0,delete this._events[e]):r.splice(a,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},a.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r=this._events[e],o(r))this.removeListener(e,r);else for(;r.length;)this.removeListener(e,r[r.length-1]);return delete this._events[e],this},a.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?o(this._events[e])?[this._events[e]]:this._events[e].slice():[]},a.listenerCount=function(e,t){var r;return r=e._events&&e._events[t]?o(e._events[t])?1:e._events[t].length:0}},{}],64:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},{}],65:[function(e,t,r){function a(){}var o=t.exports={};o.nextTick=function(){var e="undefined"!=typeof window&&window.setImmediate,t="undefined"!=typeof window&&window.postMessage&&window.addEventListener;if(e)return function(e){return window.setImmediate(e)};if(t){var r=[];return window.addEventListener("message",function(e){var t=e.source;if((t===window||null===t)&&"process-tick"===e.data&&(e.stopPropagation(),r.length>0)){var a=r.shift();a()}},!0),function(e){r.push(e),window.postMessage("process-tick","*")}}return function(e){setTimeout(e,0)}}(),o.title="browser",o.browser=!0,o.env={},o.argv=[],o.on=a,o.addListener=a,o.once=a,o.off=a,o.removeListener=a,o.removeAllListeners=a,o.emit=a,o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")}},{}],66:[function(e,t,r){(function(e){!function(a){function o(e){throw RangeError(w[e])}function n(e,t){for(var r=e.length;r--;)e[r]=t(e[r]);return e}function i(e,t){return n(e.split(P),t).join(".")}function s(e){for(var t,r,a=[],o=0,n=e.length;n>o;)t=e.charCodeAt(o++),t>=55296&&56319>=t&&n>o?(r=e.charCodeAt(o++),56320==(64512&r)?a.push(((1023&t)<<10)+(1023&r)+65536):(a.push(t),o--)):a.push(t);return a}function u(e){return n(e,function(e){var t="";return e>65535&&(e-=65536,t+=B(e>>>10&1023|55296),e=56320|1023&e),t+=B(e)}).join("")}function c(e){return 10>e-48?e-22:26>e-65?e-65:26>e-97?e-97:v}function p(e,t){return e+22+75*(26>e)-((0!=t)<<5)}function m(e,t,r){var a=0;for(e=r?M(e/T):e>>1,e+=M(e/t);e>L*R>>1;a+=v)e=M(e/L);return M(a+(L+1)*e/(e+C))}function l(e){var t,r,a,n,i,s,p,l,d,y,h=[],b=e.length,f=0,S=A,g=D;for(r=e.lastIndexOf(q),0>r&&(r=0),a=0;r>a;++a)e.charCodeAt(a)>=128&&o("not-basic"),h.push(e.charCodeAt(a));for(n=r>0?r+1:0;b>n;){for(i=f,s=1,p=v;n>=b&&o("invalid-input"),l=c(e.charCodeAt(n++)),(l>=v||l>M((I-f)/s))&&o("overflow"),f+=l*s,d=g>=p?k:p>=g+R?R:p-g,!(d>l);p+=v)y=v-d,s>M(I/y)&&o("overflow"),s*=y;t=h.length+1,g=m(f-i,t,0==i),M(f/t)>I-S&&o("overflow"),S+=M(f/t),f%=t,h.splice(f++,0,S)}return u(h)}function d(e){var t,r,a,n,i,u,c,l,d,y,h,b,f,S,g,N=[];for(e=s(e),b=e.length,t=A,r=0,i=D,u=0;b>u;++u)h=e[u],128>h&&N.push(B(h));for(a=n=N.length,n&&N.push(q);b>a;){for(c=I,u=0;b>u;++u)h=e[u],h>=t&&c>h&&(c=h);for(f=a+1,c-t>M((I-r)/f)&&o("overflow"),r+=(c-t)*f,t=c,u=0;b>u;++u)if(h=e[u],t>h&&++r>I&&o("overflow"),h==t){for(l=r,d=v;y=i>=d?k:d>=i+R?R:d-i,!(y>l);d+=v)g=l-y,S=v-y,N.push(B(p(y+g%S,0))),l=M(g/S);N.push(B(p(l,0))),i=m(r,f,a==n),r=0,++a}++r,++t}return N.join("")}function y(e){return i(e,function(e){return x.test(e)?l(e.slice(4).toLowerCase()):e})}function h(e){return i(e,function(e){return E.test(e)?"xn--"+d(e):e})}var b="object"==typeof r&&r,f="object"==typeof t&&t&&t.exports==b&&t,S="object"==typeof e&&e;(S.global===S||S.window===S)&&(a=S);var g,N,I=2147483647,v=36,k=1,R=26,C=38,T=700,D=72,A=128,q="-",x=/^xn--/,E=/[^ -~]/,P=/\x2E|\u3002|\uFF0E|\uFF61/g,w={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},L=v-k,M=Math.floor,B=String.fromCharCode;if(g={version:"1.2.4",ucs2:{decode:s,encode:u},decode:l,encode:d,toASCII:h,toUnicode:y},"function"==typeof define&&"object"==typeof define.amd&&define.amd)define("punycode",function(){return g});else if(b&&!b.nodeType)if(f)f.exports=g;else for(N in g)g.hasOwnProperty(N)&&(b[N]=g[N]);else a.punycode=g}(this)}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],67:[function(e,t,r){"use strict";function a(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,n){t=t||"&",r=r||"=";var i={};if("string"!=typeof e||0===e.length)return i;var s=/\+/g;e=e.split(t);var u=1e3;n&&"number"==typeof n.maxKeys&&(u=n.maxKeys);var c=e.length;u>0&&c>u&&(c=u);for(var p=0;c>p;++p){var m,l,d,y,h=e[p].replace(s,"%20"),b=h.indexOf(r);b>=0?(m=h.substr(0,b),l=h.substr(b+1)):(m=h,l=""),d=decodeURIComponent(m),y=decodeURIComponent(l),a(i,d)?o(i[d])?i[d].push(y):i[d]=[i[d],y]:i[d]=y}return i};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{}],68:[function(e,t,r){"use strict";function a(e,t){if(e.map)return e.map(t);for(var r=[],a=0;a<e.length;a++)r.push(t(e[a],a));return r}var o=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,s){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?a(i(e),function(a){var i=encodeURIComponent(o(a))+r;return n(e[a])?e[a].map(function(e){return i+encodeURIComponent(o(e))}).join(t):i+encodeURIComponent(o(e[a]))}).join(t):s?encodeURIComponent(o(s))+r+encodeURIComponent(o(e)):""};var n=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}},{}],69:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode"),r.encode=r.stringify=e("./encode")},{"./decode":67,"./encode":68}],70:[function(e,t,r){function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function o(e,t,r){if(e&&c(e)&&e instanceof a)return e;var o=new a;return o.parse(e,t,r),o}function n(e){return u(e)&&(e=o(e)),e instanceof a?e.format():a.prototype.format.call(e)}function i(e,t){return o(e,!1,!0).resolve(t)}function s(e,t){return e?o(e,!1,!0).resolveObject(t):t}function u(e){return"string"==typeof e}function c(e){return"object"==typeof e&&null!==e}function p(e){return null===e}function m(e){return null==e}var l=e("punycode");r.parse=o,r.resolve=i,r.resolveObject=s,r.format=n,r.Url=a;var d=/^([a-z0-9.+-]+:)/i,y=/:[0-9]*$/,h=["<",">",'"',"`"," ","\r","\n","	"],b=["{","}","|","\\","^","`"].concat(h),f=["'"].concat(b),S=["%","/","?",";","#"].concat(f),g=["/","?","#"],N=255,I=/^[a-z0-9A-Z_-]{0,63}$/,v=/^([a-z0-9A-Z_-]{0,63})(.*)$/,k={javascript:!0,"javascript:":!0},R={javascript:!0,"javascript:":!0},C={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},T=e("querystring");a.prototype.parse=function(e,t,r){if(!u(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e;a=a.trim();var o=d.exec(a);if(o){o=o[0];var n=o.toLowerCase();this.protocol=n,a=a.substr(o.length)}if(r||o||a.match(/^\/\/[^@\/]+@[^@\/]+/)){var i="//"===a.substr(0,2);!i||o&&R[o]||(a=a.substr(2),this.slashes=!0)}if(!R[o]&&(i||o&&!C[o])){for(var s=-1,c=0;c<g.length;c++){var p=a.indexOf(g[c]);-1!==p&&(-1===s||s>p)&&(s=p)}var m,y;y=-1===s?a.lastIndexOf("@"):a.lastIndexOf("@",s),-1!==y&&(m=a.slice(0,y),a=a.slice(y+1),this.auth=decodeURIComponent(m)),s=-1;for(var c=0;c<S.length;c++){var p=a.indexOf(S[c]);-1!==p&&(-1===s||s>p)&&(s=p)}-1===s&&(s=a.length),this.host=a.slice(0,s),a=a.slice(s),this.parseHost(),this.hostname=this.hostname||"";var h="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!h)for(var b=this.hostname.split(/\./),c=0,D=b.length;D>c;c++){var A=b[c];if(A&&!A.match(I)){for(var q="",x=0,E=A.length;E>x;x++)q+=A.charCodeAt(x)>127?"x":A[x];if(!q.match(I)){var P=b.slice(0,c),w=b.slice(c+1),L=A.match(v);L&&(P.push(L[1]),w.unshift(L[2])),w.length&&(a="/"+w.join(".")+a),this.hostname=P.join(".");break}}}if(this.hostname.length>N?this.hostname="":this.hostname=this.hostname.toLowerCase(),!h){for(var M=this.hostname.split("."),B=[],c=0;c<M.length;++c){var U=M[c];B.push(U.match(/[^A-Za-z0-9_-]/)?"xn--"+l.encode(U):U)}this.hostname=B.join(".")}var _=this.port?":"+this.port:"",V=this.hostname||"";this.host=V+_,this.href+=this.host,h&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==a[0]&&(a="/"+a))}if(!k[n])for(var c=0,D=f.length;D>c;c++){var z=f[c],F=encodeURIComponent(z);F===z&&(F=escape(z)),a=a.split(z).join(F)}var O=a.indexOf("#");-1!==O&&(this.hash=a.substr(O),a=a.slice(0,O));var G=a.indexOf("?");if(-1!==G?(this.search=a.substr(G),this.query=a.substr(G+1),t&&(this.query=T.parse(this.query)),a=a.slice(0,G)):t&&(this.search="",
this.query={}),a&&(this.pathname=a),C[n]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var _=this.pathname||"",U=this.search||"";this.path=_+U}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=encodeURIComponent(e),e=e.replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",a=this.hash||"",o=!1,n="";this.host?o=e+this.host:this.hostname&&(o=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(o+=":"+this.port)),this.query&&c(this.query)&&Object.keys(this.query).length&&(n=T.stringify(this.query));var i=this.search||n&&"?"+n||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||C[t])&&o!==!1?(o="//"+(o||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):o||(o=""),a&&"#"!==a.charAt(0)&&(a="#"+a),i&&"?"!==i.charAt(0)&&(i="?"+i),r=r.replace(/[?#]/g,function(e){return encodeURIComponent(e)}),i=i.replace("#","%23"),t+o+r+i+a},a.prototype.resolve=function(e){return this.resolveObject(o(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(u(e)){var t=new a;t.parse(e,!1,!0),e=t}var r=new a;if(Object.keys(this).forEach(function(e){r[e]=this[e]},this),r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol)return Object.keys(e).forEach(function(t){"protocol"!==t&&(r[t]=e[t])}),C[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r;if(e.protocol&&e.protocol!==r.protocol){if(!C[e.protocol])return Object.keys(e).forEach(function(t){r[t]=e[t]}),r.href=r.format(),r;if(r.protocol=e.protocol,e.host||R[e.protocol])r.pathname=e.pathname;else{for(var o=(e.pathname||"").split("/");o.length&&!(e.host=o.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==o[0]&&o.unshift(""),o.length<2&&o.unshift(""),r.pathname=o.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var n=r.pathname||"",i=r.search||"";r.path=n+i}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var s=r.pathname&&"/"===r.pathname.charAt(0),c=e.host||e.pathname&&"/"===e.pathname.charAt(0),l=c||s||r.host&&e.pathname,d=l,y=r.pathname&&r.pathname.split("/")||[],o=e.pathname&&e.pathname.split("/")||[],h=r.protocol&&!C[r.protocol];if(h&&(r.hostname="",r.port=null,r.host&&(""===y[0]?y[0]=r.host:y.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===o[0]?o[0]=e.host:o.unshift(e.host)),e.host=null),l=l&&(""===o[0]||""===y[0])),c)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,y=o;else if(o.length)y||(y=[]),y.pop(),y=y.concat(o),r.search=e.search,r.query=e.query;else if(!m(e.search)){if(h){r.hostname=r.host=y.shift();var b=r.host&&r.host.indexOf("@")>0?r.host.split("@"):!1;b&&(r.auth=b.shift(),r.host=r.hostname=b.shift())}return r.search=e.search,r.query=e.query,p(r.pathname)&&p(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!y.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var f=y.slice(-1)[0],S=(r.host||e.host)&&("."===f||".."===f)||""===f,g=0,N=y.length;N>=0;N--)f=y[N],"."==f?y.splice(N,1):".."===f?(y.splice(N,1),g++):g&&(y.splice(N,1),g--);if(!l&&!d)for(;g--;g)y.unshift("..");!l||""===y[0]||y[0]&&"/"===y[0].charAt(0)||y.unshift(""),S&&"/"!==y.join("/").substr(-1)&&y.push("");var I=""===y[0]||y[0]&&"/"===y[0].charAt(0);if(h){r.hostname=r.host=I?"":y.length?y.shift():"";var b=r.host&&r.host.indexOf("@")>0?r.host.split("@"):!1;b&&(r.auth=b.shift(),r.host=r.hostname=b.shift())}return l=l||r.host&&y.length,l&&!I&&y.unshift(""),y.length?r.pathname=y.join("/"):(r.pathname=null,r.path=null),p(r.pathname)&&p(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},a.prototype.parseHost=function(){var e=this.host,t=y.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},{punycode:66,querystring:69}],71:[function(e,t,r){t.exports=function(e){return e&&"object"==typeof e&&"function"==typeof e.copy&&"function"==typeof e.fill&&"function"==typeof e.readUInt8}},{}],72:[function(e,t,r){(function(t,a){function o(e,t){var a={seen:[],stylize:i};return arguments.length>=3&&(a.depth=arguments[2]),arguments.length>=4&&(a.colors=arguments[3]),h(t)?a.showHidden=t:t&&r._extend(a,t),I(a.showHidden)&&(a.showHidden=!1),I(a.depth)&&(a.depth=2),I(a.colors)&&(a.colors=!1),I(a.customInspect)&&(a.customInspect=!0),a.colors&&(a.stylize=n),u(a,e,a.depth)}function n(e,t){var r=o.styles[t];return r?"["+o.colors[r][0]+"m"+e+"["+o.colors[r][1]+"m":e}function i(e,t){return e}function s(e){var t={};return e.forEach(function(e,r){t[e]=!0}),t}function u(e,t,a){if(e.customInspect&&t&&T(t.inspect)&&t.inspect!==r.inspect&&(!t.constructor||t.constructor.prototype!==t)){var o=t.inspect(a,e);return g(o)||(o=u(e,o,a)),o}var n=c(e,t);if(n)return n;var i=Object.keys(t),h=s(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),C(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return p(t);if(0===i.length){if(T(t)){var b=t.name?": "+t.name:"";return e.stylize("[Function"+b+"]","special")}if(v(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(R(t))return e.stylize(Date.prototype.toString.call(t),"date");if(C(t))return p(t)}var f="",S=!1,N=["{","}"];if(y(t)&&(S=!0,N=["[","]"]),T(t)){var I=t.name?": "+t.name:"";f=" [Function"+I+"]"}if(v(t)&&(f=" "+RegExp.prototype.toString.call(t)),R(t)&&(f=" "+Date.prototype.toUTCString.call(t)),C(t)&&(f=" "+p(t)),0===i.length&&(!S||0==t.length))return N[0]+f+N[1];if(0>a)return v(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special");e.seen.push(t);var k;return k=S?m(e,t,a,h,i):i.map(function(r){return l(e,t,a,h,r,S)}),e.seen.pop(),d(k,f,N)}function c(e,t){if(I(t))return e.stylize("undefined","undefined");if(g(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return S(t)?e.stylize(""+t,"number"):h(t)?e.stylize(""+t,"boolean"):b(t)?e.stylize("null","null"):void 0}function p(e){return"["+Error.prototype.toString.call(e)+"]"}function m(e,t,r,a,o){for(var n=[],i=0,s=t.length;s>i;++i)E(t,String(i))?n.push(l(e,t,r,a,String(i),!0)):n.push("");return o.forEach(function(o){o.match(/^\d+$/)||n.push(l(e,t,r,a,o,!0))}),n}function l(e,t,r,a,o,n){var i,s,c;if(c=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]},c.get?s=c.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):c.set&&(s=e.stylize("[Setter]","special")),E(a,o)||(i="["+o+"]"),s||(e.seen.indexOf(c.value)<0?(s=b(r)?u(e,c.value,null):u(e,c.value,r-1),s.indexOf("\n")>-1&&(s=n?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n"))):s=e.stylize("[Circular]","special")),I(i)){if(n&&o.match(/^\d+$/))return s;i=JSON.stringify(""+o),i.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(i=i.substr(1,i.length-2),i=e.stylize(i,"name")):(i=i.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),i=e.stylize(i,"string"))}return i+": "+s}function d(e,t,r){var a=0,o=e.reduce(function(e,t){return a++,t.indexOf("\n")>=0&&a++,e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0);return o>60?r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]:r[0]+t+" "+e.join(", ")+" "+r[1]}function y(e){return Array.isArray(e)}function h(e){return"boolean"==typeof e}function b(e){return null===e}function f(e){return null==e}function S(e){return"number"==typeof e}function g(e){return"string"==typeof e}function N(e){return"symbol"==typeof e}function I(e){return void 0===e}function v(e){return k(e)&&"[object RegExp]"===A(e)}function k(e){return"object"==typeof e&&null!==e}function R(e){return k(e)&&"[object Date]"===A(e)}function C(e){return k(e)&&("[object Error]"===A(e)||e instanceof Error)}function T(e){return"function"==typeof e}function D(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||"undefined"==typeof e}function A(e){return Object.prototype.toString.call(e)}function q(e){return 10>e?"0"+e.toString(10):e.toString(10)}function x(){var e=new Date,t=[q(e.getHours()),q(e.getMinutes()),q(e.getSeconds())].join(":");return[e.getDate(),M[e.getMonth()],t].join(" ")}function E(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var P=/%[sdj%]/g;r.format=function(e){if(!g(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(o(arguments[r]));return t.join(" ")}for(var r=1,a=arguments,n=a.length,i=String(e).replace(P,function(e){if("%"===e)return"%";if(r>=n)return e;switch(e){case"%s":return String(a[r++]);case"%d":return Number(a[r++]);case"%j":try{return JSON.stringify(a[r++])}catch(t){return"[Circular]"}default:return e}}),s=a[r];n>r;s=a[++r])i+=b(s)||!k(s)?" "+s:" "+o(s);return i},r.deprecate=function(e,o){function n(){if(!i){if(t.throwDeprecation)throw new Error(o);t.traceDeprecation?console.trace(o):console.error(o),i=!0}return e.apply(this,arguments)}if(I(a.process))return function(){return r.deprecate(e,o).apply(this,arguments)};if(t.noDeprecation===!0)return e;var i=!1;return n};var w,L={};r.debuglog=function(e){if(I(w)&&(w=t.env.NODE_DEBUG||""),e=e.toUpperCase(),!L[e])if(new RegExp("\\b"+e+"\\b","i").test(w)){var a=t.pid;L[e]=function(){var t=r.format.apply(r,arguments);console.error("%s %d: %s",e,a,t)}}else L[e]=function(){};return L[e]},r.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow","boolean":"yellow",undefined:"grey","null":"bold",string:"green",date:"magenta",regexp:"red"},r.isArray=y,r.isBoolean=h,r.isNull=b,r.isNullOrUndefined=f,r.isNumber=S,r.isString=g,r.isSymbol=N,r.isUndefined=I,r.isRegExp=v,r.isObject=k,r.isDate=R,r.isError=C,r.isFunction=T,r.isPrimitive=D,r.isBuffer=e("./support/isBuffer");var M=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];r.log=function(){console.log("%s - %s",x(),r.format.apply(r,arguments))},r.inherits=e("inherits"),r._extend=function(e,t){if(!t||!k(t))return e;for(var r=Object.keys(t),a=r.length;a--;)e[r[a]]=t[r[a]];return e}}).call(this,e("FWaASH"),"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./support/isBuffer":71,FWaASH:65,inherits:64}],73:[function(e,t,r){(function(){var r,a;a=e("./XMLFragment"),r=function(){function e(e,t,r){var o,n,i;if(this.children=[],this.rootObject=null,this.is(e,"Object")&&(i=[e,t],t=i[0],r=i[1],e=null),null!=e&&(e=""+e||"",null==t&&(t={version:"1.0"})),null!=t&&null==t.version)throw new Error("Version number is required");if(null!=t){if(t.version=""+t.version||"",!t.version.match(/1\.[0-9]+/))throw new Error("Invalid version number: "+t.version);if(o={version:t.version},null!=t.encoding){if(t.encoding=""+t.encoding||"",!t.encoding.match(/[A-Za-z](?:[A-Za-z0-9._-]|-)*/))throw new Error("Invalid encoding: "+t.encoding);o.encoding=t.encoding}null!=t.standalone&&(o.standalone=t.standalone?"yes":"no"),n=new a(this,"?xml",o),this.children.push(n)}null!=r&&(o={},null!=e&&(o.name=e),null!=r.ext&&(r.ext=""+r.ext||"",o.ext=r.ext),n=new a(this,"!DOCTYPE",o),this.children.push(n)),null!=e&&this.begin(e)}return e.prototype.begin=function(t,r,o){var n,i;if(null==t)throw new Error("Root element needs a name");return this.rootObject&&(this.children=[],this.rootObject=null),null!=r?(n=new e(t,r,o),n.root()):(t=""+t||"",i=new a(this,t,{}),i.isRoot=!0,i.documentObject=this,this.children.push(i),this.rootObject=i,i)},e.prototype.root=function(){return this.rootObject},e.prototype.end=function(e){return toString(e)},e.prototype.toString=function(e){var t,r,a,o,n;for(r="",n=this.children,a=0,o=n.length;o>a;a++)t=n[a],r+=t.toString(e);return r},e.prototype.is=function(e,t){var r;return r=Object.prototype.toString.call(e).slice(8,-1),null!=e&&r===t},e}(),t.exports=r}).call(this)},{"./XMLFragment":74}],74:[function(e,t,r){(function(){var e,r={}.hasOwnProperty;e=function(){function e(e,t,r,a){this.isRoot=!1,this.documentObject=null,this.parent=e,this.name=t,this.attributes=r,this.value=a,this.children=[]}return e.prototype.element=function(t,a,o){var n,i,s,u,c;if(null==t)throw new Error("Missing element name");t=""+t||"",this.assertLegalChar(t),null==a&&(a={}),this.is(a,"String")&&this.is(o,"Object")?(u=[o,a],a=u[0],o=u[1]):this.is(a,"String")&&(c=[{},a],a=c[0],o=c[1]);for(i in a)r.call(a,i)&&(s=a[i],s=""+s||"",a[i]=this.escape(s));return n=new e(this,t,a),null!=o&&(o=""+o||"",o=this.escape(o),this.assertLegalChar(o),n.raw(o)),this.children.push(n),n},e.prototype.insertBefore=function(t,a,o){var n,i,s,u,c,p;if(this.isRoot)throw new Error("Cannot insert elements at root level");if(null==t)throw new Error("Missing element name");t=""+t||"",this.assertLegalChar(t),null==a&&(a={}),this.is(a,"String")&&this.is(o,"Object")?(c=[o,a],a=c[0],o=c[1]):this.is(a,"String")&&(p=[{},a],a=p[0],o=p[1]);for(s in a)r.call(a,s)&&(u=a[s],u=""+u||"",a[s]=this.escape(u));return n=new e(this.parent,t,a),null!=o&&(o=""+o||"",o=this.escape(o),this.assertLegalChar(o),n.raw(o)),i=this.parent.children.indexOf(this),this.parent.children.splice(i,0,n),n},e.prototype.insertAfter=function(t,a,o){var n,i,s,u,c,p;if(this.isRoot)throw new Error("Cannot insert elements at root level");if(null==t)throw new Error("Missing element name");t=""+t||"",this.assertLegalChar(t),null==a&&(a={}),this.is(a,"String")&&this.is(o,"Object")?(c=[o,a],a=c[0],o=c[1]):this.is(a,"String")&&(p=[{},a],a=p[0],o=p[1]);for(s in a)r.call(a,s)&&(u=a[s],u=""+u||"",a[s]=this.escape(u));return n=new e(this.parent,t,a),null!=o&&(o=""+o||"",o=this.escape(o),this.assertLegalChar(o),n.raw(o)),i=this.parent.children.indexOf(this),this.parent.children.splice(i+1,0,n),n},e.prototype.remove=function(){var e,t;if(this.isRoot)throw new Error("Cannot remove the root element");return e=this.parent.children.indexOf(this),[].splice.apply(this.parent.children,[e,e-e+1].concat(t=[])),t,this.parent},e.prototype.text=function(t){var r;if(null==t)throw new Error("Missing element text");return t=""+t||"",t=this.escape(t),this.assertLegalChar(t),r=new e(this,"",{},t),this.children.push(r),this},e.prototype.cdata=function(t){var r;if(null==t)throw new Error("Missing CDATA text");if(t=""+t||"",this.assertLegalChar(t),t.match(/]]>/))throw new Error("Invalid CDATA text: "+t);return r=new e(this,"",{},"<![CDATA["+t+"]]>"),this.children.push(r),this},e.prototype.comment=function(t){var r;if(null==t)throw new Error("Missing comment text");if(t=""+t||"",t=this.escape(t),this.assertLegalChar(t),t.match(/--/))throw new Error("Comment text cannot contain double-hypen: "+t);return r=new e(this,"",{},"<!-- "+t+" -->"),this.children.push(r),this},e.prototype.raw=function(t){var r;if(null==t)throw new Error("Missing raw text");return t=""+t||"",r=new e(this,"",{},t),this.children.push(r),this},e.prototype.up=function(){if(this.isRoot)throw new Error("This node has no parent. Use doc() if you need to get the document object.");return this.parent},e.prototype.root=function(){var e;if(this.isRoot)return this;for(e=this.parent;!e.isRoot;)e=e.parent;return e},e.prototype.document=function(){return this.root().documentObject},e.prototype.end=function(e){return this.document().toString(e)},e.prototype.prev=function(){var e;if(this.isRoot)throw new Error("Root node has no siblings");if(e=this.parent.children.indexOf(this),1>e)throw new Error("Already at the first node");return this.parent.children[e-1]},e.prototype.next=function(){var e;if(this.isRoot)throw new Error("Root node has no siblings");if(e=this.parent.children.indexOf(this),-1===e||e===this.parent.children.length-1)throw new Error("Already at the last node");return this.parent.children[e+1]},e.prototype.clone=function(t){var r;return r=new e(this.parent,this.name,this.attributes,this.value),t&&this.children.forEach(function(e){var a;return a=e.clone(t),a.parent=r,r.children.push(a)}),r},e.prototype.importXMLBuilder=function(e){var t;return t=e.root().clone(!0),t.parent=this,this.children.push(t),t.isRoot=!1,this},e.prototype.attribute=function(e,t){var r;if(null==e)throw new Error("Missing attribute name");if(null==t)throw new Error("Missing attribute value");return e=""+e||"",t=""+t||"",null==(r=this.attributes)&&(this.attributes={}),this.attributes[e]=this.escape(t),this},e.prototype.removeAttribute=function(e){if(null==e)throw new Error("Missing attribute name");return e=""+e||"",delete this.attributes[e],this},e.prototype.toString=function(e,t){var r,a,o,n,i,s,u,c,p,m,l,d;s=null!=e&&e.pretty||!1,n=null!=e&&e.indent||"  ",i=null!=e&&e.newline||"\n",t||(t=0),c=new Array(t+1).join(n),u="",s&&(u+=c),u+=null==this.value?"<"+this.name:""+this.value,l=this.attributes;for(r in l)a=l[r],u+="!DOCTYPE"===this.name?" "+a:" "+r+'="'+a+'"';if(0===this.children.length)null==this.value&&(u+="?xml"===this.name?"?>":"!DOCTYPE"===this.name?">":"/>"),s&&(u+=i);else if(s&&1===this.children.length&&this.children[0].value)u+=">",u+=this.children[0].value,u+="</"+this.name+">",u+=i;else{for(u+=">",s&&(u+=i),d=this.children,p=0,m=d.length;m>p;p++)o=d[p],u+=o.toString(e,t+1);s&&(u+=c),u+="</"+this.name+">",s&&(u+=i)}return u},e.prototype.escape=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")},e.prototype.assertLegalChar=function(e){var t,r;if(t=/[\u0000-\u0008\u000B-\u000C\u000E-\u001F\uD800-\uDFFF\uFFFE-\uFFFF]/,r=e.match(t))throw new Error("Invalid character ("+r+") in string: "+e)},e.prototype.is=function(e,t){var r;return r=Object.prototype.toString.call(e).slice(8,-1),null!=e&&r===t},e.prototype.ele=function(e,t,r){return this.element(e,t,r)},e.prototype.txt=function(e){return this.text(e)},e.prototype.dat=function(e){return this.cdata(e)},e.prototype.att=function(e,t){return this.attribute(e,t)},e.prototype.com=function(e){return this.comment(e)},e.prototype.doc=function(){return this.document()},e.prototype.e=function(e,t,r){return this.element(e,t,r)},e.prototype.t=function(e){return this.text(e)},e.prototype.d=function(e){return this.cdata(e)},e.prototype.a=function(e,t){return this.attribute(e,t)},e.prototype.c=function(e){return this.comment(e)},e.prototype.r=function(e){return this.raw(e)},e.prototype.u=function(){return this.up()},e}(),t.exports=e}).call(this)},{}],75:[function(e,t,r){(function(){var r;r=e("./XMLBuilder"),t.exports.create=function(e,t,a){return null!=e?new r(e,t,a).root():new r}}).call(this)},{"./XMLBuilder":73}]},{},[1]);
