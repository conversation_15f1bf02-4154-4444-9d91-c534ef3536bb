angular.module('viewUserContacts.controllers', [])

    .controller('viewUserContactsCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';


    $scope.pageLimit;    
    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
    };
    $scope.filterSearch= false;


      $scope.allContactsCount = function(){    
        APIService.setData({
            req_url: PrefixUrl + '/userContacts/userContactsCount'
        }).then(function(resp) {
          console.log("====resp======",resp);
          $scope.totalContacts = resp.data[0].myCount;
        });
      }


      $scope.filterUserContactsAllCount = function(){    
        APIService.setData({
            req_url: PrefixUrl + '/userContacts/filterUserContactsAllCount',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number}
        }).then(function(resp) {
          console.log("====resp======",resp);
          $scope.totalContacts = resp.data[0].myCount;
        });
      }


      $scope.showForUserCount = function(){    
        APIService.setData({
            req_url: PrefixUrl + '/userContacts/showForUserCount',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number, user_id:$scope.userObject.user_id}
        }).then(function(resp) {
          console.log("====resp======",resp);
          $scope.totalContacts = resp.data[0].myCount;
        });
      }


      $scope.filterUserContactsCount = function(){    
        APIService.setData({
            req_url: PrefixUrl + '/userContacts/filterUserContactsCount',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number, user_id:$scope.userObject.user_id}
        }).then(function(resp) {
          console.log("====resp======",resp);
          $scope.totalContacts = resp.data[0].myCount;
        });
      }


      

     console.log('aaaaaaaaaaaaaasdfsd1111111 ' ,$stateParams)

    $scope.$watch('settings.pageLimit', function (pageLimit) {

     if ($stateParams.data != "") {

       $scope.userObject = JSON.parse($stateParams.data);
         $scope.$watch('settings.currentPage', function (value) {
            $scope.pageLimit= $scope.settings.pageLimit;

            console.log('ffffffffffff ',$scope.filterSearch);
            if($scope.filterSearch){
                APIService.setData({
                    req_url: PrefixUrl + '/userContacts/filterUserContacts' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,user_id: $scope.userObject.user_id, name:$scope.name,phone_number:$scope.phone_number } 
                }).then(function(resp) {
                  $scope.contactsList= resp.data;
                  $scope.filterUserContactsCount();
                },function(resp) {
                 
                });
              
            }else{
              console.log('currentPage'+$scope.settings.currentPage)  
              // console.log('userDetailslll='+$scope.userDetails.length)

              APIService.setData({
                  req_url: PrefixUrl + '/userContacts/showForUser/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, user_id:$scope.userObject.user_id}
              }).then(function(resp) {
                	console.log("====resp======",resp);
              	$scope.contactsList=resp.data;
                $scope.showForUserCount();
                 },function(resp) {
                    // This block execute in case of error.
              });
            }

          });


      }else{
        console.log('aaaaaaaaaaa ')
            
        // $scope.userObject = JSON.parse($stateParams.data);
        $scope.$watch('settings.currentPage', function (value) {

            $scope.pageLimit= $scope.settings.pageLimit;

            if($scope.filterSearch){
                APIService.setData({
                    req_url: PrefixUrl + '/userContacts/filterUserContactsAll/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number}
                }).then(function(resp) {
                    console.log("====resp======",resp);
                  $scope.contactsList=resp.data;
                  $scope.filterUserContactsAllCount();
                   },function(resp) {
                      // This block execute in case of error.     
                });
            }else{              
                APIService.setData({
                    req_url: PrefixUrl + '/userContacts/showAll/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====resp======",resp);
                  $scope.contactsList=resp.data;
                  $scope.allContactsCount();

                   },function(resp) {
                      // This block execute in case of error.            
                });
            }
        });
      }
    });

    $scope.filterUserContacts = function(user) {

      $scope.filterSearch =true;

        if ($scope.name) {
            var name= $scope.name;
        }else{
            var name= null;
        }

        if ($scope.phone_number) {
            var phone_number= $scope.phone_number;
        }else{
            var phone_number= null;
        }

       
        if ($stateParams.data != "") {
          APIService.setData({
              req_url: PrefixUrl + '/userContacts/filterUserContacts' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, user_id: $scope.userObject.user_id , name:$scope.name,phone_number:$scope.phone_number } 
          }).then(function(resp) {
            $scope.contactsList= resp.data;
            $scope.filterUserContactsCount();
          },function(resp) {
           
          });
        }else{
          APIService.setData({
              req_url: PrefixUrl + '/userContacts/filterUserContactsAll' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, name:$scope.name,phone_number:$scope.phone_number } 
          }).then(function(resp) {
            $scope.contactsList= resp.data;
            $scope.filterUserContactsAllCount();


          },function(resp) {
           
          });
        }
    };

    $scope.exportAsExcel = function () {
      console.log('1111111111')
        var blob = new Blob([document.getElementById('exportable').innerHTML], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
        });
        saveAs(blob, new Date().toISOString().slice(0, 10)+"withdraw.xls");
    };
})
