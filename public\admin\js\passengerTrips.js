angular.module('passengerTrips.controllers', [])

    .controller('passengerTripsCtrl', function ($scope,$state, APIService) {

        $scope.page = 'main';
        $scope.upcomingTrip=true;
        $scope.cancelTrip=false;
        $scope.pendingTrip=false;
        $scope.boostedTrip=false;
        $scope.tripHistory=false;
        $scope.bookingRequest=false;
        $scope.count=0;
        $scope.upcomingTripsData;
        $scope.pendingTripsData;
        $scope.bookingRequestsData;
        $scope.cancelTripsData;
        $scope.boostedTripsData;
        $scope.tripsHistoryData;
        $scope.origin;
        $scope.destination;
        $scope.buisness_name;
        $scope.filterSearch= false;
        $scope.filterSearchUpcomingTrip= false;
        $scope.filterSearchPendingTrip= false;
        $scope.filterSearchcancelTrip= false;
        $scope.filterSearchBoostedTrip= false;
        $scope.filterSearchBookingRequest= false;




        
        $scope.settings = {
          currentPage: 0,
          offset: 0,
          pageLimit: 10,
          pageLimits: [2, 5, 10,20,100]
        };
        

        


        // $scope.currentDate= new Date();
        $scope.currentDate= Date.parse(new Date());

        // $scope.getProductDetails = function(){
        //     APIService.getData({ req_url: PrefixUrl + "/trip"}).then(function (res) {
        //         console.log(res)
        //         $scope.tripDetails=res.data;
        //            $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.accepted_user_id && trp.status =='ACCEPTED') {
        //                     // $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
        //                     trp.trp_time= Date.parse(trp.time);
        //                 }
        //             });

        //         console.log($scope.tripDetails)
        //             $scope.upcomingTripsCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.accepted_user_id && trp.status =='ACCEPTED' && trp.trp_time >= $scope.currentDate) {
        //                     $scope.upcomingTripsCount = $scope.upcomingTripsCount + 1;                    
        //                 }
        //             });

        //             $scope.pendingTripsCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.status =='ACTIVE'  && trp.trp_time >= $scope.currentDate) {
        //                     $scope.pendingTripsCount = $scope.pendingTripsCount + 1;                    
        //                 }
        //             });

        //             $scope.bookingRequestCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
        //                     $scope.bookingRequestCount = $scope.bookingRequestCount + 1;                    
        //                 }
        //             });

        //             $scope.tripHistoryCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 // if (!trp.booking_request.length==0 && (trp.status =='ACTIVE')) {
        //                     $scope.tripHistoryCount = $scope.tripHistoryCount + 1;                    
        //                 // }
        //             });

        //             $scope.boostedTripCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.is_trip_boost == true) {
        //                     $scope.boostedTripCount = $scope.boostedTripCount + 1;                    
        //                 }
        //             });

        //             $scope.cancelTripCount=0;
        //             $scope.tripDetails.forEach(function (trp, k) {                
        //                 if (trp.status == 'CANCELLED') {
        //                     $scope.cancelTripCount = $scope.cancelTripCount + 1;                    
        //                 }
        //             });
        //             // alert($scope.upcomingTrips);
        //     },function(er){
        //         localStorage.removeItem("UserDeatails");
        //         localStorage.removeItem("token");
        //         $state.go('login');
        //     })
    
        // }
        // $scope.getProductDetails();


    $scope.checkPagination = function(id) {

        $scope.$watch('settings.pageLimit', function (pageLimit) {
            console.log('pageLimits'+pageLimit)
            $scope.pageLimit= pageLimit;
            console.log('upcomingTrip---',$scope.filterSearchUpcomingTrip)
                if ($scope.upcomingTrip) {
                    if ($scope.filterSearchUpcomingTrip) {
                        APIService.setData({
                            req_url: PrefixUrl + '/trip/filterTripsUpcomingPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                        }).then(function(resp) {
                          $scope.upcomingTripsData= resp.data;
                        },function(resp) {
                         
                        });

                      var userData=localStorage.getItem('UserDeatails');
                      var parsedUser= JSON.parse(userData);
                      // console.log(parsedUser.user_details)
                      if (parsedUser == null || parsedUser.user_details.role != 'admin') {
                        localStorage.removeItem("UserDeatails");
                        localStorage.removeItem("token");
                        $state.go('login');
                      }
                    }else{
                      APIService.setData({
                          req_url: PrefixUrl + '/trip/upcomingTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                      }).then(function(resp) {
                        console.log("====respPagination======",resp);
                        $scope.upcomingTripsData=resp.data;
                         },function(resp) {
                            // This block execute in case of error.
                              localStorage.removeItem("UserDeatails");
                              localStorage.removeItem("token");
                              $state.go('login');
                      });

                      var userData=localStorage.getItem('UserDeatails');
                      var parsedUser= JSON.parse(userData);
                      // console.log(parsedUser.user_details);
                      if (parsedUser == null || parsedUser.user_details.role != 'admin') {
                        localStorage.removeItem("UserDeatails");
                        localStorage.removeItem("token");
                        $state.go('login');
                      }
                }
              }

            else if ($scope.tripHistory) {
                if($scope.filterSearch){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterTripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data
                    $scope.totalTripHistory= resp.data.length; 
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/tripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            else if ($scope.pendingTrip) {
                if($scope.filterSearchPendingTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterPendingTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.pendingTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/pendingTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.TripsLength= res.data.length;
                      $scope.pendingTripsData= res.data;

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            else if ($scope.cancelTrip) {
                if($scope.filterSearchcancelTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterCancelTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.cancelTripsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/cancelTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.cancelTripsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            else if ($scope.boostedTrip) {
                if($scope.filterSearchBoostedTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterBoostedTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.boostedTripsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/boostedTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.boostedTripsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            else if ($scope.bookingRequest) {
                if($scope.filterSearchBookingRequest){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterBookingRequestPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.bookingRequestsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/bookingRequestsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.bookingRequestsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            
          }
          );


          $scope.$watch('settings.currentPage', function (value) {
            console.log('currentPage'+$scope.settings.currentPage) 
            console.log('upcomingTrip---1',$scope.filterSearchUpcomingTrip)


             if ($scope.upcomingTrip) {
                    if ($scope.filterSearchUpcomingTrip) {
                        APIService.setData({
                            req_url: PrefixUrl + '/trip/filterTripsUpcomingPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                        }).then(function(resp) {
                          $scope.upcomingTripsData= resp.data;
                        },function(resp) {
                         
                        });

                      var userData=localStorage.getItem('UserDeatails');
                      var parsedUser= JSON.parse(userData);
                      // console.log(parsedUser.user_details)
                      if (parsedUser == null || parsedUser.user_details.role != 'admin') {
                        localStorage.removeItem("UserDeatails");
                        localStorage.removeItem("token");
                        $state.go('login');
                      }
                    }else{
                      APIService.setData({
                          req_url: PrefixUrl + '/trip/upcomingTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                      }).then(function(resp) {
                        console.log("====respPagination======",resp);
                        $scope.upcomingTripsData=resp.data;
                         },function(resp) {
                            // This block execute in case of error.
                              localStorage.removeItem("UserDeatails");
                              localStorage.removeItem("token");
                              $state.go('login');
                      });

                      var userData=localStorage.getItem('UserDeatails');
                      var parsedUser= JSON.parse(userData);
                      // console.log(parsedUser.user_details);
                      if (parsedUser == null || parsedUser.user_details.role != 'admin') {
                        localStorage.removeItem("UserDeatails");
                        localStorage.removeItem("token");
                        $state.go('login');
                      }
                }
              }

            else if ($scope.tripHistory) {
                if($scope.filterSearch){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterTripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data;
                    $scope.totalTripHistory= resp.data.length; 

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/tripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            } 

            else if ($scope.pendingTrip) {
                if($scope.filterSearchPendingTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterPendingTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.pendingTripsData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/pendingTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.TripsLength= res.data.length;
                      $scope.pendingTripsData= res.data;

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            } 

            else if ($scope.cancelTrip) {
                if($scope.filterSearchcancelTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterCancelTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.cancelTripsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/cancelTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.cancelTripsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }

            else if ($scope.boostedTrip) {
                if($scope.filterSearchBoostedTrip){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterBoostedTripPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.boostedTripsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/boostedTripsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.boostedTripsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }



            else if ($scope.bookingRequest) {
                if($scope.filterSearchBookingRequest){
                  APIService.setData({
                    req_url: PrefixUrl + '/trip/filterBookingRequestPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.bookingRequestsData= resp.data;
                     },function(resp) {
                        // This block execute in case of error.
                  });
                }else{
                    APIService.setData({
                      req_url: PrefixUrl + '/trip/bookingRequestsPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
                    }).then(function(resp) {
                    console.log("====respPagination======",resp);
                      $scope.bookingRequestsData=resp.data

                     },function(resp) {
                        // This block execute in case of error.
                  });
                }
            }
              
          }
          );
    
      }
      
      // $scope.checkPagination();

        

        $scope.getTripsForSelectedDate=function(startDate,endDate){
          if ($scope.upcomingTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTripsForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.upcomingTripsData= res.data;
            },function(er){

            })
          }

          if ($scope.pendingTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/pendingTripForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.pendingTripsData= res.data;
            },function(er){

            })
          }
          
          if ($scope.cancelTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/cancelTripForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.cancelTripsData= res.data;
            },function(er){

            })
          }

          if ($scope.boostedTrip==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/boostedTripForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.boostedTripsData= res.data;
            },function(er){

            })
          }


          if ($scope.tripHistory==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/tripHistoryForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.tripsHistoryData= res.data;
            },function(er){

            })
          }


          if ($scope.bookingRequest==true) {
           APIService.setData({ req_url: PrefixUrl + "/trip/bookingRequestForDatePassenger" ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.bookingRequestsData= res.data;
            },function(er){

            })
          }


        }


        $scope.cancelTrips=function(){

            $scope.cancelTrip=true;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;

            APIService.setData({ req_url: PrefixUrl + "/trip/cancelTripsPassenger"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.checkPagination();
                // $scope.cancelTripsData= res.data;
            },function(er){

            })


            // $scope.$watch('settings.pageLimit', function (pageLimit) {
            //   console.log('pageLimits'+pageLimit)
            //   $scope.pageLimit= pageLimit;
            //     APIService.setData({
            //         req_url: PrefixUrl + '/trip/cancelTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //     }).then(function(resp) {
            //       console.log("====respPagination======",resp);
            //       $scope.cancelTripsData=resp.data
            //        },function(resp) {
            //           // This block execute in case of error.
            //     });
            // }
            // );


            // $scope.$watch('settings.currentPage', function (value) {
            //   console.log('currentPage'+$scope.settings.currentPage)  
            //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
            //     APIService.setData({
            //           req_url: PrefixUrl + '/trip/cancelTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //       }).then(function(resp) {
            //         console.log("====respPagination======",resp);
            //         $scope.cancelTripsData=resp.data
            //          },function(resp) {
            //             // This block execute in case of error.
            //       });
            // }
            // );

        }
        
        $scope.bookingTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=true;

        }

        $scope.upcomingTrips=function(){
            $scope.cancelTrip=false;
            $scope.upcomingTrip=true;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;

            APIService.setData({ req_url: PrefixUrl + "/trip/upcomingTripsPassenger"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.checkPagination();

                // $scope.upcomingTripsData= res.data;
            },function(er){

            })

            // $scope.$watch('settings.pageLimit', function (pageLimit) {
            //   console.log('pageLimits'+pageLimit)
            //   $scope.pageLimit= pageLimit;
            //     APIService.setData({
            //         req_url: PrefixUrl + '/trip/upcomingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            //     }).then(function(resp) {
            //       console.log("====respPagination======",resp);
            //       $scope.upcomingTripsData=resp.data
            //        },function(resp) {
            //           // This block execute in case of error.
            //     });
            // }
            // );


            // $scope.$watch('settings.currentPage', function (value) {
            //   console.log('currentPage'+$scope.settings.currentPage)  
            //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
            //     APIService.setData({
            //           req_url: PrefixUrl + '/trip/upcomingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            //       }).then(function(resp) {
            //         console.log("====respPagination======",resp);
            //         $scope.upcomingTripsData=resp.data
            //          },function(resp) {
            //             // This block execute in case of error.
            //       });
            // }
            // );


        }

        $scope.pendingTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=true;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;
            
            APIService.setData({ req_url: PrefixUrl + "/trip/pendingTripsPassenger"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.pendingTripsData= res.data;
                $scope.checkPagination();

            },function(er){

            })



            // $scope.$watch('settings.pageLimit', function (pageLimit) {
            //   console.log('pageLimits'+pageLimit)
            //   $scope.pageLimit= pageLimit;
            //     APIService.setData({
            //         req_url: PrefixUrl + '/trip/pendingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            //     }).then(function(resp) {
            //       console.log("====respPagination======",resp);
            //       $scope.pendingTripsData=resp.data
            //        },function(resp) {
            //           // This block execute in case of error.
            //     });
            // }
            // );


            // $scope.$watch('settings.currentPage', function (value) {
            //   console.log('currentPage'+$scope.settings.currentPage)  
            //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
            //     APIService.setData({
            //           req_url: PrefixUrl + '/trip/pendingTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
            //       }).then(function(resp) {
            //         console.log("====respPagination======",resp);
            //         $scope.pendingTripsData=resp.data
            //          },function(resp) {
            //             // This block execute in case of error.
            //       });
            // }
            // );

        }

        $scope.boostedTrips=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=true;
            $scope.tripHistory=false;
            $scope.bookingRequest=false;


            APIService.setData({ req_url: PrefixUrl + "/trip/boostedTripsPassenger"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.checkPagination();

                // $scope.boostedTripsData= res.data;
            },function(er){

            })

            // $scope.$watch('settings.pageLimit', function (pageLimit) {
            //   console.log('pageLimits'+pageLimit)
            //   $scope.pageLimit= pageLimit;
            //     APIService.setData({
            //         req_url: PrefixUrl + '/trip/boostedTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //     }).then(function(resp) {
            //       console.log("====respPagination======",resp);
            //       $scope.boostedTripsData=resp.data
            //        },function(resp) {
            //           // This block execute in case of error.
            //     });
            // }
            // );


            // $scope.$watch('settings.currentPage', function (value) {
            //   console.log('currentPage'+$scope.settings.currentPage)  
            //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
            //     APIService.setData({
            //           req_url: PrefixUrl + '/trip/boostedTrips' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //       }).then(function(resp) {
            //         console.log("====respPagination======",resp);
            //         $scope.boostedTripsData=resp.data
            //          },function(resp) {
            //             // This block execute in case of error.
            //       });
            // }
            // );
            
        }


        $scope.tripsHistory=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=true;
            $scope.bookingRequest=false;


            APIService.setData({ 
                  req_url: PrefixUrl + '/trip/tripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
              }).then(function (res) {
                console.log(res)
                // $scope.TripsLength= res.data.length;
                $scope.checkPagination();

                // $scope.tripsHistoryData= res.data;
            },function(er){

            })


            $scope.$watch('settings.pageLimit', function (pageLimit) {
              console.log('pageLimits'+pageLimit)
              $scope.pageLimit= pageLimit;
                APIService.setData({
                    req_url: PrefixUrl + '/trip/tripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.tripsHistoryData=resp.data
                   },function(resp) {
                      // This block execute in case of error.
                });
            }
            );


            $scope.$watch('settings.currentPage', function (value) {
              console.log('currentPage'+$scope.settings.currentPage)  
              console.log('userDetailslll='+$scope.upcomingTripsData.length)
                APIService.setData({
                      req_url: PrefixUrl + '/trip/tripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                  }).then(function(resp) {
                    console.log("====respPagination======",resp);
                    $scope.tripsHistoryData=resp.data
                     },function(resp) {
                        // This block execute in case of error.
                  });
            }
            );

            $scope.tripsHistoryCount();

            
        }


        $scope.bookingRequests=function(){

            $scope.cancelTrip=false;
            $scope.upcomingTrip=false;
            $scope.pendingTrip=false;
            $scope.boostedTrip=false;
            $scope.tripHistory=false;
            $scope.bookingRequest=true;

            APIService.setData({ req_url: PrefixUrl + "/trip/bookingRequestsPassenger"}).then(function (res) {
                console.log(res)
                $scope.TripsLength= res.data.length;
                $scope.checkPagination();
                // $scope.bookingRequestsData= res.data;
            },function(er){

            })

            // $scope.$watch('settings.pageLimit', function (pageLimit) {
            //   console.log('pageLimits'+pageLimit)
            //   $scope.pageLimit= pageLimit;
            //     APIService.setData({
            //         req_url: PrefixUrl + '/trip/bookingRequests' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //     }).then(function(resp) {
            //       console.log("====respPagination======",resp);
            //       $scope.bookingRequestsData=resp.data
            //        },function(resp) {
            //           // This block execute in case of error.
            //     });
            // }
            // );


            // $scope.$watch('settings.currentPage', function (value) {
            //   console.log('currentPage'+$scope.settings.currentPage)  
            //   console.log('userDetailslll='+$scope.upcomingTripsData.length)
            //     APIService.setData({
            //           req_url: PrefixUrl + '/trip/bookingRequests' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}
            //       }).then(function(resp) {
            //         console.log("====respPagination======",resp);
            //         $scope.bookingRequestsData=resp.data
            //          },function(resp) {
            //             // This block execute in case of error.
            //       });
            // }
            // );
            
        }


      $scope.filterTrips = function(startDate,endDate) {


        if (startDate) {
            $scope.startDate= startDate;
        }else{
            $scope.startDate= null;
        }

        if (endDate) {
            $scope.endDate= endDate;
        }else{
            $scope.endDate= null;
        }

        if ($scope.origin) {
            $scope.origin= $scope.origin;
        }else{
            $scope.origin= null;
        }

        if ($scope.destination) {
            $scope.destination= $scope.destination;
        }else{
            $scope.destination= null;
        }

        if ($scope.buisness_name) {
            $scope.buisness_name= $scope.buisness_name;
        }else{
            $scope.buisness_name= null;
        }

        if ($scope.phone_number) {
            $scope.phone_number= $scope.phone_number;
        }else{
            $scope.phone_number= null;
        }


        if ($scope.upcomingTrip) {
          console.log('upcomingTrip')
          $scope.filterSearchUpcomingTrip= true;
          $scope.filterSearchPendingTrip= false;
          $scope.filterSearch= false;
          $scope.filterSearchcancelTrip= false;
          $scope.filterSearchBoostedTrip= false;
          $scope.filterSearchBookingRequest= false;



          APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsUpcomingPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.upcomingTripsData= resp.data;
          },function(resp) {
           
          });

        }else if ($scope.tripHistory) {
          
          $scope.filterSearchUpcomingTrip= false;
          $scope.filterSearchPendingTrip= false;
          $scope.filterSearch= true;
          $scope.filterSearchcancelTrip= false;
          $scope.filterSearchBoostedTrip= false;
          $scope.filterSearchBookingRequest= false;


          APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsHistoryPassenger' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.tripsHistoryData= resp.data;
            // $scope.totalTripHistory= resp.data.length; 
            $scope.filterTripsHistoryCount();
          },function(resp) {
           
          });
        }

        else if ($scope.pendingTrip) {

          $scope.filterSearchUpcomingTrip= false;
          $scope.filterSearchPendingTrip= true;
          $scope.filterSearch= false;
          $scope.filterSearchcancelTrip= false;
          $scope.filterSearchBoostedTrip= false;
          $scope.filterSearchBookingRequest= false;




          APIService.setData({
              req_url: PrefixUrl + '/trip/filterPendingTripPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.pendingTripsData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.cancelTrip) {

          $scope.filterSearchUpcomingTrip= false;
          $scope.filterSearchPendingTrip= false;
          $scope.filterSearch= false;
          $scope.filterSearchcancelTrip= true;
          $scope.filterSearchBoostedTrip= false;
          $scope.filterSearchBookingRequest= false;

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterCancelTrip' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.cancelTripsData= resp.data;
          },function(resp) {
           
          });
        }

        else if ($scope.boostedTrip) {


          $scope.filterSearchUpcomingTrip= false;
          $scope.filterSearchPendingTrip= false;
          $scope.filterSearch= false;
          $scope.filterSearchcancelTrip= false;
          $scope.filterSearchBoostedTrip= true;
          $scope.filterSearchBookingRequest= false;



          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBoostedTripPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.boostedTripsData= resp.data;
            $scope.checkPagination();
          },function(resp) {
           
          });
        }

        else if ($scope.bookingRequest) {


          $scope.filterSearchUpcomingTrip= false;
          $scope.filterSearchPendingTrip= false;
          $scope.filterSearch= false;
          $scope.filterSearchcancelTrip= false;
          $scope.filterSearchBoostedTrip= false;
          $scope.filterSearchBookingRequest= true;

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBookingRequestPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.bookingRequestsData= resp.data;
            $scope.checkPagination();
          },function(resp) {
           
          });
        }


      }


      $scope.filterTripsForUser = function(prod){

        if ($scope.upcomingTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/upcomingTripForUserPassenger' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.upcomingTripsData= tripList;

            
          },function(resp) {
           
          });
        }

        else if ($scope.pendingTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/pendingTripForUser' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.pendingTripsData= tripList;

            
          },function(resp) {
           
          });
        }




      else if ($scope.cancelTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/cancelTripForUser' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.cancelTripsData= tripList;

            
          },function(resp) {
           
          });
      }

        
        else if ($scope.boostedTrip) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBoostedTripForUser' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.boostedTripsData= tripList;

            
          },function(resp) {
           
          });

        }
        else if ($scope.tripsHistory) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsHistoryForUser' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.tripsHistoryData= tripList;

            
          },function(resp) {
           
          });
        }

        else if ($scope.bookingRequest) {

          APIService.setData({
              req_url: PrefixUrl + '/trip/filterBookingRequestForUser' ,data:{ currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number} 
          }).then(function(resp) {
            var usersList= resp.data;
            var tripList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                userObject.push(key);
                key.tripDetails.forEach(function (trip) {
                    console.log('trip')
                    console.log(trip)
                    tripList.push(trip);
                });

            });
            console.log('llllllllllllll')
            // console.log(tripList)
            tripList.forEach(function (trip) {
                
                trip['ownerDetails']=userObject;
            });

            console.log(tripList)

            $scope.bookingRequestsData= tripList;

            
          },function(resp) {
           
          });
        }



      } 


      $scope.upcomingTrips();


    $scope.tripsHistoryCount = function(){
      APIService.setData({
              req_url: PrefixUrl + '/trip/totalTripHistoryPassenger'  
          }).then(function(resp) {
              console.log('ttttttttt',resp.data[0].myCount)
              $scope.totalTripHistory=  resp.data[0].myCount;
              $scope.TripsLength=  resp.data[0].myCount;
            
           },function(resp) {
                  // This block execute in case of error.
                    localStorage.removeItem("UserDeatails");
                    localStorage.removeItem("token");
                    $state.go('login');
            });
    }


    
    $scope.filterTripsHistoryCount = function(){
      APIService.setData({
              req_url: PrefixUrl + '/trip/filterTripsHistoryCount',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit, startDate: $scope.startDate,endDate:$scope.endDate, origin:$scope.origin, destination:$scope.destination, buisness_name:$scope.buisness_name, phone_number:$scope.phone_number}  
          }).then(function(resp) {
              console.log('ttttttttt',resp.data[0].myCount)
              if (resp.data.length == 0) {
                $scope.totalTripHistory=  0;
              }else{
                $scope.totalTripHistory=  resp.data[0].myCount;
              }
            
            });
    }    

    $scope.alltrips = function(prod){
        console.log(prod)
        $state.go("app.tripDetailsPassenger",{data:JSON.stringify(prod)})
    console.log(prod._id);
    }

    $scope.findUserBusinessTrips = function(user) {
     
      console.log(user)
      $state.go('app.tripDetailsPassenger',{data:JSON.stringify(user)});

    };


    $scope.tripsHistoryCount();

});