/**
 * Bid Resource Configuration
 * Defines the AdminJS resource for Bid model
 * PRD Reference: Sections 4.3, 10.2, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Bid from '../../../../models/Bid.js';
import Trip from '../../../../models/Trip.js';
import Driver from '../../../../models/Driver.js';
import logger from '../../../../utils/logger.js';
import mqttService from '../../../../services/mqttService.js';
import { format } from 'date-fns';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

const dateRangeFilterComponent = componentLoader.add(
  'dateRangeFilter', 
  join(__dirname, '../components/date-range-filter') // Path to your React component
);

// Register bid Analytics component
const bidAnalyticsComponent = componentLoader.add(
  'bidAnalytics', 
  join(__dirname, '../components/bid-analytics') // Path to your component
);

/**
 * Bid resource configuration
 */
export default {
  resource: Bid,
  options: {
    navigation: {
      name: 'Bidding Management',
      icon: 'Money',
    },
    // Configure pagination for the list view
    listProperties: ['trip_id', 'driver_id', 'amount', 'status', 'created_at'],
    filterProperties: ['trip_id', 'driver_id', 'status', 'amount', 'created_at'],
    editProperties: ['status', 'notes'],
    showProperties: [
      'trip_id', 'driver_id', 'amount', 'status', 'notes', 'created_at', 'updated_at'
    ],
    properties: {
      trip_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        reference: 'Trip',
        isRequired: true,
      },
      driver_id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        reference: 'Driver',
        isRequired: true,
      },
      amount: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number',
        // Add range filter for bid amount
        availableFilters: ['equal', 'not-equal', 'greater', 'less'],
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        availableValues: [
          { value: 'pending', label: 'Pending' },
          { value: 'accepted', label: 'Accepted' },
          { value: 'rejected', label: 'Rejected' },
        ],
      },
      created_at: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
        components: {
          filter: dateRangeFilterComponent,
        },
      },
      updated_at: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime',
      },
    },
    // Configure pagination options
    perPage: 10,
    perPageOptions: [10, 25, 50],
    sort: {
      sortBy: 'created_at',
      direction: 'desc',
    },
    actions: {
      // Custom action to view bid analytics
      bidAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        component: bidAnalyticsComponent,
        handler: async (request, response, context) => {
          try {
            // Fetch data for analytics
            const bids = await Bid.find().populate('trip_id').populate('driver_id');
            
            // Calculate bid acceptance rate by car type
            const carTypeBids = {};
            const carTypeAccepted = {};
            
            // Calculate average bid by car type
            const carTypeBidAmounts = {};
            const carTypeBidCounts = {};
            
            // Calculate bidding frequency per driver
            const driverBidCounts = {};
            const driverNames = {};
            
            // Process bids for analytics
            for (const bid of bids) {
              if (bid.trip_id && bid.trip_id.car_type) {
                const carType = bid.trip_id.car_type;
                
                // Acceptance rate calculation
                if (!carTypeBids[carType]) carTypeBids[carType] = 0;
                if (!carTypeAccepted[carType]) carTypeAccepted[carType] = 0;
                
                carTypeBids[carType]++;
                if (bid.status === 'accepted') {
                  carTypeAccepted[carType]++;
                }
                
                // Average bid calculation
                if (!carTypeBidAmounts[carType]) carTypeBidAmounts[carType] = 0;
                if (!carTypeBidCounts[carType]) carTypeBidCounts[carType] = 0;
                
                carTypeBidAmounts[carType] += bid.amount;
                carTypeBidCounts[carType]++;
              }
              
              // Driver bidding frequency
              if (bid.driver_id) {
                const driverId = bid.driver_id._id.toString();
                const driverName = bid.driver_id.name || `Driver ${driverId.substring(0, 5)}`;
                
                if (!driverBidCounts[driverId]) driverBidCounts[driverId] = 0;
                driverBidCounts[driverId]++;
                driverNames[driverId] = driverName;
              }
            }
            
            // Format data for charts
            const acceptanceRateData = {
              labels: [],
              data: []
            };
            
            const averageBidData = {
              labels: [],
              data: []
            };
            
            // Process car type data
            for (const carType in carTypeBids) {
              const acceptanceRate = carTypeBids[carType] > 0 
                ? Math.round((carTypeAccepted[carType] / carTypeBids[carType]) * 100) 
                : 0;
              
              const averageBid = carTypeBidCounts[carType] > 0 
                ? Math.round(carTypeBidAmounts[carType] / carTypeBidCounts[carType]) 
                : 0;
              
              acceptanceRateData.labels.push(carType);
              acceptanceRateData.data.push(acceptanceRate);
              
              averageBidData.labels.push(carType);
              averageBidData.data.push(averageBid);
            }
            
            // Process driver frequency data (limit to top 5)
            const driverEntries = Object.entries(driverBidCounts)
              .sort((a, b) => b[1] - a[1])
              .slice(0, 5);
            
            const biddingFrequencyData = {
              labels: driverEntries.map(([driverId]) => driverNames[driverId]),
              data: driverEntries.map(([, count]) => count)
            };
            
            // Log the action
            logger.info('Bid analytics viewed by admin', {
              adminId: context.currentAdmin._id,
              timestamp: new Date().toISOString()
            });
            
            return {
              analyticsData: {
                acceptanceRate: acceptanceRateData,
                averageBidByCarType: averageBidData,
                biddingFrequency: biddingFrequencyData
              },
              message: 'Bid analytics loaded',
            };
          } catch (error) {
            logger.error('Error loading bid analytics', { 
              error: error.message,
              stack: error.stack,
              timestamp: new Date().toISOString()
            });
            return {
              notice: {
                message: 'Error loading bid analytics',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to export bids data
      exportBids: {
        actionType: 'resource',
        icon: 'DocumentDownload',
        handler: async (request, response, context) => {
          try {
            const { query } = request;
            const filters = query.filters || {};
            
            // Build filter query
            const filterQuery = {};
            
            if (filters.trip_id) {
              filterQuery.trip_id = filters.trip_id;
            }
            
            if (filters.driver_id) {
              filterQuery.driver_id = filters.driver_id;
            }
            
            if (filters.status) {
              filterQuery.status = filters.status;
            }
            
            if (filters.amount_from) {
              filterQuery.amount = { $gte: parseFloat(filters.amount_from) };
            }
            
            if (filters.amount_to) {
              if (filterQuery.amount) {
                filterQuery.amount.$lte = parseFloat(filters.amount_to);
              } else {
                filterQuery.amount = { $lte: parseFloat(filters.amount_to) };
              }
            }
            
            if (filters.date_from || filters.date_to) {
              filterQuery.created_at = {};
              
              if (filters.date_from) {
                filterQuery.created_at.$gte = new Date(filters.date_from);
              }
              
              if (filters.date_to) {
                filterQuery.created_at.$lte = new Date(filters.date_to);
              }
            }
            
            // Fetch bids with filters
            const bids = await Bid.find(filterQuery)
              .populate('trip_id')
              .populate('driver_id')
              .sort({ created_at: -1 });
            
            // Format data for export
            const formattedBids = bids.map(bid => ({
              'Trip ID': bid.trip_id ? bid.trip_id._id.toString() : 'N/A',
              'Driver': bid.driver_id ? bid.driver_id.name : 'N/A',
              'Amount': bid.amount.toFixed(2),
              'Status': bid.status.charAt(0).toUpperCase() + bid.status.slice(1),
              'Notes': bid.notes || 'N/A',
              'Created At': format(new Date(bid.created_at), 'yyyy-MM-dd HH:mm:ss'),
              'Updated At': format(new Date(bid.updated_at), 'yyyy-MM-dd HH:mm:ss')
            }));
            
            // Log the export action
            logger.info('Bids data exported by admin', {
              adminId: context.currentAdmin._id,
              filterCount: Object.keys(filterQuery).length,
              recordCount: bids.length,
              timestamp: new Date().toISOString()
            });
            
            return {
              data: formattedBids,
              message: `Successfully exported ${bids.length} bids`,
            };
          } catch (error) {
            logger.error('Error exporting bids data', { 
              error: error.message,
              stack: error.stack,
              timestamp: new Date().toISOString()
            });
            
            return {
              notice: {
                message: 'Error exporting bids data',
                type: 'error',
              },
            };
          }
        },
      },
    },
  },
}