import express from 'express';
import TripController from '../controllers/tripController';
import TripArchieveController from '../controllers/tripArchieveController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initTripArchieveRoutes = () => {
  const tripArchieveRoutes = express.Router();

  


  tripArchieveRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.page);
  tripArchieveRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.show);
  tripArchieveRoutes.get('/tripbyvehid/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON>rror<PERSON><PERSON><PERSON> ,  TripController.showTripByVehicleId);
  tripArchieveRoutes.get('/getCancel/:id',passport.authenticate('jwt', { session: false }), jwtAuth<PERSON><PERSON>r<PERSON><PERSON><PERSON> ,  TripController.showSt);
  tripArchieveRoutes.get('/cancelRequestsForUser/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelRequestsForUser);
  tripArchieveRoutes.get('/sendRequest/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.sendRequest);
  tripArchieveRoutes.get('/cancelSendRequests/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelRequest);
  tripArchieveRoutes.get('/getriphistory/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripHistory);
  tripArchieveRoutes.get('/book/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequset);
  tripArchieveRoutes.get('/bookUpcoming/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequestUpcoming);
  tripArchieveRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.create);
  tripArchieveRoutes.post('/search',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.search);
  tripArchieveRoutes.post('/searchLocal',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchLocal);
  tripArchieveRoutes.post('/boostsearch',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchForBoostTrip);
  tripArchieveRoutes.put('/update/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.updateForBookingReq);
  tripArchieveRoutes.put('/boostTrip/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostTrip);
  tripArchieveRoutes.put('/updateS/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.update);
  tripArchieveRoutes.delete('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.remove);
  tripArchieveRoutes.get('/get_counts/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getCounts);
  tripArchieveRoutes.post('/upcomingTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTrips);
  tripArchieveRoutes.post('/upcomingTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsPassenger);
  tripArchieveRoutes.post('/pendingTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTrips);
  tripArchieveRoutes.post('/pendingTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsPassenger);
  tripArchieveRoutes.post('/bookingRequests',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequests);
  tripArchieveRoutes.post('/bookingRequestsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestsPassenger);
  tripArchieveRoutes.post('/cancelTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTrips);
  tripArchieveRoutes.post('/cancelTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripsPassenger);
  tripArchieveRoutes.post('/boostedTrips',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTrips);
  tripArchieveRoutes.post('/boostedTripsPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsPassenger);
  tripArchieveRoutes.post('/tripsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistory);
  tripArchieveRoutes.post('/tripsHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryPassenger);
  tripArchieveRoutes.post('/upcomingTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForVehicle);
  tripArchieveRoutes.post('/pendingTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsForVehicle);
  tripArchieveRoutes.post('/cancelTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripsForVehicle);
  tripArchieveRoutes.post('/boostedTripsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForVehicle);
  tripArchieveRoutes.post('/bookingRequestsForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestsForVehicle);
  tripArchieveRoutes.post('/tripHistoryForVehicle',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripHistoryForVehicle);
  tripArchieveRoutes.post('/upcomingTripsForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForDate);
  tripArchieveRoutes.post('/upcomingTripsForDatePassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsForDatePassenger);
  tripArchieveRoutes.post('/pendingTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForDate);
  tripArchieveRoutes.post('/pendingTripForDatePassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForDatePassenger);
  tripArchieveRoutes.post('/tripsHistoryByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryByUserId);
  tripArchieveRoutes.post('/tripsHistoryByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripsHistoryByUserIdPassenger);
  tripArchieveRoutes.post('/upcomingTripsByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsByUserId);
  tripArchieveRoutes.post('/upcomingTripsByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripsByUserIdPassenger);
  tripArchieveRoutes.post('/pendingTripsByUserId',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsByUserId);
  tripArchieveRoutes.post('/pendingTripsByUserIdPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripsByUserIdPassenger);
  tripArchieveRoutes.post('/boostedTripsForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForUser);
  tripArchieveRoutes.post('/boostedTripsForUserPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripsForUserPassenger);
  tripArchieveRoutes.post('/filterTripsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistory);
  tripArchieveRoutes.post('/filterTripsHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryPassenger);
  tripArchieveRoutes.post('/filterTripsUpcoming',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsUpcoming);
  tripArchieveRoutes.post('/filterTripsUpcomingPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsUpcomingPassenger);
  tripArchieveRoutes.post('/filterPendingTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterPendingTrip);
  tripArchieveRoutes.post('/filterPendingTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterPendingTripPassenger);
  tripArchieveRoutes.post('/filterCancelTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterCancelTrip);
  tripArchieveRoutes.post('/filterCancelTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterCancelTripPassenger);
  tripArchieveRoutes.post('/filterBoostedTrip',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTrip);
  tripArchieveRoutes.post('/filterBoostedTripPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTripPassenger);
  tripArchieveRoutes.post('/filterBookingRequest',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBookingRequest);
  tripArchieveRoutes.post('/filterBoostedTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBoostedTripForUser);
  tripArchieveRoutes.post('/filterTripsHistoryForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryForUser);
  tripArchieveRoutes.post('/filterBookingRequestForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterBookingRequestForUser);
  tripArchieveRoutes.post('/upcomingTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.upcomingTripForUser);
  tripArchieveRoutes.post('/pendingTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.pendingTripForUser);
  tripArchieveRoutes.post('/cancelTripForUser',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripForUser);
  tripArchieveRoutes.post('/cancelTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelTripForDate);
  tripArchieveRoutes.post('/boostedTripForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.boostedTripForDate);
  tripArchieveRoutes.post('/tripHistoryForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.tripHistoryForDate);
  tripArchieveRoutes.post('/bookingRequestForDate',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookingRequestForDate);
  tripArchieveRoutes.post('/search_trip_history_for_date',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchTripHistoryForDate);
  tripArchieveRoutes.get('/getTripById/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripById);

  tripArchieveRoutes.post('/filterTripsDetailsHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsDetailsHistory);
  tripArchieveRoutes.post('/totalTripHistory',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.totalTripHistory);
  tripArchieveRoutes.post('/totalTripHistoryPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.totalTripHistoryPassenger);
  tripArchieveRoutes.post('/filterTripsHistoryCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.filterTripsHistoryCount);
  tripArchieveRoutes.post('/searchPassenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchPassenger);
  tripArchieveRoutes.get('/sendRequestPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.sendRequestPassenger);
  tripArchieveRoutes.get('/bookRequsetPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequsetPassenger);
  tripArchieveRoutes.get('/getTripByIdPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripByIdPassenger);
  tripArchieveRoutes.get('/showPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.showPassenger);
  tripArchieveRoutes.get('/bookUpcomingPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.bookRequestUpcomingPassenger);
  tripArchieveRoutes.get('/cancelSendRequestsPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.cancelRequestPassenger);
  tripArchieveRoutes.get('/getCancelPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.showStPassenger);
  tripArchieveRoutes.get('/getripHistoryPassenger/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.getTripHistoryPassenger);
  tripArchieveRoutes.post('/search_trip_history_for_date_passenger',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  TripController.searchTripHistoryForDatePassenger);



  // tripArchieveRoutes.get('/', TripController.page);
  // tripArchieveRoutes.get('/:id', TripController.show);
  // tripArchieveRoutes.get('/tripbyvehid/:id', TripController.showTripByVehicleId);
  // tripArchieveRoutes.get('/getCancel/:id', TripController.showSt);
  // tripArchieveRoutes.get('/sendRequest/:id', TripController.sendRequest);
  // tripArchieveRoutes.get('/cancelSendRequests/:id', TripController.cancelRequest);
  // tripArchieveRoutes.get('/getriphistory/:id', TripController.getTripHistory); 
  // tripArchieveRoutes.get('/book/:id', TripController.bookRequset);
  // tripArchieveRoutes.get('/bookUpcoming/:id', TripController.bookRequestUpcoming);
  // tripArchieveRoutes.post('/', TripController.create);
  // tripArchieveRoutes.post('/search', TripController.search);
  // tripArchieveRoutes.post('/boostsearch', TripController.searchForBoostTrip);
  // tripArchieveRoutes.put('/update/:id', TripController.updateForBookingReq);
  // tripArchieveRoutes.put('/boostTrip/:id', TripController.boostTrip);
  // tripArchieveRoutes.put('/updateS/:id', TripController.update);
  // tripArchieveRoutes.delete('/:id', TripController.remove);
  // tripArchieveRoutes.get('/get_counts/:id', TripController.getCounts);




  // tripArchieveRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.page
  // });

  // tripArchieveRoutes.get('/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.show
  // });

  //  tripArchieveRoutes.get('/get_counts/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, (req, res) => {
  //   // console.log(res);
  //      TripController.getCounts
  // });

  return tripArchieveRoutes;
};

export default initTripArchieveRoutes;
