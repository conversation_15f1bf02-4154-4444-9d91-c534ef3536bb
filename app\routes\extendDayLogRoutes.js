import express from 'express';
import ExtendDayLogController from '../controllers/extendDayLogController';
var passport     = require('passport');
const jwtAuthErrorHandler = require('../middleware/jwtErrorHandle');

const initextendDayLogRoutes = () => {
  const extendDayLogRoutes = express.Router();

  extendDayLogRoutes.post('/createLog',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>andler ,  ExtendDayLogController.create);
  extendDayLogRoutes.post('/showData',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  ExtendDayLogController.showData);
  
  // rateReviewRoutes.get('/',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  RateAndReviewController.show);
  // rateReviewRoutes.get('/review/:id',passport.authenticate('jwt', { session: false }), jwtAuthError<PERSON>and<PERSON> ,  RateAndReviewController.page);
  // rateReviewRoutes.post('/',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.create);
  // rateReviewRoutes.put('/update/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.update);
  // rateReviewRoutes.delete('/:vehicle_id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.remove);
  // rateReviewRoutes.post('/reviewBy',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.reviewBy);
  // rateReviewRoutes.delete('/removeReview/:id',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.removeReview);
  // rateReviewRoutes.post('/filterReview',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.filterReview);
  // rateReviewRoutes.post('/getByPostMethod',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler ,  RateAndReviewController.getByPostMethod);
  // rateReviewRoutes.post('/reviewCount',passport.authenticate('jwt', { session: false }), jwtAuthErrorHandler, RateAndReviewController.reviewCount);



  return extendDayLogRoutes;
};

export default initextendDayLogRoutes;
