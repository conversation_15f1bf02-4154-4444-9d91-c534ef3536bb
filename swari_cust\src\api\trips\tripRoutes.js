/**
 * Trip Routes
 * Defines API endpoints for trip operations
 * PRD Reference: Sections 4.2, 10.2
 */

import {Router} from 'express';

import { check, body } from 'express-validator';
import {
  createTrip,
  getTripById,
  getTripByCustomer,
  getUserTrips,
  cancelTrip,
  completeTrip // <-- Import new controller function
} from './tripController.js';
import {
  verifyToken,
  isCustomer,
  isDriver, // <-- Import isDriver
  validateRequest,
  verifySwariNodeToken // <-- Import verifySwariNodeToken
} from '../auth/authMiddleware.js';
import { validateTripCreation, validateCoordinates, validateTripIdParam } from './tripMiddleware.js'; // <-- Import new middleware

const router = Router();

/**
 * @route POST /api/trips/create
 * @desc Create a new trip
 * @access Customer only
 */
router.post('/trips/create', [
  verifyToken,
  isCustomer,
  validateTripCreation,
  validateCoordinates,
  validateRequest
], createTrip);

/**
 * @route GET /api/trips/customer
 * @desc Get trips for the logged-in customer
 * @access Customer only
 */
router.get('/trips/customer', [
  verifyToken,
  isCustomer
], getTripByCustomer);



/**
 * @route GET /api/trips/:id
 * @desc Get trip by ID
 * @access Customer only
 */
router.get('/trips/:id', [
  verifyToken,
  isCustomer
], getTripById);

/**
 * @route GET /api/trips
 * @desc Get all trips for logged-in user
 * @access Customer only
 */
router.get('/trips', [
  verifyToken,
  isCustomer
], getUserTrips);
/**
 * @route POST /api/trips/:id/cancel
 * @desc Cancel a trip
 * @access Customer only
 */
router.post('/trips/:id/cancel', [
  verifyToken,
  isCustomer,
  validateTripIdParam, // <-- Add middleware to validate ID
  validateRequest
], cancelTrip);

/**
 * @route POST /api/trips/:id/complete/customer
 * @desc Complete a trip by customer
 * @access Customer only
 */
router.post('/trips/:id/complete/customer', [
  verifyToken,
  isCustomer,
  validateTripIdParam,
  validateRequest
], completeTrip);

/**
 * @route POST /api/trips/:id/complete/driver
 * @desc Complete a trip by driver
 * @access Driver only (via SwariNode token)
 */
router.post('/trips/:id/complete/driver', [
  verifySwariNodeToken, // Use SwariNode token for driver actions
  isDriver, // Ensure the user has a driver role
  validateTripIdParam,
  validateRequest
], completeTrip);

export default router;