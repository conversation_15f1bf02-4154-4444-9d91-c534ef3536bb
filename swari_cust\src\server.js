/**
 * Server Entry Point
 * Sets up Express server and connects to MongoDB
 * PRD Reference: Sections 10.1, 10.2
 */

import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import config from './config/config.js';
import logger from './utils/logger.js';

// Import routes
import authRoutes from './api/auth/authRoutes.js';
import tripRoutes from './api/trips/tripRoutes.js';
import bidRoutes from './api/bids/bidRoutes.js';
import chatRoutes from './api/chat/chatRoutes.js';
import walletRoutes from './api/wallet/walletRoutes.js';
import cancellationRoutes from './api/cancellations/cancellationRoutes.js';
import ratingRoutes from './api/ratings/ratingRoutes.js';
import customerRoutes from './api/customer/customerRoutes.js';
import mqttService from './services/mqttService.js';
import driverAuthRoutes from './api/auth/driverAuthRoutes.js';
import VehicleType from './models/VehicleType.js';
import addTestTransactions from './tests/transaction.test.js';
import adminRoutes from './api/admin/adminRoutes.js';
import configureAdminJS  from './api/admin/adminjs/adminConfig.js';

// Initialize Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet({ contentSecurityPolicy: false }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Routes
app.use('/api', authRoutes);
app.use('/api', tripRoutes);
app.use('/api', bidRoutes);
app.use('/api', chatRoutes);
app.use('/api', walletRoutes);
app.use('/api', cancellationRoutes);
app.use('/api', customerRoutes);
app.use('/api/ratings', ratingRoutes);
app.use('/api', driverAuthRoutes);
// Initialize and mount AdminJS panel
configureAdminJS(app);

app.use('/api', adminRoutes);


// app.use((req, res, next) => {
//   res.setHeader(
//     "Content-Security-Policy",
//     "script-src 'self' 'unsafe-inline'"
//   );
//   next();
// });

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { error: err.message, stack: err.stack });
  res.status(500).json({ message: 'Server error', error: err.message });
});



// Connect to MongoDB only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  mongoose.connect(config.mongoURI, { useNewUrlParser: true, useUnifiedTopology: true })
    .then(async () => {
      logger.info('MongoDB connected successfully');
      
     
      
      // Initialize MQTT service
     // mqttService.connect();
     // Initialize MQTT service
try {
  mqttService.connect();
  logger.info('MQTT service initialized successfully');
} catch (error) {
  logger.warn('Failed to connect to MQTT broker, continuing without MQTT support', { error: error.message });
  // Application will continue without MQTT
}

      
      // Start the server
      const PORT = config.port;
      app.listen(PORT, () => {
        logger.info(`Server running on port ${PORT}`);
      });
    })
    .catch(err => {
      logger.info('MongoDB connection error', { error: err.message });
      // Only exit the process if not in test environment
      if (process.env.NODE_ENV !== 'test') {
        process.exit(1);
      }
    });
}

export default app; // Export for testing