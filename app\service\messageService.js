import User from '../models/user';
import UserMessage from '../models/userMessage';

export default class MessageService {
  static updateUser(userId, socketId) {
    return User.findOneAndUpdate({_id:userId},{$set:{"chat.socketId":socketId}})
    
        
  }
  static addGroupMessage(groupMessage) {
    return GroupMessage.create({group_id:groupMessage.group_id, message:groupMessage.message,senderId:groupMessage.senderId})
    
  }

  static add(message, senderId, recieverId) {
    console.log(message+" senderId "+senderId+" receiverId "+recieverId);
    if(!(message && senderId && recieverId))
      throw new Error('message, senderId and recieverId required');
     User.find({ _id: { $in: [ senderId, recieverId ] } })
      .then(users => {
        if(users.length < 2)
          throw new Error('Invalid UserId provided');
        var createdAt=new Date();
        return UserMessage.create({ message, senderId, recieverId, createdAt });
      })
      .then(() => console.log('message inserted to DB'))
      .catch(console.log);
  }
}
