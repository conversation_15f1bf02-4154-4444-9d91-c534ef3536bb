import mongoose from 'mongoose';
const Schema = mongoose.Schema;

const VehicleTypeSchema = new Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        enum: ['suv', 'sedan', 'hatchback', 'van'],
        lowercase: true,
        trim: true
    },
    created_at: {
        type: Date,
        default: Date.now
    }
});

// Create a static method to initialize default vehicle types
VehicleTypeSchema.statics.initializeVehicleTypes = async function() {
    const defaultTypes = ['suv', 'sedan', 'hatchback', 'van'];
    
    for (const type of defaultTypes) {
        try {
            await this.findOneAndUpdate(
                { name: type },
                { name: type },
                { upsert: true, new: true }
            );
        } catch (error) {
            if (error.code !== 11000) { // Ignore duplicate key errors
                console.error(`Error initializing vehicle type ${type}:`, error);
            }
        }
    }
};

const VehicleType = mongoose.model('VehicleType', VehicleTypeSchema);

// Initialize vehicle types when the model is first loaded
// VehicleType.initializeVehicleTypes().catch(console.error);

export default VehicleType;