const http = require('http');

const verificationHelpers = {
    checkTokenValidity(fcm_token) {
      return new Promise((resolve, reject) => {
        try {

        const data = JSON.stringify({
          token: fcm_token,
          data: { type: 'silent_ping' }
        });

        // Set up the request options
        const options = {
          hostname: "localhost",
          port: 4000,
          path: "/send-ping",
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Content-Length": data.length,
          },
        };

        // Make the request
        const req = http.request(options, (res) => {
          let responseData = "";

          // Receive data
          res.on("data", (chunk) => {
            responseData += chunk;
          });

          // Handle response end
          res.on("end", () => {
            console.log("Response recieved ");
            if (res.statusCode === 200) {
            //   const responseJson = JSON.parse(responseData);
            //   if (responseJson.success) {
                    resolve(true);
            //   } else {
            //         resolve(false);
            //   }
            } else {
                resolve(false);
            }
          });
        });

        // Handle error
        req.on("error", (error) => {
          console.error("Error:", error);
          resolve(false);
        });

        // Write data to request body
        req.write(data);

          req.end();
  
        } catch (error) {
          reject(error);
        }
      });    
    },
  
    async updateTrackingStatus(tracking) {
      const tokenValid = await this.checkTokenValidity(tracking.fcm_token);
      const inactivityDays = Math.floor(
        (new Date() - tracking.last_activity) / (1000 * 60 * 60 * 24)
      );
  
      if (!tokenValid) {
        tracking.activity_history.push({
          type: 'token_expired',
          timestamp: new Date()
        });
        tracking.app_active_status = false;
      }
  
      if (!tokenValid && inactivityDays > 15) {
        tracking.is_uninstalled = true;
        tracking.app_active_status = false;
        tracking.activity_history.push({
          type: 'uninstall',
          timestamp: new Date()
        });
      }
  
      await tracking.save();
      return {
        token_valid: tokenValid,
        days_inactive: inactivityDays,
        is_uninstalled: tracking.is_uninstalled
      };
    }
};
export default verificationHelpers;