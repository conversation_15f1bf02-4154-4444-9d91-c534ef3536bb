/**
 * Support Ticket Model
 * Defines the schema for support ticket data
 * PRD Reference: Sections 4.4, 10.3
 */

import mongoose from 'mongoose';

const SupportTicketSchema = new mongoose.Schema({
  customer_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: false
  },
  driver_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Driver',
    required: false
  },
  admin_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  },
  subject: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['open', 'in_progress', 'resolved', 'closed'],
    default: 'open'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  category: {
    type: String,
    enum: ['account', 'payment', 'trip', 'technical', 'other'],
    default: 'other'
  },
  related_trip_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trip',
    required: false
  },
  attachments: [{
    url: String,
    filename: String,
    mimetype: String,
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],
  responses: [{
    responder_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    responder_type: {
      type: String,
      enum: ['customer', 'driver', 'admin'],
      required: true
    },
    content: {
      type: String,
      required: true
    },
    created_at: {
      type: Date,
      default: Date.now
    }
  }],
  resolution_time: {
    type: Number,
    default: null
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } });

// Create indexes for faster queries
SupportTicketSchema.index({ status: 1, priority: 1 });
SupportTicketSchema.index({ customer_id: 1 });
SupportTicketSchema.index({ driver_id: 1 });
SupportTicketSchema.index({ created_at: 1 });



const SupportTicket = mongoose.model('SupportTicket', SupportTicketSchema);
export default SupportTicket;