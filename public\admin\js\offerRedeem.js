angular.module('offerRedeem.controllers', [])

    .controller('offerRedeemCtrl', function ($scope,APIService, $state,$stateParams) {
      $scope.pageLimit;
      $scope.filter={}
        
      $scope.settings = {
        currentPage: 0,
        offset: 0,
        pageLimit: 10,
        pageLimits: [2, 5, 10,20,100]
      };
      $scope.pageLimit= $scope.settings.pageLimit;
        $scope.offerObj={};
      

          $scope.getViewRedeemOffer= function(){
            if ($scope.filter.startDate) {
              console.log('startDate',$scope.filter.startDate)
          }

          if ($scope.filter.endDate) {
            console.log('endDate',$scope.filter.endDate)
          }
          if($scope.filter.city)
          {

            console.log('city',$scope.filter.city)


          }
          if($scope.filter.state)
          {
            console.log('state',$scope.filter.state)
            
          }  
	        console.log('offerObj -- ',$scope.offerObj)
	      	APIService.setData({
	          req_url: PrefixUrl + '/offerRedeemed/getViewRedeemOffer' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,city:$scope.filter.city,state:$scope.filter.state,startDate:$scope.filter.startDate,endDate:$scope.filter.endDate,} 
	      	}).then(function(resp) {
                $scope.offerRedeem =resp.data;
                $scope.offerRedeems =resp.data[0].books[0].offerList[0];

                console.log("offer",$scope.offerRedeems);
	             },function(resp) {
	                // This block execute in case of error.
	         });

          }
          $scope.getViewRedeemOffer();
    });