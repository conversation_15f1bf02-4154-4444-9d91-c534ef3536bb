/**
 * MQTT Service
 * Handles MQTT connections and messaging for real-time features
 * PRD Reference: Sections 4.3, 10.4
 */

import mqtt from 'mqtt';
import logger from '../utils/logger.js';
import config from '../config/config.js';

class MQTTService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.topics = {
      bidding: 'bidding/trip/',
      chat: 'chat/room/',
      tripStatus: 'trips/status/' // Add new topic for trip status updates
    };
  }

  /**
   * Connect to MQTT broker using MQTT 5 protocol
   * @param {object} customOptions - Optional custom connection options
   */
  connect(customOptions = {}) {
    try {
      // Connect to MQTT broker (EMQX) over WebSockets
      const brokerUrl = config.mqttBrokerUrl || 'ws://*************:8083';
      console.log("MQTT CONFIG VALUE:", config.mqttBrokerUrl);

      console.log("RAW BROKER URL:", brokerUrl);
console.log("HAS PROTOCOL:", brokerUrl.startsWith('ws://'));

      logger.info('Connecting to MQTT broker with MQTT 5 protocol', { 
        brokerUrl , 
        configValue: JSON.stringify(config.mqttBrokerUrl)
      });
    

      // Extract hostname from the broker URL for connection options
     // const url = new URL(brokerUrl);

      // MQTT 5 specific connection options
      const mqtt5Options = {
       // host: url.hostname,
     //   port: 8083,
       protocol: "ws",
        path: '/mqtt',
        username:  "admin",
        password:  "public",
        protocolVersion: 5, // MQTT 5 protocol
        keepalive: 60, // Set keep-alive interval to 60 seconds
        // MQTT 5 specific properties
        properties: {
          sessionExpiryInterval: 3600, // 1 hour in seconds
          receiveMaximum: 100,
          maximumPacketSize: 16384, // 16KB
          requestResponseInformation: true,
          requestProblemInformation: true,
          userProperties: {
            clientType: 'swari_customer_app',
            appVersion: config.appVersion || '1.0.0',
            ...customOptions.userProperties
          },
          ...customOptions.properties
        },
        clientId: `swari_backend_${Math.random().toString(16).substring(2, 10)}`,
        clean: true,
        connectTimeout: 4000,
        reconnectPeriod: 60000,
        ...customOptions
      };

      logger.info('Connecting to MQTT broker with MQTT 5 protocol',);

      this.client = mqtt.connect(brokerUrl, mqtt5Options);
     // this.client = mqtt.connect(mqtt5Options);
      // let testVar = mqtt.connect(mqtt5Options);

      // logger.info('Connecting to MQTT broker with MQTT 5 protocol', {client: this.client});

      // Setup enhanced MQTT 5 event handlers
      this.client.on('connect', (packet) => {
        this.connected = true;
        // logger.info('Connected to MQTT broker with MQTT 5 protocol', {
        //   sessionPresent: packet.sessionPresent,
        //   properties: packet.properties
        // });
      });

      this.client.on('error', (err) => {
        this.connected = false;
        logger.error('MQTT connection error ', { error: err.message });
      });

      this.client.on('close', (err) => {
        this.connected = false;
        logger.info('MQTT connection closed', { error: err });
      });

      // MQTT 5 specific events
      this.client.on('disconnect', (packet) => {
        this.connected = false;
        logger.info('MQTT disconnect received', {
          reasonCode: packet.reasonCode,
          properties: packet.properties
        });
      });

      this.client.on('reconnect', () => {
        logger.info('Attempting to reconnect to MQTT broker');
      });

      return this.client;
    } catch (error) {
      logger.error('Failed to connect to MQTT broker', { error: error.message });
      throw error;
    }
  }

  /**
   * Publish a message to a topic
   * @param {string} topic - The topic to publish to
   * @param {object} message - The message to publish
   * @param {object} options - MQTT 5 publish options
   */
  publish(topic, message, options = {}) {
    if (!this.connected || !this.client) {
      logger.error('Cannot publish: MQTT client not connected');
      return false;
    }

    try {
      // Default MQTT 5 options
      const mqtt5Options = {
        qos: 1,
        // MQTT 5 specific properties
        properties: {
          contentType: 'application/json',
          messageExpiryInterval: options.expiryInterval || 3600, // Default 1 hour
          userProperties: options.userProperties || {},
          responseTopic: options.responseTopic || undefined,
          correlationData: options.correlationData || undefined
        },
        ...options
      };

      this.client.publish(topic, JSON.stringify(message), mqtt5Options);
      logger.info(`Published message to ${topic} with MQTT 5 properties`);
      return true;
    } catch (error) {
      logger.error('Failed to publish MQTT message', { error: error.message, topic });
      return false;
    }
  }

  /**
   * Subscribe to a topic
   * @param {string} topic - The topic to subscribe to
   * @param {function} callback - The callback function for received messages
   * @param {object} options - MQTT 5 subscription options
   */
  subscribe(topic, callback, options = {}) {
    if (!this.connected || !this.client) {
      logger.error('Cannot subscribe: MQTT client not connected');
      return false;
    }

    try {
      // Default MQTT 5 subscription options
      const mqtt5Options = {
        qos: 1,
        // MQTT 5 specific properties
        properties: {
          subscriptionIdentifier: options.subscriptionId || Math.floor(Math.random() * 100000),
          userProperties: options.userProperties || {},
          ...options.properties
        },
        // Support for shared subscriptions
        ...(options.shared ? { rh: 1 } : {}), // Retain handling
        ...options
      };

      this.client.subscribe(topic, mqtt5Options, (err, granted) => {
        if (err) {
          logger.error('Failed to subscribe to topic', { error: err.message, topic });
          return;
        }
        logger.info(`Subscribed to topic: ${topic}`, {
          qos: granted[0]?.qos,
          properties: mqtt5Options.properties
        });
      });

      this.client.on('message', (receivedTopic, message, packet) => {
        if (receivedTopic === topic) {
          try {
            const parsedMessage = JSON.parse(message.toString());
            // Pass both the parsed message and the MQTT 5 packet properties to the callback
            callback(parsedMessage, {
              qos: packet.qos,
              retain: packet.retain,
              properties: packet.properties,
              topic: receivedTopic
            });
          } catch (error) {
            logger.error('Failed to parse MQTT message', { error: error.message });
          }
        }
      });

      return true;
    } catch (error) {
      logger.error('Failed to subscribe to MQTT topic', { error: error.message, topic });
      return false;
    }
  }

  /**
   * Publish a bid update to the bidding topic with MQTT 5 features
   * @param {string} tripId - The trip ID
   * @param {object} bidData - The bid data
   * @param {object} options - MQTT 5 publish options
   */
  publishBidUpdate(tripId, bidData, options = {}) {
    const topic = `${this.topics.bidding}${tripId}`;
    // Add bid-specific MQTT 5 properties
    const mqtt5Options = {
      userProperties: {
        bidType: bidData.type || 'standard',
        bidTimestamp: new Date().toISOString(),
        ...options.userProperties
      },
      // Set message expiry for bids (default 30 minutes)
      expiryInterval: options.expiryInterval || 1800,
      ...options
    };
    return this.publish(topic, bidData, mqtt5Options);
  }

  /**
   * Publish a trip status update to the trip status topic with MQTT 5 features
   * @param {string} tripId - The trip ID
   * @param {object} eventData - The event data (e.g., { event: 'created', trip: tripObject })
   * @param {object} options - MQTT 5 publish options
   */
  publishTripStatusUpdate(tripId, eventData, options = {}) {
    const topic = `${this.topics.tripStatus}${tripId}`;
    const mqtt5Options = {
      userProperties: {
        eventType: eventData.event || 'update',
        timestamp: new Date().toISOString(),
        ...(options.userProperties || {})
      },
      expiryInterval: options.expiryInterval || 3600, // Default 1 hour
      ...options
    };
    return this.publish(topic, eventData, mqtt5Options);
  }

  /**
   * Publish a chat message to the chat topic with MQTT 5 features
   * @param {string} tripId - The trip ID
   * @param {object} messageData - The message data
   * @param {object} options - MQTT 5 publish options
   */
  publishChatMessage(tripId, messageData, options = {}) {
    const topic = `${this.topics.chat}${tripId}`;
    // Add chat-specific MQTT 5 properties
    const mqtt5Options = {
      userProperties: {
        messageType: messageData.type || 'text',
        messageTimestamp: new Date().toISOString(),
        ...options.userProperties
      },
      // Set response topic for chat replies if not provided
      responseTopic: options.responseTopic || `${this.topics.chat}${tripId}/replies`,
      // Set correlation data for message threading if not provided
      correlationData: options.correlationData || Buffer.from(messageData.id || Date.now().toString()),
      ...options
    };
    return this.publish(topic, messageData, mqtt5Options);
  }

  /**
   * Subscribe to chat messages for a specific trip with MQTT 5 features
   * @param {string} tripId - The trip ID
   * @param {function} callback - The callback function for received messages
   * @param {object} options - MQTT 5 subscription options
   */
  subscribeToChatTopic(tripId, callback, options = {}) {
    const topic = `${this.topics.chat}${tripId}`;
    // Add chat-specific MQTT 5 subscription options
    const mqtt5Options = {
      userProperties: {
        subscriptionType: 'chat',
        tripId: tripId,
        ...options.userProperties
      },
      // Enable shared subscription if requested
      shared: options.shared || false,
      ...options
    };
    return this.subscribe(topic, callback, mqtt5Options);
  }

  /**
   * Subscribe to a shared topic with MQTT 5 features (load balancing)
   * @param {string} topic - The topic to subscribe to
   * @param {string} groupName - The shared subscription group name
   * @param {function} callback - The callback function for received messages
   * @param {object} options - Additional MQTT 5 subscription options
   */
  subscribeShared(topic, groupName, callback, options = {}) {
    // Format for shared subscriptions in MQTT 5: $share/{group}/{topic}
    const sharedTopic = `$share/${groupName}/${topic}`;

    const mqtt5Options = {
      userProperties: {
        subscriptionType: 'shared',
        groupName: groupName,
        ...options.userProperties
      },
      shared: true,
      ...options
    };

    return this.subscribe(sharedTopic, callback, mqtt5Options);
  }

  /**
   * Implement request-response pattern using MQTT 5 features
   * @param {string} requestTopic - The topic to send the request to
   * @param {object} requestData - The request data
   * @param {function} responseCallback - Callback for handling the response
   * @param {object} options - Additional MQTT 5 options
   * @returns {string} - The correlation ID for the request
   */
  sendRequest(requestTopic, requestData, responseCallback, options = {}) {
    if (!this.connected || !this.client) {
      logger.error('Cannot send request: MQTT client not connected');
      return null;
    }

    try {
      // Generate a unique correlation ID for this request
      const correlationId = options.correlationId || `req_${Date.now()}_${Math.random().toString(16).substring(2, 10)}`;

      // Create a unique response topic
      const responseTopic = options.responseTopic || `responses/${correlationId}`;

      // Subscribe to the response topic first
      this.subscribe(responseTopic, (responseData, packet) => {
        // Handle the response
        responseCallback(responseData, packet);

        // Unsubscribe from the response topic after receiving the response
        this.client.unsubscribe(responseTopic);
      }, {
        userProperties: { requestId: correlationId }
      });

      // Send the request with the response topic and correlation ID
      this.publish(requestTopic, requestData, {
        responseTopic: responseTopic,
        correlationData: Buffer.from(correlationId),
        userProperties: {
          requestType: options.requestType || 'standard',
          requestTimestamp: new Date().toISOString(),
          ...options.userProperties
        },
        // Set a reasonable expiry for the request (default 1 minute)
        expiryInterval: options.expiryInterval || 60,
        ...options
      });

      logger.info(`Sent request to ${requestTopic} with correlation ID ${correlationId}`);
      return correlationId;
    } catch (error) {
      logger.error('Failed to send request', { error: error.message, topic: requestTopic });
      return null;
    }
  }

  /**
   * Disconnect from the MQTT broker with MQTT 5 features
   * @param {object} options - MQTT 5 disconnect options
   */
  disconnect(options = {}) {
    if (!this.client) {
      logger.info('No MQTT client to disconnect');
      return;
    }

    try {
      // MQTT 5 disconnect options
      const disconnectOptions = {
        reasonCode: options.reasonCode || 0, // Normal disconnect
        properties: {
          sessionExpiryInterval: 0, // End the session
          reasonString: options.reasonString || 'Normal disconnect',
          userProperties: options.userProperties || {},
          ...options.properties
        }
      };

      this.client.end(true, disconnectOptions, () => {
        this.connected = false;
        logger.info('Disconnected from MQTT broker', {
          reasonCode: disconnectOptions.reasonCode,
          reasonString: disconnectOptions.properties.reasonString
        });
      });
    } catch (error) {
      logger.error('Error disconnecting from MQTT broker', { error: error.message });
    }
  }
}

// Create a singleton instance
const mqttService = new MQTTService();

export default mqttService;