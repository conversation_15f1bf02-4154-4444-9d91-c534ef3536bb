/**
 * Analytics Resource Configuration
 * Defines the AdminJS resource for Analytics and Reporting
 * PRD Reference: Sections 4.11, 8.1, 10.2
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import mongoose from 'mongoose';
import logger from '../../../../utils/logger.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

// Register realTime Dashboard component
 const reaTtimeDashboardComponent = componentLoader.add(
  'realTimeDashboard', 
  join(__dirname, '../components/real-time-dashboard') // Path to your React component
);

// Register historical Reports component
const historicalReportsComponent = componentLoader.add(
  'historicalReports', 
  join(__dirname, '../components/historical-reports') // Path to your component
);
// Register user Growth Report component
const userGrowthReportComponent = componentLoader.add(
  'userGrowthReport', 
  join(__dirname, '../components/user-growth-report') // Path to your React component
);

// Register trip Analytics Report component
const tripAnalyticsReportComponent = componentLoader.add(
  'tripAnalyticsReport', 
  join(__dirname, '../components/trip-analytics-report') // Path to your component
);
// Register revenue Report component
const revenueReportComponent = componentLoader.add(
  'revenueReport', 
  join(__dirname, '../components/revenue-report') // Path to your React component
);

// Register geo Distribution Report component
const geoDistributionReportComponent = componentLoader.add(
  'geoDistributionReport', 
  join(__dirname, '../components/geo-distribution-report') // Path to your component
);
// Register abuse Detection Report component
const abuseDetectionReportComponent = componentLoader.add(
  'abuseDetectionReport', 
  join(__dirname, '../components/abuse-detection-report') // Path to your React component
);

// Register export Reports component
const exportReportsComponent = componentLoader.add(
  'exportReports', 
  join(__dirname, '../components/export-reports') // Path to your component
);










/**
 * Analytics resource configuration
 * This is a virtual resource that doesn't directly map to a MongoDB collection
 * but provides access to analytics dashboards and reports
 */
export default {
  id: 'analytics',
  name: 'Analytics',
  options: {
    navigation: {
      name: 'Analytics & Reporting',
      icon: 'Chart',
      priority: 50,
    },
    // Since this is a virtual resource, we don't need actual properties
    listProperties: ['id', 'name', 'description'],
    showProperties: ['id', 'name', 'description'],
    // Disable standard CRUD actions
    actions: {
      list: {
        isVisible: false,
      },
      new: {
        isVisible: false,
      },
      edit: {
        isVisible: false,
      },
      delete: {
        isVisible: false,
      },
      // Custom action for real-time dashboard
      realTimeDashboard: {
        actionType: 'resource',
        icon: 'Dashboard',
        label: 'Real-Time Metrics',
        component: reaTtimeDashboardComponent,
        handler: async (request, response, context) => {
          try {
            // Log dashboard access
            logger.info('Real-time dashboard accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Real-time dashboard loaded',
            };
          } catch (error) {
            logger.error('Error loading real-time dashboard', { error: error.message });
            return {
              message: 'Error loading real-time dashboard',
              error: error.message,
            };
          }
        },
      },
      // Custom action for historical reports
      historicalReports: {
        actionType: 'resource',
        icon: 'Document',
        label: 'Historical Reports',
        component: historicalReportsComponent,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('Historical reports accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Historical reports loaded',
            };
          } catch (error) {
            logger.error('Error loading historical reports', { error: error.message });
            return {
              message: 'Error loading historical reports',
              error: error.message,
            };
          }
        },
      },
      // Custom action for user growth reports
      userGrowthReport: {
        actionType: 'resource',
        icon: 'User',
        label: 'User Growth',
        component: userGrowthReportComponent,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('User growth report accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'User growth report loaded',
            };
          } catch (error) {
            logger.error('Error loading user growth report', { error: error.message });
            return {
              message: 'Error loading user growth report',
              error: error.message,
            };
          }
        },
      },
      // Custom action for trip analytics
      tripAnalyticsReport: {
        actionType: 'resource',
        icon: 'Car',
        label: 'Trip Analytics',
        component: tripAnalyticsReportComponent ,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('Trip analytics report accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Trip analytics report loaded',
            };
          } catch (error) {
            logger.error('Error loading trip analytics report', { error: error.message });
            return {
              message: 'Error loading trip analytics report',
              error: error.message,
            };
          }
        },
      },
      // Custom action for revenue reports
      revenueReport: {
        actionType: 'resource',
        icon: 'Money',
        label: 'Revenue Analytics',
        component: revenueReportComponent,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('Revenue report accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Revenue report loaded',
            };
          } catch (error) {
            logger.error('Error loading revenue report', { error: error.message });
            return {
              message: 'Error loading revenue report',
              error: error.message,
            };
          }
        },
      },
      // Custom action for geo-distribution reports
      geoDistributionReport: {
        actionType: 'resource',
        icon: 'Globe',
        label: 'Geo-Distribution',
        component: geoDistributionReportComponent,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('Geo-distribution report accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Geo-distribution report loaded',
            };
          } catch (error) {
            logger.error('Error loading geo-distribution report', { error: error.message });
            return {
              message: 'Error loading geo-distribution report',
              error: error.message,
            };
          }
        },
      },
      // Custom action for abuse detection reports
      abuseDetectionReport: {
        actionType: 'resource',
        icon: 'Alert',
        label: 'Abuse Detection',
        component: abuseDetectionReportComponent ,
        handler: async (request, response, context) => {
          try {
            // Log report access
            logger.info('Abuse detection report accessed', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
            });
            
            return {
              message: 'Abuse detection report loaded',
            };
          } catch (error) {
            logger.error('Error loading abuse detection report', { error: error.message });
            return {
              message: 'Error loading abuse detection report',
              error: error.message,
            };
          }
        },
      },
      // Custom action for exporting reports
      exportReports: {
        actionType: 'resource',
        icon: 'Download',
        label: 'Export Reports',
        component: exportReportsComponent,
        handler: async (request, response, context) => {
          try {
            const { reportType, format, dateRange } = request.payload;
            
            // Log export action
            logger.info('Report export initiated', {
              admin_id: context.currentAdmin?._id,
              admin_email: context.currentAdmin?.email,
              ip: request.ip,
              reportType,
              format,
              dateRange,
            });
            
            return {
              message: 'Export initiated',
              reportType,
              format,
            };
          } catch (error) {
            logger.error('Error exporting report', { error: error.message });
            return {
              message: 'Error exporting report',
              error: error.message,
            };
          }
        },
      },
    },
  },
};