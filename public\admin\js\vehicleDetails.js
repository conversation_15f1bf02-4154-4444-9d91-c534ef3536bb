angular.module('vehicleDetails.controllers', [])

    .controller('vehicleDetailsCtrl', function ($scope, APIService,$state, $stateParams) {
        $scope.page = 'main';
        $scope.vehiclesDetails =[];

   




        if($stateParams.data){
            console.log($stateParams.data)
            // $scope.vehiclesDetails = JSON.parse($stateParams.data);
            $scope.userDetails = JSON.parse($stateParams.data);
            $scope.userId = $scope.userDetails._id;
            // $scope.userId = $scope.vehiclesDetails[0].user_id;
            console.log( $scope.vehiclesDetails)

            APIService.getData({ req_url: PrefixUrl + "/vehicle/"+$scope.userId}).then(function (res) {
                console.log(res)
    
                $scope.vehiclesDetails = res.data;
                console.log($scope.userName);
                // $scope.vehiclesDetails=res.data;
               
            
            },function(er){
                localStorage.removeItem("UserDeatails");
                localStorage.removeItem("token");
                $state.go('login');
            })


            var userData=localStorage.getItem('UserDeatails');
            var parsedUser= JSON.parse(userData);
            // console.log(parsedUser.user_details)
            if (parsedUser == null || parsedUser.user_details.role != 'admin') {
              localStorage.removeItem("UserDeatails");
              localStorage.removeItem("token");
              $state.go('login');
            }
        }
  
        $scope.alltrips = function(prod){
            $state.go("app.tripDetails",{data:JSON.stringify(prod)})
        console.log(prod);
        }



        $scope.filterVehicleList = function(startDate,endDate){



          if ($scope.reg_no) {
              var reg_no= $scope.reg_no;
          }else{
              var reg_no= null;
          }

          if ($scope.category) {
              var category= $scope.category;
          }else{
              var category= null;
          }

          if ($scope.vehical_model) {
              var vehical_model= $scope.vehical_model;
          }else{
              var vehical_model= null;
          }


        

          APIService.setData({
              req_url: PrefixUrl + '/vehicle/filterVehicleDetailsVehicleList' ,data:{user_id:$scope.userId, reg_no:reg_no, category:category, vehical_model: vehical_model} 
          }).then(function(resp) {
            $scope.vehiclesDetails= resp.data;
          },function(resp) {
           
          });
        }


})