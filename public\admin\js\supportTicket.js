angular.module('supportTicket.controllers', [])

    .controller('supportTicketCtrl', function ($scope,$state,APIService,$stateParams) {
     $scope.page = 'main';
     $scope.userDetails = [];
     $scope.title;
     $scope.category;
     $scope.description;
     $scope.filterDetail= {};
    $scope.pageLimit;

        
    $scope.settings = {
      currentPage: 0,
      offset: 0,
      pageLimit: 10,
      pageLimits: [2, 5, 10,20,100]
    };
    $scope.filterSearch= false;


    //  $scope.getSupportTicket = function(){
    //   // console.log('totalData',getSupportTicket())
    //     APIService.setData({
    //         req_url: PrefixUrl + '/SupportTicket/getTicketsByAdmin/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,status:true}
    //     }).then(function(resp) {
    //       console.log('totalData',resp.data)
    //       $scope.SupportTicket= resp.data;
    //        },function(resp) {
    //           // This block execute in case of error.
    //     });
    //   }
    // $scope.getSupportTicket();



    $scope.$watch('settings.currentPage', function (value) {
      console.log('currentPage'+$scope.settings.currentPage)  
      console.log('userDetailslll='+$scope.userDetails.length)
      $scope.pageLimit= $scope.settings.pageLimit;

    // if($scope.filterSearch){
        console.log('111111112222222222')

        // APIService.setData({
        //       req_url: PrefixUrl + '/SupportTicket/getTicketsByAdmin/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,status:true}
        //   }).then(function(resp) {
        //     console.log("====respPagination======",resp);
        //     $scope.SupportTicket=resp.data
        //   // $scope.userDetailsLength= $scope.userDetails.length;
        //      },function(resp) {
        //         // This block execute in case of error.
        //   });
      // }
      // else{

       
        APIService.setData({
              req_url: PrefixUrl + '/SupportTicket/getTicketsByAdmin/',data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,status:true}
          }).then(function(resp) {
            console.log("====respPagination======",resp);
            $scope.SupportTicket=resp.data
          // $scope.userDetailsLength= $scope.userDetails.length;
             },function(resp) {
                // This block execute in case of error.
          });
      // } 
      }    
    
    );



      $scope.getSupportTicketCountAll = function(){
        // console.log('totalData',getSupportTicket())
          APIService.setData({
              req_url: PrefixUrl + '/SupportTicket/getSupportTicketCountAll/'
          }).then(function(resp) {
            console.log('totalData',resp.data)
            $scope.totalTicket= resp.data[0].myCount;
             },function(resp) {
                // This block execute in case of error.
          });
        }
      $scope.getSupportTicketCountAll();



    $scope.filterTicket = function(user) {

      // $scope.filterSearch =true;

        if ($scope.filterDetail.serial_number) {
            $scope.filterDetail.serial_number= $scope.filterDetail.serial_number;
        }else{
            $scope.filterDetail.serial_number= null;
        }

        if ($scope.filterDetail.phone_number) {
            $scope.filterDetail.phone_number= $scope.filterDetail.phone_number;
        }else{
            $scope.filterDetail.phone_number= null;
        }

        if ($scope.filterDetail.status) {
          if ($scope.filterDetail.status == "true") {
            $scope.filterDetail.status= true;
          }else if ($scope.filterDetail.status == "false") {
            $scope.filterDetail.status= false;
          }
        }else{
            $scope.filterDetail.status= null;
        }

        $scope.filterDetail.status=true,

        $scope.filterDetail.currentPage= $scope.settings.currentPage;
        $scope.filterDetail.page_limit= $scope.settings.pageLimit;

       
        APIService.setData({
            req_url: PrefixUrl + '/SupportTicket/filterSupportTicket' ,data:$scope.filterDetail  
        }).then(function(resp) {
          $scope.SupportTicket= resp.data;
        },function(resp) {
         
        });
    };


    $scope.ticketAction = function(ticket) {
      if (ticket.status) 
      {
        console.log('Close')
        if (confirm('Are you sure you want to Close Ticket')) {
          $scope.updateTicket(ticket, false);
        }else{

        }

      }else{
        console.log('open')
        if (confirm('Are you sure you want to Open Ticket')) {
          $scope.updateTicket(ticket, true);
        }else{
          
        }


      }
    }

    $scope.updateTicket = function(ticket, status) {
      APIService.updateData({
            req_url: PrefixUrl + '/SupportTicket/'+ ticket._id,data:{status:status}
        }).then(function(resp) {
            location.reload();
           },function(resp) {
              // This block execute in case of error.
        });
    }

    $scope.ticketDetail = function(ticket) {
     
      console.log(ticket)
      $state.go('app.supportTicketDetail',{data:JSON.stringify(ticket)});

    }

  })



