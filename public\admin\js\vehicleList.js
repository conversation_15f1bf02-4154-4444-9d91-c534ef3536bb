angular.module('vehicleList.controllers', [])

    .controller('VehicleListCtrl', function ($scope, APIService,$state, $stateParams) {
        $scope.page = 'main';
        $scope.reg_no;
        $scope.category;
        $scope.vehical_model;
        $scope.buisness_name;
        $scope.district;
        $scope.state;
        $scope.pageLimit;
        $scope.filterSearch =false;


      $scope.settings = {
        currentPage: 0,
        offset: 0,
        pageLimit: 10,
        pageLimits: [2, 5, 10,20,100]
      };


   

      $scope.totalVehiclesForFirstTime = function(startDate,endDate){
        APIService.setData({
            req_url: PrefixUrl + '/vehicle/totalVehiclesForFirstTime/'
        }).then(function(resp) {
          console.log('totalVehicles',resp.data)
          $scope.totalVehicles= resp.data;
           },function(resp) {
              // This block execute in case of error.
        });
      }

      $scope.totalVehiclesCount = function(startDate,endDate){

          $scope.filterSearch =true;

          if (startDate) {
              $scope.startDate= startDate;
          }else{
              $scope.startDate= null;
          }

          if (endDate) {
            $scope.endDate= endDate;
          }else{
              $scope.endDate= null;
          }

          if ($scope.reg_no) {
              $scope.reg_no= $scope.reg_no;
          }else{
              $scope.reg_no= null;
          }

          if ($scope.category) {
              $scope.category= $scope.category;
          }else{
              $scope.category= null;
          }

          if ($scope.vehical_model) {
              $scope.vehical_model= $scope.vehical_model;
          }else{
              $scope.vehical_model= null;
          }


          if ($scope.buisness_name) {
              $scope.buisness_name= $scope.buisness_name;
          }else{
              $scope.buisness_name= null;
          }


          if ($scope.district) {
              $scope.district= $scope.district;
          }else{
              $scope.district= null;
          }

          if ($scope.state) {
              $scope.state= $scope.state;
          }else{
              $scope.state= null;
          }


          if ($scope.phone_number) {
              $scope.phone_number= $scope.phone_number;
          }else{
              $scope.phone_number= null;
          }


          APIService.setData({
              req_url: PrefixUrl + '/vehicle/totalVehicles/' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, reg_no:$scope.reg_no, category:$scope.category, vehical_model: $scope.vehical_model,buisness_name:$scope.buisness_name,district: $scope.district, state:$scope.state,phone_number:$scope.phone_number} 
          }).then(function(resp) {
            // $scope.vehiclesDetails= resp.data;
            $scope.totalVehicles= resp.data[0].myCount;

          },function(resp) {
           
          });
      }

        $scope.getProductDetails = function(){


          $scope.$watch('settings.pageLimit', function (pageLimit) {
            console.log('pageLimits'+pageLimit)
            $scope.pageLimit= pageLimit;
            if($scope.filterSearch){
              APIService.setData({
                req_url: PrefixUrl + '/vehicle/filterVehicleList' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, reg_no:$scope.reg_no, category:$scope.category, vehical_model: $scope.vehical_model,buisness_name:$scope.buisness_name,district: $scope.district, state:$scope.state,phone_number:$scope.phone_number} 
              }).then(function(resp) {
                console.log("====respPagination======",resp);
                $scope.vehiclesDetails=resp.data
              // $scope.userDetailsLength= $scope.userDetails.length;
                 },function(resp) {
                    // This block execute in case of error.
                     localStorage.removeItem("UserDeatails");
                    localStorage.removeItem("token");
                    $state.go('login');
              });
            }else{
              APIService.setData({
                  req_url: PrefixUrl + '/vehicle/getVehicleListPagination' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
              }).then(function(resp) {
                console.log("====respPagination======",resp);
                $scope.vehiclesDetails=resp.data
              // $scope.userDetailsLength= $scope.userDetails.length;
                 },function(resp) {
                    // This block execute in case of error.
                     localStorage.removeItem("UserDeatails");
                    localStorage.removeItem("token");
                    $state.go('login');
              });
            }
          }
          );


        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        // console.log(parsedUser.user_details)
        if (parsedUser == null || parsedUser.user_details.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }


          $scope.$watch('settings.currentPage', function (value) {
            if($scope.filterSearch){
              console.log('currentPage'+$scope.settings.currentPage)  
              APIService.setData({
                req_url: PrefixUrl + '/vehicle/filterVehicleList' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, reg_no:$scope.reg_no, category:$scope.category, vehical_model: $scope.vehical_model,buisness_name:$scope.buisness_name,district: $scope.district, state:$scope.state,phone_number:$scope.phone_number} 
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.vehiclesDetails=resp.data
                // $scope.userDetailsLength= $scope.userDetails.length;
                   },function(resp) {
                      // This block execute in case of error.
                });
            }else{
              APIService.setData({
                    req_url: PrefixUrl + '/vehicle/getVehicleListPagination' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit}
                }).then(function(resp) {
                  console.log("====respPagination======",resp);
                  $scope.vehiclesDetails=resp.data
                // $scope.userDetailsLength= $scope.userDetails.length;
                   },function(resp) {
                      // This block execute in case of error.
              });
            }
          }
          );
            // APIService.getData({ req_url: PrefixUrl + "/vehicle"}).then(function (res) {
            //     console.log(res)
    
            //     $scope.vehiclesDetails=res.data;
               
            
            // },function(er){
            //     console.log('eeeeeeeeee')
            //     localStorage.removeItem("UserDeatails");
            //     localStorage.removeItem("token");
            //     $state.go('login');

            // })
    
        }

            $scope.getProductDetails();
            // $scope.totalVehicles();
            $scope.totalVehiclesForFirstTime();
        
         

        // $scope.removeProduct = function(productId){
        //     APIService.removeData({ req_url: PrefixUrl + "/product/"+productId}).then(function (res) {
        //         console.log(res)
        //         alert("Product is deleted.");
        //         $scope.getProductDetails();
               
            
        //     },function(er){
             
        //     })
    
        // }

        $scope.filterVehicleList = function(startDate,endDate){
        

          $scope.filterSearch =true;

          if (startDate) {
              $scope.startDate= startDate;
          }else{
              $scope.startDate= null;
          }

          if (endDate) {
            $scope.endDate= endDate;
          }else{
              $scope.endDate= null;
          }

          if ($scope.reg_no) {
              $scope.reg_no= $scope.reg_no;
          }else{
              $scope.reg_no= null;
          }

          if ($scope.category) {
              $scope.category= $scope.category;
          }else{
              $scope.category= null;
          }

          if ($scope.vehical_model) {
              $scope.vehical_model= $scope.vehical_model;
          }else{
              $scope.vehical_model= null;
          }


          if ($scope.buisness_name) {
              $scope.buisness_name= $scope.buisness_name;
          }else{
              $scope.buisness_name= null;
          }


          if ($scope.district) {
              $scope.district= $scope.district;
          }else{
              $scope.district= null;
          }

          if ($scope.state) {
              $scope.state= $scope.state;
          }else{
              $scope.state= null;
          }


          if ($scope.phone_number) {
              $scope.phone_number= $scope.phone_number;
          }else{
              $scope.phone_number= null;
          }


          APIService.setData({
              req_url: PrefixUrl + '/vehicle/filterVehicleList' ,data:{currentPage:$scope.settings.currentPage,page_limit:$scope.pageLimit,startDate:$scope.startDate,endDate:$scope.endDate, reg_no:$scope.reg_no, category:$scope.category, vehical_model: $scope.vehical_model,buisness_name:$scope.buisness_name,district: $scope.district, state:$scope.state,phone_number:$scope.phone_number} 
          }).then(function(resp) {
            $scope.vehiclesDetails= resp.data;
            $scope.totalVehiclesCount(startDate,endDate);

          },function(resp) {
           
          });
        }


        $scope.filterByUserVehicleList = function(prod){
          APIService.setData({
              req_url: PrefixUrl + '/vehicle/filterByUserVehicleList' ,data:{ buisness_name:$scope.buisness_name, district: $scope.district, state:$scope.state} 
          }).then(function(resp) {
            var usersList= resp.data;
            var vehicleList=[];
            var userObject=[];
            usersList.forEach(function (key) {
                // key.vehicleDetails;
                userObject=key;
                key.vehicleDetails.forEach(function (vehicle) {
                    console.log('vehicle')
                    console.log(vehicle)
                    vehicleList.push(vehicle);
                });

            });
            console.log('llllllllllllll')
            console.log(vehicleList)
            vehicleList.forEach(function (vehicle) {
                // console.log('vehicle')
                // console.log(vehicle)
                vehicle['userDetails']=userObject;
            });

            $scope.vehiclesDetails= vehicleList;

            // $scope.vehiclesDetails= resp.data;
          //   APIService.setData({
          //     req_url: PrefixUrl + '/vehicle/filterByUserVehicleList' ,data:{ buisness_name:$scope.buisness_name} 
          // }).then(function(resp) {
          //   // $scope.vehiclesDetails= resp.data;
          // },function(resp) {
           
          // });
          },function(resp) {
           
          });
        }

        $scope.alltrips = function(prod){
            $state.go("app.tripDetails",{data:JSON.stringify(prod)})
        console.log(prod._id);
         
        }


     
})