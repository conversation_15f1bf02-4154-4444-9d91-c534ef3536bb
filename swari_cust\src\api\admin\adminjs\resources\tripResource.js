/**
 * Trip Resource Configuration
 * Defines the AdminJS resource for Trip model
 * PRD Reference: Sections 4.2, 10.2, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Trip from '../../../../models/Trip.js';
import Customer from '../../../../models/Customer.js';
import Driver from '../../../../models/Driver.js';
import Bid from '../../../../models/Bid.js';
import logger from '../../../../utils/logger.js';
import mqttService from '../../../../services/mqttService.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const componentLoader = new ComponentLoader();

const coordsShowComponent = componentLoader.add(
  'coordsshow', 
  join(__dirname, '../components/coords-show') // Path to your React component
);

// Register the custom component
const customDaysShowComponent = componentLoader.add(
  'customDaysShow', 
  join(__dirname, '../components/custom-days-show') // Path to your component
);



// Register trip details
const tripDetailsComponent = componentLoader.add(
  'tripDetails', 
  join(__dirname, '../components/trip-details') // Path to your component
);

// Register bid management
const bidManagementComponent = componentLoader.add(
  'bidManagement', 
  join(__dirname, '../components/bid-management') // Path to your component
);
// Register trip Analytics
const tripAnalyticsComponent = componentLoader.add(
  'tripAnalytics', 
  join(__dirname, '../components/trip-analytics') // Path to your component
);
// Register bid Analytics
const bidAnalyticsComponent = componentLoader.add(
  'bidAnalytics', 
  join(__dirname, '../components/bid-analytics') // Path to your component
);
// Register trip Management
const tripManagementComponent = componentLoader.add(
  'tripManagement', 
  join(__dirname, '../components/trip-management') // Path to your component
);
// Register trip Map
const tripMapComponent = componentLoader.add(
  'tripMap', 
  join(__dirname, '../components/trip-map') // Path to your component
);
// Register trip Dashboard
const tripDashboardComponent = componentLoader.add(
  'tripDashboard', 
  join(__dirname, '../components/trip-dashboard') // Path to your component
);









/**
 * Trip resource configuration
 */
export default {
  resource: Trip,
  options: {
    navigation: {
      name: 'Trip Management',
      icon: 'Car',
    },
    listProperties: ['trip_type', 'pickup_address', 'dropoff_address', 'date_time', 'car_type', 'status', 'created_at'],
    filterProperties: ['trip_type', 'car_type', 'status', 'date_time', 'created_at', 'customer_id'],
    editProperties: ['status', 'notes'],
    showProperties: [
      'customer_id', 'trip_type', 'pickup_address', 'dropoff_address', 
      'pickup_coords', 'dropoff_coords', 'date_time', 'return_date_time', 
      'custom_days', 'car_type', 'status', 'notes', 'created_at', 'updated_at'
    ],
    properties: {
      customer_id: {
        isVisible: { list: false, filter: true, show: true, edit: false },
        reference: 'Customer',
        isRequired: true,
      },
      pickup_coords: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'mixed',
        components: {
          show: coordsShowComponent
        }
      },
      dropoff_coords: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'mixed',
        components: {
          show: coordsShowComponent
        }
      },
      date_time: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
      },
      return_date_time: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime',
      },
      custom_days: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'mixed',
        components: {
          show: customDaysShowComponent
        }
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        availableValues: [
          { value: 'pending', label: 'Pending' },
          { value: 'accepted', label: 'Accepted' },
          { value: 'in-progress', label: 'In Progress' },
          { value: 'completed', label: 'Completed' },
          { value: 'cancelled', label: 'Cancelled' },
        ],
      },
      created_at: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
      },
      updated_at: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime',
      },
    },
    actions: {
      // Custom action to view trip details with real-time updates
      tripDetails: {
        actionType: 'record',
        icon: 'Info',
        component: tripDetailsComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const tripId = record.params._id;
          
          try {
            const trip = await Trip.findById(tripId).populate('customer_id');
            
            if (!trip) {
              return {
                notice: {
                  message: 'Trip not found',
                  type: 'error',
                },
              };
            }
            
            // Log the action
            logger.info('Trip details viewed by admin', {
              adminId: currentAdmin._id,
              tripId: trip._id
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              trip: trip,
              notice: {
                message: 'Trip details loaded',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error loading trip details', { error: error.message });
            return {
              notice: {
                message: 'Error loading trip details',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view trip bids
      tripBids: {
        actionType: 'record',
        icon: 'Money',
        component: bidManagementComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const tripId = record.params._id;
          
          try {
            const trip = await Trip.findById(tripId);
            
            if (!trip) {
              return {
                notice: {
                  message: 'Trip not found',
                  type: 'error',
                },
              };
            }
            
            // Get bids for this trip
            const bids = await Bid.find({ trip_id: tripId }).populate('driver_id');
            
            // Calculate bid analytics
            const bidAmounts = bids.map(bid => bid.amount);
            const analytics = {
              averageBid: bidAmounts.length > 0 ? bidAmounts.reduce((a, b) => a + b, 0) / bidAmounts.length : 0,
              lowestBid: bidAmounts.length > 0 ? Math.min(...bidAmounts) : 0,
              highestBid: bidAmounts.length > 0 ? Math.max(...bidAmounts) : 0,
            };
            
            // Format bids for frontend
            const formattedBids = bids.map(bid => ({
              _id: bid._id,
              driver_id: bid.driver_id._id,
              driver_name: bid.driver_id.name,
              amount: bid.amount,
              status: bid.status,
              notes: bid.notes,
              created_at: bid.created_at
            }));
            
            // Log the action
            logger.info('Trip bids viewed by admin', {
              adminId: currentAdmin._id,
              tripId: trip._id,
              bidCount: bids.length
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              bids: formattedBids,
              analytics,
              notice: {
                message: 'Trip bids loaded',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error loading trip bids', { error: error.message });
            return {
              notice: {
                message: 'Error loading trip bids',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view trip analytics
      tripAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        component: tripAnalyticsComponent,
        handler: async (request, response, context) => {
          try {
            // Log the action
            logger.info('Trip analytics viewed by admin', {
              adminId: context.currentAdmin._id
            });
            
            return {
              message: 'Trip analytics loaded',
            };
          } catch (error) {
            logger.error('Error loading trip analytics', { error: error.message });
            return {
              notice: {
                message: 'Error loading trip analytics',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view bid analytics
      bidAnalytics: {
        actionType: 'resource',
        icon: 'Chart',
        component: bidAnalyticsComponent,
        handler: async (request, response, context) => {
          try {
            // Log the action
            logger.info('Bid analytics viewed by admin', {
              adminId: context.currentAdmin._id
            });
            
            return {
              message: 'Bid analytics loaded',
            };
          } catch (error) {
            logger.error('Error loading bid analytics', { error: error.message });
            return {
              notice: {
                message: 'Error loading bid analytics',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action for comprehensive trip management
      tripManagement: {
        actionType: 'resource',
        icon: 'Car',
        component: tripManagementComponent,
        handler: async (request, response, context) => {
          try {
            // Log the action
            logger.info('Trip management accessed by admin', {
              adminId: context.currentAdmin._id
            });
            
            return {
              message: 'Trip management loaded',
            };
          } catch (error) {
            logger.error('Error loading trip management', { error: error.message });
            return {
              notice: {
                message: 'Error loading trip management',
                type: 'error',
              },
            };
          }
        },
      },
      // Custom action to view trip on map
      tripMap: {
        actionType: 'record',
        icon: 'Location',
        component: tripMapComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const tripId = record.params._id;
          
          try {
            const trip = await Trip.findById(tripId);
            
            if (!trip) {
              return {
                notice: {
                  message: 'Trip not found',
                  type: 'error',
                },
              };
            }
            
            // Log the action
            logger.info('Trip map viewed by admin', {
              adminId: currentAdmin._id,
              tripId: trip._id
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              trip: trip,
              notice: {
                message: 'Trip map loaded',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error loading trip map', { error: error.message });
            return {
              notice: {
                message: 'Error loading trip map',
                type: 'error',
              },
            };
          }
        },
      },
    },
    // Custom dashboard for trips with tabs for different statuses
    dashboard: {
      component: tripDashboardComponent,
      handler: async (request, response, context) => {
        try {
          // Get counts for each status
          const pendingCount = await Trip.countDocuments({ status: 'pending' });
          const acceptedCount = await Trip.countDocuments({ status: 'accepted' });
          const inProgressCount = await Trip.countDocuments({ status: 'in-progress' });
          const completedCount = await Trip.countDocuments({ status: 'completed' });
          const cancelledCount = await Trip.countDocuments({ status: 'cancelled' });
          
          // Log the action
          logger.info('Trip dashboard viewed by admin', {
            adminId: context.currentAdmin._id
          });
          
          return {
            counts: {
              pending: pendingCount,
              accepted: acceptedCount,
              inProgress: inProgressCount,
              completed: completedCount,
              cancelled: cancelledCount,
              total: pendingCount + acceptedCount + inProgressCount + completedCount + cancelledCount
            }
          };
        } catch (error) {
          logger.error('Error loading trip dashboard', { error: error.message });
          return {
            notice: {
              message: 'Error loading trip dashboard',
              type: 'error',
            },
          };
        }
      },
    },
  },
};