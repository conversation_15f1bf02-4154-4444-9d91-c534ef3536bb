import mongoose from 'mongoose';
const Schema = mongoose.Schema;
const timeZone = require('mongoose-timezone');


const SuspendUserLogSchema = new Schema({
    user_id: String,
    suspend:{type:Boolean,default:false},
    suspend_remarks:String,
    activate_remarks:String,
    suspend_remarks_date:Date,
    activate_remarks_date:Date,
    created_at: Date,
    
    
});
SuspendUserLogSchema.plugin(timeZone, { paths: ['created_at','suspend_remarks_date','activate_remarks_date'] });

export default mongoose.model('SuspendUserLog', SuspendUserLogSchema);
