/**
 * Driver Resource Configuration
 * Defines the AdminJS resource for Driver model
 * PRD Reference: Sections 4.1, 10.3
 */

import AdminJS, {ComponentLoader} from 'adminjs';
import Driver from '../../../../models/Driver.js';
import Transaction from '../../../../models/Transaction.js';
import CancellationLog from '../../../../models/CancellationLog.js';
import logger from '../../../../utils/logger.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Helper function to check for abuse (>3 cancellations per day)
 * @param {string} userId - User ID to check
 * @param {string} userType - Type of user ('customer' or 'driver')
 * @returns {Promise<boolean>} - True if user has more than 3 cancellations today
 */
const checkForAbuse = async (userId, userType) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const cancellations = await CancellationLog.countDocuments({
    initiator_id: userId,
    initiator_type: userType,
    created_at: { $gte: today, $lt: tomorrow }
  });
  
  return cancellations >= 3;
};

const componentLoader = new ComponentLoader();

const flagUserActionComponent = componentLoader.add(
  'FlagUserAction', // Unique name for your component
  join(__dirname, '../components/flag-user-action') // Path to your React component
);

// Register the custom component
const adjustWalletActionComponent = componentLoader.add(
  'AdjustWalletAction', // Unique name for the component
  join(__dirname, '../components/adjust-wallet-action') // Path to your component
);

/**
 * Driver resource configuration
 */
export default {
  resource: Driver,
  options: {
    navigation: {
      name: 'User Management',
      icon: 'User',
    },
    listProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'created_at'],
    filterProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'created_at'],
    editProperties: ['name', 'email', 'phone', 'wallet_balance', 'is_flagged', 'flag_reason'],
    showProperties: ['name', 'email', 'phone', 'wallet_balance', 'average_rating', 'is_flagged', 'flag_reason', 'flagged_at', 'flagged_by', 'created_at', 'updated_at'],
    actions: {
      // Flag user action
      flagUser: {
        actionType: 'record',
        icon: 'Flag',
        component: flagUserActionComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const { flag_reason } = request.payload;
          
          try {
            const driverId = record.params._id;
            const driver = await Driver.findById(driverId);
            
            if (!driver) {
              return {
                notice: {
                  message: 'Driver not found',
                  type: 'error',
                },
              };
            }
            
            driver.is_flagged = true;
            driver.flag_reason = flag_reason;
            driver.flagged_at = new Date();
            driver.flagged_by = currentAdmin._id;
            
            await driver.save();
            
            // Log the action
            logger.info('Driver flagged by admin', {
              adminId: currentAdmin._id,
              driverId: driver._id,
              reason: flag_reason
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Driver has been flagged successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error flagging driver', { error: error.message });
            return {
              notice: {
                message: 'Error flagging driver',
                type: 'error',
              },
            };
          }
        },
      },
      // Unflag user action
      unflagUser: {
        actionType: 'record',
        icon: 'Reset',
        guard: 'Are you sure you want to remove the flag from this driver?',
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          
          try {
            const driverId = record.params._id;
            const driver = await Driver.findById(driverId);
            
            if (!driver) {
              return {
                notice: {
                  message: 'Driver not found',
                  type: 'error',
                },
              };
            }
            
            driver.is_flagged = false;
            driver.flag_reason = null;
            driver.flagged_at = null;
            driver.flagged_by = null;
            
            await driver.save();
            
            // Log the action
            logger.info('Driver unflagged by admin', {
              adminId: currentAdmin._id,
              driverId: driver._id
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Flag has been removed successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error unflagging driver', { error: error.message });
            return {
              notice: {
                message: 'Error removing flag',
                type: 'error',
              },
            };
          }
        },
        isVisible: (context) => context.record?.params.is_flagged === true,
      },
      // Adjust wallet action
      adjustWallet: {
        actionType: 'record',
        icon: 'Wallet',
        component: adjustWalletActionComponent,
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          const { amount, reason } = request.payload;
          
          try {
            const driverId = record.params._id;
            const driver = await Driver.findById(driverId);
            
            if (!driver) {
              return {
                notice: {
                  message: 'Driver not found',
                  type: 'error',
                },
              };
            }
            
            const parsedAmount = parseFloat(amount);
            const balanceBefore = driver.wallet_balance;
            const balanceAfter = balanceBefore + parsedAmount;
            
            // Create a transaction record
            const transaction = new Transaction({
              user_id: driverId,
              user_type: 'driver',
              transaction_type: 'admin_adjustment',
              amount: parsedAmount,
              notes: reason || 'Admin wallet adjustment',
              balance_before: balanceBefore,
              balance_after: balanceAfter
            });
            
            await transaction.save();
            
            // Update driver wallet balance
            driver.wallet_balance = balanceAfter;
            await driver.save();
            
            // Log the action
            logger.info('Driver wallet adjusted by admin', {
              adminId: currentAdmin._id,
              driverId: driver._id,
              amount: parsedAmount,
              reason: reason
            });
            
            return {
              record: record.toJSON(context.currentAdmin),
              notice: {
                message: 'Wallet has been adjusted successfully',
                type: 'success',
              },
            };
          } catch (error) {
            logger.error('Error adjusting wallet', { error: error.message });
            return {
              notice: {
                message: 'Error adjusting wallet',
                type: 'error',
              },
            };
          }
        },
      },
      // Check for abuse action - This is the fixed version of the incomplete function
      checkAbuse: {
        actionType: 'record',
        icon: 'Alert',
        handler: async (request, response, context) => {
          const { record, currentAdmin } = context;
          
          try {
            const driverId = record.params._id;
            const isAbusive = await checkForAbuse(driverId, 'driver');
            
            if (isAbusive) {
              return {
                notice: {
                  message: 'Driver has more than 3 cancellations today and may be abusive',
                  type: 'warning',
                },
              };
            } else {
              return {
                notice: {
                  message: 'Driver does not show signs of abuse',
                  type: 'success',
                },
              };
            }
          } catch (error) {
            logger.error('Error checking for abuse', { error: error.message });
            return {
              notice: {
                message: 'Error checking for abuse',
                type: 'error',
              },
            };
          }
        },
      },
    },
  },
};