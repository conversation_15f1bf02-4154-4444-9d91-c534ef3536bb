angular.module('notification.controllers', [])

    .controller('NotificationCtrl', function ($scope, APIService, $state,$rootScope) {

        $scope.admin ={};
        $scope.expireProduct=[];

        var userData=localStorage.getItem('UserDeatails');
        var parsedUser= JSON.parse(userData);
        console.log(parsedUser)
        if (parsedUser.role != 'admin') {
          localStorage.removeItem("UserDeatails");
          localStorage.removeItem("token");
          $state.go('login');
        }



   $scope.messageSent = function(admin){

    if(!admin.message){
        alert("Please Enter message.")
    }else {
       
        APIService.setData({ req_url: PrefixUrl + '/user/message', data: admin }).then(function (res) {
            console.log(res)

            alert('Message is sent successfully.')
            $scope.admin ={};
            //$scope.productDetails=res.data;
            //console.log( $scope.productDetails)
    
        },function(er){

        })
    }
       

    }

    $scope.getNotification = function(){
        APIService.getData({ req_url: PrefixUrl + "/user/notifi"}).then(function (res) {
            console.log(res)

            $scope.expireProduct=res.data;
           
        
        },function(er){
         
        })

    }
    $scope.getNotification();
   
  
   
})