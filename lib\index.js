import Responder from './expressResponder';
import * as express  from './express';
import logger from './logger';
import start from './app';
const cron = require("node-cron");
const fs = require("fs");

const app = { start };

export { Responder, express, logger, app };


  // schedule tasks to be run on the server   
    // cron.schedule("* * * * *", function() {
    //   console.log("running a task every minute");
    // });

    // app.listen(3128);